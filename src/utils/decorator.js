// 此处为对应的装饰器辅助类
import { reqDataField } from './parameter';
// 注册当前请求对应的上下文请求组
import { request } from 'bus-common-component/lib/extension';
// 引入成功或失败相关方法
import {
	InterceptResponse,
	handleError,
} from '@/api/error';
// 获取指定上下文地址的查询路由
const services = new request({
	address: '/bc-manage-server',
	InterceptResponse,
	handleError,
});
const qcServices = new request({
	address: '/qc-manage-server',
	InterceptResponse,
	handleError,
});
const bfServices = new request({
	address: '/bf-manage-server',
	InterceptResponse,
	handleError,
});
const khServices = new request({
	address: '/khyx-manage-server',
	InterceptResponse,
	handleError,
});
const workServices = new request({
	address: '/work-manage-server',
	InterceptResponse,
	handleError,
});
const qcBizServices = new request({
	address: '/qc-bizengine-server',
	InterceptResponse,
	handleError,
});
const waServices = new request({
	address: '/wa-manage-server',
	InterceptResponse,
	handleError,
});
const beServices = new request({
	address: '/bc-be-server',
	InterceptResponse,
	handleError,
});
const bffservices = new request({
	address: '/bc-bff-server',
	InterceptResponse,
	handleError,
});
// qcServices.setOption({
// 	headers: {
// 		'tk-token-authorization':
// 			'Bearer b8fb424e543245348f83cbbcc6e538c4|1800000',
// 	},
// });

/**
 * 请求方法装饰器
 * @description: 用于将请求的参数进行对应的默认格式转换处理,防止抛空等问题
 * @param {*} data 当前请求传入参数
 * @return {*}
 */
export function Parameters(data = []) {
	return function(target, name, descriptor) {
		let func = descriptor.value;
		descriptor.value = async function(...args) {
			let obj = args[0] || {};
			if(sessionStorage.getItem('vuex') && JSON.parse(sessionStorage.getItem('vuex')).userInfo){
				obj.merchantId = JSON.parse(sessionStorage.getItem('vuex')).userInfo.merchantId
			}
			// 声明公共参数接收
			this['param'] = {};
			// 传入指定的地址请求
			this.services = services;
			this.qcServices = qcServices;
			this.bfServices = bfServices;
			this.khServices = khServices;
			this.workServices = workServices;
			this.qcBizServices = qcBizServices;
			this.waServices = waServices;
			this.beServices = beServices;
			this.bffservices = bffservices;
			obj = reqDataField(obj, false);
			data.map((item) => {
				if (item == '_data') {
					this.param = obj;
					return;
				}
				this.param[item] = obj[item];
			});
			let res = await func.apply(this);
			// 请求成执行
			if (res.code == 0 && res.data) {
				res.data = reqDataField(res.data, true);
			}
			return res;
		};
	};
}
