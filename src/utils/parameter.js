// 引入参数格式转换
import { transformation } from "@config";

/**
 * 对标参数
 * @description: 当前对象中需要处理参数格式的参数进行对应的转化
 * @param {*} data 传入对象
 * @param {*} bool 解析方向-true接口反参 flase 接口入参
 * @return {*}
 */
export function reqDataField(data, bool) {
  if (data.constructor == Object) {
    Object.keys(data).map(item => {
      data[item] = transData(item, data[item], bool);
    })
  }
  return data;
}

/**
 * 参数格式转化
 * @description: 根据传入参数进行对应的参数格式转换
 * @param {*} item 转换字段
 * @param {*} val 转化值
 * @param {*} bool 转化方法
 * @return {*}
 */
function transData(item, val, bool) {
  let trans = transformation.filter(n => n.label.filter(k => k == item).length > 0);
  if (trans.length > 0) return bool ? trans[0].format(val) : trans[0].theFormat(val);
  return val;
}

// 修改时间格式话方法,用于格式化指定参数
Date.prototype.format = function (fmt) {
  let o = {
    "M+": this.getMonth() + 1, //月份 
    "d+": this.getDate(), //日 
    "h+": this.getHours(), //小时 
    "m+": this.getMinutes(), //分 
    "s+": this.getSeconds(), //秒 
    "q+": Math.floor((this.getMonth() + 3) / 3), //季度 
    "S": this.getMilliseconds() //毫秒 
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    }
  }
  return fmt;
}

// 获取Url指定参数
export function getUrlCodeParam(name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)'); //构造一个含有目标参数的正则表达式对象
  let url = window.location.search.replace(/%20/g, '%2B');
  url = decodeURIComponent(url);
  console.log('getUrlParam解码' + url);
  var r = url.substr(1).match(reg); //匹配目标参数
  // var r = window.location.search.substr(1).match(reg); //匹配目标参数
  if (r != null) return unescape(r[2]);
  return null; //返回参数值
}

//func 是事件处理程序，delay 是事件执行的延迟时间，单位：毫秒
export function debounce(func, delay) {
  var timer = null;
  return function() {
    var that = this;
    var args = arguments;
    //每次触发事件 都把定时器清掉重新计时
    clearTimeout(timer);
    timer = setTimeout(function() {
      //执行事件处理程序
      func.call(that, args);
    }, delay);
  };
}

// 格式化手机号
export function formatMobile(phoneNum) {
  let mobile;
  if (phoneNum.length > 7) {
    mobile = phoneNum.substring(0, 3) + '****' + phoneNum.slice(-4);
  } else {
    mobile = phoneNum;
  }
  return mobile;
}