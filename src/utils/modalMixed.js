export default {
  data() {
    return {
      typeTitle: "添加",
    };
  },
  props: {
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false
    },
    // 修改时传入参数
    parameterData: Object,
    // 操作类型
    dataType: String,
  },
  // 监听值变化
  watch: {
    parameterData: {
      handler(val) {
        if (typeof val == "object" && Object.keys(val).length > 0 && this.dataType == "set") {
          let data = JSON.parse(JSON.stringify(val))
          // 触发修改事件
          this.getModuleSet(data);
        }
      },
      deep: true,
    },
    dataType: {
      handler(val) {
        // 当前为添加事件时调用弹窗重置方法
        if (val == "add") this.getModuleReset(), this.typeTitle = "添加";
        // 当前为修改事件时调用修改方法
        if (val == "set" && typeof this.parameterData == "object" && Object.keys(this.parameterData).length > 0) this.getModuleSet(JSON.parse(JSON.stringify(this.parameterData))), this.typeTitle = "修改";
      },
      deep: true,
    },
  },
  computed: {
    // 监听弹窗是否开启
    isvisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", false);
        return val;
      }
    },
  },
}