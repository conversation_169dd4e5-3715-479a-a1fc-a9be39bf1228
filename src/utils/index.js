/*
 * @Author: your name
 * @Date: 2021-01-23 10:57:25
 * @LastEditTime: 2021-03-02 14:05:31
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \child\src\utils.js
 */
// 正则匹配规则
const regular = {
  oneFile: new RegExp("([^./]+)", "g"), // 匹配一级文件夹
};

/**
 * @name: 功能模块路由及状态获取
 * @desc: 读取功能模块下当前用户已开发的各项模块将其对应的配置加载到路由及状态中
 * @param {*}
 * @return {Array}
 */
export function fileLoading() {
  // 获取对应的文件夹
  let data = require.context("../module/", true, /\/$/);
  // 保存输出节点
  let file = [];
  // 遍历获取对应的文件夹组
  data.keys().map(item => {
    // 获取文件夹名称
    let obj = item.match(regular.oneFile);
    // 当前文件夹名为空时不进行对应的匹配
    if (!obj) return;
    // 保存当前匹配的文件夹及其对应的文件输出内容 此处不使用对应的import方法避免返回异步事件
    file.push(new fileLoad(obj[0]));
  });
  return file;
}

/**
 * 对照类
 */
class fileLoad {
  constructor(file) {
    // 文件夹名称
    this.file = file;
    try {
      // 对应功能的路由配置
      this.router = require(`@m/${file}/router.js`);
    } catch (e) {
      this.router = [];
    }
    try {
      // 对应功能的状态配置
      this.store = require(`@m/${file}/store.js`);
    } catch (e) {
      this.store = {};
    }
  }
  // 读取路由默认参数
  getRouterDefault() {
    return this.router.default || [];
  }
  // 读取状态默认参数
  getStoreDefault() {
    return this.store.default || {};
  }
  // 是否存在对应的状态渲染对象爱
  isStoreDefault() {
    return this.store.default && Object.keys(this.store.default).length > 0;
  }
}


// 对外输出对应的文件
export default {
  fileLoading
};