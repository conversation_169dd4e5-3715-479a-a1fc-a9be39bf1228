/*
 * @Author: your name
 * @Date: 2021-01-22 13:38:15
 * @LastEditTime: 2021-05-20 15:33:39
 * @LastEditors: liu quan
 * @Description: In User Settings Edit
 * @FilePath: \bus-child-view\src\main.js
 */
import '@babel/polyfill';
import Vue from 'vue';
import App from './App.vue';
// // 获取路由插件
import Router from 'vue-router';
import * as Cookies from 'js-cookie';

// // 引入模块进行对应的注册
import { store, routerData } from '@m';

import constant from '@constant';
// // 引入初始化方法
import initialization from './initialization';
import './assets/css/democss.scss'

Vue.config.productionTip = false;

import { base_dicts } from './assets/js/dicts.js';
Vue.prototype.$baseDict = {
	base: base_dicts,
};
// 导出子应用生命周期 挂载前
export async function bootstrap(props) {
	console.log(props);
}

// let instance = undefined;
// let router = undefined;

// 导出子应用生命周期 挂载前 挂载后
/**
 * 注意，实例化路由时，判断当运行在qiankun环境时，
 * 路由要添加前缀，
 * 前缀与主应用注册子应用函数genActiveRule("/viscount")内的参数一致
 * Authorization 登录请求头返回参数
 * ApplicationName 分配应用名称
 * **/
export async function mount({
	ApplicationName,
	Authorization,
} = {}) {
	let router = new Router({
		base: window.__POWERED_BY_QIANKUN__
			? `/${ApplicationName}/${constant.name}`
			: `${constant.name}/`,
		mode: 'history',
		routes: routerData.routes,
	});

	router.beforeEach(async (to, from, next) => {
		if (to.query['tk-jwt-authorization']) {
			//新开标签页|退出登录携带签名不一致 需要做模拟登录
			if (
				String(
					Cookies.get('tk-token-authorization')
				).replace('+', ' ') !=
					store.getters.authorization ||
				to.query['tk-jwt-authorization'] !==
					store.getters.tkJwtAuthorization
			) {
				await store.dispatch('getUserInfo', {
					token: to.query['tk-jwt-authorization'],
				});
				if (store.getters.authorization) {
					store.commit(
						'SETTKJWTAUTHORIZATION',
						to.query['tk-jwt-authorization']
					);
					next();
				}
			} else {
				next();
			}
		} else {
			next();
		}
	});

	Vue.use(initialization);

	Vue.mixin({
		data() {
			return {
				Authorization:
					Authorization ||
					store.getters.authorization,
			};
		},
	});

	loadJS(
		'/bc-manage-view/configuration.js?t=' +
			new Date().getTime()
	)
		.then(() => {
			let instance = new Vue({
				store,
				router,
				render: (h) => h(App),
			});
			window.$VueInstance = instance;

			if (!window.__POWERED_BY_QIANKUN__) {
				//样式文件从主应用获取http://192.168.90.101:8888/work-manage-view/login
				require([
					'@a/chunk-libs.26f78017.css',
					'@a/app.50bb3f6e.css',
				], () => {
					instance.$mount(`#${constant.name}`);
				});

				window.$store = Vue.prototype.$store = store;
			} else {
				instance.$mount(`#${constant.name}`);
			}
		})
		.catch((e) => {
			if (
				confirm('加载configuration.js错误' + e.name)
			) {
				window.location.reload();
			}
		});
}

// 导出子应用生命周期 挂载前 卸载后
export async function unmount() {
	// instance.$destroy();
	// instance = null;
	// router = null;
}

// 单独开发环境
window.__POWERED_BY_QIANKUN__ || mount();

function loadJS(urls) {
	if (!Array.isArray(urls)) {
		urls = [urls];
	}
	let success = 0;
	return new Promise(function(resolve, reject) {
		urls.every((url) => {
			createScriptElement(
				url,
				function() {
					++success;
					if (success === urls.length) {
						resolve();
					}
				},
				function(e) {
					reject(e);
				}
			);
			return true;
		});
	});
}

function createScriptElement(url, success, fail) {
	var script = document.createElement('script');
	script.type = 'text/javascript';
	script.onload = function() {
		success();
	};
	script.onerror = function(e) {
		fail(e);
	};
	script.src = url;
	document
		.getElementsByTagName('head')[0]
		.appendChild(script);
}
