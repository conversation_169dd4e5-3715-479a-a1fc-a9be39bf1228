const base_dicts = {
  isFlagList: [
    { value: "1", name: "是" },
    { value: "0", name: "否" },
  ], //是否 0:否 1:是
  validList: [
    { value: "1", name: "有效" },
    { value: "0", name: "无效" },
  ],
  supportList: [
    { value: "1", name: "支持" },
    { value: "0", name: "不支持" },
  ],
  needList: [
    { value: "1", name: "需要" },
    { value: "0", name: "不需要" },
  ],
  isBuffetList: [
    { value: "一步式", key: "1" },
    { value: "预指定", key: "2" },
    { value: "同时支持两种方式", key: "3" },
  ],
  itemTypeList: [
    { value: "String", key: "1" },
    { value: "Int", key: "0" },
  ], //配置参数值类型
  businessTypeList: [{ value: "网上开户", key: "stkkh" }], // 业务类型列表
  mediaTypeList: [
    { value: "身份证大头照", key: "idimg" },
    { value: "公安大头照", key: "gaimg" },
    { value: "正面免冠照", key: "nohatimg" },
    { value: "身份证正面", key: "idfrontimg" },
    { value: "身份证反面", key: "idbackimg" },
  ], // 影像类型列表
  stateList: [
    { value: "无效", key: "0" },
    { value: "有效", key: "1" },
  ], //状态类型
  brokerStateList: [
    { value: "启用", key: "1" },
    { value: "停止", key: "0" },
  ], //经纪人状态：1-启用 0-停止
  qusetionTypeList: [
    { value: "单选", key: "0" },
    { value: "多选", key: "1" },
    { value: "文本", key: "2" },
  ], //问题类型
  isTrueList: [
    { value: "否", key: "0" },
    { value: "是", key: "1" },
  ], //答案类型
  isPlatList: [
    { value: "金证", key: "1" },
    { value: "恒生", key: "0" },
  ], //答案类型
  flowInstanceStatusList: [
    { value: "受理中", key: "1" },
    { value: "审核中", key: "2" },
    { value: "办理中", key: "3" },
    { value: "审核不通过", key: "4" },
  ], //受理单状态
  taskSourceList: [
    { value: "全部", key: "-1" },
    { value: "认领", key: "1" },
    { value: "自动派单", key: "2" },
  ], //任务来源
  flowTypeList: [{ value: "其他业务", key: "1" }], //业务分类
  acceptTypeList: [{ value: "BOSS_见证", key: "1" }], //受理方式列表
  gatewayInstStatusList: [
    { key: -1, value: "全部" },
    { key: 1, value: "生成中" },
    { key: 2, value: "待处理" },
    { key: 3, value: "处理中" },
    { key: 4, value: "成功" },
    { key: 5, value: "终止" },
    { key: 6, value: "失败" },
  ], //任务管理-网关实例状态
  openAccountTypes: [
    { value: "下载app开户", key: "1" },
    { value: "H5开户", key: "2" },
    { value: "sdk开户", key: "3" },
  ], //开户方式
  channelTypes: [
    { value: "下载营业部开户", key: "1" },
    { value: "合作银行", key: "2" },
    { value: "三方渠道", key: "3" },
    { value: "其他", key: "4" },
  ], //渠道管理-渠道类型
  channelCustomeStateList: [
    { value: "未完成", key: "0" },
    { value: "已完成", key: "1" },
    { value: "作废", key: "2" },
  ], // 0:未提交 1:已提交 -1:作废
  exchangeTypeList: [
    { value: "深圳", key: "1" },
    { value: "上海", key: "2" },
  ], //重点客户管理-交易所类型
  monitorTypeList: [{ value: "基本限制", key: "1" }], //重点客户管理-监控类型
  idTypeList: [{ value: "身份证", key: "00" }], //重点客户管理-证件类型
  needAuditFlow: [
    "2000",
    "2007",
    "2008",
    "2011",
    "2012",
    "2013",
    "2014",
    "2015",
    "2020",
    "2021",
  ],
  hidePwds: [
    "g_client_bank_pwd",
    "g_trade_pwd",
    "g_fund_pwd",
    "g_xh_bank_pwd",
    "g_aux_fund_pwds",
    "g_yypt_bank_trd_pwd",
    "g_yypt_pay_pwd",
    "g_aux_bank_pwds",
    "g_cgbg_bank_pwd_change",
    "g_new_fund_pwd",
    "g_new_trade_pwd",
    "g_cgbg_fund_pwd_select",
    "g_cgkh_bank_pwd_open",
    "g_cgkh_fund_pwd_select",
    "g_pt jy_password",
    "g_mmcz_trade_pswd",
    "g _mmcz_fund_pswd",
  ], //密码字段
  auditDetailFliter: [
    "g_flow_instance_id",
    "g_flow_instance_status",
    "g_current_component_id",
    "g_if_expire",
    "g_flow_instance_unique",
    "g_component_path",
    "g_check_instance_id",
    "g_check_node_id",
    "g_check_staff_no",
    "g_check_status",
    "g_flow_result",
    "g_accept_date",
    "g_accept_time",
    "g_check_date",
    "g_check_time",
    "g_deal_date",
    "g_deal_time",
    "g_accept_end_date",
    "g_accept_end_time",
    "g_flow_id",
    "system_abbr",
    "client_type",
    "if_allowed_inherit",
    "archive_frequency",
    "invalid_time",
    "if_allowed_step_back",
    "developer_id",
    "accept_time_type",
    "deal_time_type",
    "allowed_terminal_type",
    "if_enable",
    "busi_person_id",
    "warn_mode",
    "wake_up_mode",
    "process_engine_type",
    "process_engine_funcno",
    "context_init_funcno",
    "business_abbr",
    "context_read_funcno",
    "context_write_funcno",
    "create_flow_funcno",
    "login_check_type",
    "if_need_check",
    "gateway_func_no",
    "check_temp_id",
    "if_must_single",
    "remind_temp_id",
    "accept_type",
    "flow_busi_type",
    "reject_jump_mode",
  ],
  selectList: [
    {
      value: "否",
      key: "0",
    },
    {
      value: "是",
      key: "1",
    },
  ], //是否默认选中
  pwdTypeList: [
    {
      value: "无操作",
      key: "0",
    },
    {
      value: "验密",
      key: "1",
    },
    {
      value: "设密",
      key: "2",
    },
  ], //密码操作类型
  checkList: [
    {
      value: "校验",
      key: "0",
    },
    {
      value: "不校验",
      key: "1",
    },
  ], //密码操作类型
  // 策略类型
  strategyTypeList: [
    {
      value: "前置条件检查",
      key: "1",
    },
    {
      value: "准入条件检查",
      key: "2",
    },
  ],
};
export { base_dicts };
