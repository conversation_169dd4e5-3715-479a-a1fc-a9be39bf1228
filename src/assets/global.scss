#bc-manage-view {
	/*工程自定义样式命名同工程名*/
	.mt5 {
		margin-top: 5px !important;
	}
}

#ba-manage-view,
#ba-manage-view-content {
	/* 统一样式调整 */
	.ant-calendar-picker,
	.ant-input:not(.ant-layout-header, .base_tree_input_search
			> .ant-input),
	.ant-select:not(.ant-pagination-options
			> .ant-select):not([style]) {
		width: 160px !important;
		border-radius: 2px !important;
	}
	.ant-table-wrapper {
		margin-top: 10px !important;
	}
	.ant-btn {
		border-radius: 2px !important;
	}
	.w260 {
		width: 260px;
	}
	.ant-divider-horizontal:not([style]) {
		margin: 1px 0 !important;
	}
	.ant-btn {
		margin: 0 10px !important;
	}
	/* 页头 */
	.ant-page-header {
		padding: 12px 24px !important;
	}
	.ant-page-header-heading-title {
		font-size: 14px !important;
		line-height: 22px !important;
	}
	/* 搜索表单样式 */
	.ant-advanced-search-form {
		padding: 24px !important;
	}
	.ant-advanced-search-form .ant-calendar-picker,
	.ant-input:not(.ant-layout-header, .base_tree_input_search
			> .ant-input),
	.ant-select:not(.ant-pagination-options
			> .ant-select):not([style]) {
		width: 206px !important;
		border-radius: 2px !important;
	}
	.ant-advanced-search-form .ant-form-item-label {
		width: 100px !important;
	}
	.ant-advanced-search-form
		.ant-form-item-control
		.ant-input-affix-wrapper {
		width: 206px !important;
	}
	.ant-advanced-search-form .ant-form-item {
		display: flex;
	}
	.ant-advanced-search-form
		.ant-form-item-control-wrapper {
		flex: 1;
	}
	.ant-modal-body
		.ant-layout
		.ant-input:not(.base_tree_input_search
			> .ant-input) {
		width: 298px !important;
		max-width: 298px;
	}
	.ant-modal-body .ant-layout .ant-select {
		width: 298px !important;
	}
}

/* 渠道编辑的样式特殊处理 */
.channel-base-input.ant-input {
	width: 160px !important;
}
.channel-base-edit .ant-select {
	width: 160px !important;
	border-radius: 2px;
}
.channel-base-edit .upload_div {
	width: 160px !important;
	border-radius: 2px;
}
.single_omit {
	margin-bottom: 0;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.header.ant-layout-header {
	height: auto;
}
.header_menu {
	display: inline-block;
}
.layout_content {
	height: calc(100vh - 100px);
}

.pop_header.ant-layout-header {
	background: #1890ff;
	height: 40px;
	line-height: 40px;
}
.pop_header_title,
.pop_header_icon {
	color: #fff;
}
.pop_content {
	padding: 10px 20px;
	background: #fff;
}
.pop_footer.ant-layout-footer {
	padding: 10px 50px;
	text-align: center;
	background: #fff;
}
.pop_footer .ant-btn {
	margin: 0 10px;
}
.pop_content .ant-form-item {
	margin-bottom: 0;
}
.pop_title {
	margin-bottom: 18px;
	padding: 0 10px;
	border-left: 5px solid #1890ff;
	font-weight: bold;
	font-size: 15px;
	line-height: 1;
}
.label_style {
	padding-right: 10px !important;
	text-align: right;
}
.pop_shadow {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.1);
	z-index: 1000;
}
.pop_content .ant-row {
	margin-bottom: 10px;
}
.mark_style {
	color: #ff0000;
}
.select_wid {
	width: 100%;
}
.upload_div {
	border: 1px solid #ccc;
	height: 32px;
	line-height: 32px;
	border-radius: 5px;
	color: rgba(0, 0, 0, 0.65);
	padding-right: 10px;
	display: flex;
	width: 298px;
}
.search_inp .label_style {
	display: inline-block;
	padding-right: 10px;
	text-align: right;
	width: 100px;
}
