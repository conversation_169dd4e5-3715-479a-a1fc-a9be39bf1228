@charset "utf-8";
/* reset start */

#bc-manage-view,
#bc-manage-view-content {
	/* 统一样式调整 */
	.ant-table-wrapper {
		margin-top: 10px !important;
	}
	.ant-btn {
		border-radius: 2px !important;
	}
	.w260 {
		width: 260px;
	}
	.ant-divider-horizontal:not([style]) {
		margin: 1px 0 !important;
	}
	.ant-btn {
		margin: 0 10px !important;
	}
	/* 页头 */
	.ant-page-header {
		padding: 12px 24px !important;
	}
	.ant-page-header-heading-title {
		font-size: 14px !important;
		line-height: 22px !important;
	}
	/* 搜索表单样式 */
	.ant-advanced-search-form {
		padding: 24px !important;
	}
	.ant-advanced-search-form .ant-form-item-label {
		width: 100px !important;
	}
	.ant-advanced-search-form
		.ant-form-item-control
		.ant-input-affix-wrapper {
		width: 206px !important;
	}
	.ant-advanced-search-form .ant-form-item {
		display: flex;
	}
	.ant-advanced-search-form
		.ant-form-item-control-wrapper {
		flex: 1;
	}
	.ant-modal-body
		.ant-layout
		.ant-input:not(.base_tree_input_search
			> .ant-input) {
		width: 298px !important;
		max-width: 298px;
	}
	.ant-modal-body .ant-layout .ant-select {
		width: 298px !important;
	}
}

.base_sty_column_box {
	height: 100%;
}

.base_sty_column_box .base_tree_nav_box {
	width: 300px;
	height: 100%;
	background: #ffffff;
	float: left;
	position: relative;
	overflow: auto;
}

.base_sty_column_box .base_tree_nav_list {
	position: relative;
}

.base_sty_column_box
	.base_tree_nav_box
	.base_tree_switch_line {
	width: 8px;
	height: 100%;
	background: #c8d1dc;
	position: absolute;
	right: 0;
	top: 0;
}
.base_sty_column_box
	.base_tree_nav_box
	.base_tree_switch_line {
	width: 8px;
	height: 100%;
	background: #c8d1dc;
	position: absolute;
	right: 0;
	top: 0;
}
.base_sty_column_right {
	/*padding: 24px;*/
	margin-left: 210px;
	height: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
}

.btn_row {
	height: auto !important;
	padding: 10px 24px 0px;
}

.base_sty_column_box {
	height: 100%;
}

.base_sty_column_box .base_tree_nav_box {
	width: 300px;
	height: 100%;
	background: #ffffff;
	float: left;
	position: relative;
	overflow: auto;
}

.base_sty_column_box .base_tree_nav_list {
	position: relative;
}

.base_sty_column_box
	.base_tree_nav_box
	.base_tree_switch_line {
	width: 8px;
	height: 100%;
	background: #c8d1dc;
	position: absolute;
	right: 0;
	top: 0;
}
.base_sty_column_box
	.base_tree_nav_box
	.base_tree_switch_line {
	width: 8px;
	height: 100%;
	background: #c8d1dc;
	position: absolute;
	right: 0;
	top: 0;
}

.base_tree_nav_box
	.base_tree_switch_line
	.base_tree_switch_btn,
.base_tree_nav_box
	.base_tree_switch_line
	.base_tree_switch_btn1 {
	display: block;
	width: 8px;
	height: 40px;
	background: #abbbcf;
	border-top: 1px solid #ffffff;
	border-bottom: 1px solid #ffffff;
	position: absolute;
	top: 50%;
	left: 0;
	margin-top: -21px;
}

.base_tree_nav_box
	.base_tree_switch_line
	.base_tree_switch_btn:before {
	content: '';
	width: 0;
	height: 0;
	border-top: 5px solid transparent;
	border-bottom: 5px solid transparent;
	border-right: 5px solid #ffffff;
	position: absolute;
	left: 1px;
	top: 50%;
	margin-top: -5px;
}
.base_tree_nav_box
	.base_tree_switch_line
	.base_tree_switch_btn1:before {
	content: '';
	width: 0;
	height: 0;
	border-top: 5px solid transparent;
	border-bottom: 5px solid transparent;
	border-left: 5px solid #ffffff;
	position: absolute;
	left: 1px;
	top: 50%;
	margin-top: -5px;
}
.base_sty_column_right {
	/*padding: 24px;*/
	margin-left: 210px;
	height: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
}

.base_sty_column_right .base_products_cet {
	height: 100%;
	padding: 24px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.base_sty_column_right .base_products_cet_param {
	padding: 24px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.base_tree_nav_box .ant-layout-header {
	padding: 0 10px;
}
.base_right_click_menu {
	width: 160px;
	padding: 5px;
	background: #ffffff;
	border: 1px solid #dddddd;
	-webkit-box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.16);
	box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.16);
	position: absolute;
	left: 80px;
	top: 200px;
	z-index: 99;
}

.base_right_click_menu a {
	display: block;
	height: 30px;
	font-size: 14px;
	color: #666666;
	line-height: 30px;
}

.base_right_click_menu a i {
	display: inline-block;
	width: 18px;
	height: 30px;
	margin: 0 8px;
	vertical-align: top;
}
.base_tree_nav_box
	.ant-tree
	li
	.ant-tree-node-content-wrapper.ant-tree-node-selected {
	color: #1890ff;
	background: none;
}
