<template>
	<a-modal
		title="导入"
		:width="500"
		v-model="isvisible"
		ok-text="确认"
		cancel-text="取消"
		@ok="submit"
		@cancel="getModuleReset"
		:maskClosable="false"
	>
		<a-form-model
			ref="form"
			:model="form"
			:label-col="{ span: 8 }"
			:wrapper-col="{ span: 14 }"
		>
			<a-form-model-item label="导入文件">
				<a-upload
					:fileList.sync="fileList"
					name="file"
					:multiple="true"
					:beforeUpload="() => false"
					@change="handleChange"
					accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, .json"
				>
					<a-button> <a-icon type="upload" /> 上传文件 </a-button>
				</a-upload>
			</a-form-model-item>
			<a-form-model-item label="模板文件" v-if="downloadeUrl">
				<a @click="downloademon">
					<a-icon type="download" />下载{{downloadeTitle}}
				</a>
			</a-form-model-item>
		</a-form-model>
	</a-modal>
</template>
<script>
	import modalMixed from "@u/modalMixed";
	import { request } from "bus-common-component/lib/extension";
	// import api from '../api';
	export default {
		data() {
			return {
				form: {}, // 权限添加表单
				dictFiledShow: false,
				// 异步加载
				confirmLoading: false,
				file: {},
				fileList: []
			};
        },
        props: {
            downloadeTitle: {
                type: String,
				default: ''
            },
            downloadeUrl: {
				type: String,
				default: ''
			},

            uploadUrl: {
				type: String,
				default: ''
			},
			baseUrl: {
				type: String,
				default: '/bc-manage-server'
			}
        },
		// inject: ["api"],
		mixins: [modalMixed],
		watch: {
			isvisible(newVal) {
				if (newVal) {
					this.file = undefined;
					this.fileList = [];
				}
			}
		},
		methods: {
			downloademon() {
				window.location.href = this.downloadeUrl
			},
			getModuleReset() {},
			// 提交数据权限分组创建
			submit() {
				if (!this.file || this.fileList.length <= 0)
					return this.$message.error(`上传文件不能为空`);
				let data = new FormData();
				data.set("file", this.file);
				new request({ address: this.baseUrl })
					.upload({
						reqUrl: this.uploadUrl,
						param: data
					})
					.then(({ code, msg }) => {
						if (code != 0)
							return this.$message.error(`上传失败：${msg}`);
						this.$message.success("上传成功！");
						this.isvisible = false;
						this.$emit("success");
					});
			},
			handleChange({ file, fileList }) {
				this.file = file;
				if (fileList.length >= 0 && fileList.length <= 1) {
					this.fileList = fileList;
				}
			}
		}
	};
</script>
