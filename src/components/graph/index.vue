<template>
  <div v-if="isInit"
    ref="graph"
    class="graph-div"
    :style="style"></div>
</template>

<script>
import GraphDraw from './graphDraw.js';
import api from './api';
// import { mapMutations, mapGetters } from 'vuex'

export default {
  name: 'GraphPage',
  // inject: ['api'],
  props: {
    // 类型
    pageType: {
      type: String,
      default: '1',
    },
    // 节点数据
    graphData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 运行结果数据
    resultData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    height: {
      type: Number,
      default: 0,
    },
    // 是否可编辑
    isEdit: {
      type: Boolean,
      default: false,
    },
    // // 运算符数据
    // operatorData: {
    //   type: Array,
    //   default: () => {
    //     return []
    //   },
    // },
  },
  data() {
    return {
      mxBasePath: '../src',
      src: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fhbimg.huabanimg.com%2F51e147ebd12fb6ab268089dfba99662dea89d5ee5a65a-YsmCkP_fw658&refer=http%3A%2F%2Fhbimg.huabanimg.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1647597972&t=89048ef2b4d7150e450aff0aaa20ef7e',
      style: {
        position: 'relative',
        overflow: 'hidden',
        cursor: 'default',
        minHeight: '0',
      },
      // operatorData: [], //运算符数据字典
      ruleTemplates: [], //资源区数据
      nodeEntities: {},
      opts: {
        width: 0,
        height: 800,
      },
      dictNum: 0, // 字典查询次数
      graph: null,
      nodeArr: [], // 节点数据
      lineArr: [], // 连接线数据

      timer: null,
      isInit: true, // 初始化标记

      isFirstCreated: false, //是否第一次加载
      createNodeNum: 0, // 创建完成节点树
      dictOptionsObj: {}, //字典集数据
      operatorData: [],
    };
  },
  watch: {
    graphData(val) {
      if (val) {
        console.log(val);
        this.isInit = false;
        let timer = setTimeout(() => {
          this.isInit = true;
          this.$nextTick(() => {
            this.initGraph();
            this.queryInfo();
          });
          clearTimeout(timer);
        }, 0);
      }
    },
    // 测试结果数据
    resultData(val) {
      if (val && !this.isEdit && val.result) {
        console.log(val);
        this.isInit = false;
        let timer = setTimeout(() => {
          this.isInit = true;
          this.$nextTick(() => {
            this.initGraph('test');
            // this.selectDataDictionaries()
            this.queryInfo();
          });
          clearTimeout(timer);
        }, 0);
      }
    },

    // 运算符数据
    operatorData(val) {
      if (val) {
        // this.initGraph()
        if (this.graph?.graph) {
          this.graph.graph.destroy();
          this.initGraph();
        }
        this.queryInfo();
      }
    },
  },

  created() {
    // this.selectDataDictionaries()
    this.isFirstCreated = true;
    let _this = this;

    this.opts.height = this.height;
    this.style.overflow = 'auto';
    if (this.isEdit) {
      document.onkeydown = function (e) {
        if (e.keyCode == 46) {
          _this.graph.removeCells();
        }
      };
    }
  },
  mounted() {
    console.log(this.isEdit);
    if (this.isEdit) {
      this.style.minHeight = document.body.clientHeight - 116 + 'px';
    }

    // console.dir(document.body.clientHeight - 116, 'height----')
    this.timer = setTimeout(() => {
      this.initGraph();
      this.queryInfo();
      clearTimeout(this.timer);
    }, 0);
    this.selectDataDictionaries();
  },
  methods: {
    selectDataDictionaries() {
      api
        .queryDataDictionaries({
          dictType: 'qc.bizengine.operator',
        })
        .then(({ code, data }) => {
          if (code != 0) return;
          console.log(data['qc.bizengine.operator'], '-------运算符数据字典');
          this.operatorData = [];
          this.operatorData = data['qc.bizengine.operator'];
          // this.$refs.graph.queryInfo()
          this.$nextTick(() => {
            if (this.graph?.graph) {
              this.graph.graph.destroy();
              this.initGraph();
            }
            this.queryInfo();
          });
        });
    },
    // 取消编辑节点
    cancelEdit() {
      let cell = this.graph.graph.getSelectionCell();
      this.$emit('changeActiveNode', this.graph.getNodeValue(cell));
    },
    // 清除图表
    clearGraph() {
      this.isInit = false;
      let timer = setTimeout(() => {
        this.isInit = true;
        this.$nextTick(() => {
          this.graph.graph.destroy();
          this.initGraph();
          if (this.pageType === '1') {
            this.graph.drawEndNode();
          }
          this.initSource();
        });
        clearTimeout(timer);
      }, 0);
    },
    // 查询运算符字典
    // selectDataDictionaries() {
    //   api
    //     .queryDataDictionaries({
    //       dictType: 'qc.bizengine.operator',
    //     })
    //     .then(({ code, data }) => {
    //       if (code != 0) return
    //       console.log(data['qc.bizengine.operator'], '-------运算符数据字典')
    //       this.operatorData = []
    //       this.operatorData = data['qc.bizengine.operator']
    //       this.queryInfo()
    //     })
    // },
    // 删除节点
    removeCells() {
      this.graph.removeCells();
    },
    // 设置或关系
    setOr() {
      this.graph.setOrRelation();
    },
    // 撤销
    undo() {
      console.log('撤销');
      console.log(this.graph.undoManager);
      this.graph.undoManager.undo();

      console.log(this.graph.undoManager);
    },
    // 重做
    redo() {
      console.log('重做');
      this.graph.undoManager.redo();
    },
    getMultiple(val) {
      if (
        val === '包含' ||
        val === '不包含' ||
        val === '属于' ||
        val === '不属于'
      ) {
        return true;
      } else {
        return false;
      }
    },
    // 查询节点详情
    queryInfo() {
      // this.api
      //   .queryDesignNodeInfo({ strategyId: this.$route.query.id })
      //   .then(({ code, data }) => {
      //     if (code != 0) return
      console.log('-----节点回显数据', this.graphData);
      this.dictNum = 0;
      let arr = [];
      if (this.graphData && this.graphData.length > 0) {
        this.graphData.forEach((item) => {
          item.nodes.forEach((item2) => {
            // console.log(item2)
            item2.ruleExpressions.forEach((item3) => {
              item3.forEach((it) => {
                console.log(it);
                if (it.elPropType === 'enum' && it.elJudgmentValue) {
                  console.log(it, this.dictOptionsObj);
                  if (!this.dictOptionsObj[it.elPropDict] && it.elPropDict) {
                    arr.push(it.elPropDict);
                  }
                }
              });
            });
          });
        });
      }
      let num = 0;
      arr.forEach((itName) => {
        this.queryDict(itName, this.graphData, arr, num);
      });
      if (arr.length === 0) {
        this.drawNodes(this.graphData);
      }
      console.log(arr);
      // })
      // console.log(result)
    },
    // 节点绘制
    drawNodes(data) {
      console.log(data, this.pageType);
      // if ((!data || data.length === 0) && this.pageType === '1') {
      //   this.graph.drawEndNode()
      // }

      let nodeArr = [];
      let lineArr = [];
      let rootNodeId = '';
      if (data && data.length > 0) {
        data.forEach((item) => {
          if (item.isRoot === '1') {
            lineArr.push({
              source: '2-Y',
              target: item.nodeGroupId + '-E',
            });
          }
          if (item.noTargetNodeGroupId) {
            item.noTargetNodeGroupId.split(',').forEach((item4) => {
              lineArr.push({
                source: item.nodeGroupId + '-N',
                target: item4 + '-E',
                value: 'No',
              });
            });
          }
          if (item.yesTargetNodeGroupId) {
            item.yesTargetNodeGroupId.split(',').forEach((item4) => {
              lineArr.push({
                source: item.nodeGroupId + '-Y',
                target: item4 + '-E',
                value: 'Yes',
              });
            });
          }
          if (
            !item.yesTargetNodeGroupId &&
            !item.noTargetNodeGroupId &&
            this.pageType === '1'
          ) {
            lineArr.push({
              source: item.nodeGroupId + '-Y',
              target: '3-E',
              value: 'Yes',
            });
          }

          item.nodes.forEach((item2) => {
            let obj = {
              label: item2.nodeTitle,
              id: item2.nodeId,
              rules: [],
              templateNo: item2.nodeRelatedNo,
              parentId: item.nodeGroupId,
              type: item2.nodeType,
              remark: item2.remark,
              strategyType: item2.strategyType,
            };
            item2.ruleExpressions.forEach((item3) => {
              let newArr = [];
              item3.forEach((it) => {
                let objData = {
                  key: it.elPropKey,
                  name: it.elPropName,
                  value: it.elJudgmentValue,
                  rule: this.getRuleValue(it.elOperatorKey, 1),
                  type: it.elPropType,
                  dict: it.elPropDict,
                  label: '',
                  selectOptions: [],
                  propFunc: it.elPropFunc,
                  qcConvertFunctionVO: it.qcConvertFunctionVO,
                  // elPropFunc: it.elPropFunc,
                };

                if (
                  objData.type === 'enum' &&
                  objData.value &&
                  this.getMultiple(objData.rule)
                ) {
                  let arr = objData.value.split(',');
                  let textArr = [];
                  objData.selectOptions = this.dictOptionsObj[objData.dict];
                  arr.forEach((e) => {
                    textArr.push(
                      this.dictOptionsObj[objData.dict].filter(
                        (el) => el.value === e
                      )[0]?.label
                    );
                  });
                  objData.value = arr;
                  objData.label = textArr.join(',');
                } else if (objData.type === 'enum' && objData.value) {
                  if (
                    this.dictOptionsObj[objData.dict] &&
                    this.dictOptionsObj[objData.dict].length > 0
                  ) {
                    console.log(objData);
                    objData.selectOptions = this.dictOptionsObj[objData.dict];
                    objData.label = this.dictOptionsObj[objData.dict].filter(
                      (el) => el.value === objData.value
                    )[0]?.label;
                  }
                }
                newArr.push(objData);
              });

              obj.rules.push(newArr);
              console.log(obj);
            });
            nodeArr.push(obj);
          });
        });
        // console.log(nodeArr, lineArr)
        this.nodeArr = nodeArr;
        this.lineArr = lineArr;

        let nodeObj = {};
        let passNodeArr = [];
        let errorNodeArr = [];
        let resultLineArr = [];
        if (this.resultData && this.resultData.strategyLog) {
          resultLineArr.push({
            source: '2-Y',
          });
          let resNodeArr = this.resultData.strategyLog.nodeLogs;
          resNodeArr.forEach((ite, idx) => {
            if (ite.nodeRuleResult === '1') {
              passNodeArr.push(ite.nodeId);
            } else {
              errorNodeArr.push(ite.nodeId);
            }

            if (idx != resNodeArr.length - 1) {
              let txt = ite.nodeRuleResult === '1' ? '-Y' : '-N';
              resultLineArr.push({
                source: ite.nodeId + txt,
                target: resNodeArr[idx + 1].nodeId + '-E',
                value: ite.nodeRuleResult === '1' ? 'Yes' : 'No',
                status: ite.nodeRuleResult === '1' ? 'Y' : 'N',
              });
            } else if (idx === resNodeArr.length - 1) {
              console.log(ite.nodeRuleResult);
              resultLineArr.push({
                source: ite.nodeId + '-Y',
                target: '3-E',
                value: ' ',
                status: ite.nodeRuleResult === '1' ? 'Y' : 'N',
              });
            }
          });
        }

        this.$nextTick(() => {
          console.log(rootNodeId);

          nodeArr.forEach((node) => {
            let ipInfo = {};

            ipInfo = {
              x: 0,
              y: 0,
            };
            if (passNodeArr.findIndex((el) => el === node.id) != -1) {
              node.status = 'Y';
            } else if (errorNodeArr.findIndex((el) => el === node.id) != -1) {
              node.status = 'N';
            } else {
              node.status = '';
            }
            if (node.id === node.parentId) {
              // console.log(node, this.graph)
              this.graph.drawRuleNode(node, ipInfo);
            } else if (nodeObj[node.parentId]) {
              if (passNodeArr.findIndex((el) => el === node.parentId) != -1) {
                node.status = 'G';
              } else if (
                errorNodeArr.findIndex((el) => el === node.parentId) != -1
              ) {
                node.status = 'G';
              }
              console.log(node, passNodeArr, errorNodeArr);
              let cell = this.graph.drawRuleNode(node, ipInfo);
              nodeObj[node.parentId].push(cell);
            } else {
              console.log(node);
              if (passNodeArr.findIndex((el) => el === node.parentId) != -1) {
                node.status = 'G';
              } else if (
                errorNodeArr.findIndex((el) => el === node.parentId) != -1
              ) {
                node.status = 'G';
              }
              let cell = this.graph.drawRuleNode(node, ipInfo);
              nodeObj[node.parentId] = [cell];
            }
          });
          // console.log(nodeObj)
          for (let key in nodeObj) {
            let status = '';
            if (passNodeArr.findIndex((el) => el === key) != -1) {
              status = 'Y';
            } else if (errorNodeArr.findIndex((el) => el === key) != -1) {
              status = 'N';
            } else {
              status = '';
            }
            this.graph.drawOrGroup(key, nodeObj[key], status);
            if (nodeObj[key].length === 1) {
              lineArr.forEach((el) => {
                // console.log(el, nodeObj[key][0].id, key)
                if (el.target.split('-')[0] === key) {
                  el.target =
                    nodeObj[key][0].id + '-' + el.target.split('-')[1];
                }
                if (el.source.split('-')[0] === key) {
                  el.source =
                    nodeObj[key][0].id + '-' + el.source.split('-')[1];
                }
              });
            }
          }
          if (this.pageType === '1') {
            const icon = 'end';
            const label = '结束';
            const opts = {
              x: 0,
              y: 0,
            };
            this.graph.drawStartEndNode({
              icon,
              label,
              opts,
            });
          }
          console.log(lineArr, resultLineArr);
          resultLineArr.forEach((res) => {
            lineArr.forEach((res2) => {
              console.log(res2.source);
              if (res2.source === '2-Y') {
                res2.status = 'Y';
              } else if (
                res.source.split('-')[0] === res2.source.split('-')[0] &&
                res.target === '3-E' &&
                res2.target === '3-E' &&
                this.pageType === '1'
              ) {
                res2.status = res.status;
                res2.value = ' ';
              } else if (
                res.target === res2.target &&
                res.source === res2.source &&
                res.value === res2.value
              ) {
                res2.status = res.status;
              }
            });
            if (this.pageType === '1') {
              if (
                lineArr.findIndex(
                  (el) =>
                    res.target === el.target &&
                    res.source === el.source &&
                    res.value === el.value
                ) === -1
              ) {
                console.log(
                  res.source,
                  res.target,
                  lineArr,
                  lineArr.findIndex(
                    (el) =>
                      res.target === el.target &&
                      res.source === el.source &&
                      res.value === el.value
                  ),
                  '-----------默认线'
                );
                lineArr.push({
                  source: res.source,
                  target: res.target,
                  value: ' ',
                  status: 'D',
                });
              }
            }
          });

          lineArr.forEach((temp) => {
            // console.log(temp)
            this.graph.drawLine({
              source: temp.source,
              target: temp.target,
              label: temp.value ? temp.value : 'Yes',
              status: temp.status,
            });
          });
          this.$nextTick(() => {
            this.executeLayout();
            this.graph.undoManager.clear();
          });
        });
      } else if ((!data || data.length === 0) && this.pageType === '1') {
        this.graph.drawEndNode();
      }
    },
    // 数据字典查询
    queryDict(val, nodeData, arrData) {
      // console.log(val)

      api
        .queryDataDictionaries({
          dictType: val,
        })
        .then(({ code, data }) => {
          if (code != 0) return;
          console.log(data);
          // if (!data[val]) return
          this.dictNum += 1;
          let arr = [];
          data[val].forEach((item) => {
            arr.push({
              label: item.dictLabel,
              value: item.dictValue,
            });
          });
          let obj = this.dictOptionsObj;
          obj[val] = arr;
          // this.setDictOptionsObj(obj)
          // console.log(this.dictNum, arrData)
          if (arrData.length === this.dictNum) {
            this.drawNodes(nodeData);
          }

          // val.selectOptions = arr
        });
    },
    // 自动布局
    executeLayout() {
      this.graph.executeLayout();
    },
    // 放大缩小
    zoomLayout(val) {
      if (val === 'in') {
        this.graph.graph.zoomIn();
      } else if (val === 'out') {
        this.graph.graph.zoomOut();
      }
    },

    // 初始化Graph
    initGraph(val) {
      const container = this.$refs.graph;
      this.opts.width = this.$refs.graph.clientWidth - 160;
      this.opts.isDisable = !this.isEdit ? true : false;
      this.opts.mode = val;
      const graph = new GraphDraw(container, {
        ...this.opts,
        ruleTemplates: this.ruleTemplates, // 规则资源区模板数据
        nodeSelectFunction: this.showNodeData, // 节点选中时回调函数
        lineSelectFunction: this.showLineData, // 路径选中时回调函数
        removeNodeFunction: this.removeNode, // 节点移除时回调函数
        drawNodeEndFunction: this.drawNodeEnd, //节点绘制完成回调函数
      });
      this.graph = graph;
      // this.graph.initGraph()
      // this.initSource()
      // this.graph.drawStartNode()
      // this.ruleTemplates.forEach((temp) => {
      //   const id = graph.drawRuleNode(temp)
      //   this.nodeEntities[id] = temp
      // })
      // this.graph.drawLine('2', '3', 'No')
    },
    // 节点创建完结束回调
    drawNodeEnd(cell) {
      this.$nextTick(() => {
        // console.dir(document.querySelector('.rule-node-' + cell.id))
        // console.log(this.nodeArr)
        let w = document.querySelector('.rule-node-' + cell.id).clientWidth,
          h = document.querySelector('.rule-node-' + cell.id).clientHeight;
        let geo = this.graph.graph.getCellGeometry(cell);
        geo = geo.clone();
        geo.width = w;
        geo.height = h;
        // console.log(geo)
        // this.graph.graph.getModel().setGeometry(cell, geo)
        // this.graph.graph.getModel().geometryForCellChanged(cell, geo)
        this.graph.updateCellNode({ w, h, cell });
        if (cell.parent.value === '分组') {
          // console.log(cell, cell.parent, '分组绘制')
          let groupW = 0,
            groupH = 0,
            childNodeArr = [];
          cell.parent.children.forEach((item) => {
            if (item.value != 'Yes' && item.value != 'No') {
              groupW += item.geometry.width + 30;
              // groupH += item.geometry.height
              if (groupH < item.geometry.height) {
                groupH = item.geometry.height + 40;
              }
              childNodeArr.push(item);
            }
          });
          if (cell.children.length > 0) {
            this.graph.graph.removeCells(cell.children);
            this.graph.removeNodeFunction(cell.children);
          }
          // let geo2 = this.graph.graph.getCellGeometry(cell.parent)
          // geo2 = geo2.clone()
          // geo2.width = groupW + 20
          // geo2.height = groupH
          // this.graph.graph.getModel().geometryForCellChanged(cell.parent, geo2)
          this.graph.updateCellNode({
            w: groupW + 20,
            h: groupH,
            cell: cell.parent,
          });
          let y = 20,
            x = 20;
          childNodeArr.forEach((item, index) => {
            // let geo3 = this.graph.graph.getCellGeometry(item)
            // geo3 = geo3.clone()
            if (index > 0) {
              x = x + childNodeArr[index - 1].geometry.width + 10;
            }
            // geo3.x = x
            // geo3.y = y
            // this.graph.graph.getModel().geometryForCellChanged(item, geo3)
            this.graph.updateCellNode({
              x: x,
              y: y,
              cell: item,
            });
          });
        }
        if (cell.children.length > 0 && cell.parent.value === '分组') {
          this.graph.graph.removeCells(cell.children);
          this.graph.removeNodeFunction(cell.children);
        }
        if (cell.children.length === 0 && cell.parent.value != '分组') {
          this.graph.drawEntryNode(cell, 'Y');
          this.graph.drawEntryNode(cell, 'N');
          this.graph.drawEntryNode(cell);
        }
        if (this.isFirstCreated) {
          this.createNodeNum += 1;
          if (this.createNodeNum === this.nodeArr.length) {
            this.isFirstCreated = false;
            this.executeLayout();
            this.graph.undoManager.clear();
          }
        }
        // this.graph.undoManager.clear()
        // console.log(w, h, cell)
      });
    },

    // 节点选中回调
    showNodeData(data, evt) {
      // console.log(this.updateRulesData())
      if (!this.isEdit) return;
      if (!data) {
        // this.setActiveNodeData([])
        this.$emit('changeActiveNode', []);
      }
      if (data.value == '开始') {
        // this.setActiveNodeData([])
        this.$emit('changeActiveNode', []);
        // this.setActiveNodeId('')
        return;
      }
      if (data.value == '结束') {
        // this.setActiveNodeData([])
        // this.setActiveNodeId('')
        this.$emit('changeActiveNode', []);
        return;
      }
      // let flag = false
      // this.rulesTreeData.forEach((item) => {
      //   item.forEach((itm) => {
      //     if (itm.id == data.id) {
      //       // console.log(item)
      //       this.setTemplateId(itm.templateId)
      //       flag = true
      //     }
      //   })
      // })
      // if (!flag) {
      //   this.rulesTreeData.push([data])
      //   // 更新规则器数据
      //   this.updateRulesData(this.rulesTreeData)
      //   this.setTemplateId(data.templateId)
      // }
      // // 设置选中因子策略编号
      // this.setTemplateId(data.templateNo)
      // 设置右侧表单树据
      // this.setActiveNodeData(data)
      this.$emit('changeActiveNode', data);
      // // 设置选中节点id
      // this.setActiveNodeId(data.id)

      console.log(
        '节点选中时回调函数 data, evt:',
        data,
        evt,
        this.rulesTreeData
      );
    },
    // 路径选中回调
    showLineData(data, evt) {
      if (!this.isEdit) return;
      console.log('路径选中时回调函数 data, evt:', data, evt);
      this.$emit('changeActiveNode', []);
      // this.setActiveNodeData([])
      // this.updateRulesData(this.rulesTreeData)
      // this.setActiveNodeData([])
      // this.rulesTreeData.forEach((item) => {
      //   item.forEach((itm) => {
      //     if (itm.id === data.source) {
      //       if (itm.yArr) {
      //         if (itm.yArr.includes(data.target)) {
      //           itm.yArr.push(data.target)
      //         }
      //       } else {
      //         itm.yArr = [data.target]
      //       }
      //     }
      //     if (data.source === '2') {
      //       itm.isRootNode = true
      //     }
      //   })
      // })
      console.log(this.rulesTreeData);
    },
    // 节点删除回调
    removeNode(node) {
      if (!this.isEdit) return;
      // this.setActiveNodeData([])
      this.$emit('changeActiveNode', []);
      // this.rulesTreeData.forEach((item, index) => {
      //   item.forEach((itm, idx) => {
      //     if (itm.id == node.id) {
      //       // console.log(item)
      //       item.splice(idx, 1)
      //     }
      //   })
      //   if (item.length == 0) {
      //     this.rulesTreeData.splice(index, 1)
      //   }
      // })
      // this.updateRulesData(this.rulesTreeData)
      // this.setActiveNodeData([])
      console.log('节点移除时回调函数 be removed node:', node);
    },
    // 节点更新回调
    updateCell(node) {
      if (!this.isEdit) return;
      // console.log(node, '更新节点回调')
      // const node = {
      //   label: 'update labe',
      //   rules: [
      //     {
      //       name: '1号码',
      //       rule: '===',
      //       value: '0',
      //     },
      //     {
      //       name: '2号码',
      //       rule: '>',
      //       value: '名单',
      //     },
      //   ],
      // }
      this.graph.updateRuleNode(node); // 更新节点数据
    },
    // 选项匹配
    getRuleValue(val, type) {
      console.log(val, this.operatorData, '运算符');
      let result = '';
      this.operatorData.forEach((item) => {
        if (item.dictValue === val && type === 1) {
          result = item.dictLabel;
        } else if (item.dictLabel === val && type === 2) {
          result = item.dictValue;
        }
      });
      return result;
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.source-list {
  position: relative;

  > div {
    cursor: pointer;
  }
}
.graph-div {
  // height: 500px;
  height: 100%;
  // background-color: #fff;
  background: rgba(246, 246, 246, 1);
  // background: url('../../assets/imgs/grid.gif');

  ::v-deep .start-node,
  ::v-deep .end-node {
    // background-color: #5f95ff;
    background: #fff;
    width: 119px;
    height: 36px;
    // color: #fff;
    color: #333;
    // border-radius: 25px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.35);

    div {
      display: inline-block;
      font-size: 14px;
    }
    .start {
      width: 12px;
      height: 14px;
      font-size: 12px;
      /* background-image: url('../assets/shandian.jpg'); */
      /* background-position: center;
        background-size: contain; */
    }
    .label {
      margin-left: 4px;
    }
  }
  ::v-deep .group-wrap {
    background: #fff;
    box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.35);
    border-radius: 6px;
  }
  ::v-deep .rule-or {
    text-align: center;
    // padding: 5px;
    height: 24px;
    line-height: 24px;
    color: #999;
    // background-color: #eff4ff;
    background: #fff;
    border-bottom: 1px solid #fff;
    border-color: rgba(246, 246, 246, 1);
    position: relative;

    &:before,
    &:after {
      content: ' ';
      display: block;
      border-top: 1px dashed #999;
      width: 35%;
      position: absolute;
    }
    &:before {
      margin-top: 12px;
      left: 10px;
    }
    &:after {
      margin-top: -12px;
      right: 10px;
    }
  }
  ::v-deep .rule-node {
    // width: 180px;
    // height: 100px;
    text-align: left;
    position: relative;
    min-width: 120px;
    min-height: 50px;
    // background-color: #eff4ff;
    background: #fff;
    box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.35);
    border-radius: 6px;
    overflow: hidden;

    .title {
      // display: inline-block;
      height: 28px;
      line-height: 28px;
      text-align: center;
      font-size: 14px;
      // color: #fff;
      color: #333;
      padding: 0 20px;
      // background-color: #5f95ff;
      background: rgba(238, 246, 255, 1);
    }
    .delete {
      position: absolute;
      font-size: 20px;
      color: #666;
      cursor: pointer;
      right: 0;
      top: 2px;
    }
    .rule-row {
      font-size: 12px;
      height: 24px;
      line-height: 24px;
      color: #0d1a26;
      padding: 0 10px;
      // background-color: #eff4ff;
      background: #fff;
      border-bottom: 1px solid #fff;
      border-color: rgba(246, 246, 246, 1);

      span {
        margin-right: 8px;
        &:last-child {
          margin-right: 0;
        }
      }
    }

    // .rule-row:last-child {
    //   border-bottom: 0;
    // }
  }
  ::v-deep .Y-point {
    height: 10px;
    width: 10px;
    border-radius: 50%;
    background: #fff;
    // background: #67c23a;
    border: 1px solid rgba(129, 211, 248, 1);
    // opacity: 0.8;
    // border: 2px solid green;
  }
  ::v-deep .N-point {
    height: 10px;
    width: 10px;
    border-radius: 50%;
    // background: #f56c6c;
    background: #fff;
    border: 1px solid rgba(217, 0, 27, 1);
    // opacity: 0.8;
    // border: 2px solid red;
  }
  ::v-deep .node-pass {
    // border: 2px solid #67c23a;
    border: 2px solid rgba(2, 167, 240, 1);
  }
  ::v-deep .node-error {
    // border: 2px solid #f56c6c;
    border: 2px solid rgba(217, 0, 27, 1);
    // background-color: #f56c6c;
  }
  ::v-deep .node-default {
    opacity: 0.5;
  }
}
::v-deep .node-strategy-box {
  display: flex;
  // min-height: 102px;
}

::v-deep .node-left-box {
  // width: 56px;
  background: rgba(238, 246, 255, 0.98);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
}
::v-deep .node-right-box {
  max-width: 250px;
  // min-width: 140px;
}
::v-deep .node-remark {
  // padding: 6px 10px;
  height: 24px;
  padding: 0 10px;
  line-height: 24px;
  background: #fff;
  color: #555;
  width: 100%;
  // cursor: pointer;
  // max-width: 256px;

  // white-space: pre-wrap;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
::v-deep .strategy-icon {
  height: 28px;
  width: 28px;
  display: inline-block;
  background: url('~@/assets/images/strategy.png') no-repeat center;
  background-size: 100%;
}
::v-deep .groovy-icon {
  height: 28px;
  width: 28px;
  display: inline-block;
  background: url('~@/assets/images/groovy.png') no-repeat center;
  background-size: 100%;
}
::v-deep .noopsyche-icon {
  height: 28px;
  width: 28px;
  display: inline-block;
  background: url('~@/assets/images/noopsyche.png') no-repeat center;
  background-size: 100%;
}

::v-deep .strategy-title {
  height: 28px;
  line-height: 28px;
  text-align: center;
  font-size: 14px;
  color: #015478;
  padding: 0 20px;
  background: #fff;
  font-weight: 700;
  border-bottom: 1px solid rgb(215, 215, 215);
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
<style>
.mxPopupMenuItem {
  width: 100%;
  cursor: pointer;
  padding-left: 10px;
}
.mxPopupMenu {
  border-radius: 3px;
}
</style>
