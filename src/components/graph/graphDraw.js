import {
  mxGraph as MxGraph,
  mxClient,
  mxUtils,
  mxEvent,
  mxRubberband,
  mxUndoManager,
  mxGraphHandler,
  mxGraphView,
  mxHierarchicalLayout,
  mxConstants,
  mxConstraintHandler,
  mxPerimeter,
  mxPoint,
} from 'mxgraph/javascript/mxClient'

// import { createIDByLen } from '@utils/common'
// import color from '@/module/formDesigner/config/rule/color'

let createIDByLen = function (num = 8) {
  const str = '0123456789abcdefghijklmnopqrstuvwxyz'
  let id = ''
  for (let i = 0; i < num; i++) {
    const index = Math.floor(Math.random() * 36)
    id += str[index]
  }
  return id
}
const startIcon =
  '<i aria-label="图标: play-circle" class="anticon anticon-play-circle"><svg viewBox="64 64 896 896" data-icon="play-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path><path d="M719.4 499.1l-296.1-215A15.9 15.9 0 0 0 398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 0 0 0-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"></path></svg></i>'
const endIcon =
  '<i aria-label="图标: close-circle" class="anticon anticon-close-circle"><svg viewBox="64 64 896 896" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M685.4 354.8c0-4.4-3.6-8-8-8l-66 .3L512 465.6l-99.3-118.4-66.1-.3c-4.4 0-8 3.5-8 8 0 1.9.7 3.7 1.9 5.2l130.1 155L340.5 670a8.32 8.32 0 0 0-1.9 5.2c0 4.4 3.6 8 8 8l66.1-.3L512 564.4l99.3 118.4 66 .3c4.4 0 8-3.5 8-8 0-1.9-.7-3.7-1.9-5.2L553.5 515l130.1-155c1.2-1.4 1.8-3.3 1.8-5.2z"></path><path d="M512 65C264.6 65 64 265.6 64 513s200.6 448 448 448 448-200.6 448-448S759.4 65 512 65zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path></svg></i>'

export default class GraphDraw {
  constructor(container, opts) {
    const {
      width,
      isDisable,
      mode,
      nodeSelectFunction,
      lineSelectFunction,
      removeNodeFunction,
      drawNodeEndFunction,
    } = opts
    this.container = container
    this.ruleTemplates = []
    this.nodeSelectFunction = nodeSelectFunction || function () {}
    this.lineSelectFunction = lineSelectFunction || function () {}
    this.removeNodeFunction = removeNodeFunction || function () {}
    this.drawNodeEndFunction = drawNodeEndFunction || function () {}
    this.graph = new MxGraph(container)
    this.idCellMap = {}
    const x = width && Number.isInteger(width) ? width / 2 : 20 // x轴的初始位置
    this.isDisable = isDisable ? true : false
    this.mode = mode ? mode : ''
    this.startX = x
    this.startY = 20 // y轴的初始位置
    this.endX = x
    this.endY = 600 // y轴的初始位置
    this.startCell = null
    this.undoManager = null
    this.startEndNodeOpts = {
      // 开始节点
      w: 119, // 宽度
      h: 36, // 高度
    }
    this.RuleNodeOpts = {
      // 规则节点
      w: 200, // 宽度
      h: 120, // 高度
    }
    this.initListener()
    this.initGraph()
  }

  // 撤回并删除末尾操作记录
  undoAndDelete() {
    this.undoManager.undo()
    this.undoManager.history.splice(this.undoManager.history.length - 1, 1)
    this.undoManager.indexOfNextAdd -= 1
  }
  // 更新节点大小
  updateCellNode({ x, y, w, h, cell }) {
    // console.log(cell, '更新节点位置及大小')
    // this.graph.getModel().beginUpdate()
    // try {
    let geo = this.graph.getCellGeometry(cell)
    // let geo = new mxRectangle()
    geo = geo.clone()
    geo.width = w ? w : geo.width
    geo.height = h ? h : geo.height
    geo.x = x ? x : geo.x
    geo.y = y ? y : geo.y
    // console.log(cell, geo)
    // console.log(this.undoManager.history)
    // this.graph.getModel().geometryForCellChanged(cell, geo)

    this.graph.getModel().beginUpdate()
    try {
      // this.graph.getModel().geometryForCellChanged(cell, geo)
      // console.log(data, '更新dom')
      // this.graph.cellResized(cell, geo)
      // this.undoAndDelete()
      // cell.setGeometry(geo)
      // this.graph.cellsResized([cell], [geo], false)
      // this.graph.view.refresh()
      // console.log(this.graph.view.refresh)
      // this.graph.cellSizeUpdated(cell)
      // this.graph.getModel().geometryForCellChanged(cell, geo)
      // this.graph.getModel().mxGeometryChange(this.graph.getModel(), cell, geo)
      // console.log(this.graph.getModel())
      // this.graph.updateCellSize(cell)
      // this.graph.removeCells([cell])
      // this.graph.drawRuleNode(node, ipInfo)
      // console.log('更新节点大小')
      this.graph.model.setGeometry(cell, geo)
    } finally {
      // this.undoManager.undo()
      // this.undoManager.redo()
      this.graph.getModel().endUpdate()
    }

    // let arr = this.undoManager.history
    // if (arr[arr.length - 1].changes.length === 1) {
    // this.undoAndDelete()
    // }
    // this.graph.getModel().geometryForCellChanged(cell, geo)
    // console.log(this.undoManager.history)
    // this.graph.updateCellSize(cell)
    // } finally {
    //   this.graph.getModel().endUpdate()
    // }
  }
  // 设置分组
  setOrRelation() {
    let cells = this.graph.getSelectionCells()

    let arr = []
    cells.forEach((item) => {
      if (
        mxUtils.isNode(item.value) &&
        item.value.nodeName.toLowerCase() == 'userobject'
      ) {
        if (item.getAttribute('cell-type') === 'rule') {
          arr.push(item)
        }
      }
    })

    if (arr.length > 1) {
      const parent = this.graph.getDefaultParent()
      this.graph.getModel().beginUpdate()
      let group = null
      try {
        group = this.graph.insertVertex(parent, createIDByLen(), '分组', 20, 20)
        console.log(group)
        arr.forEach((item) => {
          this.graph.removeCells(item.children)
          this.removeNodeFunction(item.children)
          // this.drawNodeEndFunction(item)
        })
        group.setConnectable(false)
        this.drawEntryNode(group, 'Y')
        this.drawEntryNode(group, 'N')
        this.drawEntryNode(group)

        this.graph.groupCells(group, 1, arr)
      } finally {
        this.graph.getModel().endUpdate()
      }
      this.idCellMap[group.id] = group
      // this.graph.setSelectionCell(group)
    }
  }
  // 绘制分组
  drawOrGroup(id, arr, status) {
    let cells = arr
    if (cells && cells.length > 1) {
      const parent = this.graph.getDefaultParent()
      this.graph.getModel().beginUpdate()
      let group = null
      try {
        group = this.graph.insertVertex(parent, id, '分组', 0, 0, 300, 300)
        cells.forEach((item) => {
          this.graph.removeCells(item.children)
          this.removeNodeFunction(item.children)
        })
        if (status === 'Y') {
          group.setStyle('Y')
        } else if (status === 'N') {
          group.setStyle('N')
        }
        group.setConnectable(false)
        this.drawEntryNode(group, 'Y')
        this.drawEntryNode(group, 'N')
        this.drawEntryNode(group)

        this.graph.groupCells(group, 1, cells)
      } finally {
        this.graph.getModel().endUpdate()
      }
      this.idCellMap[group.id] = group
      if (!this.isDisable) {
        // this.graph.setSelectionCell(group)
      }
    }
  }

  executeLayout() {
    const layout = new mxHierarchicalLayout(
      this.graph,
      mxConstants.DIRECTION_NORTH,
      false
    )
    layout.edgeStyle = null
    layout.fineTuning = false
    // layout.maintainParentLocation = true
    layout.disableEdgeStyle = false
    // layout.tightenToSource = false
    // this.graph.alignCells(mxConstants.ALIGN_CENTER)
    // console.log(this.graph, this.startCell, layout)
    layout.execute(this.graph.getDefaultParent(), this.startCell)

    // console.log(layout.execute)
    let cw = this.container.clientWidth
    let bounds = this.graph.getGraphBounds()
    let dx = cw - bounds.width
    // console.log(this.graph.cellSizeUpdated)
    this.graph.center(true)
    this.container.scrollLeft = 0
    this.container.scrollTop = 0
    this.graph.view.setTranslate(dx / 2 - bounds.x, 30)
    // let style = []

    // this.graph.getStylesheet().putDefaultVertexStyle(style)
    // console.log(this.graph.updateCellSize)
  }

  setRuleTemplate(template = {}) {
    template.templateNo !== undefined &&
      (this.ruleTemplates[template.templateNo] = template)
  }

  initTemplate(template, node, dragNode) {
    // console.log(template, node, dragNode)
    // console.log(mxDragSource)
    // let mxDragSource = new mxDragSource()
    // mxDragSource.reset()
    this.setRuleTemplate(template)
    const funct = (graph, evt, cell, x, y) => {
      // console.log('新增规则节点：', template.templateNo, x, y)
      // console.log(cell)
      if (graph.canImportCell(cell)) {
        const templateData = this.ruleTemplates[template.templateNo]
        this.drawRuleNode(
          {
            id: createIDByLen(),
            ...templateData,
          },
          { x, y }
        )
      }
    }

    // console.log(mxUtils.makeDraggable)

    mxUtils.makeDraggable(node, this.graph, funct, dragNode)
  }

  initGraph() {
    if (this.checkBrowser()) {
      this.initStyle()
      this.initConfig()
      this.overwriteFunction()
      this.drawStartNode()
      mxConstraintHandler.prototype.highlightColor =
        mxConstants.DEFAULT_VALID_COLOR
      // this.drawEndNode()
    }
  }
  initListener() {
    this.graph
      .getSelectionModel()
      .addListener(mxEvent.CHANGE, (sender, evt) => {
        console.log('选中节点 change sender, evt:', sender, evt)
        this.cellListener(sender, evt)
      })

    // this.graph
    //   .getSelectionModel()
    //   .addListener(mxEvent.MOVE_END, (sender, evt) => {
    //     console.log('-----move sender, evt:', sender, evt)
    //     // this.cellListener(sender, evt)
    //   })
  }
  getLineValue(cell) {
    // console.log(cell.id)
    return {
      id: cell.id,
      source: cell.source.id,
      target: cell.target.id,
      value: cell.value,
    }
  }
  getNodeValue(cell) {
    console.log(cell)
    return {
      id: cell.id,
      width: cell.geometry.width,
      height: cell.geometry.height,
      x: cell.geometry.x,
      y: cell.geometry.y,
      value: this.getLabel(cell),
      templateNo: this.getTemplateNo(cell),
      rules: this.getRules(cell),
      parentId: cell.parent?.id,
      type: cell.value?.attributes?.nodeType?.value,
      remark: cell.value?.attributes?.remark?.value || '',
    }
  }
  /**
   * 获取图表数据
   * return {
   * lines: [
   *  {
   *    id, // 路径实例Id
   *    source, // 路径源节点实例Id
   *    target, // 路径目标节点实例Id
   *    value,  // 路径上的值
   *  }
   * ],
   * nodes: [
   *  {
   *    id, // 节点实例Id
   *    width,  // 节点宽
   *    height, // 节点高
   *    x,  // 节点 x 坐标
   *    y,  // 节点 y 坐标
   *    value,  // 节点标题
   *    templateNo, // 节点模板No
   *    rules,  // 节点规则数组值
   *  }
   * ]}
   */
  getValues() {
    const cells = this.graph.getModel()?.cells || []
    let lines = []
    let nodes = []
    for (const key in cells) {
      const cell = cells[key]
      // console.log('key, cell:', key, cell)

      if (cell.edge === true) {
        // console.log(cell)
        // 线
        lines.push(this.getLineValue(cell))
      }
      if (
        mxUtils.isNode(cell.value) &&
        cell.value.nodeName.toLowerCase() == 'userobject'
      ) {
        // 节点
        nodes.push(this.getNodeValue(cell))
      }
    }
    return {
      lines,
      nodes,
    }
  }
  removeCells(node) {
    const cell = node || this.graph.getSelectionCell()

    console.log('------删除节点', cell)
    if (cell !== undefined) {
      if (
        this.getLabel(cell) != '开始' &&
        this.getLabel(cell) != '结束' &&
        cell.value != 'Yes' &&
        cell.value != 'No' &&
        cell.id != '2-Y' &&
        cell.id != '3-E'
      ) {
        this.graph.removeCells([cell])
        this.removeNodeFunction(cell)
        this.graph.refresh()
      } else if (cell.edge === true) {
        this.graph.removeCells([cell])
        this.removeNodeFunction(cell)
      }
    }
  }
  getLabel(cell) {
    // console.log(cell)
    return cell.value?.attributes?.label?.value
  }
  getTemplateNo(cell) {
    return cell.value?.attributes?.templateNo?.value
  }
  getRules(cell) {
    return JSON.parse(cell.value?.attributes?.rules?.value || '[]')
  }
  cellListener({ cells }) {
    console.log('cellListener cells:', cells)
    if (cells.length > 0) {
      const cell = cells[0]
      // 画线判断
      if (cell.edge === true) {
        if (cell.target) {
          let arr = this.getValues().lines
          console.log(arr)
          let sameNum = 0
          // let str =
          //   cell.source.id.split('-')[1] === 'Y'
          //     ? 'N'
          //     : cell.source.id.split('-')[1] === 'N'
          //     ? 'Y'
          //     : ''
          arr.forEach((item) => {
            // console.log(item)

            if (
              // item.target === cell.target.id &&
              item.source === cell.source.id
            ) {
              sameNum += 1
            }
            // else if (
            //   item.source.split('-')[1] === str &&
            //   item.target === cell.target.id
            // ) {
            //   sameNum += 1
            // }
          })

          if (cell.target.id.split('-')[1] != 'E') {
            this.undoAndDelete()
          } else if (
            cell.target.id.split('-')[1] === 'E' &&
            cell.source.id.split('-')[1] === 'E'
          ) {
            this.undoAndDelete()
          } else if (sameNum > 1) {
            this.undoAndDelete()
          } else if (
            cell.target.id.split('-')[0] === cell.source.id.split('-')[0]
          ) {
            this.undoAndDelete()
          } else if (
            arr.filter((i) => i.source === '2-Y').length > 1 &&
            cell.source.id === '2-Y'
          ) {
            this.undoAndDelete()
          } else {
            this.lineSelectFunction(this.getLineValue(cell))
            this.setLineColor(cell)
          }
        } else {
          this.undoAndDelete()
        }
      } else {
        this.nodeSelectFunction(this.getNodeValue(cell))
      }
    } else {
      this.nodeSelectFunction(false)
    }
  }
  initStyle() {
    // 节点选中边框颜色
    mxConstants.VERTEX_SELECTION_COLOR = 'rgba(2, 167, 240, 1)'
    // mxConstants.VERTEX_SELECTION_DASHED = false
    mxConstants.VERTEX_SELECTION_STROKEWIDTH = 1
    // 选中连接线边框颜色
    mxConstants.EDGE_SELECTION_COLOR = 'rgba(2, 167, 240, 1)'
    //高亮默认颜色
    mxConstants.DEFAULT_VALID_COLOR = 'rgba(2, 167, 240, 1)'
    //高亮边框宽度
    mxConstants.HIGHLIGHT_STROKEWIDTH = 1

    var style = []

    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter
    // style[mxConstants.STYLE_STROKECOLOR] = 'gray'
    // style[mxConstants.STYLE_ROUNDED] = true
    style[mxConstants.STYLE_FILLCOLOR] = 'transparent'
    style[mxConstants.STYLE_GRADIENTCOLOR] = 'transparent'
    style[mxConstants.VERTEX_SELECTION_DASHED] = false
    // style[mxConstants.STYLE_FONTCOLOR] = '#774400'
    // style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER
    // style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE
    // style[mxConstants.STYLE_FONTSIZE] = '12'
    // style[mxConstants.STYLE_FONTSTYLE] = 1
    style[mxConstants.ARROW_SPACING] = 0

    this.graph.getStylesheet().putDefaultVertexStyle(style)

    // Creates the default style for edges
    style = this.graph.getStylesheet().getDefaultEdgeStyle()
    style[mxConstants.STYLE_STROKECOLOR] = '#888'
    style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = 'transparent'
    // style[mxConstants.STYLE_EDGE] = mxEdgeStyle.SideToSide
    // style[mxConstants.STYLE_ROUNDED] = true
    // style[mxConstants.STYLE_EDGE] = mxEdgeStyle.ElbowConnector
    // style[mxConstants.STYLE_ELBOW] = mxConstants.ELBOW_VERTICAL
    // style[mxConstants.STYLE_ROUNDED] = true
    style[mxConstants.STYLE_FONTCOLOR] = 'black'
    style[mxConstants.STYLE_FONTSIZE] = '10'
  }
  initConfig() {
    // console.log(this.graph)
    // Defines the default constraints for all shapes
    // this.graph.getAllConnectionConstraints = function (terminal) {
    //   if (terminal != null && this.model.isVertex(terminal.cell)) {
    //     return [new mxConnectionConstraint(new mxPoint(1, 1), true)]
    //   }

    //   return null
    // }
    // this.graph.setConnectableEdges = false
    // 设置连接线曲线

    // let style = this.graph.getStylesheet().getDefaultEdgeStyle()
    // console.log(style, mxConstants, mxEdgeStyle)
    // style[mxConstants.STYLE_ROUNDED] = true

    // style[mxConstants.STYLE_CURVED] = true
    // style[mxConstants.STYLE_EDGE] = mxEdgeStyle.ElbowConnector

    // Disables the built-in context menu
    // mxEvent.disableContextMenu(this.container)
    this.graph.setVertexLabelsMovable(true)

    this.graph.setCellsResizable(false)
    // console.log(this.graph.setCellsResizable)

    this.graph.setConnectable(true)
    // 启用对齐线帮助定位
    mxGraphHandler.prototype.guidesEnabled = true
    // 选择基本元素开启
    this.graph.setEnabled(true)
    // Enables HTML labels
    this.graph.setHtmlLabels(true)

    // this.graph.isCellFoldable = false

    // Enables rubberband selection
    new mxRubberband(this.graph)

    // this.graph.view.setTranslate(this.startX - 80, 30)
    // let keyHandler = new mxKeyHandler(this.graph)
    // keyHandler.bindKey(46, () => {
    //   console.log('点击删除')
    //   this.removeCells()
    // })
    // 设置初始化编辑
    this.graph.view.setTranslate(20, 20)

    //禁止div内容拖动
    this.graph.setVertexLabelsMovable(false)

    // 禁止连接线文字拖动
    this.graph.edgeLabelsMovable = false

    // 重写方法不允许那条线(edge)可以编辑
    this.graph.isCellEditable = function (cell) {
      console.log(cell)
      return false
    }
    // 禁止无效画线
    this.graph.setAllowDanglingEdges(false)
    this.graph.setDisconnectOnMove(false)

    this.graph.setAutoSizeCells(true)

    // 撤回重做
    this.undoManager = new mxUndoManager(10000)
    let _this = this
    let listener = function (sender, evt) {
      // console.log(evt.getProperty('edit'))
      _this.undoManager.undoableEditHappened(evt.getProperty('edit'))
      // console.log(
      //   sender,
      //   evt,
      //   evt.getProperty('edit'),
      //   _this.undoManager,
      //   '重做'
      // )
    }
    this.graph.getModel().addListener(mxEvent.UNDO, listener)
    this.graph.getView().addListener(mxEvent.UNDO, listener)

    // 取消折叠
    this.graph.foldingEnabled = false
    this.graph.centerZoom = false
    if (this.isDisable) {
      this.graph.setPanning(true)
      this.graph.panningHandler.useLeftButtonForPanning = true
      this.graph.autoSizeCells = true
      this.graph.setEnabled(false)
    } else {
      // 右击菜单
      mxEvent.disableContextMenu(this.container)
      this.graph.popupMenuHandler.factoryMethod = function (menu, cell, evt) {
        return createPopupMenu(menu, cell, evt)
      }
    }
    function createPopupMenu(menu, cell) {
      console.log(cell, menu)
      if (cell != null) {
        let menu1 = menu.addItem('删除', null, function () {
          // mxUtils.alert('MenuItem1')
          _this.removeCells(cell)
        })
        menu1.lastChild.innerHTML = 'Delete'
        menu1.lastChild.style.color = 'gray'
      } else {
        // menu.addItem('No-Cell Item', null, function () {
        //   mxUtils.alert('MenuItem2')
        // })
      }
      menu.div.style.position = 'absolute'
      menu.div.style.background = '#fff'
      menu.div.style.width = '180px'
      menu.div.style.padding = '3px'
      menu.div.style.boxShadow = '0px 0px 3px rgb(0 0 0 / 35%)'
      // menu.addSeparator()
      // menu.addItem('MenuItem3', null, function () {
      //   mxUtils.alert(
      //     'MenuItem3: ' + this.graph.getSelectionCount() + ' selected'
      //   )
      // })
    }
    // this.graph.autoExtend = false

    // 分组设置
    this.graph.setDropEnabled(true)
    this.graph.setSplitEnabled(false)

    this.graph.isValidDropTarget = function (target, cells) {
      // console.log(target, cells, evt, '分组绘制')

      let flag = true
      if (target.value && target.value == '分组') {
        //   _this.drawNodeEndFunction(item)
      } else {
        flag = false
      }
      cells.forEach((item) => {
        if (item.edge) {
          flag = false
          return
        }
      })
      return flag
    }
  }
  checkBrowser() {
    // Checks if the browser is supported
    if (!mxClient.isBrowserSupported()) {
      // Displays an error message if the browser is not supported.
      mxUtils.error('Browser is not supported!', 200, false)
      return false
    } else {
      return true
    }
  }
  drawStartNode(
    opts = {
      x: this.startX,
      y: this.startY,
    }
  ) {
    const icon = 'start'
    const label = '开始'
    const cell = this.drawStartEndNode({
      icon,
      label,
      opts,
    })
    this.startCell = cell
  }
  drawEndNode(
    opts = {
      x: this.endX,
      y: this.endY,
    }
  ) {
    const icon = 'end'
    const label = '结束'
    this.drawStartEndNode({
      icon,
      label,
      opts,
    })
  }
  drawStartEndNode({ icon, label, opts }) {
    console.log(label)
    const { x, y, w, h } = opts
    // const { x, y } = opts
    const doc = mxUtils.createXmlDocument()
    let obj = doc.createElement('UserObject')
    obj.setAttribute('cell-type', icon)
    obj.setAttribute('icon', icon)
    obj.setAttribute('label', label)

    // const parent = this.graph.getDefaultParent()
    // const cell = this.graph.insertVertex(
    //   parent,
    //   label === '结束' ? '3' : null,
    //   obj,
    //   x,
    //   y,
    //   w || this.startEndNodeOpts.w,
    //   h || this.startEndNodeOpts.h
    // )
    // this.idCellMap[cell.id] = cell
    const Ynode = mxUtils.createXmlDocument()
    let obj2 = Ynode.createElement('UserObject2')
    obj2.setAttribute('point-Y', 'Yes')

    const parent = this.graph.getDefaultParent()
    this.graph.getModel().beginUpdate()
    let cell = null
    try {
      cell = this.graph.insertVertex(
        parent,
        label === '结束' ? '3' : '2',
        obj,
        x,
        y,
        w || this.startEndNodeOpts.w,
        h || this.startEndNodeOpts.h
      )
      cell.setConnectable(false)
      const style = new Object()

      style[mxConstants.STYLE_ROUNDED] = true

      style[mxConstants.STYLE_FONTCOLOR] = 'transparent'
      style[mxConstants.STYLE_FILLCOLOR] = '#fff'
      style[mxConstants.STYLE_GRADIENTCOLOR] = '#fff'
      style[mxConstants.STYLE_SHAPE] = 'ellipse'
      style[mxConstants.ARROW_SPACING] = 0
      style[mxConstants.STYLE_STROKECOLOR] = 'rgba(129, 211, 248, 1)'
      style[mxConstants.STYLE_STROKEWIDTH] = 1

      this.graph.getStylesheet().putCellStyle('YStyle', style)

      if (label !== '结束') {
        let v12 = this.graph.insertVertex(
          cell,
          '2-Y',
          '',
          0.5,
          1,
          10,
          10,
          'YStyle'
        )
        v12.geometry.offset = new mxPoint(-5, -5)
        v12.geometry.relative = true
        this.idCellMap[v12.id] = v12
      } else {
        let v12 = this.graph.insertVertex(
          cell,
          '3-E',
          '',
          0.5,
          0,
          10,
          10,
          'YStyle'
        )
        v12.geometry.offset = new mxPoint(-5, -5)
        v12.geometry.relative = true
        this.idCellMap[v12.id] = v12
      }
    } finally {
      this.graph.getModel().endUpdate()
    }
    this.idCellMap[cell.id] = cell
    if (!this.isDisable) {
      // this.graph.setSelectionCell(cell)
    }

    return cell
  }
  overwriteFunction() {
    const _this = this
    this.graph.convertValueToString = function (cell) {
      // console.log('绘制 cell:', cell)
      if (
        mxUtils.isNode(cell.value) &&
        cell.value.nodeName.toLowerCase() == 'userobject'
      ) {
        const nodeType = cell.getAttribute('cell-type')
        if (nodeType === 'start' || nodeType === 'end') {
          return _this.createStartEndNode(cell)
        } else if (nodeType === 'rule') {
          // console.dir(_this.createRuleNode(cell))
          // console.log(cell)
          let dom = _this.createRuleNode(cell)
          // console.log(cell.getAttribute('nodeType'))
          _this.drawNodeEndFunction(cell)
          // _this.graph.isAutoSizeCell(cell)

          // console.dir(dom)
          return dom
          // } else if (nodeType === 'edge' || cell.edge === true) {
          //   return _this.createLine(cell)
        }
      } else if (cell.edge === true) {
        // _this.setLineColor(cell)
        // console.log(cell, '线的渲染')
        // return cell.value || cell.source?.value || ''
        return ''
      } else if (cell.style === 'YStyle' || cell.style === 'NStyle') {
        // return _this.createPointNode(cell)
        return ''
      } else if (cell.value === '分组') {
        // console.log(cell, '分组')
        // _this.drawNodeEndFunction(cell)
        _this.updateGroup(cell)
        return _this.createGroupNode(cell)
      }
      return cell.value
    }

    var cellLabelChanged = this.graph.cellLabelChanged
    this.graph.cellLabelChanged = function (cell, newValue) {
      // console.log('更新cell:', cell, newValue, autoSize)
      if (
        mxUtils.isNode(cell.value) &&
        cell.value.nodeName.toLowerCase() == 'userobject'
      ) {
        // Clones the value for correct undo/redo

        var elt = cell.value.cloneNode(true)
        elt.setAttribute('label', newValue)
        newValue = elt
      }

      cellLabelChanged.apply(this, arguments)
    }

    // Redirects the perimeter to the label bounds if intersection
    // between edge and label is found
    const mxGraphViewGetPerimeterPoint = mxGraphView.prototype.getPerimeterPoint
    mxGraphView.prototype.getPerimeterPoint = function (
      terminal,
      next,
      orthogonal
      // border
    ) {
      var point = mxGraphViewGetPerimeterPoint.apply(this, arguments)

      if (point != null) {
        var perimeter = this.getPerimeterFunction(terminal)

        if (terminal.text != null && terminal.text.boundingBox != null) {
          // Adds a small border to the label bounds
          var b = terminal.text.boundingBox.clone()
          b.grow(3)

          if (mxUtils.rectangleIntersectsSegment(b, point, next)) {
            point = perimeter(b, terminal, next, orthogonal)
          }
        }
      }

      return point
    }
  }
  setLineColor(cell) {
    console.log(cell)
    if (cell.source.value === 'No') {
      const Nstyle = new Object()
      Nstyle[mxConstants.STYLE_STROKECOLOR] = 'rgba(271, 0, 27,1)'
      Nstyle[mxConstants.STYLE_FONTCOLOR] = 'rgba(271, 0, 27,1)'
      this.graph.getStylesheet().putCellStyle('Nedge', Nstyle)
      cell.setStyle('Nedge')
    } else if (cell.source.value === 'Yes' || cell.source.id === '2-Y') {
      const Ystyle = new Object()
      Ystyle[mxConstants.STYLE_STROKECOLOR] = 'rgba(2, 167, 240,1)'
      Ystyle[mxConstants.STYLE_FONTCOLOR] = 'rgba(2, 167, 240,1)'
      this.graph.getStylesheet().putCellStyle('Yedge', Ystyle)
      cell.setStyle('Yedge')
    }
    this.graph.refresh()
  }
  updateGroup(cell) {
    let groupW = 0,
      groupH = 0,
      childNodeArr = []
    cell.children.forEach((item) => {
      if (item.value != 'Yes' && item.value != 'No') {
        groupW += item.geometry.width + 30
        // groupH += item.geometry.height
        if (groupH < item.geometry.height) {
          groupH = item.geometry.height + 40
        }
        childNodeArr.push(item)
      }
    })
    this.updateCellNode({
      w: groupW + 20,
      h: groupH,
      cell: cell,
    })
    if (childNodeArr.length < 1) {
      this.graph.removeCells([cell])
      this.removeNodeFunction(cell)
    }
    // let y = 20,
    //   x = 20
    // childNodeArr.forEach((item, index) => {
    //   // let geo3 = this.graph.graph.getCellGeometry(item)
    //   // geo3 = geo3.clone()
    //   if (index > 0) {
    //     x = x + childNodeArr[index - 1].geometry.width + 10
    //   }
    //   // geo3.x = x
    //   // geo3.y = y
    //   // this.graph.graph.getModel().geometryForCellChanged(item, geo3)
    //   this.updateCellNode({
    //     x: x,
    //     y: y,
    //     cell: item,
    //   })
    // })
    // console.log(cell)
  }
  createRuleElement(rule) {
    let row = document.createElement('div')
    row.setAttribute('class', 'rule-row')

    let nameSpan = document.createElement('span')
    nameSpan.innerHTML = rule.name
    let ruleSpan = document.createElement('span')
    ruleSpan.innerHTML = rule.rule
    let valueSpan = document.createElement('span')
    // console.log(rule)
    if (rule.type === 'enum') {
      valueSpan.innerHTML = rule.label
    } else if (rule.type === 'date') {
      if (rule.rule && (rule.rule == '等于当前' || rule.rule == '不等于当前')) {
        valueSpan.innerHTML = ''
        // valueSpan.innerHTML = rule.value ? rule.value : '(空)'
      } else if (
        rule.rule &&
        (rule.rule == '大于当前' || rule.rule == '小于当前')
      ) {
        valueSpan.innerHTML = rule.value ? rule.value + ' 天' : ' 天'
      } else if (rule.rule) {
        valueSpan.innerHTML = rule.value ? rule.value : '(空)'
      } else {
        valueSpan.innerHTML = rule.value
      }
    } else {
      if (rule.rule) {
        valueSpan.innerHTML = rule.value ? rule.value : '(空)'
      } else {
        valueSpan.innerHTML = rule.value
      }
    }

    row.appendChild(nameSpan)
    row.appendChild(ruleSpan)
    row.appendChild(valueSpan)
    return row
  }
  createStartEndNode(cell) {
    // console.log(this)
    const type = cell.getAttribute('icon')
    let div = document.createElement('div')

    if (this.mode === 'test') {
      div.setAttribute('class', type + '-node node-pass')
    } else {
      div.setAttribute('class', type + '-node')
    }

    let icon = document.createElement('div')
    icon.setAttribute('class', type)
    if (type === 'start') {
      icon.innerHTML = startIcon
    } else if (type === 'end') {
      icon.innerHTML = endIcon
    }
    let label = document.createElement('div')
    label.setAttribute('class', 'label')
    label.innerHTML = cell.getAttribute('label')
    div.appendChild(icon)
    div.appendChild(label)

    return div
  }
  createPointNode(cell) {
    // console.log(cell)
    let type = cell.style === 'NStyle' ? 'N' : 'Y'
    let div = document.createElement('div')
    div.setAttribute('class', type + '-point')
    if (this.mode === 'test') {
      div.setAttribute('style', 'display: none')
    }
    // console.log(div)
    return div
  }
  createGroupNode(cell) {
    // console.log(cell, '分组cell')
    let div = document.createElement('div')
    div.setAttribute('class', 'group-wrap')
    // let text = 'border: 1px dashed #BBB;'
    let text = ''
    if (cell.style === 'Y') {
      text = 'border: 2px solid #67c23a;'
    } else if (cell.style === 'N') {
      text = 'border: 2px solid #f56c6c'
    }
    div.setAttribute(
      'style',
      'width:' +
        cell.geometry.width +
        'px;height:' +
        cell.geometry.height +
        'px;' +
        text
    )
    // console.log(div)
    return div
  }
  createRule(container, rules) {
    // console.log(rules)
    rules.forEach((rule) => {
      container.appendChild(this.createRuleElement(rule))
    })
  }
  createOrHtml(container) {
    let row = document.createElement('div')
    row.setAttribute('class', 'rule-or')
    row.innerHTML = '或'
    container.appendChild(row)
  }
  createRuleNode(cell) {
    let container = document.createElement('div')

    // container.style.width = cell.geometry.width

    // console.log(container.style.width, cell.geometry.width)

    let status = cell.style
    let statusClass =
      status === 'Y'
        ? 'node-pass'
        : status === 'N'
        ? 'node-error'
        : status === 'G'
        ? ''
        : this.mode === 'test'
        ? 'node-default'
        : ''
    // console.log(status)
    container.setAttribute(
      'class',
      'rule-node rule-node-' + cell.id + ' ' + statusClass
    )

    let nodeType = cell.getAttribute('nodeType')
    // console.log(nodeType)
    if (nodeType === '0') {
      // container.setAttribute(
      //   'style',
      //   'width: ' +
      //     cell.geometry.width +
      //     'px; height:' +
      //     cell.geometry.height +
      //     'px'
      // )

      // 标题行
      let titleContainer = document.createElement('div')
      titleContainer.setAttribute('class', 'title')

      // 标题标签
      let title = document.createElement('div')
      title.innerHTML = cell.getAttribute('label')
      titleContainer.appendChild(title)

      // 删除图标
      // let del = document.createElement('i')
      // del.setAttribute('class', 'delete')
      // del.innerHTML = delIcon

      // mxEvent.addListener(del, 'click', () => {
      //   this.removeCells()
      // })
      // titleContainer.appendChild(del)

      container.appendChild(titleContainer)

      // 分隔线
      // let hr = document.createElement('hr')
      // container.appendChild(hr)
      // 规则列表
      const rulesStr = cell.getAttribute('rules')

      const rules = JSON.parse(rulesStr)
      let ruleList = document.createElement('div')
      ruleList.setAttribute('class', 'rule-list')
      if (rules.length > 1) {
        // 或
        rules.forEach((rule, index) => {
          if (index !== 0) {
            this.createOrHtml(ruleList)
          }
          this.createRule(ruleList, rule)
        })
      } else if (rules.length === 1) {
        this.createRule(ruleList, rules[0])
      }

      // console.log('rules:', rules)

      // ruleList.innerHTML = cell.getAttribute('rules')

      container.appendChild(ruleList)
      const showIcon =
        '<i><svg t="1655445646094" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3258" width="16" height="16"><path d="M490.666667 601.6L797.866667 298.666667l59.733333 59.733333-302.933333 302.933333-59.733334 64-59.733333-59.733333L128 358.4 187.733333 298.666667l302.933334 302.933333z" fill="#444444" p-id="3259"></path></svg></i>'
      const hiddenIcon =
        '<i><svg t="1655446278137" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5485" width="16" height="16"><path d="M490.666667 422.4l302.933333 302.933333 59.733333-59.733333-298.666666-307.2L494.933333 298.666667l-59.733333 59.733333L128 665.6 187.733333 725.333333l302.933334-302.933333z" fill="#444444" p-id="5486"></path></svg></i>'

      if (ruleList.children.length > 4) {
        let moreBtn = document.createElement('div')
        moreBtn.setAttribute('class', 'more-btn')
        moreBtn.setAttribute(
          'style',
          'height: 18px;cursor:pointer;text-align:center;line-height: 18px;border-top: 1px dashed #f6f6f6;'
        )
        // ruleList.children[ruleList.children.length - 1]
        console.log(cell.getAttribute('isShow'))
        if (cell.getAttribute('isShow') === 'show') {
          // cell.setAttribute('isShow', true)
          moreBtn.innerHTML = hiddenIcon
          ruleList.children[ruleList.children.length - 1].style.border = '0'
          ruleList.setAttribute('style', 'height: auto;overflow:hidden')
        } else {
          // cell.setAttribute('isShow', false)
          moreBtn.innerHTML = showIcon
          if (ruleList.children[3].className != 'rule-or') {
            ruleList.children[3].style.border = '0'
            ruleList.setAttribute('style', 'height: 96px;overflow:hidden')
          } else {
            ruleList.children[2].style.border = '0'
            ruleList.setAttribute('style', 'height: 72px;overflow:hidden')
          }
        }

        let _this = this
        moreBtn.addEventListener('click', function () {
          console.log(cell, cell.getAttribute('isShow'))
          if (cell.getAttribute('isShow') === 'show') {
            _this.updateRuleNode({
              rules: rules,
              type: nodeType,
              isShow: 'hidden',
            })
          } else {
            _this.updateRuleNode({
              rules: rules,
              type: nodeType,
              isShow: 'show',
            })
          }
        })
        container.appendChild(moreBtn)
        // console.dir(ruleList.children[3].className == 'rule-or')
      }
      // console.dir(container)
    } else if (nodeType === '1') {
      let strategyBox = document.createElement('div')
      strategyBox.setAttribute('class', 'node-strategy-box')
      let leftBox = document.createElement('div')
      leftBox.setAttribute('class', 'node-left-box')

      let leftIcon = document.createElement('span')
      if (cell.getAttribute('strategyType') === '0') {
        leftIcon.setAttribute('class', 'strategy-icon')
      } else if (cell.getAttribute('strategyType') === '1') {
        leftIcon.setAttribute('class', 'groovy-icon')
      } else if (cell.getAttribute('strategyType') === '2') {
        leftIcon.setAttribute('class', 'noopsyche-icon')
      }
      leftBox.appendChild(leftIcon)
      strategyBox.appendChild(leftBox)

      let rightBox = document.createElement('div')
      rightBox.setAttribute('class', 'node-right-box')

      // 标题标签
      let title = document.createElement('div')
      title.setAttribute('class', 'strategy-title')
      title.setAttribute('title', cell.getAttribute('label'))
      title.innerHTML = cell.getAttribute('label')

      rightBox.appendChild(title)

      let remark = document.createElement('div')
      let txt = '描述：'
      if (cell.getAttribute('remark')) {
        if (cell.getAttribute('remark').length > 50) {
          txt = txt + cell.getAttribute('remark').substring(0, 50) + '...'
        } else {
          txt = txt + cell.getAttribute('remark')
        }
      }
      remark.setAttribute('title', '描述：' + cell.getAttribute('remark'))
      remark.innerHTML = txt
      remark.setAttribute('class', 'node-remark')
      rightBox.appendChild(remark)
      strategyBox.appendChild(rightBox)
      container.appendChild(strategyBox)
    }

    return container
  }
  createLine(cell) {
    let container = document.createElement('div')
    container.setAttribute('class', 'rule-line')
    container.innerHTML =
      cell.edge === true ? 'Yes' : cell.getAttribute('label')

    return container
  }
  createUserObject(data) {
    const { templateNo, label, rules } = data
    const doc = mxUtils.createXmlDocument()
    let obj = doc.createElement('UserObject')
    obj.setAttribute('templateNo', templateNo)
    obj.setAttribute('label', label)
    obj.setAttribute('cell-type', 'rule')
    obj.setAttribute('rules', JSON.stringify(rules))
    // if (data.type === '1') {
    //   obj.setAttribute('remark', data.remark)
    // }
    obj.setAttribute('nodeType', data.type)
    obj.setAttribute('strategyType', data.strategyType)
    obj.setAttribute('remark', data.remark)
    obj.setAttribute('isShow', data.isShow)

    // console.log(rules.label, '222211111')
    return obj
  }
  drawRuleNode(
    data,
    opts = {
      x: this.startX,
      y: 300,
    }
  ) {
    const { x, y, w, h } = opts
    // console.log(data)
    const parent = this.graph.getDefaultParent()
    this.graph.getModel().beginUpdate()
    let cell = null
    try {
      cell = this.graph.insertVertex(
        parent,
        data.id || null,
        this.createUserObject(data),
        x,
        y,
        w || null,
        h || null
        // w || this.RuleNodeOpts.w,
        // h || this.RuleNodeOpts.h,
        // 'whiteSpace=wrap;'
      )
      cell.setStyle(data.status)

      cell.setConnectable(false)
      this.drawEntryNode(cell, 'Y')
      this.drawEntryNode(cell, 'N')
      this.drawEntryNode(cell)
    } finally {
      // this.drawNodeEndFunction(cell)
      this.graph.getModel().endUpdate()
    }
    this.idCellMap[cell.id] = cell
    if (!this.isDisable) {
      // this.graph.setSelectionCell(cell)
    }
    // console.log(cell)
    return cell
    // console.dir(cell.getValue(), this.graph)
  }
  drawEntryNode(cell, type) {
    const style = new Object()
    style[mxConstants.STYLE_ROUNDED] = true
    style[mxConstants.STYLE_FONTCOLOR] = 'transparent'
    style[mxConstants.STYLE_FILLCOLOR] = '#fff'
    style[mxConstants.STYLE_GRADIENTCOLOR] = '#fff'
    style[mxConstants.STYLE_SHAPE] = 'ellipse'
    style[mxConstants.ARROW_SPACING] = 0
    let obj = {
      x: 0,
      y: 0,
      id: '',
    }

    if (type === 'Y') {
      obj.x = 0.25
      obj.y = 1
      obj.id = cell.id + '-Y'
      style[mxConstants.STYLE_STROKECOLOR] = 'rgba(129, 211, 248, 1)'
      style[mxConstants.STYLE_STROKEWIDTH] = 1
      this.graph.getStylesheet().putCellStyle('YStyle', style)
    } else if (type === 'N') {
      obj.x = 0.75
      obj.y = 1
      obj.id = cell.id + '-N'
      style[mxConstants.STYLE_STROKECOLOR] = 'rgba(217, 0, 27, 1)'
      style[mxConstants.STYLE_STROKEWIDTH] = 1
      this.graph.getStylesheet().putCellStyle('NStyle', style)
    } else {
      obj.x = 0.5
      obj.y = 0
      obj.id = cell.id + '-E'
      style[mxConstants.STYLE_STROKECOLOR] = 'rgba(129, 211, 248, 1)'
      style[mxConstants.STYLE_STROKEWIDTH] = 1
    }
    // console.log(obj)
    // this.graph.getStylesheet().putCellStyle('nodeStyle', style)
    this.graph.getModel().beginUpdate()
    try {
      let v12 = this.graph.insertVertex(
        cell,
        obj.id,
        type === 'N' ? 'No' : 'Yes',
        obj.x,
        obj.y,
        10,
        10,
        type === 'N' ? 'NStyle' : 'YStyle'
      )
      v12.geometry.offset = new mxPoint(-5, -5)
      v12.geometry.relative = true
      this.idCellMap[v12.id] = v12
    } finally {
      this.graph.getModel().endUpdate()
    }
  }
  updateRuleNode({ rules, type, isShow }) {
    const cell = this.graph.getSelectionCell()

    const data = {
      label: this.getLabel(cell),
      rules: rules || [],
      templateNo: this.getTemplateNo(cell),
      type: type,
      isShow: isShow ? isShow : 'show',
    }

    this.graph.cellLabelChanged(cell, this.createUserObject(data))
  }
  addTools() {
    // Undo / redo
    var undoManager = new mxUndoManager()
    var listener = function (sender, evt) {
      undoManager.undoableEditHappened(evt.getProperty('edit'))
    }
    this.graph.getModel().addListener(mxEvent.UNDO, listener)
    this.graph.getView().addListener(mxEvent.UNDO, listener)

    document.body.appendChild(
      mxUtils.button('Undo', function () {
        undoManager.undo()
      })
    )

    document.body.appendChild(
      mxUtils.button('Redo', function () {
        undoManager.redo()
      })
    )
  }
  drawLine({ id, source, target, label, status }) {
    // console.log(source, target, label, status)
    const parent = this.graph.getDefaultParent()

    // var doc = mxUtils.createXmlDocument()
    // var relation = doc.createElement('UserObject')
    // relation.setAttribute('cell-type', 'edge')
    // relation.setAttribute('label', label)
    // let e1 = this.graph.insertEdge(
    //   parent,
    //   id || null,
    //   // relation,
    //   label,
    //   this.idCellMap[source],
    //   this.idCellMap[target]
    // )
    // let status = this.idCellMap[target].parent.style

    const Ystyle = new Object()
    Ystyle[mxConstants.STYLE_STROKECOLOR] = 'rgba(2, 167, 240,1)'
    Ystyle[mxConstants.STYLE_FONTCOLOR] = 'rgba(2, 167, 240,1)'
    this.graph.getStylesheet().putCellStyle('Yedge', Ystyle)
    const Nstyle = new Object()
    Nstyle[mxConstants.STYLE_STROKECOLOR] = 'rgba(271, 0, 27,1)'
    Nstyle[mxConstants.STYLE_FONTCOLOR] = 'rgba(271, 0, 27,1)'
    this.graph.getStylesheet().putCellStyle('Nedge', Nstyle)
    const Dstyle = new Object()
    Dstyle[mxConstants.STYLE_STROKECOLOR] = '#efefef'
    Dstyle[mxConstants.STYLE_FONTCOLOR] = '#efefef'
    this.graph.getStylesheet().putCellStyle('Dedge', Dstyle)
    let styleValue = ''
    if (this.mode === 'test') {
      styleValue =
        status === 'Y'
          ? 'Yedge'
          : status === 'N'
          ? 'Nedge'
          : status === 'D'
          ? ''
          : this.mode === 'test'
          ? 'Dedge'
          : ''
    } else {
      styleValue = label === 'No' ? 'Nedge' : 'Yedge'
    }

    this.graph.getModel().beginUpdate()
    try {
      this.graph.insertEdge(
        parent,
        id || null,
        // relation,
        label,
        this.idCellMap[source],
        this.idCellMap[target],
        styleValue
      )
    } finally {
      this.graph.getModel().endUpdate()
    }

    // e1.geometry.width = 100
  }
}
