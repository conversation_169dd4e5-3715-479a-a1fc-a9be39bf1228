<!--
 * @Author: wuchao <EMAIL>
 * @Date: 2022-06-13 14:28:18
 * @LastEditors: wuchao <EMAIL>
 * @LastEditTime: 2022-06-15 16:24:57
 * @FilePath: \ge-manage-view\src\module\dealManage\module\weEditor.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div style="border: 1px solid #ccc">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editor"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      style="height: 300px; overflow-y: hidden"
      v-model="html"
      :defaultConfig="editorConfig"
      :mode="mode"
      ref="editor"
      @onCreated="onCreated"
      @onBlur=onBlur

    />
  </div>
</template>

<script>
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
// import { DomEditor } from "@wangeditor/editor";
import "@wangeditor/editor/dist/css/style.css";
export default {
  components: { Editor, Toolbar },
  props: {
    content: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      editor: null,
      html: "",
      toolbarConfig: {},
      editorConfig: { placeholder: "请输入内容..." },
      mode: "default", // or 'simple'
    };
  },
  watch: {
    content() {
      this.html = this.content;
    },
  },
  methods: {
    //获取输入完成的文本
    getEditorContent() {
      let flag = false;
      if (!this.editor.getText()) {
        flag = true;
      }
      this.$emit("getContent", this.html, flag);
    },
    isClear() {
      this.editor.clear();
    },
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    onBlur(){
      
    },
    check() {
      this.editor.disable();
    },
    //插入代码
    insertHTML(value) {
      this.editor.restoreSelection()
      this.editor.insertText(value);
    },
  },
  mounted() {
    // 模拟 ajax 请求，异步渲染编辑器
    setTimeout(() => {
      this.html = this.content;
    }, 1500);
  },

  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  },
};
</script>

<style lang="scss"></style>
