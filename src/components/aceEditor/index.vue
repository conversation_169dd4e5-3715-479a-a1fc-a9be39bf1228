<template>
  <div class="ace-container">
    <div class="ace-editor"
      style="width:100%;height:100%;"
      ref="ace"></div>

    <div v-if="showMode" class="config-panel">
      <a-form-model ref="form"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }">
        <a-form-model-item label="语言">
          <a-select class="value"
            v-model="modePath"
            @change="handleModelPathChange">
            <a-select-option v-for="mode in modeArray"
              :key="mode.name"
              :value="mode.path">
              {{mode.name}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="换行">
          <a-select style="width: 100%"
            class="value"
            v-model="wrap"
            @change="handleWrapChange">
            <a-select-option v-for="wrapa in wrapArray"
              :key="wrapa.name"
              :value="wrapa.value">
              {{wrapa.name}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </div>

  </div>
</template>
 
<script>
import ace from 'ace-builds';
import 'ace-builds/src-noconflict/snippets/groovy';
import 'ace-builds/src-noconflict/snippets/javascript';
import 'ace-builds/src-noconflict/snippets/html';
import 'ace-builds/src-noconflict/snippets/css';
import 'ace-builds/src-noconflict/snippets/scss';
import 'ace-builds/src-noconflict/snippets/json';
import 'ace-builds/src-noconflict/snippets/java';
import 'ace-builds/src-noconflict/snippets/text';
import 'ace-builds/webpack-resolver';
import 'ace-builds/src-noconflict/ext-language_tools';
import 'ace-builds/src-noconflict/theme-monokai';
import 'ace-builds/src-noconflict/theme-eclipse';
import 'ace-builds/src-noconflict/mode-javascript';
import 'ace-builds/src-noconflict/mode-groovy';

// const themeArray = [{
//   name: 'monokai',
//   path: 'ace/theme/monokai'
// }]

const wrapArray = [
  {
    name: '开启',
    value: 1,
  },
  {
    name: '关闭',
    value: 0,
  },
];

const gutteArray = [
  {
    name: '开启',
    value: 1,
  },
  {
    name: '关闭',
    value: 0,
  },
];

const modeArray = [
  {
    name: 'Groovy',
    path: 'ace/mode/groovy',
  },
  {
    name: 'JavaScript',
    path: 'ace/mode/javascript',
  },
  {
    name: 'HTML',
    path: 'ace/mode/html',
  },
  {
    name: 'CSS',
    path: 'ace/mode/css',
  },
  {
    name: 'SCSS',
    path: 'ace/mode/scss',
  },
  {
    name: 'Json',
    path: 'ace/mode/json',
  },
  {
    name: 'Java',
    path: 'ace/mode/java',
  },
  {
    name: 'Text',
    path: 'ace/mode/text',
  },
];

export default {
  name: 'AceEditor',
  props: {
    value: String,
    showMode: Boolean
  },
  mounted() {
    this.aceEditor = ace.edit(this.$refs.ace, {
      minLines: 10,
      fontSize: 14,
      value: this.value ? this.value : '',
      theme: this.themePath,
      mode: this.modePath,
      wrap: this.wrap,
      showGutter: this.showGutter,
      tabSize: 4,
    });
    // 激活自动提示
    this.aceEditor.setOptions({
      enableSnippets: true,
      enableLiveAutocompletion: true,
      enableBasicAutocompletion: true,
    });
    this.aceEditor.setAutoScrollEditorIntoView(true);
    this.aceEditor.getSession().on('change', this.change);
  },
  data() {
    return {
      aceEditor: null,
      wrap: 1,
      showGutter: 1,
      themePath: 'ace/theme/eclipse',
      modePath: 'ace/mode/groovy',
      modeArray: modeArray,
      wrapArray: wrapArray,
      gutteArray: gutteArray,
    };
  },
  methods: {
    change() {
      this.$emit('input', this.aceEditor.getSession().getValue());
    },
    setValue(value) {
      this.aceEditor.getSession().setValue(value);
    },
    getAceEditor() {
      return this.aceEditor;
    },
    resize(force) {
      this.aceEditor.resize(force);
    },
    handleModelPathChange(modelPath) {
      this.aceEditor.getSession().setMode(modelPath);
    },
    handleWrapChange(wrap) {
      this.aceEditor.getSession().setUseWrapMode(wrap);
    },
  },
};
</script>
 
<style lang='scss' scoped>
.ace-container {
  position: relative;
  width: 100%;

  .config-panel {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 50%;
    overflow: scroll;
    padding: 10px 0 0 0;
    border-radius: 5px;
    box-shadow: grey 0 1px 5px;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 1;
    .item {
      text-align: center;
      .title {
        color: white;
        margin: 0 10px;
        font-size: 14px;
      }
    }
  }

  .bookmarklet {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 20px;
    height: 20px;
    z-index: 2;
    cursor: pointer;
    border-width: 9px;
    border-style: solid;
    border-color: lightblue gray gray rgb(206, 173, 230);
    border-image: initial;
  }
}
.ant-modal-body .ant-form.ant-form-horizontal .ant-row.ant-form-item {
  padding: 0;
}
</style>