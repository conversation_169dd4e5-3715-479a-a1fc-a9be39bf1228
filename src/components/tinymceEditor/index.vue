
<template>
  <div class="tinymce-container" :style="{width:containerWidth}">
    <editor
      v-model="content"
      :init="init"
      v-bind="$props"
      v-on="$listeners"
      @input="v=> $emit('input', v)"
    ></editor>
  </div>
</template>

<script>
import "tinymce/tinymce";
import Editor from "@tinymce/tinymce-vue";
import "./langs/zh-CN";
import "tinymce/icons/default/icons.min.js"; //图标
import "tinymce/themes/silver/theme.min.js";
import "tinymce/skins/ui/oxide/skin.min.css";

// 编辑器插件plugins
// 更多插件参考：https://www.tiny.cloud/docs/plugins/
import "tinymce/plugins/lists";
import 'tinymce/plugins/link' // 超链接插件
import "tinymce/plugins/image";
import "tinymce/plugins/table";
import "tinymce/plugins/wordcount";
import "tinymce/plugins/code";
import "tinymce/plugins/media";
import "tinymce/plugins/contextmenu";
import "tinymce/plugins/colorpicker";
import "tinymce/plugins/textcolor";
import "tinymce/plugins/paste";
import "tinymce/plugins/image";
import "tinymce/plugins/code";
import "tinymce/plugins/fullscreen";

export default {
  name: "tkEditor",
  components: {
    Editor,
  },
  props: {
    id: {
      type: String,
      default: function () {
        return (
          "vue-tinymce-" +
          +new Date() +
          ((Math.random() * 1000).toFixed(0) + "")
        );
      },
    },
    value: {
      type: String,
      default: "",
    },
    inline: {
      type: Boolean,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    plugins: {
      type: [String, Array],
      default: "lists image table wordcount code fullscreen",
    },
    toolbar: {
      type: [String, Array],
      default:
        "bold italic underline strikethrough code | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent blockquote | undo redo | image | removeformat | fullscreen",
    },
    height: {
      type: [Number, String],
      required: false,
      default: 360,
    },
    width: {
      type: [Number, String],
      required: false,
      default: "auto",
    },
    initParams: {
      type: Object,
      required: false,
      default: () => {
        return {};
      },
    }
  },
  data() {
    return {
      content: this.value,
      //初始化配置
      init: {},
    };
  },
  computed: {
    containerWidth() {
      const width = this.width;
      if (/^[\d]+(\.[\d]+)?$/.test(width)) {
        // matches `100`, `'100'`
        return `${width}px`;
      }
      return width;
    },
  },
  watch: {
    value(val) {
      this.content = val;
    },
  },
  created() {
    this.setInit();
  },
  mounted() {},
  methods: {
    setInit() {
      this.init = {
        language: "zh_CN", // 语言
        plugins: this.plugins,
        toolbar: this.toolbar,
        height: this.height, //编辑器高度
        fontsize_formats: "12px 14px 16px 18px 24px 36px 48px 56px 72px",
        font_formats:
          "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;",
        importcss_append: true,
        branding: false, // 去水印
        autosave_ask_before_unload: false,
        nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin
        valid_children: "+div[style]",
        ...this.initParams
      };
    },
  },
};
</script>
<style lang="less" scoped>
.tinymce-container {
  position: relative;
  line-height: normal;
}

.tinymce-container {
  ::v-deep {
    .mce-fullscreen {
      z-index: 10000;
    }
  }
}
</style>