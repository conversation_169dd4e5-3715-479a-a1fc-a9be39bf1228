// 此处配置参数，修改后需要重启生效
// 引入当前工程相关配置文件
let config = require("../package.json");
const projectName = config.name; // 获取当前工程名称

const name = "思迪业务管理系统"; // 工程访问标题


// 配置当前工程需要进行转换的参数
const transformation = [
  { label: ["visible"], format: val => val == "0" ? true : false, theFormat: val => val ? '0' : '1' },
  { label: ["loginIp"], format: val => val == "0" ? true : false, theFormat: () => window.$VueStore.getters.clientIp.cip },
];


// 此处是用Es5语法防止编译错误
module.exports = {
  projectName, name, transformation
};