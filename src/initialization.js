/*
 * @Author: 陈志杰
 * @Date: 2021-01-23 10:20:58
 * @LastEditTime: 2021-05-05 20:03:48
 * @LastEditors: liu quan
 * @Description: In User Settings Edit
 * @FilePath: \bus-child-view\src\initialization.js
 */
// 此处是用UI库进行全局引入-避免缺少对应的UI组件
// 引入ant-design UI库组件
import constant from '@constant';
import busCommonComponent from "bus-common-component";
import loading from "bus-common-component/lib/loading";
import "bus-common-component/lib/selectForm/style.css";
// 引入图片预览插件
import 'viewerjs/dist/viewer.css'
import Viewer from 'v-viewer'


import { InterceptResponse } from '@/api/error';

export default {
  install: function (Vue) {
    busCommonComponent.permission = window[`${constant.name}-permission`];
    busCommonComponent.child = constant.name;
    // // Vue注册UI组件
    Vue.use(busCommonComponent);
    Vue.use(loading);
    Vue.use(Viewer)
    // Vue.use(dict);
    Vue.mixin({
      data() {
        return {
          intercept_response: InterceptResponse,
          DateFormat: (val) => val && new Date(val.replace(/-/g, "/")),
        };
      }
    })
  }
}