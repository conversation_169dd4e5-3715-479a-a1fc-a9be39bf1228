<!--
 * @Author: 陈志杰
 * @Date: 2021-01-22 13:38:15
 * @LastEditTime: 2021-05-05 20:20:10
 * @LastEditors: liu quan
 * @Description: In User Settings Edit
 * @FilePath: \bus-child-view\src\App.vue
-->
<template>
  <div :id="appId" style="height: 100%;">
    <a-config-provider :locale="locale">
      <keep-alive>
        <router-view />
      </keep-alive>
    </a-config-provider>
  </div>
</template>

<script>
import { zhCN } from "bus-common-component";
import constant from '@constant';

export default {
  name: 'App',
  data() {
    return {
      appId: constant.name,
      locale: zhCN,
    };
  },
  created() { },
}
</script>

<style>
.ant-layout.ant-layout-has-sider {
  height: 100%;
}
</style>
