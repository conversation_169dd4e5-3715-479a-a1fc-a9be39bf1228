/*
 * @Author: liu quan
 * @Date: 2021-03-15 09:52:56
 * @LastEditors: liu quan
 * @LastEditTime: 2021-07-15 17:53:03
 * @FilePath: \bus-child-view\src\api\error.js
 */
export const InterceptResponse = (res) => {
  let { request, data, headers: { authorization } } = res;
  // 判断若当前返回的请求头中携带对应的登录标识则将其保存至store中用于后方验证
  authorization && window.$VueStore && window.$VueStore.commit("common/SET_AUTHORIZATION", authorization)
  // 当前结果集为文本时
  if (request.responseType == "text" && !data) return false;
  // 系统异常
  // if (data.code == 10) return window.$VueStore.dispatch("common/setError", { msg: "系统异常" });
  // 920 - 用户未登录
  if (data.code == 920 || data.code == 922){
    window.location.replace("/work-manage-view/login")
    return false;
  }
  // 业务异常
  // if (data.code != 0) return window.$VueStore.dispatch("common/setExceptions", data);
  // 默认返回true
  return true;
}

export const handleError = (res) => {
  window.$VueInstance.$message.error(res.message);
  return true;
}

window.tkGlobalConfig = window.tkGlobalConfig || {};
window.tkGlobalConfig.InterceptResponse = window.tkGlobalConfig.InterceptResponse || InterceptResponse;
window.tkGlobalConfig.handleError = window.tkGlobalConfig.handleError || handleError;