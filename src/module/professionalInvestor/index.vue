<template>
  <a-card title="专业投资者申请" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="dealYb()" @reset="handleReset">
          <a-form-model-item label="受理单号">
            <a-input
              v-model="tableForm.requestNo"
              placeholder="请输入受理单号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户编号">
            <a-input
              v-model="tableForm.clientId"
              placeholder="请输入客户编号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="资产账号">
            <a-input
              v-model="tableForm.fundAccount"
              placeholder="请输入资产账号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="申请日期">
            <a-range-picker v-model="tableForm.createTime" @change="onChange" />
          </a-form-model-item>
          <a-form-model-item label="客户姓名">
            <a-input
              v-model="tableForm.clientName"
              placeholder="请输入客户姓名"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="证件号码">
            <a-input
              v-model="tableForm.idNo"
              placeholder="请输入证件号码"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="证件类别">
            <a-select
              v-model="tableForm.idKind"
              placeholder="请选择证件类别"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in idList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="状态">
            <a-select
              v-model="tableForm.status"
              placeholder="请选择任务状态"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in stateList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="page_main">
      <a-divider />
      <a-row class="btn_row">
        <a-col>
          <a-button type="primary" icon="redo" @click="dealYb()"
            >刷新
          </a-button>
        </a-col>
      </a-row>
      <div class="access-table">
        <tk-table
          ref="table"
          :tableData.sync="columns"
          getMethods="bc-manage-server/crh/monitorList/page"
          :isSelected="true"
          :isPaging="true"
          :tableFrom="tableForm"
          :selectedRowKeys.sync="selectedRowKeys"
          tableId="id"
          :tableFromFilter="tableFormFilter"
        >
          <template slot="operation" slot-scope="data">
            <a class="action-btn" @click="showDetail(data)"> 查看 </a>
          </template>
        </tk-table>
      </div>
    </div>
  </a-card>
</template>

<script>
import api from './api'
const defaultForm = {
  bizType: undefined,
  name: '',
  state: undefined,
}

export default {
  name: 'appointmentTasks',
  data() {
    return {
      tableForm: {
        requestNo: '', //受理单号
        bizName: '专业投资者',
        clientId: '', //客户ID
        fundAccount: '', //资产账号
        clientName: '', // 客户姓名
        idNo: '', //证件号码
        status: '1', // 任务状态
        idKind: '',
        createTime: '', //销户申请时间
        beginTime: '',
        endTime: '',
      },
      executeGroupMap: null, //执行组织列表
      stateList: [
        { value: '1', name: '受理' },
        { value: '2', name: '审核' },
        { value: '3', name: '办理' },
        { value: '4', name: '受理取消' },
        { value: '5', name: '强制取消' },
        { value: '6', name: '结束' },
        { value: '8', name: '客户取消' },
      ], //状态列表
      idList: [
        { value: '0', name: '身份证' },
        { value: '1', name: '外国护照' },
        { value: '2', name: '营业执照' },
        { value: '3', name: '军官证' },
        { value: '4', name: '社会保障号' },
        { value: '6', name: '港澳通行证' },
        { value: '7', name: '技术监督局代码' },
        { value: '9', name: '行政机关' },
        { value: 'A', name: '批文' },
        { value: 'C', name: '解放军文职干部证' },
        { value: 'D', name: '警官证' },
        { value: 'E', name: '解放军士兵证' },
        { value: 'F', name: '户口簿' },
        { value: 'G', name: '港澳居民来往内地通行证' },
        { value: 'H', name: '台湾居民来往大陆通行证' },
        { value: 'I', name: '外国人永久居留证' },
        { value: 'J', name: '本国护照' },
        { value: 'K', name: '武警文职干部证' },
        { value: 'L', name: '武警士兵证' },
        { value: 'M', name: '社会团体' },
        { value: 'N', name: '临时身份证' },
        { value: 'P', name: '全国组织机构代码' },
        { value: 'Q', name: '海外客户编号' },
        { value: 'R', name: '境外身份证' },
        { value: 'S', name: '港澳台居民身份证' },
        { value: 'T', name: '登记证书' },
        { value: 'X', name: '其他有效证件' },
        { value: 'i', name: '香港地区居民身份证' },
        { value: 'j', name: '澳门地区居民身份证' },
        { value: 'k', name: 'LEI' },
        { value: 'l', name: '港澳台居民居住证' },
        { value: '', name: '全部' },
      ], //证件类别列表
      //表格数据
      data: [],
      //表头数据
      columns: [
        {
          label: '申请日期',
          field: 'createDate',
          filter: (item) => this.formatDate(item),
        },
        {
          label: '申请时间',
          field: 'createTime',
          filter: (item) => this.formatTime(item),
        },
        {
          label: '受理单号',
          field: 'requestNo',
          isSorter: false,
        },
        {
          label: '流程名称',
          field: 'businflowName',
          isSorter: false,
          filter: (item) => (item ? item : '专业投资者申请'),
        },
        {
          label: '客户编号',
          field: 'clientId',
          isSorter: false,
        },
        {
          label: '资产账号',
          field: 'fundAccount',
          isSorter: false,
        },
        {
          label: '证件类别',
          field: 'idKind',
          filter: (item) => this.getIdKindList(item),
        },
        {
          label: '证件号码',
          field: 'idNo',
          isSorter: false,
          filter: (item) => this.getMosaicNum(item, 3, 3),
        },
        {
          label: '客户姓名',
          field: 'clientName',
          isSorter: false,
        },
        {
          label: '手机号',
          field: 'mobileTel',
          isSorter: false,
          filter: (item) => this.getMosaicNum(item, 3, 3),
        },
        {
          label: '客户营业部',
          field: 'branchNo',
          isSorter: false,
          filter: (item) => this.getBranchName(item),
        },
        {
          label: '审核人姓名',
          field: 'auditorName',
          isSorter: false,
        },
        {
          label: '审核人编号',
          field: 'auditorNo',
          isSorter: false,
        },
        {
          label: '状态',
          field: 'status',
          filter: (item) => this.getTaskStatusList(item),
        },
        {
          label: '渠道',
          field: 'channelName',
          isSorter: false,
          filter: () => 'H5',
        },
        {
          label: '终端方式',
          field: 'appId',
          isSorter: false,
          filter: () => '手机端',
        },
        {
          label: '审核退回备注',
          field: '',
          isSorter: false,
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: 220,
        },
      ],
      selectedRowKeys: [], // Check here to configure the default column
      branchList: [],
    }
  },
  components: {},
  created() {
    // 获取营业部列表
    this.getBranchList()
  },
  methods: {
    //处理按钮
    dealYb() {
      this.$refs.table.getTableData()
    },
    handleReset() {
      this.tableForm = JSON.parse(JSON.stringify(defaultForm))
    },
    getTaskStatusList(val) {
      const filterData = this.stateList.filter(({ value }) => value === val)[0]
      return filterData ? filterData.name : ''
    },
    getIdKindList(val) {
      const filterData = this.idList.filter(({ value }) => value === val)[0]
      return filterData ? filterData.name : ''
    },
    getMosaicNum(str, beginLength, endLength) {
      beginLength = beginLength || 0
      endLength = endLength || 0
      if (str == null || str == undefined) {
        return str
      } else {
        str = str.toString()
        if (str.length <= beginLength + endLength) {
          return str
        } else {
          return (
            str.slice(0, beginLength) +
            '*'.repeat(str.length - beginLength - endLength) +
            (endLength > 0 ? str.slice(-endLength) : '')
          )
        }
      }
    },
    getBranchList() {
      api
        .queryDataDictionaries({
          dictType: 'wa.common.branchList',
        })
        .then((res) => {
          if (res.code == '0') {
            this.branchList = res.data['wa.common.branchList']
          }
        })
    },
    getBranchName(val) {
      let clientBranchItem = this.branchList.filter(
        (item) => item.dictValue === val
      )
      return clientBranchItem[0]?.dictLabel
    },
    showDetail(data) {
      let href = `/bc-manage-view/professionalInvestorDetail?id=${data.requestNo}`
      window.open(href, '_blank')
    },
    onChange(date, dateString) {
      this.tableForm.beginTime = dateString[0]
      this.tableForm.endTime = dateString[1]
    },
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableForm.beginTime)) >
        Date.parse(this.DateFormat(this.tableForm.endTime))
      ) {
        param['beginTime'] = ''
        param['endTime'] = ''
      } else if (
        Date.parse(this.DateFormat(this.tableForm.beginTime)) ===
        Date.parse(this.DateFormat(this.tableForm.endTime))
      ) {
        param['beginTime'] += ' 00:00:00'
        param['endTime'] += ' 23:59:59'
      }
      delete param.createTime
      return param
    },
    // 格式化日期
    formatDate(date) {
      if (!date) {
        return ''
      }
      return (
        String(date).slice(0, 4) +
        '-' +
        String(date).slice(4, 6) +
        '-' +
        String(date).slice(6, 8)
      )
    },
    // 格式化时间
    formatTime(date) {
      if (!date) {
        return ''
      }
      return (
        String(date).slice(-date.length, -4) +
        ':' +
        String(date).slice(-4, -2) +
        ':' +
        String(date).slice(-2)
      )
    },
  },
}
</script>

<style>
.alert-test {
  text-align: center;
  padding: 5px;
}
</style>
