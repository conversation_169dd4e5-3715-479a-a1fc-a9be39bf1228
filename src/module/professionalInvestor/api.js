// 加载对应的装饰器
import { Parameters } from '@u/decorator'

class api {
  /**
   * 查询详情信息
   * @param {Object} param
   */
  @Parameters(['id'])
  queryDetail () {
    return this.services.initGet({
      reqUrl: 'crh/monitorList/showDetail',
      param: this.param,
    })
  }

  /**
   * 查询规则引擎数据字典
   * @param {Object} param
   */
  @Parameters(["dictType"])
  queryDataDictionaries() {
    return this.workServices.initGet({
      reqUrl: "dict/map",
      param: this.param,
    });
  }
}

export default new api()
