<template>
  <div>
    <a-modal
      :visible="showPop"
      :destroyOnClose="true"
      class="ant_modal_bigtable"
      @cancel="closePop"
    >
      <template slot="footer">
        <a-button key="back" @click="closePop"> 关闭 </a-button>
      </template>
      <h5 class="paper_name">{{ examPaperVO.paperName }}</h5>
      <a-form-model
        ref="form"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 8 }"
      >
        <div
          class="question"
          v-for="(item, index) in examPaperVO?.examQuestionVOList"
          :key="index"
        >
          <div>{{ item.orderNum + '、' + item.questionName }}</div>
          <a-radio-group v-model="item.optionCheck" disabled>
            <div v-for="(_, idx) in item.examQuestionOptionVOList" :key="idx">
              <a-radio :value="_.optionNo" key="30060">{{
                _.optionContent
              }}</a-radio>
            </div>
          </a-radio-group>
        </div>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
export default {
  name: '',
  data() {
    return {
      selectedRowValue1: {
        businessType: '0',
      },
    }
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    selectedRowValue: {
      type: Object,
    },
    examPaperVO: {
      type: Object,
    },
  },
  mounted() {},
  computed: {
    showPop: {
      get() {
        return this.isPopShow
      },
      set(val) {
        this.$emit('update:isPopShow', val) // visible 改变的时候通知父组件
      },
    },
  },
  methods: {
    //关闭
    closePop() {
      this.showPop = false
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .idKind .ant-checkbox-wrapper {
  display: block;
}

::v-deep .idKind .ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0;
}

::v-deep .ant-modal {
  width: 70% !important;
}
.paper_name {
  text-align: center;
}

::v-deep .ant-modal-body {
  max-height: 800px !important;
}
.question {
  margin-top: 15px;
}
</style>
