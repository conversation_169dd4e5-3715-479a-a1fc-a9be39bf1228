<template>
  <div>
    <a-layout>
      <a-layout-content
        style="background: #f9f9f9; padding: 10px 25px 90px 25px"
      >
        <!-- 审核任务 -->
        <div>
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">审核任务</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="业务类型">
              专业投资者申请
            </a-descriptions-item>
            <a-descriptions-item :span="6" label="终端编号">
              手机端
            </a-descriptions-item>
            <a-descriptions-item label="终端信息">
              {{ userAgent }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <!-- 条件检查记录 -->
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">条件检查记录</a-col>
                </a-row>
              </div>
              <div
                class="checkUpItem"
                v-for="(item, index) in checkUpList"
                :key="index"
              >
                {{ item.errorMessage }}
              </div>
            </div>
          </a-row>
        </div>
        <!-- 客户基本信息 -->
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="list_header">
              <a-row>
                <a-col :span="6" class="pop-title">客户基本信息</a-col>
              </a-row>
            </div>
            <a-descriptions bordered :column="2">
              <a-descriptions-item label="客户编号">
                {{ clientBaseInfo?.client_id }}
              </a-descriptions-item>
              <a-descriptions-item label="资产账号">
                {{ clientBaseInfo?.fund_account }}
              </a-descriptions-item>
              <a-descriptions-item label="姓名">
                {{ clientBaseInfo?.client_name }}
              </a-descriptions-item>
              <a-descriptions-item label="自然人身份类别">
                普通自然人
              </a-descriptions-item>
              <a-descriptions-item label="证件类型">
                {{ getIdKindName(clientBaseInfo?.id_kind) }}
              </a-descriptions-item>
              <a-descriptions-item label="证件号码">
                {{ getMosaicNum(clientBaseInfo?.id_no, 3, 3) }}
              </a-descriptions-item>
              <a-descriptions-item label="证件有效期">
                {{
                  formatDate(clientBaseInfo?.id_begindate) +
                  '~' +
                  formatDate(clientBaseInfo?.id_enddate)
                }}
              </a-descriptions-item>
              <a-descriptions-item label="证件地址">
                {{ getMosaicNum(clientBaseInfo?.id_address, 3, 3) }}
              </a-descriptions-item>
              <a-descriptions-item label="签发机关">
                {{ clientBaseInfo?.issued_depart }}
              </a-descriptions-item>
              <a-descriptions-item label="客户属性">
                {{ clientBaseInfo?.organ_flag == '0' ? '个人' : '机构' }}
              </a-descriptions-item>
              <a-descriptions-item label="开户日期">
                {{ formatDate(clientBaseInfo?.open_date) }}
              </a-descriptions-item>
              <a-descriptions-item label="开户营业部">
                {{ clientBaseInfo.branchName }}
              </a-descriptions-item>
              <a-descriptions-item label="手机号码">
                {{ getMosaicNum(clientBaseInfo?.mobile_tel, 3, 3) }}
              </a-descriptions-item>
              <a-descriptions-item label="投资者适当性类型">
                {{
                  submitMsg?.prof_flag_old == '0'
                    ? '普通投资者'
                    : profList[submitMsg?.prof_type_old]
                }}
              </a-descriptions-item>
              <a-descriptions-item label="风险等级">
                {{ clientBaseInfo.riskLevel }}
              </a-descriptions-item>
              <a-descriptions-item label="风险测评有效开始日期">
                {{ formatDate(clientBaseInfo?.corp_begin_date) }}
              </a-descriptions-item>
              <a-descriptions-item label="风险测评有效结束日期">
                {{ formatDate(clientBaseInfo?.corp_end_date) }}
              </a-descriptions-item>
              <a-descriptions-item label="职业">
                {{ clientBaseInfo?.profession_name }}
              </a-descriptions-item>
            </a-descriptions>
          </a-row>
        </div>
        <!-- 专业投资者申请信息 -->
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="list_header">
              <a-row>
                <a-col :span="6" class="pop-title">专业投资者申请信息</a-col>
              </a-row>
            </div>
            <a-descriptions bordered>
              <a-descriptions-item label="当前投资者类别">
                {{
                  submitMsg?.prof_flag_old == '0'
                    ? '普通投资者'
                    : profList[submitMsg?.prof_type_old]
                }}
              </a-descriptions-item>
              <a-descriptions-item label="总资产(万元)">
                {{ submitMsg?.asset_balance }}
              </a-descriptions-item>
              <a-descriptions-item label="首次交易日">
                {{ formatDate(submitMsg?.first_exchdate) }}
              </a-descriptions-item>
              <a-descriptions-item label="拟申请投资者类别">
                {{
                  submitMsg?.prof_flag == '0'
                    ? '普通投资者'
                    : profList[submitMsg?.prof_type]
                }}
              </a-descriptions-item>
              <a-descriptions-item label="外部金融资产证明(万元)">
                {{ submitMsg?.out_finance_asset || '0' }}
              </a-descriptions-item>
              <a-descriptions-item label="近三年年收入证明(万元)">
                {{ submitMsg?.last_three_years_avg_income }}
              </a-descriptions-item>
              <a-descriptions-item
                label="投资经验证明"
                v-if="submitMsg?.invest_age"
              >
                {{ submitMsg?.invest_age }}
              </a-descriptions-item>
            </a-descriptions>
          </a-row>
        </div>
        <!-- 资料上传 -->
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="list_header">
              <a-row>
                <a-col :span="6" class="pop-title">资料上传</a-col>
              </a-row>
            </div>
            <viewer
              class="idcardImg"
              v-for="(item, index) in archfileinfoList"
              :key="index"
            >
              <h4>{{ item.archfileName }}</h4>
              <img
                width="100%"
                style="cursor: pointer"
                :src="
                  '/bc-manage-server/file/downloadImg?path=/gj-kjyf-dev3-ywblwt-sd-bc' +
                  item.filePath
                "
              />
            </viewer>
          </a-row>
        </div>
        <!-- 协议签署 -->
        <div v-if="elecAgreementSignVO?.agreementName">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="list_header">
              <a-row>
                <a-col :span="6" class="pop-title">协议签署</a-col>
              </a-row>
            </div>
            <table
              width="100%"
              class="zh_info_table"
              cellpadding="0"
              cellspacing="0"
            >
              <tr>
                <th><span>协议名称</span></th>
                <th><span>操作</span></th>
              </tr>
              <tr>
                <td>
                  {{ elecAgreementSignVO?.agreementName }}
                </td>
                <td>
                  <a
                    @click="
                      showAgreement(elecAgreementSignVO?.archfileinfo.filePath)
                    "
                    >查看</a
                  >
                </td>
              </tr>
            </table>
          </a-row>
        </div>
        <!-- 知识评测 -->
        <div v-if="examPaperVO?.score">
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">知识评测</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="得分">
              {{ examPaperVO?.score }}
            </a-descriptions-item>
            <a-descriptions-item label="评测内容">
              <a @click="showKnowledPaper">查看</a>
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <!-- 操作流水 -->
        <div v-if="operatorinfo.length > 0">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">操作流水</a-col>
                </a-row>
              </div>
              <table
                width="100%"
                class="zh_info_table"
                cellpadding="0"
                cellspacing="0"
              >
                <tr>
                  <th><span>操作时间</span></th>
                  <th><span>受理单号</span></th>
                  <th><span>员工编号</span></th>
                  <th><span>操作类型</span></th>
                  <th><span>操作信息</span></th>
                  <th><span>操作备注</span></th>
                </tr>
                <tr
                  v-for="(item, index) in detailInfo?.actoprecord"
                  :key="index"
                >
                  <td>
                    {{
                      formatDate(item.createDate) +
                      ' ' +
                      formatTime(item.createTime)
                    }}
                  </td>
                  <td>
                    <span>{{ item.requestNo }}</span>
                  </td>
                  <td>
                    <span>{{ item.operatorNo }}</span>
                  </td>
                  <td>
                    <span>{{ item.operKind }}</span>
                  </td>
                  <td>
                    <span>{{ item.submitContent }}</span>
                  </td>
                  <td>
                    <span>{{ item.remark }}</span>
                  </td>
                </tr>
              </table>
            </div>
          </a-row>
        </div>
        <!-- 审核流水 -->
        <div v-if="auditrecordList.length > 0">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">审核流水</a-col>
                </a-row>
              </div>
              <table
                width="100%"
                class="zh_info_table"
                cellpadding="0"
                cellspacing="0"
              >
                <tr>
                  <th><span>时间</span></th>
                  <th><span>姓名</span></th>
                  <th><span>员工编号</span></th>
                  <th><span>节点名称</span></th>
                  <th><span>审核意见</span></th>
                </tr>
                <tr v-for="(item, index) in auditrecordList" :key="index">
                  <td>
                    {{
                      formatDate(item.createDate) +
                      ' ' +
                      formatTime(item.createTime)
                    }}
                  </td>
                  <td>
                    <span>{{ item.operatorName }}</span>
                  </td>
                  <td>
                    <span>{{ item.operatorNo }}</span>
                  </td>
                  <td>
                    <span>{{ item.submitContent }}</span>
                  </td>
                  <td>
                    <span>{{ item.opRemark }}</span>
                  </td>
                </tr>
              </table>
            </div>
          </a-row>
        </div>
        <!-- 办理流水 -->
        <div v-if="excuterecordList.length > 0">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">办理流水</a-col>
                </a-row>
              </div>
              <table
                width="100%"
                class="zh_info_table"
                cellpadding="0"
                cellspacing="0"
              >
                <tr>
                  <th><span>申请时间</span></th>
                  <th><span>受理单号</span></th>
                  <th><span>流程编号</span></th>
                  <th><span>办理功能号</span></th>
                  <th><span>异常信息</span></th>
                </tr>
                <tr v-for="(item, index) in excuterecordList" :key="index">
                  <td>
                    {{
                      formatDate(item.createDate) +
                      ' ' +
                      formatTime(item.createTime)
                    }}
                  </td>
                  <td>
                    <span>{{ item.requestNo }}</span>
                  </td>
                  <td>
                    <span>{{ item.businflowNo }}</span>
                  </td>
                  <td>
                    <span>{{ item.functionId }}</span>
                  </td>
                  <td>
                    <span>{{ item.errorNo }}</span>
                  </td>
                </tr>
              </table>
            </div>
          </a-row>
        </div>
      </a-layout-content>
    </a-layout>
    <addOrEdit :isPopShow.sync="isPopShow" :examPaperVO="examPaperVO" />
  </div>
</template>
<script>
import api from '../api'
import addOrEdit from './addOrEdit'

export default {
  name: 'professionalInvestorDetail',
  data() {
    return {
      isPopShow: false,
      detailInfo: {},
      appId: '', //设备编号
      userAgent: '', //终端信息
      clientBaseInfo: {}, //客户基本信息
      checkUpList: [], //条件检查记录
      submitMsg: {}, //专业投资者信息
      archfileinfoList: [], //客户档案信息
      elecAgreementSignVO: {}, //协议类型
      examPaperVO: {}, //知识评测
      operatorinfo: [], //操作流水
      auditrecordList: [], //审核流水
      excuterecordList: [], //办理流水
      idList: [
        { value: '0', name: '身份证' },
        { value: '1', name: '外国护照' },
        { value: '2', name: '营业执照' },
        { value: '3', name: '军官证' },
        { value: '4', name: '社会保障号' },
        { value: '6', name: '港澳通行证' },
        { value: '7', name: '技术监督局代码' },
        { value: '9', name: '行政机关' },
        { value: 'A', name: '批文' },
        { value: 'C', name: '解放军文职干部证' },
        { value: 'D', name: '警官证' },
        { value: 'E', name: '解放军士兵证' },
        { value: 'F', name: '户口簿' },
        { value: 'G', name: '港澳居民来往内地通行证' },
        { value: 'H', name: '台湾居民来往大陆通行证' },
        { value: 'I', name: '外国人永久居留证' },
        { value: 'J', name: '本国护照' },
        { value: 'K', name: '武警文职干部证' },
        { value: 'L', name: '武警士兵证' },
        { value: 'M', name: '社会团体' },
        { value: 'N', name: '临时身份证' },
        { value: 'P', name: '全国组织机构代码' },
        { value: 'Q', name: '海外客户编号' },
        { value: 'R', name: '境外身份证' },
        { value: 'S', name: '港澳台居民身份证' },
        { value: 'T', name: '登记证书' },
        { value: 'X', name: '其他有效证件' },
        { value: 'i', name: '香港地区居民身份证' },
        { value: 'j', name: '澳门地区居民身份证' },
        { value: 'k', name: 'LEI' },
        { value: 'l', name: '港澳台居民居住证' },
        { value: '', name: '全部' },
      ], //证件类别列表
      profList: {
        A: 'A类专业投资者',
        B: 'B类专业投资者',
        C: 'C类专业投资者',
      },
      corpRiskLevelList: this.$dict.dictTypeList('bc.common.corpRiskLevel'), // 风险等级数据字典
    }
  },
  components: { addOrEdit },
  created() {
    if (this.$route.query.id) {
      this.getDetail()
    }
  },
  methods: {
    showKnowledPaper() {
      this.isPopShow = true
    },
    // 获取字典项
    queryDict(branchNo) {
      api
        .queryDataDictionaries({
          dictType: 'wa.common.branchList',
        })
        .then((res) => {
          if (res.code == '0') {
            let clientBranchItem = res.data['wa.common.branchList'].filter(
              (item) => item.dictValue === branchNo
            )
            this.$set(
              this.clientBaseInfo,
              'branchName',
              clientBranchItem[0]?.dictLabel
            )
          }
        })
    },
    // 获取字典项
    queryRiskDict(riskType) {
      api
        .queryDataDictionaries({
          dictType: 'bc.common.corpRiskLevel',
        })
        .then((res) => {
          if (res.code == '0') {
            let clientRiskItem = res.data['bc.common.corpRiskLevel'].filter(
              (item) => item.dictValue === riskType
            )
            this.$set(
              this.clientBaseInfo,
              'riskLevel',
              clientRiskItem[0]?.dictLabel
            )
          }
        })
    },
    // 获取客户详情
    getDetail() {
      api
        .queryDetail({
          id: this.$route.query.id,
        })
        .then((res) => {
          if (res.code === 0) {
            this.appId = res.data?.acceptanceDTO?.appId
            this.userAgent = res.data?.acceptanceDTO?.userAgent
            this.clientBaseInfo =
              JSON.parse(res.data?.submitMsg?.auditClientInfo) || {}
            this.checkUpList = res.data?.businruleMatchRecordList || []
            this.submitMsg = res.data?.submitMsg || {}
            this.archfileinfoList = res.data?.archfileinfoList || []
            this.elecAgreementSignVO = res.data?.elecAgreementSignVO || {}
            this.examPaperVO = res.data?.examPaperVO || {}
            this.operatorinfo = res.data?.operatorinfo || []
            this.auditrecordList = res.data?.auditrecordList || []
            this.excuterecordList = res.data?.excuterecordList || []
            this.queryDict(this.clientBaseInfo.branch_no)
            this.queryRiskDict(this.clientBaseInfo.corp_risk_level)
          } else {
            this.$message.error(res.msg)
          }
      })
      .catch((e) => {
        this.$message.error(e.message)
      })
    },
    // 跳转协议页面
    showAgreement(filePath) {
      let href =
        '/bc-manage-server/file/downloadImg?path=/gj-kjyf-dev3-ywblwt-sd-bc' +
        filePath
      window.open(href, '_blank')
    },
    // 获取风险测评等级
    getCorpRiskLevelName(corpRiskLevel) {
      return this.corpRiskLevelList.filter(
        (item) => item.dictValue == corpRiskLevel
      )[0]?.dictLabel
    },
    getMosaicNum(str, beginLength, endLength) {
      beginLength = beginLength || 0
      endLength = endLength || 0
      if (str == null || str == undefined) {
        return str
      } else {
        str = str.toString()
        if (str.length <= beginLength + endLength) {
          return str
        } else {
          return (
            str.slice(0, beginLength) +
            '*'.repeat(str.length - beginLength - endLength) +
            (endLength > 0 ? str.slice(-endLength) : '')
          )
        }
      }
    },
    getIdKindName(val) {
      const filterData = this.idList.filter(({ value }) => value === val)[0]
      return filterData ? filterData.name : ''
    },
    // 格式化日期
    formatDate(date) {
      if (!date) {
        return ''
      }
      return (
        String(date).slice(0, 4) +
        '-' +
        String(date).slice(4, 6) +
        '-' +
        String(date).slice(6, 8)
      )
    },
    // 格式化时间
    formatTime(date) {
      if (!date) {
        return ''
      }
      return (
        String(date).slice(-date.length, -4) +
        ':' +
        String(date).slice(-4, -2) +
        ':' +
        String(date).slice(-2)
      )
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}

::v-deep .ant-layout-content {
  background-color: #fff !important;
}

.pop-title {
  border-left: 5px solid #1890ff;
  padding-left: 10px;
}

.pop_header {
  background-color: #ffffff;
  color: '#282828';
}

.flow_col {
  word-break: break-all;
  min-height: 45px;
  margin: 20px 0px;
  padding-right: 25px;
}

.flow_col span {
  float: left;
  margin-right: 20px;
}

.flow_row .flow_col:not(:last-child):after {
  content: '';
  min-width: 1px;
  min-height: 45px;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: '';
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}

.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

::v-deep .ant-card-body {
  padding: 15px;
  text-align: center;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.zh_info_table {
  width: 100%;
  border: 1px solid #e6e9f0;
}

.zh_info_table th {
  border: 0 none;
  border-bottom: 1px solid #e6e9f0;
  background: #fafafd;
  text-align: left;
  padding: 8px;
  font-weight: normal;
  font-size: 12px;
  line-height: 1.4;
  color: #1f2e4d;
}

.zh_info_table th:first-child {
  text-align: center;
}

.zh_info_table td {
  border: 0 none;
  border-bottom: 1px solid #e6e9f0;
  padding: 8px;
  text-align: left;
  font-weight: normal;
  font-size: 12px;
  line-height: 1.4;
  color: #1f2e4d;
}

.zh_info_table td:first-child {
  text-align: center;
}

.idcardImg img {
  width: 335px;
  height: 200px;
}
.idcardImg {
  display: inline-block;
  margin-right: 10px;
  margin-top: 10px;
}
.idcardImg h4 {
  text-align: center;
  background-color: rgb(217, 233, 245);
}
.checkUpItem {
  padding: 16px 24px;
  border: 1px solid #e8e8e8;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
}
</style>
