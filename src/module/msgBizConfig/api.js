// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询业务定义列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBcBusinessList() {
    return this.services.initGet({
      reqUrl: "bcBusiness/list",
      param: this.param,
    });
  }

  /**
   * @param {Object} param
   */
  @Parameters(["_data"])
  contextQuery() {
    return this.bfServices.initGet({
      reqUrl: "bf/context/list",
      param: this.param,
    });
  }

  /**
   * 新增消息模板关系配置
   * @param {*}
   */
  @Parameters(["_data"])
  addBcMsgBizConfig() {
    return this.services.initPost({
      reqUrl: "bcMsgBizConfig/add",
      param: this.param,
    });
  }

  /**
   * 修改消息模板关系配置
   * @param {*}
   */
  @Parameters(["_data"])
  editBcMsgBizConfig() {
    return this.services.initPost({
      reqUrl: "bcMsgBizConfig/edit",
      param: this.param,
    });
  }

  /**
   * 删除多个消息模板关系配置
   * @param {Object} param
   * - bcMsgBizConfigIds {String} 参数主键ID列表，多个以“，”隔开
   *
   */
  @Parameters(["bcMsgBizConfigIds"])
  deletesBcMsgBizConfig() {
    return this.services.initPost({
      reqUrl: "bcMsgBizConfig/deletes",
      param: this.param,
    });
  }

  /**
   * 新增消息模板参数
   * @param {*}
   */
  @Parameters(["_data"])
  addBcMsgParamConfig() {
    return this.services.initPost({
      reqUrl: "bcMsgParamConfig/add",
      param: this.param,
    });
  }

  /**
   * 修改消息模板参数
   * @param {*}
   */
  @Parameters(["_data"])
  editBcMsgParamConfig() {
    return this.services.initPost({
      reqUrl: "bcMsgParamConfig/edit",
      param: this.param,
    });
  }

  /**
   * 删除多个消息模板参数
   * @param {Object} param
   * - bcMsgParamConfigIds {String} 参数主键ID列表，多个以“，”隔开
   *
   */
  @Parameters(["bcMsgParamConfigIds"])
  deletesBcMsgParamConfig() {
    return this.services.initPost({
      reqUrl: "bcMsgParamConfig/deletes",
      param: this.param,
    });
  }
}

export default new api();
