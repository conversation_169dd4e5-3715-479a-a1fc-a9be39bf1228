<template>
  <a-modal
    :title="`${parameterData.id ? '修改' : '新增'}消息模板关系`"
    class="data-ant-module"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 18 }"
      :rules="rules"
    >
      <a-form-model-item label="消息模板名称" prop="bizName">
        <a-input v-model="form.bizName" placeholder="请输入消息模板名称">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="业务类型" prop="bizType">
        <a-select
          v-model="form.bizType"
          placeholder="请选择业务类型"
          show-search
          option-filter-prop="children"
          allowClear
        >
          <a-select-option
            v-for="item in parameterData.businessList"
            :value="item.bizType"
            :key="item.id"
          >
            {{ item.bizName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="消息类型" prop="messageType">
        <a-select
          v-model="form.messageType"
          placeholder="请选择消息类型"
          show-search
          option-filter-prop="children"
          allowClear
          mode="multiple"
        >
          <a-select-option
            v-for="item in parameterData.messageTypeList"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="消息模板ID" prop="messageTemplateId">
        <a-input
          v-model="form.messageTemplateId"
          placeholder="请输入消息模板ID"
        >
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="任务类型" prop="taskType">
        <a-select
          v-model="form.taskType"
          placeholder="请选择任务类型"
          allowClear
          show-search
          option-filter-prop="children"
        >
          <a-select-option
            v-for="item in parameterData.taskTypeList"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="任务状态" prop="taskStatus">
        <a-select
          v-model="form.taskStatus"
          placeholder="请选择任务状态"
          allowClear
          show-search
          option-filter-prop="children"
        >
          <a-select-option
            v-for="item in parameterData.taskStatusList"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="是否启用" prop="status">
        <a-radio-group v-model="form.status">
          <a-radio
            v-for="(item, index) in ['无效', '有效']"
            :key="index"
            :value="index + ''"
          >
            {{ item }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="三方消息类型" prop="thirdFlag">
        <a-select
          v-model="form.thirdFlag"
          placeholder="请选择三方消息类型"
          allowClear
          show-search
          option-filter-prop="children"
        >
          <a-select-option
            v-for="(item, index) in ['内部', '外部']"
            :key="index"
            :value="index + ''"
          >
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  bizName: "",
  bizType: undefined,
  messageType: undefined,
  messageTemplateId: "",
  taskType: undefined,
  taskStatus: undefined,
  status: "1",
  thirdFlag: undefined,
};

// 注册当前请求对应的上下文请求组
export default {
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        bizName: [
          { required: true, message: "消息模板名称不能为空", trigger: "blur" },
        ],
        bizType: [
          { required: true, message: "请选择业务类型", trigger: "blur" },
        ],
        messageType: [
          { required: true, message: "请选择消息类型", trigger: "blur" },
        ],
        messageTemplateId: [
          { required: true, message: "消息模板ID不能为空", trigger: "blur" },
        ],
        taskType: [
          { required: true, message: "请选择任务类型", trigger: "blur" },
        ],
        taskStatus: [
          { required: true, message: "请选择任务状态", trigger: "blur" },
        ],
        status: [
          { required: true, message: "请选择是否启用", trigger: "blur" },
        ],
        thirdFlag: [
          { required: true, message: "请选择三方消息类型", trigger: "blur" },
        ],
      },
      confirmLoading: false,
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    query() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = this.parameterData;
      if (this.form.messageType) {
        this.form.messageType = this.form.messageType.split(",");
      }
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        param.messageType = param.messageType.join(',')
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `消息模板关系${
                this.parameterData.id ? "修改" : "新增"
              }失败：${msg}`
            );
          this.$message.success(
            `消息模板关系${this.parameterData.id ? "修改" : "新增"}成功！`
          );
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        this.parameterData.id
          ? this.api.editBcMsgBizConfig(param).then(callback)
          : this.api.addBcMsgBizConfig(param).then(callback);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
