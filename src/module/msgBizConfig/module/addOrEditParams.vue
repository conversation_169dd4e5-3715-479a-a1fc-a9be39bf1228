<template>
  <a-modal
    :title="`${parameterData.id ? '修改' : '新增'}消息模板参数`"
    class="data-ant-module"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 18 }"
      :rules="rules"
    >
      <a-form-model-item label="消息模板字段" prop="messageKey">
        <a-input v-model="form.messageKey" placeholder="请输入消息模板字段">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="消息模板字段名称" prop="contextName">
        <a-input
          v-model="form.contextName"
          placeholder="请输入消息模板字段名称"
        >
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="元数据" prop="bizType">
        <a-select
          v-model="form.contextKey"
          placeholder="请选择元数据"
          show-search
          option-filter-prop="children"
          allowClear
          @search="handleSearch"
          @popupScroll="handlePopupScroll"
          @select="handleSelect"
        >
          <a-select-option
            v-for="item in renderedOptions"
            :value="item.contextKey"
            :key="item.id"
          >
            {{ item.contextName }}({{ item.contextKey }})
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  messageKey: "",
  contextName: "",
  contextKey: undefined,
};
const LOAD_NUM = 30; // 加载条数--可自定义
import { debounce } from "@/utils/parameter.js";

// 注册当前请求对应的上下文请求组
export default {
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        messageKey: [
          { required: true, message: "消息模板字段不能为空", trigger: "blur" },
        ],
        contextName: [
          {
            required: true,
            message: "消息模板字段名称不能为空",
            trigger: "blur",
          },
        ],
      },
      confirmLoading: false,
      oriDataList: [], // 原数据列表 -- 从接口获取
      dataLoading: false, // 原数据列表的加载状态 -- 接口的响应状态
      searchVal: "", // 搜索的内容
      filterDataList: [], // 过滤的数据列表 -- 从dataList中过滤出含搜索内容的数据
      renderedOptions: [], // 已渲染的下拉列表
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.oriDataList = this.parameterData.contextList; // 该接口返回的数据存放在res.data
        this.renderedOptions = this.oriDataList.slice(0, LOAD_NUM);
      }
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    // 文本框值变化时触发 -- 从数据源中过滤出含搜索内容的数据，并取过滤结果的前n条作为下拉列表的可选项
    handleSearch(val) {
      this.searchVal = val;
      let filterList = [];
      if (val) {
        filterList = this.oriDataList.filter(
          (item) => item.contextName.indexOf(val) > -1
        );
      } else {
        filterList = this.oriDataList;
      }
      this.filterDataList = filterList;
      this.renderedOptions =
        filterList.length < LOAD_NUM
          ? filterList
          : filterList.slice(0, LOAD_NUM);
    },
    // 滚动时触发（防止抖动）
    handlePopupScroll: debounce(function() {
      if (this.searchVal === "") {
        this.loadMoreData(this.oriDataList);
      } else {
        this.loadMoreData(this.filterDataList);
      }
    }, 1000),
    // 加载更多数据到select框
    loadMoreData(dataList) {
      const renderedLen = this.renderedOptions.length; // 已渲染的下拉列表长度
      const totalLen = dataList.length; // 当前数据源的长度
      let addList = [];
      if (renderedLen < totalLen) {
        if (renderedLen + LOAD_NUM <= totalLen) {
          addList = dataList.slice(renderedLen, renderedLen + LOAD_NUM);
        } else {
          addList = dataList.slice(
            renderedLen,
            renderedLen + (totalLen % LOAD_NUM)
          );
        }
        this.renderedOptions = this.renderedOptions.concat(addList);
      }
    },
    // 被选中时调用，参数为选中项的 value (或 key) 值
    handleSelect(val) {
      if (this.searchVal) {
        const selectedArr = this.oriDataList.filter(
          (item) => item.contextKey === val
        ); // 从数据源中过滤出下拉框选中的值，并返回一个数组
        const restList = this.oriDataList.filter(
          (item) => item.contextKey !== val
        ); // 从数据源中过滤出其他的值，返回一个数组
        const newList = selectedArr.concat(restList).slice(0, LOAD_NUM); // 将选中的元素放到下拉列表的第一位
        this.renderedOptions = newList; // 更新已渲染的下拉列表
        this.oriDataList = selectedArr.concat(restList); // 更新数据源
        this.searchVal = ""; // 因为触发handleSelect函数时，会自动清空用户输入的内容。因此，searchVal需要重置。
      }
    },
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    query() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form.id = this.parameterData.id;
      this.form.messageKey = this.parameterData.messageKey;
      this.form.contextName = this.parameterData.contextName;
      this.form.contextKey = this.parameterData.contextKey;
      let obj = this.oriDataList.find(
        (item) => item.contextKey === this.form.contextKey
      );
      console.log(obj,'ooo')
      if (obj) {
        this.renderedOptions = [obj, ...this.renderedOptions];
      }
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        param.messageTemplateId = this.parameterData.messageTemplateId;
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `消息模板参数${
                this.parameterData.id ? "修改" : "新增"
              }失败：${msg}`
            );
          this.$message.success(
            `消息模板参数${this.parameterData.id ? "修改" : "新增"}成功！`
          );
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        this.parameterData.id
          ? this.api.editBcMsgParamConfig(param).then(callback)
          : this.api.addBcMsgParamConfig(param).then(callback);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
