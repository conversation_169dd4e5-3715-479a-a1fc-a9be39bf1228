<template>
  <a-modal
    title="消息模板参数配置"
    :width="1000"
    v-model="showPop"
    :footer="null"
  >
    <div class="access-tabl">
      <tk-table
        ref="table"
        :intercept-response="intercept_response"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcMsgParamConfig/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :isTableLoading="true"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add"> 新增 </a-button>
          <a-button
            icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove"
          >
            删除
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="modify(data)"> 修改 </a-button>
        </template>
      </tk-table>
      <addOrEditParams
        :isPopShow.sync="showDetailPop"
        :parameterData="selectData"
        @success="query"
      />
    </div>
  </a-modal>
</template>
<script>
import addOrEditParams from "./addOrEditParams";
export default {
  inject: ["api"],
  data() {
    return {
      form: {}, //表单数据,
      columns: [
        // 循环
        {
          field: "messageKey",
          label: "消息模板字段",
          isSorter: false,
        },
        {
          field: "contextName",
          label: "消息模板字段名称",
          isSorter: false,
        },
        {
          field: "contextKey",
          label: "元数据",
          isSorter: false,
          filter: (item) => this.filterValue(item),
        },
        {
          field: "createBy",
          label: "创建人",
          isSorter: false,
        },
        {
          field: "createDate",
          label: "创建时间",
          isSorter: true,
        },
        {
          field: "updateBy",
          label: "修改人",
          isSorter: false,
        },
        {
          field: "updateDate",
          label: "修改时间",
          isSorter: true,
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      tableForm: {},
      showDetailPop: false, // 是否展示详情弹框
      selectData: {}, // 选择查看的数据对象
      selectedRowKeys: [],
    };
  },
  components: { addOrEditParams },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 传入参数
    messageTemplateId: {
      type: String,
      default: "",
    },
    contextList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  },
  watch: {
    messageTemplateId: {
      handler(val) {
        // 具有菜单编号时查询对应的权限列表
        this.tableForm["messageTemplateId"] = val;
        // 初始话选中
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    filterValue(value) {
      let obj = this.contextList.find((item) => item.contextKey === value);
      return obj ? obj.contextName : value;
    },
    add() {
      this.showDetailPop = true;
      this.selectData = {
        messageTemplateId: this.messageTemplateId,
        contextList: this.contextList,
      };
    },
    modify(data) {
      this.showDetailPop = true;
      this.selectData = {
        messageTemplateId: this.messageTemplateId,
        contextList: this.contextList,
        ...data,
      };
    },
    remove() {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deletePre();
        },
      });
    },
    deletePre() {
      let bcMsgParamConfigIds = this.selectedRowKeys.join(",");
      this.api
        .deletesBcMsgParamConfig({ bcMsgParamConfigIds })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.query();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
  },
};
</script>

<style lang="scss" scoped></style>
