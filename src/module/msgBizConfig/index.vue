<template>
  <a-card title="消息模板关系" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="消息模板名称">
            <a-input
              v-model="tableForm.bizName"
              placeholder="请输入消息模板名称"
              allowClear
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="业务类型">
            <a-select
              v-model="tableForm.bizType"
              placeholder="请选择业务类型"
              allowClear
              show-search
              option-filter-prop="children"
            >
              <a-select-option
                v-for="item in businessList"
                :key="item.id"
                :value="item.bizType"
              >
                {{ item.bizName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="消息类型">
            <a-select
              v-model="tableForm.messageType"
              placeholder="请选择消息类型"
              allowClear
              show-search
              option-filter-prop="children"
            >
              <a-select-option
                v-for="item in messageTypeList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="消息模板ID">
            <a-input
              v-model="tableForm.messageTemplateId"
              placeholder="请输入消息模板ID"
              allowClear
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="任务类型">
            <a-select
              v-model="tableForm.taskType"
              placeholder="请选择任务类型"
              allowClear
              show-search
              option-filter-prop="children"
            >
              <a-select-option
                v-for="item in taskTypeList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="任务状态">
            <a-select
              v-model="tableForm.taskStatus"
              placeholder="请选择任务状态"
              allowClear
              show-search
              option-filter-prop="children"
            >
              <a-select-option
                v-for="item in taskStatusList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="三方消息类型">
            <a-select
              v-model="tableForm.thirdFlag"
              placeholder="请选择三方消息类型"
              allowClear
              show-search
              option-filter-prop="children"
            >
              <a-select-option
                v-for="(item, index) in ['内部', '外部']"
                :key="index"
                :value="index + ''"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcMsgBizConfig/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add">
            新增
          </a-button>
          <a-button
            icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove"
          >
            删除
          </a-button>
          <a-button icon="download" type="primary" @click="uploadShow = true">
            导入
          </a-button>
          <a-button icon="upload" type="primary" @click="exportExcel">
            导出
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" size="small" @click.stop="modify(data)"
            >修改</a-button
          >
          <a-button type="link" size="small" @click.stop="config(data)"
            >参数配置</a-button
          >
        </template>
      </tk-table>
    </div>
    <addOrEdit
      :isPopShow.sync="isPopShow"
      @success="query"
      :parameterData="selectData"
    ></addOrEdit>
    <paramsConfig
      v-if="showConfigPopShow"
      :isPopShow.sync="showConfigPopShow"
      :messageTemplateId="messageTemplateId"
      :contextList="contextList"
    ></paramsConfig>
    <upload
      :visible.sync="uploadShow"
      uploadUrl="bcMsgBizConfig/import"
      downloadeTitle="导入消息模板"
      @success="query"
    />
  </a-card>
</template>

<script>
import api from "./api";
import addOrEdit from "./module/addOrEdit";
import paramsConfig from "./module/paramsConfig";
import upload from "@c/upload/upload";

// 默认表单属性
const defaultForm = {
  bizName: "",
  bizType: undefined,
  messageType: undefined,
  messageTemplateId: "",
  taskType: undefined,
  taskStatus: undefined,
  thirdFlag: undefined,
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "bizName",
          label: "消息模板名称",
          isSorter: false,
        },
        {
          field: "bizType",
          label: "业务类型",
          isSorter: false,
          filter: (item) => this.filterValue("businessList", item),
        },
        {
          field: "messageType",
          label: "消息类型",
          isSorter: false,
          filter: (item) => this.getType("messageTypeList", item),
        },
        {
          field: "messageTemplateId",
          label: "消息模板ID",
          isSorter: false,
        },
        {
          field: "taskType",
          label: "任务类型",
          isSorter: false,
          filter: (item) => this.getType("taskTypeList", item),
        },
        {
          field: "taskStatus",
          label: "任务状态",
          isSorter: false,
          filter: (item) => this.getType("taskStatusList", item),
        },
        {
          field: "status",
          label: "是否启用",
          isSorter: false,
          filter: (item) => (item === "1" ? "是" : "否"),
        },
        {
          field: "thirdFlag",
          label: "三方消息类型",
          isSorter: false,
          filter: (item) => (item === "1" ? "外部" : "内部"),
        },
        {
          field: "createBy",
          label: "创建人",
          isSorter: false,
        },
        {
          field: "createDate",
          label: "创建时间",
          isSorter: true,
        },
        {
          field: "updateBy",
          label: "修改人",
          isSorter: false,
        },
        {
          field: "updateDate",
          label: "修改时间",
          isSorter: true,
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      tableForm: {
        bizName: "",
        bizType: undefined,
        messageType: undefined,
        messageTemplateId: "",
        taskType: undefined,
        taskStatus: undefined,
        thirdFlag: undefined,
      },
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isPopShow: false, //弹窗是否显示
      showConfigPopShow: false,
      uploadShow: false,
      selectData: {},
      businessList: [],
      contextList: [],
      messageTypeList: [],
      taskTypeList: [],
      taskStatusList: [],
      messageTemplateId: "",
    };
  },
  provide: { api: api },
  components: {
    addOrEdit,
    paramsConfig,
    upload,
  },
  created() {
    api.getBcBusinessList().then((res) => {
      this.businessList = res.data;
    });
    api.contextQuery().then((res) => {
      this.contextList = res.data;
    });
    this.queryDictMap();
  },
  methods: {
    // 导出exportExcel
    exportExcel() {
      let baseUrl = '/bc-manage-server/bcMsgBizConfig/export'
      if (this.selectedRowKeys.length) {
        let ids = this.selectedRowKeys.join(',')
        window.location.href = baseUrl + '?ids=' + ids;
        return
      }
      window.location.href = baseUrl;
    },
    queryDictMap() {
      return new Promise((resolve) => {
        [
          { dataIndex: "messageTypeList", key: "bc.common.messageTempType" },
          { dataIndex: "taskTypeList", key: "bc.common.messageTempTaskType" },
          {
            dataIndex: "taskStatusList",
            key: "bc.common.messageTempTaskStatus",
          },
        ].forEach((item) => {
          this.$dict.dictContent(item.key).then((data) => {
            this[item.dataIndex] = (data || []).map((data) => {
              return { label: data.dictLabel, value: data.dictValue };
            });
            resolve();
          });
        });
      });
    },
    getType(list, val) {
      if (!val) return "";
      if (list === "messageTypeList") {
        let messageList = val.split(",");
        let resultList = [];
        messageList.forEach((item) => {
          this[list].forEach((itm) => {
            if (item === itm.value) {
              resultList.push(itm.label);
            }
          });
        });
        return resultList.join("，");
      }
      let arr = [];
      arr = this[list].filter((v) => {
        return v.value == val;
      });
      return arr[0] ? arr[0].label : val;
    },
    filterValue(type, value) {
      let obj = this[type].find((item) => item.bizType === value);
      return obj ? obj.bizName : value;
    },
    add() {
      this.isPopShow = true;
      this.selectData = {
        businessList: this.businessList,
        messageTypeList: this.messageTypeList,
        taskTypeList: this.taskTypeList,
        taskStatusList: this.taskStatusList,
      };
    },
    remove() {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deletePre();
        },
      });
    },
    deletePre() {
      let bcMsgBizConfigIds = this.selectedRowKeys.join(",");
      api
        .deletesBcMsgBizConfig({ bcMsgBizConfigIds })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.query();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    modify(data) {
      this.isPopShow = true;
      this.selectData = {
        ...data,
        businessList: this.businessList,
        messageTypeList: this.messageTypeList,
        taskTypeList: this.taskTypeList,
        taskStatusList: this.taskStatusList,
      };
    },
    config(data) {
      this.showConfigPopShow = true;
      this.messageTemplateId = data.messageTemplateId;
    },
    tableFormFilter(param) {
      return param;
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
  },
};
</script>

<style lang="scss" scoped></style>
