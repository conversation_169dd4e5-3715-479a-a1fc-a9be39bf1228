<template>
  <div>
    <template-id-1
      v-if="templateId === '1'"
      :data.sync="manageDetail"
    ></template-id-1>
    <template-id-2
      v-else-if="templateId === '2'"
      :data.sync="manageDetail"
    ></template-id-2>
    <template-id-3
      v-else-if="templateId === '3'"
      :data.sync="manageDetail"
    ></template-id-3>
    <template-id-4
      v-else-if="templateId === '4'"
      :data.sync="manageDetail"
    ></template-id-4>
    <template-id-5
      v-else-if="templateId === '5'"
      :data.sync="manageDetail"
    ></template-id-5>
    <template-id-6
      v-else-if="templateId === '6'"
      :data.sync="manageDetail"
    ></template-id-6>
    <template-id-7
      v-else-if="templateId === '7'"
      :data.sync="manageDetail"
    ></template-id-7>
    <template-id-8
      v-else-if="templateId === '8'"
      :data.sync="manageDetail"
    ></template-id-8>
    <template-id-9
      v-else-if="templateId === '9'"
      :data.sync="manageDetail"
    ></template-id-9>
    <template-id-10
      v-else-if="templateId === '10'"
      :data.sync="manageDetail"
    ></template-id-10>
    <template-id-11
      v-else-if="templateId === '11'"
      :data.sync="manageDetail"
    ></template-id-11>
    <template-id-12
      v-else-if="templateId === '12'"
      :data.sync="manageDetail"
    ></template-id-12>
    <template-id-13
      v-else-if="templateId === '13'"
      :data.sync="manageDetail"
    ></template-id-13>
    <template-id-14
      v-else-if="templateId === '14'"
      :data.sync="manageDetail"
    ></template-id-14>
    <template-id-15
      v-else-if="templateId === '15'"
      :data.sync="manageDetail"
    ></template-id-15>
    <template-id-16
      v-else-if="templateId === '16'"
      :data.sync="manageDetail"
    ></template-id-16>
    <template-id-17
      v-else-if="templateId === '17'"
      :data.sync="manageDetail"
    ></template-id-17>
    <template-id-18
      v-else-if="templateId === '18'"
      :data.sync="manageDetail"
    ></template-id-18>
    <template-id-19
      v-else-if="templateId === '19'"
      :data.sync="manageDetail"
    ></template-id-19>
    <template-id-20 v-else :data.sync="manageDetail"></template-id-20>
  </div>
</template>
<script>
import api from "../api";
import moment from "moment";
import TemplateId1 from "../components/TemplateId1.vue";
import TemplateId2 from "../components/TemplateId2.vue";
import TemplateId3 from "../components/TemplateId3.vue";
import TemplateId4 from "../components/TemplateId4.vue";
import TemplateId5 from "../components/TemplateId5.vue";
import TemplateId6 from "../components/TemplateId6.vue";
import TemplateId7 from "../components/TemplateId7.vue";
import TemplateId8 from "../components/TemplateId8.vue";
import TemplateId9 from "../components/TemplateId9.vue";
import TemplateId10 from "../components/TemplateId10.vue";
import TemplateId11 from "../components/TemplateId11.vue";
import TemplateId12 from "../components/TemplateId12.vue";
import TemplateId13 from "../components/TemplateId13.vue";
import TemplateId14 from "../components/TemplateId14.vue";
import TemplateId15 from "../components/TemplateId15.vue";
import TemplateId16 from "../components/TemplateId16.vue";
import TemplateId17 from "../components/TemplateId17.vue";
import TemplateId18 from "../components/TemplateId18.vue";
import TemplateId19 from "../components/TemplateId19.vue";
import TemplateId20 from "../components/TemplateId20.vue";

export default {
  name: "taskManageDetail",
  components: {
    TemplateId1,
    TemplateId2,
    TemplateId3,
    TemplateId4,
    TemplateId5,
    TemplateId6,
    TemplateId7,
    TemplateId8,
    TemplateId9,
    TemplateId10,
    TemplateId11,
    TemplateId12,
    TemplateId13,
    TemplateId14,
    TemplateId15,
    TemplateId16,
    TemplateId17,
    TemplateId18,
    TemplateId19,
    TemplateId20,
  },
  data() {
    return {
      manageDetail: {},
      singleVideoSrc: "",
      templateId: this.$route.query.templateId,
      videoWitnessSrc: "",
      templateIdList: [
        { value: "-1", name: "全部" },
        { value: "10200", name: "分配任务关系" },
        { value: "21", name: "两融网上开户视频复审（APP）" },
        { value: "20", name: "两融开户线上预约复审" },
        { value: "19", name: "关联关系确认双向视频复审" },
        { value: "18", name: "补开股东户双向视频复审" },
        { value: "17", name: "补开股东户单向视频复审" },
        { value: "16", name: "补开股东户单向视频初审" },
        { value: "15", name: "深市衍生品账户柜台开通" },
        { value: "14", name: "开立深市衍生品账户复审" },
        { value: "13", name: "两融网上开户视频复审" },
        { value: "12", name: "私募合格投资者认证复审" },
        { value: "11", name: "私募合格投资者认证初审" },
        { value: "10", name: "资管合格投资者认证复审" },
        { value: "9", name: "资管合格投资者认证初审" },
        { value: "8", name: "产品购买双录复审" },
        { value: "7", name: "补档案资料确认" },
        { value: "6", name: "补档案资料复审" },
        { value: "5", name: "补档案资料初审" },
        { value: "4", name: "非现特殊视频确认" },
        { value: "3", name: "非现特殊视频复审" },
        { value: "2", name: "销户柜台操作" },
        { value: "1", name: "销户挽留任务" },
      ],
      identityTypeList: [
        { value: "1", name: "资金账号" },
        { value: "10", name: "信用资金账号" },
        { value: "11", name: "国金账户" },
        { value: "12", name: "客户号" },
        { value: "13", name: "期权账号" },
        { value: "100", name: "通行证pid" },
        { value: "200", name: "员工域帐号" },
        { value: "2200", name: "设备号" },
      ], //标识类型列表
      statusList: [
        { value: "0", name: "初始化" },
        { value: "1", name: "审核通过" },
        { value: "2", name: "审核驳回" },
        { value: "3", name: "视频中断" },
        { value: "9", name: "取消办理" },
      ],
      auditStatusList: [
        { value: "0", name: "初始化" },
        { value: "1", name: "审核通过" },
        { value: "2", name: "审核驳回" },
        { value: "3", name: "视频中断" },
        { value: "4", name: "取消办理" },
      ],
      clientBusinessList: [],
      clientBusFileMap: {
        1: "身份证照片正面",
        2: "身份证照片反面",
        3: "客户头像",
        4: "视频见证录像",
        5: "辅证图片",
        7: "单向视频",
        8: "客户公安头像",
        9: "净资产证明档案",
        10: "总资产证明档案",
        11: "近三年年均收入证明档案",
        12: "投资经验证明档案",
        13: "无持仓证明",
      },
      columns: [
        {
          title: "产品编号",
          dataIndex: "productNo",
          key: "productNo",
        },
        {
          title: "产品代码",
          dataIndex: "productCode",
          key: "productCode",
        },
        {
          title: "产品名称",
          dataIndex: "productName",
          key: "productName",
        },
      ],
      xhInfoColumns: [
        {
          title: "账户类型",
          dataIndex: "accountType",
          key: "accountType",
        },
        {
          title: "账号",
          dataIndex: "accountCode",
          key: "accountCode",
        },
        {
          title: "操作",
          dataIndex: "action",
          key: "action",
        },
        {
          title: "销户状态",
          dataIndex: "checkflag",
          key: "checkflag",
        },
      ],
    };
  },
  provide: [api],
  computed: {
    cancellationAccountInfoList() {
      let list = [];
      if (
        this.manageDetail?.cancellationAccountInfo?.cancellationAccountInfoList
      ) {
        this.manageDetail.cancellationAccountInfo.cancellationAccountInfoList.forEach(
          ({ ...it }) => {
            list.push(it);
          }
        );
      }
      return list;
    },
    incidenceRelationList() {
      let list = [];
      if (this.manageDetail?.incidenceRelationInfo?.incidenceRelationList) {
        this.manageDetail.incidenceRelationInfo.incidenceRelationList.forEach(
          ({ ...it }) => {
            list.push({
              ...it,
            });
          }
        );
      }
      return list;
    },
  },
  created() {
    this.queryClientDetail();
  },
  methods: {
    formatDate(v = "") {
      if (!v) return "";
      return moment(new Date(v)).format("YYYY/MM/DD HH:mm:ss");
    },
    // 查询客户资料详情
    queryClientDetail() {
      api
        .showDetai({
          taskId: this.$route.query.taskId,
        })
        .then((res) => {
          if (res.code == 0) {
            let manageDetail = res.data;
            this.manageDetail = {
              singleVideoSrc: "",
              videoWitnessSrc: "",
              clientBusinessList: [],
            };
            let clientBusinessMap =
              manageDetail.clientBusinessfile?.clientBusinessMap;
            if (clientBusinessMap) {
              manageDetail.clientBusinessList = [];
              for (const key in clientBusinessMap) {
                let fileId, fileList;
                const i = key.split("_")[1];
                if (clientBusinessMap[key].constructor.name === "Array") {
                  fileList = clientBusinessMap[key];
                } else {
                  fileId = clientBusinessMap[key];
                }
                if (fileList?.length > 0) {
                  fileList.forEach((a) => {
                    this.queryfileBytes(a, (imgFileBytes) => {
                      manageDetail.clientBusinessList.push({
                        i,
                        label: this.clientBusFileMap[i],
                        imgUrl: "data:image/jpeg;base64," + imgFileBytes,
                      });
                      this.manageDetail.clientBusinessList.sort(
                        (a, b) => a.i - b.i
                      );
                    });
                  });
                } else {
                  this.queryfileBytes(fileId, (imgFileBytes) => {
                    manageDetail.clientBusinessList.push({
                      i,
                      label: this.clientBusFileMap[i],
                      imgUrl: "data:image/jpeg;base64," + imgFileBytes,
                    });
                    this.manageDetail.clientBusinessList.sort(
                      (a, b) => a.i - b.i
                    );
                  });
                }
              }
            }
            if (manageDetail?.singleVideo?.show === "1") {
              this.queryfileBytes(
                manageDetail.singleVideo.singleVideoId,
                (singleVideoFileBytes) => {
                  this.manageDetail.singleVideoSrc =
                    "data:video/mp4;base64," + singleVideoFileBytes;
                }
              );
            }
            if (manageDetail?.videoWitness?.show === "1") {
              this.queryfileBytes(
                manageDetail.videoWitness.videoWitnessId,
                (videoWintnessFileBytes) => {
                  this.manageDetail.videoWitnessSrc =
                    "data:video/mp4;base64," + videoWintnessFileBytes;
                }
              );
            }
            this.manageDetail = Object.assign(this.manageDetail, manageDetail);
          } else {
            this.$message.error(res.msg);
          }
        });
    },
    queryfileBytes(fileId = "", callback) {
      let fileBytes = "";
      if (fileId === "" || fileId === "null") {
        return callback("");
      }
      api
        .queryImgfileBytes({
          fileId,
        })
        .then((res) => {
          if (res.code == 0) {
            fileBytes = res.data.fileBytes;
          } else {
            fileBytes = "";
          }
          callback && callback(fileBytes);
        });
    },
    getBusiTypeList(val) {
      const filterData = this.processTypeList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getIdentityTypeList(val) {
      const filterData = this.identityTypeList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getStatusList(val) {
      const filterData = this.statusList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getAuditStatusList(val) {
      const filterData = this.auditStatusList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: "#282828";
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}

.idcardImg img {
  width: 335px;
  height: 200px;
  margin: 12px;
}
.idcardImg_context {
  width: 350px;
  display: inline-block;
}
.top8 {
  position: relative;
  top: 8px;
}
</style>
