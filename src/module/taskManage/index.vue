<template>
  <a-card title="任务管理列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="任务名称">
            <a-input
              v-model="tableForm.taskName"
              placeholder="请输入任务名称"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="任务状态">
            <a-select
              v-model="tableForm.taskStatus"
              placeholder="请选择任务状态"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in taskStatusList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item label="任务大类">
            <a-select
              v-model="tableForm.taskBigType"
              placeholder="请选择任务大类"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in taskBigTypeList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="执行组织">
            <a-select
              v-model="tableForm.executeGroup"
              placeholder="请选择执行组织"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in executeGroupList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="任务类型">
            <a-select
              v-model="tableForm.templateId"
              placeholder="请选择任务类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in templateIdList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="任务创建时间">
            <a-range-picker
              v-model="createTimePicker"
              @change="onChange"
              :format="timeFormat"
              :show-time="timePickerOptions"
            />
          </a-form-model-item>
          <a-form-model-item label="模糊匹配">
            <a-input
              v-model="tableForm.taskParamKeyword"
              placeholder=""
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/gj/audit/taskManage/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="preBizId"
      >
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="look(data)">查看</a-button>
        </template>
      </tk-table>
    </div>
  </a-card>
</template>

<script>
import api from "./api";
import { typeToColumns, executeGroupList } from "./js/common.js";
import moment from "moment";
// 默认表单api属性
const defaultForm = {
  taskName: "", //任务名称
  taskStatus: "", //任务状态
  taskBigType: "", //任务大类
  executeGroup: "", //执行组织
  templateId: "", //任务类型
  taskParamKeyword: "", //模糊查询
  createTime:
    moment()
      .subtract(6, "months")
      .set({ hour: 0, minute: 0 })
      .format("YYYY/MM/DD HH:mm") +
    " - " +
    moment().set({ hour: 23, minute: 59 }).format("YYYY/MM/DD HH:mm"), //任务创建时间
};

export default {
  name: "taskManage",
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      templateIdList: [
        { value: "", name: "全部" },
        { value: "10200", name: "分配任务关系" },
        { value: "21", name: "两融网上开户视频复审（APP）" },
        { value: "20", name: "两融开户线上预约复审" },
        { value: "19", name: "关联关系确认双向视频复审" },
        { value: "18", name: "补开股东户双向视频复审" },
        { value: "17", name: "补开股东户单向视频复审" },
        { value: "16", name: "补开股东户单向视频初审" },
        { value: "15", name: "深市衍生品账户柜台开通" },
        { value: "14", name: "开立深市衍生品账户复审" },
        { value: "13", name: "两融网上开户视频复审" },
        { value: "12", name: "私募合格投资者认证复审" },
        { value: "11", name: "私募合格投资者认证初审" },
        { value: "10", name: "资管合格投资者认证复审" },
        { value: "9", name: "资管合格投资者认证初审" },
        { value: "8", name: "产品购买双录复审" },
        { value: "7", name: "补档案资料确认" },
        { value: "6", name: "补档案资料复审" },
        { value: "5", name: "补档案资料初审" },
        { value: "4", name: "非现特殊视频确认" },
        { value: "3", name: "非现特殊视频复审" },
        { value: "2", name: "销户柜台操作" },
        { value: "1", name: "销户挽留任务" },
      ],
      executeGroupList,
      taskBigTypeList: [{ value: "", name: "全部" }],
      taskStatusList: [
        { value: "", name: "全部" },
        { value: "0", name: "未指定" },
        { value: "1", name: "待处理" },
        { value: "9", name: "进行中-仅查看" },
        { value: "2", name: "完成" },
        { value: "3", name: "失败" },
      ],
      tableForm: {
        taskName: "", //任务名称
        taskStatus: "", //任务状态
        taskBigType: "", //任务大类
        executeGroup: "", //执行组织
        templateId: "", //任务类型
        taskParamKeyword: "", //模糊查询
        createTime:
          moment()
            .subtract(6, "months")
            .set({ hour: 0, minute: 0 })
            .format("YYYY/MM/DD HH:mm") +
          " - " +
          moment().set({ hour: 23, minute: 59 }).format("YYYY/MM/DD HH:mm"), //任务创建时间
      },
      createTimePicker: [
        moment().subtract(6, "months").set({ hour: 0, minute: 0 }),
        moment().set({ hour: 23, minute: 59 }),
      ],
      stateList: [
        { value: "0", name: "待处理" },
        { value: "1", name: "审核中" },
        { value: "2", name: "审核驳回" },
        { value: "3", name: "办理完成" },
        { value: "4", name: "已过期" },
        { value: "5", name: "已作废" },
      ], //状态列表
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isAddPopShow: false, //添加弹窗是否显示
      isEditPopShow: false, //修改弹窗是否显示
      //查看弹窗
      isLookPopShow: false,
      selectData: {},
      operationType: "add",
      preBizNameMap: {},
      timeFormat: "YYYY/MM/DD HH:mm",
      timePickerOptions: {
        format: "HH:mm", // 设置时间格式为小时:分钟:秒
        defaultValue: [moment("00:00", "HH:mm"), moment("23:59", "HH:mm")], // 设置默认结束时间为23:59
      },
    };
  },
  provide() {
    return {
      api: api,
    };
  },
  computed: {
    columns() {
      return typeToColumns(this.tableForm.templateId);
    },
  },
  methods: {
    onChange(date, dateString) {
      console.log("data onChange start");
      console.log(date);
      this.tableForm.createTime = dateString[0] + " - " + dateString[1];
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    // 点击新增按钮
    add() {
      this.isAddPopShow = true;
      this.selectData = {};
    },
    // 点击修改按钮
    modify(data) {
      this.isEditPopShow = true;
      this.selectData = data;
    },
    // 查看
    look(data) {
      let href = `/bc-manage-view/taskManageDetail?taskId=${data.taskId}&templateId=${data.templateId}`;
      window.open(href, "_blank");
    },
    // 搜索框参数
    tableFormFilter(param) {
      return param;
    },
    getTaskStatusList(val) {
      const filterData = this.taskStatusList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
  },
};
</script>

<style lang="scss" scoped></style>
