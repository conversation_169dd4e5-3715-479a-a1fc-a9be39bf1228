<template>
  <div>
    <a-layout>
      <a-layout-header style="background: #fff">
        <a-row>
          <a-col :span="8">任务管理</a-col>
        </a-row>
      </a-layout-header>
      <a-layout-content style="background: #f9f9f9">
        <div v-if="data?.taskManagement?.show === '1'">
          <a-descriptions bordered>
            <a-descriptions-item label="任务名称">
              {{ data?.taskManagement?.taskName }}
            </a-descriptions-item>
            <a-descriptions-item label="任务类型">
              {{ data?.taskManagement?.templateName }}
            </a-descriptions-item>
            <a-descriptions-item
              v-show="data?.taskManagement?.taskStatus"
              label="任务状态"
            >
              {{ data?.taskManagement?.taskStatus }}
            </a-descriptions-item>
            <a-descriptions-item label="任务有效期">
              {{ formatDate(data?.taskManagement?.taskDeadline) }}
            </a-descriptions-item>
            <a-descriptions-item label="执行人">
              {{ data?.taskManagement?.executeUser }}
            </a-descriptions-item>
            <a-descriptions-item label="执行组织">
              {{ data?.taskManagement?.executeGroup }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(data?.taskManagement?.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ formatDate(data?.taskManagement?.updateTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="任务描述">
              {{ data?.taskManagement?.taskMsg }}
            </a-descriptions-item>
            <a-descriptions-item label="任务执行小结">
              {{ data?.taskManagement?.taskExecuteSummary }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </a-layout-content>
    </a-layout>
    <a-layout>
      <a-layout-header style="background: #fff">
        <a-row>
          <a-col :span="8">流程明细查看</a-col>
        </a-row>
      </a-layout-header>
      <a-layout-content
        style="background: #f9f9f9; padding: 0px 25px 90px 25px"
      >
        <div
          v-if="data?.doubleRecordInfo?.show === '1'"
          :style="{ marginTop: 15 + 'px' }"
        >
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title"
                >业务类型：{{ data?.doubleRecordInfo?.businessType }}</a-col
              >
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="双录名称">
              {{ data?.doubleRecordInfo?.doubleRecordName }}
            </a-descriptions-item>
          </a-descriptions>
          <div class="list_header top8">
            <a-row>
              <a-col :span="6" class="pop-title">双录产品列表</a-col>
            </a-row>
          </div>
          <a-table
            :data-source="data?.doubleRecordInfo?.doubleRecordList"
            :columns="columns"
          />
        </div>
        <div v-if="data.singleVideoSrc !== ''">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">单向视频</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card :bordered="false" class="self">
                    <video
                      width="600"
                      height="400"
                      controls="controls"
                      :src="data.singleVideoSrc"
                    ></video>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div
          v-if="data?.clientInfo?.show === '1'"
          :style="{ marginTop: 15 + 'px' }"
        >
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">客户信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="客户编号">
              {{ data?.clientInfo?.clientId }}
            </a-descriptions-item>
            <a-descriptions-item label="客户姓名">
              {{ data?.clientInfo?.clientName }}
            </a-descriptions-item>
            <a-descriptions-item label="身份证号">
              {{ data?.clientInfo?.idNo }}
            </a-descriptions-item>
            <a-descriptions-item label="性别">
              {{ data?.clientInfo?.gender === "0" ? "男" : "女" }}
            </a-descriptions-item>
            <a-descriptions-item label="生日">
              {{ data?.clientInfo?.idNo?.slice(6, 14) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件开始日期">
              {{ data?.clientInfo?.begindate }}
            </a-descriptions-item>
            <a-descriptions-item label="证件结束日期">
              {{ data?.clientInfo?.enddate }}
            </a-descriptions-item>
            <a-descriptions-item label="证件地址">
              {{ data?.clientInfo?.idAddress }}
            </a-descriptions-item>
            <a-descriptions-item label="证件签发机关">
              {{ data?.clientInfo?.issuedDepart }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-if="data?.clientBusinessfile?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">客户业务档案</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card class="self">
                    <viewer class="idcardImg"
                      ><div
                        v-for="(
                          { label, imgUrl }, i
                        ) in data.clientBusinessList"
                        :key="i"
                        class="idcardImg_context"
                      >
                        <p>{{ label }}</p>
                        <img style="cursor: pointer" :src="imgUrl" />
                      </div>
                    </viewer>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div v-if="data?.clientCounterfile?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">客户柜台档案</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card class="self">
                    <viewer class="idcardImg">
                      <div
                        v-for="(a, i) in data?.clientCounterfile
                          ?.customerImageDownloadVOS"
                        :key="i"
                        class="idcardImg_context"
                      >
                        <p>{{ clientCounterFileMap[a.imageNo] }}</p>
                        <img
                          style="cursor: pointer"
                          :src="`data:image/jpeg;base64,${a.imageData}`"
                        />
                      </div>
                    </viewer>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>

        <div v-if="data?.auditInfo?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">审核信息</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="初审状态">
                  {{ data?.auditInfo?.csState }}
                </a-descriptions-item>
                <a-descriptions-item label="初审结论">
                  {{ data?.auditInfo?.csPption }}
                </a-descriptions-item>
                <a-descriptions-item label="初审备注">
                  {{ data?.auditInfo?.csRemark }}
                </a-descriptions-item>
                <a-descriptions-item label="初审人">
                  {{ data?.auditInfo?.csOperator }}
                </a-descriptions-item>
                <a-descriptions-item label="初审时间">
                  {{ formatDate(data?.auditInfo?.csTime) }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>
<script>
import api from "../api";
import moment from "moment";
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      singleVideoSrc: "",
      videoWitnessSrc: "",
      clientBusinessList: [],
      clientBusFileMap: {
        1: "身份证照片正面",
        2: "身份证照片反面",
        3: "客户头像",
        4: "视频见证录像",
        5: "辅证图片",
        7: "单向视频",
        8: "客户公安头像",
        9: "净资产证明档案",
        10: "总资产证明档案",
        11: "近三年年均收入证明档案",
        12: "投资经验证明档案",
        13: "无持仓证明",
      },
      clientCounterFileMap: {
        80: "客户头像",
        82: "公安头像",
        "6A": "身份证正面",
        "6B": "身份证反面",
      },
      columns: [
        {
          title: "产品编号",
          dataIndex: "productNo",
          key: "productNo",
        },
        {
          title: "产品代码",
          dataIndex: "productCode",
          key: "productCode",
        },
        {
          title: "产品名称",
          dataIndex: "productName",
          key: "productName",
        },
      ],
      xhInfoColumns: [
        {
          title: "账户类型",
          dataIndex: "accountType",
          key: "accountType",
        },
        {
          title: "账号",
          dataIndex: "accountCode",
          key: "accountCode",
        },
        {
          title: "操作",
          dataIndex: "action",
          key: "action",
        },
        {
          title: "销户状态",
          dataIndex: "checkflag",
          key: "checkflag",
        },
      ],
    };
  },
  provide: [api],
  computed: {
    cancellationAccountInfoList() {
      let list = [];
      if (this.data?.cancellationAccountInfo?.cancellationAccountInfoList) {
        this.data.cancellationAccountInfo.cancellationAccountInfoList.forEach(
          ({ ...it }) => {
            list.push(it);
          }
        );
      }
      return list;
    },
    incidenceRelationList() {
      let list = [];
      if (this.data?.incidenceRelationInfo?.incidenceRelationList) {
        this.data.incidenceRelationInfo.incidenceRelationList.forEach(
          ({ ...it }) => {
            list.push({
              ...it,
            });
          }
        );
      }
      return list;
    },
  },
  methods: {
    formatDate(v = "") {
      if (!v) return "";
      return moment(new Date(v)).format("YYYY/MM/DD HH:mm:ss");
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: "#282828";
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}

.idcardImg img {
  width: 335px;
  height: 200px;
  margin: 12px;
}
.idcardImg_context {
  width: 350px;
  display: inline-block;
}
.top8 {
  position: relative;
  top: 8px;
}
</style>
