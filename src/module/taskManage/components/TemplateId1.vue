<template>
  <div>
    <a-layout>
      <a-layout-header style="background: #fff">
        <a-row>
          <a-col :span="8">任务管理</a-col>
        </a-row>
      </a-layout-header>
      <a-layout-content style="background: #f9f9f9">
        <div v-if="data?.taskManagement?.show === '1'">
          <a-descriptions bordered>
            <a-descriptions-item label="任务名称">
              {{ data?.taskManagement?.taskName }}
            </a-descriptions-item>
            <a-descriptions-item label="任务类型">
              {{ data?.taskManagement?.templateName }}
            </a-descriptions-item>
            <a-descriptions-item
              v-show="data?.taskManagement?.taskStatus"
              label="任务状态"
            >
              {{ data?.taskManagement?.taskStatus }}
            </a-descriptions-item>
            <a-descriptions-item label="任务有效期">
              {{ formatDate(data?.taskManagement?.taskDeadline) }}
            </a-descriptions-item>
            <a-descriptions-item label="执行人">
              {{ data?.taskManagement?.executeUser }}
            </a-descriptions-item>
            <a-descriptions-item label="执行组织">
              {{ data?.taskManagement?.executeGroup }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(data?.taskManagement?.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ formatDate(data?.taskManagement?.updateTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="任务描述">
              {{ data?.taskManagement?.taskMsg }}
            </a-descriptions-item>
            <a-descriptions-item label="任务执行小结">
              {{ data?.taskManagement?.taskExecuteSummary }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </a-layout-content>
    </a-layout>
    <a-layout>
      <a-layout-header style="background: #fff">
        <a-row>
          <a-col :span="8">流程明细查看</a-col>
        </a-row>
      </a-layout-header>
      <a-layout-content
        style="background: #f9f9f9; padding: 0px 25px 90px 25px"
      >
        <div
          v-if="data?.cancellationTaskInfo?.show === '1'"
          :style="{ marginTop: 15 + 'px' }"
        >
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">销户任务信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="客户姓名">
              {{ data?.cancellationTaskInfo?.clientName }}
            </a-descriptions-item>
            <a-descriptions-item label="客户号">
              {{ data?.cancellationTaskInfo?.clientId }}
            </a-descriptions-item>
            <a-descriptions-item label="客户所属团队">
              {{ data?.cancellationTaskInfo?.serviceName }}
            </a-descriptions-item>
            <a-descriptions-item label="利润贡献">
              {{ data?.cancellationTaskInfo?.mvalueName }}
            </a-descriptions-item>
            <a-descriptions-item label="资产等级">
              {{ data?.cancellationTaskInfo?.assetRankName }}
            </a-descriptions-item>
            <a-descriptions-item label="外部市值">
              {{ data?.cancellationTaskInfo?.mktValue }}
            </a-descriptions-item>
            <a-descriptions-item label="开户时间">
              {{ data?.cancellationTaskInfo?.openTime }}
            </a-descriptions-item>
            <a-descriptions-item label="EBOSS编号">
              {{ data?.cancellationTaskInfo?.ebossFdId }}
            </a-descriptions-item>
            <a-descriptions-item label="服务人员">
              {{ data?.cancellationTaskInfo?.serviceEmpName }}
            </a-descriptions-item>
            <a-descriptions-item label="服务人员所在部门">
              {{ data?.cancellationTaskInfo?.serviceEhrDeptStr }}
            </a-descriptions-item>
            <a-descriptions-item label="开发关系人员">
              {{ data?.cancellationTaskInfo?.saleEmpName }}
            </a-descriptions-item>
            <a-descriptions-item label="开发关系人员所在部门">
              {{ data?.cancellationTaskInfo?.saleEhrDeptStr }}
            </a-descriptions-item>
            <a-descriptions-item label="客群标签">
              {{ data?.cancellationTaskInfo?.netExpClientGroup }}
            </a-descriptions-item>
            <a-descriptions-item label="销户原因">
              {{ data?.cancellationTaskInfo?.cancellationReason }}
            </a-descriptions-item>
            <a-descriptions-item label="销户内容">
              {{ data?.cancellationTaskInfo?.cancellationText }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-show="data?.cancellationAccountInfo?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="list_header">
              <a-row>
                <a-col :span="6" class="pop-title">销户挽留任务</a-col>
              </a-row>
            </div>
            <div class="content-border">
              <a-table
                :dataSource="cancellationAccountInfoList"
                :columns="xhInfoColumns"
                :pagination="false"
                bordered
              ></a-table>
              <a-descriptions bordered>
                <a-descriptions-item label="挽留备注" :column="2">
                  {{ data?.cancellationAccountInfo?.wlRemark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
        <div v-show="data?.cancellationDetentionInfo?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">销户挽留结果</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="客户销户原因">
                  {{ data?.cancellationDetentionInfo?.cancelReason }}
                </a-descriptions-item>
                <a-descriptions-item label="挽留结果">
                  {{ data?.cancellationDetentionInfo?.cancelConclusion }}
                </a-descriptions-item>
                <a-descriptions-item label="备注小结">
                  {{ data?.cancellationDetentionInfo?.remark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
        <div
          v-if="data?.clientInfo?.show === '1'"
          :style="{ marginTop: 15 + 'px' }"
        >
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">客户信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="客户姓名">
              {{ data?.clientInfo?.clientName }}
            </a-descriptions-item>
            <a-descriptions-item label="身份证号">
              {{ data?.clientInfo?.idNo }}
            </a-descriptions-item>
            <a-descriptions-item label="性别">
              {{ data?.clientInfo?.gender === "0" ? "男" : "女" }}
            </a-descriptions-item>
            <a-descriptions-item label="生日">
              {{ data?.clientInfo?.idNo?.slice(6, 14) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件开始日期">
              {{ data?.clientInfo?.begindate }}
            </a-descriptions-item>
            <a-descriptions-item label="证件结束日期">
              {{ data?.clientInfo?.enddate }}
            </a-descriptions-item>
            <a-descriptions-item label="证件地址">
              {{ data?.clientInfo?.idAddress }}
            </a-descriptions-item>
            <a-descriptions-item label="证件签发机关">
              {{ data?.clientInfo?.issuedDepart }}
            </a-descriptions-item>
            <a-descriptions-item label="客户号">
              {{ data?.clientInfo?.clientId }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div
          v-if="data?.reservationInfo?.show === '1'"
          :style="{ marginTop: 15 + 'px' }"
        >
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">预约单信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="业务类型">
              {{ data?.reservationInfo?.businessType }}
            </a-descriptions-item>
            <a-descriptions-item label="创建人">
              {{ data?.reservationInfo?.opUserId }}
            </a-descriptions-item>
            <a-descriptions-item label="预约时间">
              {{ formatDate(data?.reservationInfo?.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="有效期至">
              {{ formatDate(data?.reservationInfo?.expireTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="预约单编号">
              {{ data?.reservationInfo?.serialNo }}
            </a-descriptions-item>
            <a-descriptions-item label="办理业务">
              {{ data?.reservationInfo?.subBusinessType }}
            </a-descriptions-item>
            <a-descriptions-item label="备注">
              {{ data?.reservationInfo?.remark }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-if="data?.clientBusinessfile?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">客户业务档案</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card class="self">
                    <viewer class="idcardImg"
                      ><div
                        v-for="({ label, imgUrl }, i) in clientBusinessList"
                        :key="i"
                        class="idcardImg_context"
                      >
                        <p>{{ label }}</p>
                        <img style="cursor: pointer" :src="imgUrl" />
                      </div>
                    </viewer>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div v-if="data?.videoInfo?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">视频信息</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="流程ID">
                  {{ data?.videoInfo?.processId }}
                </a-descriptions-item>
                <a-descriptions-item label="标识类型">
                  {{ data?.videoInfo?.identityType }}
                </a-descriptions-item>
                <a-descriptions-item label="标识">
                  {{ data?.videoInfo?.identity }}
                </a-descriptions-item>
                <a-descriptions-item label="状态">
                  {{ data?.videoInfo?.state }}
                </a-descriptions-item>
                <a-descriptions-item label="客户姓名">
                  {{ data?.videoInfo?.clientName }}
                </a-descriptions-item>
                <a-descriptions-item label="业务流水号">
                  {{ data?.videoInfo?.busiSn }}
                </a-descriptions-item>
                <a-descriptions-item label="创建时间">
                  {{ formatDate(data?.videoInfo?.createTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="更新时间">
                  {{ formatDate(data?.videoInfo?.updateTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="操作员">
                  {{ data?.videoInfo?.operator }}
                </a-descriptions-item>
                <a-descriptions-item label="操作备注">
                  {{ data?.videoInfo?.videoRemark }}
                </a-descriptions-item>
                <a-descriptions-item label="备注">
                  {{ data?.videoInfo?.remark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
        <div v-if="data?.auditInfo?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">操作信息</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="备注">
                  {{ data?.auditInfo?.xhRemark }}
                </a-descriptions-item>
                <a-descriptions-item label="柜台销户操作">
                  {{ data?.auditInfo?.xhOperate }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>
<script>
import api from "../api";
import moment from "moment";
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      singleVideoSrc: "",
      videoWitnessSrc: "",
      clientBusinessList: [],
      clientBusFileMap: {
        1: "身份证照片正面",
        2: "身份证照片反面",
        3: "客户头像",
        4: "视频见证录像",
        5: "辅证图片",
        7: "单向视频",
        8: "客户公安头像",
        9: "净资产证明档案",
        10: "总资产证明档案",
        11: "近三年年均收入证明档案",
        12: "投资经验证明档案",
        13: "无持仓证明",
      },
      columns: [
        {
          title: "产品编号",
          dataIndex: "productNo",
          key: "productNo",
        },
        {
          title: "产品代码",
          dataIndex: "productCode",
          key: "productCode",
        },
        {
          title: "产品名称",
          dataIndex: "productName",
          key: "productName",
        },
      ],
      xhInfoColumns: [
        {
          title: "账户类型",
          dataIndex: "accountType",
          key: "accountType",
        },
        {
          title: "账号",
          dataIndex: "accountCode",
          key: "accountCode",
        },
        {
          title: "操作",
          dataIndex: "action",
          key: "action",
        },
        {
          title: "销户状态",
          dataIndex: "checkflag",
          key: "checkflag",
        },
      ],
    };
  },
  provide: [api],
  computed: {
    cancellationAccountInfoList() {
      let list = [];
      if (this.data?.cancellationAccountInfo?.cancellationAccountInfoList) {
        this.data.cancellationAccountInfo.cancellationAccountInfoList.forEach(
          ({ ...it }) => {
            list.push(it);
          }
        );
      }
      return list;
    },
    incidenceRelationList() {
      let list = [];
      if (this.data?.incidenceRelationInfo?.incidenceRelationList) {
        this.data.incidenceRelationInfo.incidenceRelationList.forEach(
          ({ ...it }) => {
            list.push({
              ...it,
            });
          }
        );
      }
      return list;
    },
  },
  methods: {
    formatDate(v = "") {
      if (!v) return "";
      return moment(new Date(v)).format("YYYY/MM/DD HH:mm:ss");
    },
    queryfileBytes(fileId = "", callback) {
      let fileBytes = "";
      if (fileId === "" || fileId === "null") {
        return callback("");
      }
      api
        .queryImgfileBytes({
          fileId,
        })
        .then((res) => {
          if (res.code == 0) {
            fileBytes = res.data.fileBytes;
          } else {
            fileBytes = "";
          }
          callback && callback(fileBytes);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: "#282828";
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}

.idcardImg img {
  width: 335px;
  height: 200px;
  margin: 12px;
}
.idcardImg_context {
  width: 350px;
  display: inline-block;
}
.top8 {
  position: relative;
  top: 8px;
}
</style>
