// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  @Parameters(["taskId"])
  showDetai() {
    return this.services.initGet({
      reqUrl: "gj/audit/taskManage/showDetail",
      param: this.param,
    });
  }

  /**
   * 查询文件信息
   * @param {Object} param
   */
  @Parameters(["fileId"])
  queryImgfileBytes() {
    return this.services.initGet({
      reqUrl: "gj/download",
      param: this.param,
    });
  }

  /**
   * 查询执行组织
   * @param {Object} param
   */
  @Parameters(["pid"])
  queryExecuteGroup() {
    return this.services.initGet({
      reqUrl: "gj/queryExecuteGroup",
      param: this.param,
    });
  }
}

export default new api();
