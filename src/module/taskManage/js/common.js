import moment from "moment";
import api from "../api";

const templateIdList = [
  { value: "", name: "全部" },
  { value: "10200", name: "分配任务关系" },
  { value: "21", name: "两融网上开户视频复审（APP）" },
  { value: "20", name: "两融开户线上预约复审" },
  { value: "19", name: "关联关系确认双向视频复审" },
  { value: "18", name: "补开股东户双向视频复审" },
  { value: "17", name: "补开股东户单向视频复审" },
  { value: "16", name: "补开股东户单向视频初审" },
  { value: "15", name: "深市衍生品账户柜台开通" },
  { value: "14", name: "开立深市衍生品账户复审" },
  { value: "13", name: "两融网上开户视频复审" },
  { value: "12", name: "私募合格投资者认证复审" },
  { value: "11", name: "私募合格投资者认证初审" },
  { value: "10", name: "资管合格投资者认证复审" },
  { value: "9", name: "资管合格投资者认证初审" },
  { value: "8", name: "产品购买双录复审" },
  { value: "7", name: "补档案资料确认" },
  { value: "6", name: "补档案资料复审" },
  { value: "5", name: "补档案资料初审" },
  { value: "4", name: "非现特殊视频确认" },
  { value: "3", name: "非现特殊视频复审" },
  { value: "2", name: "销户柜台操作" },
  { value: "1", name: "销户挽留任务" },
];

export let executeGroupList = [{ value: "", name: "全部" }];
(async function () {
  try {
    const { code, message, data } = await api.queryExecuteGroup({
      pid: "1",
    });
    if (code === 0) {
      for (const { id, name } of data) {
        executeGroupList.push({
          value: id,
          name,
        });
      }
    } else {
      throw message;
    }
  } catch (e) {
    this.$message.error(e);
  }
})();
/* { value: "", name: "全部" },
[
    { value: "5", name: "基础客服" },
    { value: "", name: "其他" },
    { value: "", name: "复杂查询组" },
    { value: "", name: "一级组织" },
    { value: "", name: "特殊视频" },
    { value: "", name: "产品购买双录" },
    { value: "", name: "合格投资者认证" },
    { value: "", name: "IM群推组" },
    { value: "", name: "深市衍生品账户" },
    { value: "", name: "两融网上开户视频" },
    { value: "", name: "综合柜台视频见证组" },
    { value: "", name: "关联关系确认" },
    { value: "", name: "两融预约开户视频" },
    { value: "", name: "两融全线上开户视频" },
    { value: "", name: "柜台操作组" },
    { value: "", name: "基础客服团队" },
    { value: "", name: "理财客服团队" },
    { value: "", name: "投资顾问团队" },
    { value: "", name: "金融顾问团队" },
    { value: "", name: "客户经理团队" },
    { value: "", name: "全部" },
    { value: "", name: "全部" },
    { value: "", name: "全部" },
    { value: "", name: "全部" },
    { value: "", name: "全部" },
    { value: "", name: "全部" }, 
];*/

export function typeToColumns() {
  let columns;
  columns = [
    {
      field: "templateId",
      label: "任务类型",
      isSorter: false,
      filter: (item) => getTemplateIdList(item),
    },
    {
      field: "taskName",
      label: "任务名称",
      isSorter: false,
    },
    {
      field: "executeGroupStr",
      label: "执行组织",
      isSorter: false,
      // filter: (item) => this.getExecuteGroupList(item),
    },
    {
      field: "executeUser",
      label: "执行人",
      isSorter: false,
    },
    {
      field: "taskDeadline",
      label: "任务有效期",
      isSorter: false,
      filter: (v) => formatDate(v),
    },
    {
      field: "taskOvertimeEnd",
      label: "过期自动结束",
      isSorter: false,
    },
    {
      field: "taskStatusStr",
      label: "任务状态",
      isSorter: false,
    },
    {
      field: "createTime",
      label: "创建时间",
      isSorter: false,
      filter: (v) => formatDate(v),
    },
    {
      field: "clientName",
      label: "客户姓名",
      isSorter: false,
    },
    {
      field: "clientId",
      label: "客户号",
      isSorter: false,
    },
    {
      field: "updateTime",
      label: "更新时间",
      isSorter: false,
      filter: (v) => formatDate(v),
    },
    {
      field: "lockedUser",
      label: "锁定人",
      isSorter: false,
    },
    {
      field: "operation",
      label: "操作",
      align: "center",
      width: 300,
      fixed: "right",
    },
  ];
  return columns;
}

export function formatDate(v = "") {
  if (!v) return "";
  return moment(new Date(v)).format("YYYY/MM/DD HH:mm:ss");
}

export function getTemplateIdList(val) {
  const filterData = templateIdList.filter(
    ({ value }) => Number(value) === val
  )[0];
  return filterData ? filterData.name : "";
}

export function getExecuteGroupList(val) {
  const filterData = executeGroupList.filter(
    ({ value }) => Number(value) === val
  )[0];
  return filterData ? filterData.name : "";
}
