// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 新增基金公司
   * @param {*}
   */
  @Parameters(["_data"])
  addFundCompany() {
    return this.services.initPost({
      reqUrl: "bcFundCompany/add",
      param: this.param,
    });
  }

  /**
   * 修改基金公司
   * @param {*}
   */
  @Parameters(["_data"])
  editFundCompany() {
    return this.services.initPost({
      reqUrl: "bcFundCompany/edit",
      param: this.param,
    });
  }

  /**
   * 删除多个基金公司
   * @param {Object} param
   * - bcFundCompanyIds {String} 参数主键ID列表，多个以“，”隔开
   *
   */
  @Parameters(["bcFundCompanyIds"])
  deletesFundCompany() {
    return this.services.initPost({
      reqUrl: "bcFundCompany/deletes",
      param: this.param,
    });
  }
}

export default new api();
