<template>
  <a-modal
    :title="`${parameterData.id ? '修改' : '新增'}基金公司`"
    class="data-ant-module"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 7 }"
      :wrapper-col="{ span: 15 }"
      :rules="rules"
    >
      <a-form-model-item label="基金公司编码" prop="companyNo">
        <a-input v-model="form.companyNo" placeholder="请输入基金公司编码">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="基金公司名称" prop="companyName">
        <a-input v-model="form.companyName" placeholder="请输入基金公司名称">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="基金公司拼音">
        <a-input v-model="form.companySpell" placeholder="请输入基金公司拼音">
        </a-input>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  companyNo: "",
  companyName: "",
  companySpell: "",
};

// 注册当前请求对应的上下文请求组
export default {
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        companyNo: [
          { required: true, message: "基金公司编码不能为空", trigger: "blur" },
        ],
        companyName: [
          { required: true, message: "基金公司名称不能为空", trigger: "blur" },
        ],
      },
      confirmLoading: false,
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    query() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = this.parameterData;
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `基金公司${this.parameterData.id ? "修改" : "新增"}失败：${msg}`
            );
          this.$message.success(
            `基金公司${this.parameterData.id ? "修改" : "新增"}成功！`
          );
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        this.parameterData.id
          ? this.api.editFundCompany(param).then(callback)
          : this.api.addFundCompany(param).then(callback);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
