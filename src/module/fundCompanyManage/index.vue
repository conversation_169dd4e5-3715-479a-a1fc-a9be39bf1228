<template>
  <a-card title="基金公司列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="基金公司编码">
            <a-input
              v-model="tableForm.companyNo"
              placeholder="请输入基金公司编码"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="基金公司名称">
            <a-input
              v-model="tableForm.companyName"
              placeholder="请输入基金公司名称"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="基金公司拼音">
            <a-input
              v-model="tableForm.companySpell"
              placeholder="请输入基金公司拼音"
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcFundCompany/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add">
            新增
          </a-button>
          <a-button
            icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove"
          >
            删除
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a @click.stop="modify(data)">修改</a>
        </template>
      </tk-table>
    </div>
    <addOrEdit
      :isPopShow.sync="isPopShow"
      @success="query"
      :parameterData="selectData"
    ></addOrEdit>
  </a-card>
</template>

<script>
import api from "./api";
import addOrEdit from "./module/addOrEdit";

// 默认表单属性
const defaultForm = {
  companyNo: "",
  companyName: "",
  companySpell: "",
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "companyNo",
          label: "基金公司编码",
          isSorter: false,
        },
        {
          field: "companyName",
          label: "基金公司名称",
          isSorter: false,
        },
        {
          field: "companySpell",
          label: "基金公司拼音",
          isSorter: false,
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      tableForm: {
        companyNo: "",
        companyName: "",
        companySpell: "",
      },
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isPopShow: false, //弹窗是否显示
      selectData: {},
    };
  },
  provide: { api: api },
  components: {
    addOrEdit,
  },
  methods: {
    add() {
      this.isPopShow = true;
      this.selectData = {};
    },
    remove() {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deletePre();
        },
      });
    },
    deletePre() {
      api
        .deletesFundCompany({
          bcFundCompanyIds: this.selectedRowKeys.join(","),
        })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.query();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    modify(data) {
      this.isPopShow = true;
      this.selectData = data;
    },
    tableFormFilter(param) {
      return param;
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
  },
};
</script>

<style lang="scss" scoped></style>
