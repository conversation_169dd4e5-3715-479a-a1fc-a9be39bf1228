<!--
 * @Author: 薛冰川
 * @Date: 2022-03-29 16:10:39
 * @LastEditTime: 2022-09-27 19:09:40
 * @LastEditors: chenxy
 * @Description: index页面
 * @FilePath: /ge-manage-view/src/module/generalServiceFlow/index.vue
-->
<template>
  <a-layout>
    <a-card title="" :bordered="false">
      <div class="serachForm">
        <a-form layout="inline" :searchFrom.sync="searchFrom">
          <tkSelectForm @query="query" @reset="reset">
            <a-form-model-item label="业务单编号">
              <a-input v-model="searchFrom.bizWorkCode" :allowClear="true"/>
            </a-form-model-item>
            <a-form-model-item label="业务名称">
              <a-select show-search :filter-option="filterOption" v-model="searchFrom.bizType "
                        :allowClear="true" :options="bizTypeS">
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="客户全称">
              <a-input v-model="searchFrom.custFullName" :allowClear="true"/>
            </a-form-model-item>
            <a-form-model-item label="资金账号">
              <a-input v-model="searchFrom.capitalAccountNo" :allowClear="true"/>
            </a-form-model-item>
            <a-form-model-item label="客户类别">
              <a-select show-search :filter-option="filterOption" v-model="searchFrom.custType "
                        :allowClear="true" :options="custTypeS">
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="证件类型">
              <a-select show-search :filter-option="filterOption" v-model="searchFrom.idType "
                        :allowClear="true" :options="idTypeS">
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="证件号码">
              <a-input v-model="searchFrom.idCode" :allowClear="true"/>
            </a-form-model-item>
            <!--------------------------这里补上客户营业部选项------------------------------------>
            <a-form-model-item label="客户营业部">
              <a-select
                  placeholder="请选择"
                  mode="multiple"
                  :allowClear="true"
                  :options="custOrgCodeS"
                  v-model="searchFrom.custOrgCode "
                  :filter-option="filterOption"
              >
                <a-icon slot="suffixIcon" type="dash"/>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="业务状态">
              <a-select show-search :filter-option="filterOption" v-model="searchFrom.bizWorkStatus "
                        :allowClear="true" :options="bizWorkStatusS">
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="引流渠道">
              <a-select show-search :filter-option="filterOption" v-model="searchFrom.channel "
                        :allowClear="true" :options="channelS">
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="操作终端">
              <a-select show-search :filter-option="filterOption" v-model="searchFrom.opClient "
                        :allowClear="true" :options="opClientS">
              </a-select>
            </a-form-model-item>
            <template slot="btn">
              <!--                        <a-button type="primary" icon="search" @click.stop="query" class="btn_mr"> 查询</a-button>-->
              <a-button icon="reload" type="primary" @click.stop="reset"> 重置</a-button>
            </template>
          </tkSelectForm>
        </a-form>
      </div>
      <div>
        <tk-table
            :tableWidth="tableWidth"
            :tableData="comlun"
            :getMethods="'bc-manage-server/dc/bizWorkFlow/page'"
            :isSelected="true"
            :tableId="'id'"
            :isPaging="true"
            ref="table"
            class="scroll-view"
            :selectedRowKeys.sync="seletid"
            :tableFrom="copySearchFrom"
        >
          <template slot="operation" slot-scope="tags">
            <a @click.stop="checkDetail(tags)" >详情</a>
          </template>
        </tk-table>
      </div>
    </a-card>
  </a-layout>
</template>

<script>
import api from './api';

const defaultForm = {
  bizWorkCode: undefined,
  bizType: undefined,
  custFullName: undefined,
  capitalAccountNo: undefined,
  custType: undefined,
  idType: undefined,
  idCode: undefined,
  custOrgCode: [],
  bizWorkStatus: undefined,
  channel: undefined,
  opClient: undefined,
}
export default {
  provide: {api: api},
  computed: {
    tableWidth() {
      let width = 0;
      this.comlun.forEach(item => {
        if (item.width) width += item.width
      })
      return width;
    }
  },
  created() {
    this.queryDictMap();
  },
  data() {
    return {
      searchFrom: JSON.parse(JSON.stringify(defaultForm)),
      copySearchFrom:undefined,// 筛选条件
      bizTypeS: [], //业务名称  数据字典
      custTypeS: [], //客户类别  数据字典
      idTypeS: [], //证件类型  数据字典
      custOrgCodeS: [], //客户营业部  数据字典
      bizWorkStatusS: [], //业务状态  数据字典
      channelS: [], //引流渠道  数据字典
      opClientS: [], //操作终端  数据字典
      seletid: [], // 选中参数
      pageTemplateCodeS:[], //跳转地址数据字典
      // 展示列字段
      comlun: [
        {field: "bizWorkCode", label: "业务单编号", width: 120, isEllipsis: true},
        {
          field: "bizType",
          filter: item => this.getDictText("bizTypeS", item),
          label: "业务名称",
          width: 120,
          isEllipsis: true
        },
        {field: "custFullName", label: "客户全称", width: 120, isEllipsis: true},
        {field: "capitalAccountNo", label: "资金账号", width: 120, isEllipsis: true},
        {
          field: "custType",
          filter: item => this.getDictText("custTypeS", item),
          label: "客户类别",
          width: 120,
          isEllipsis: true
        },
        {field: "idCode", label: "证件号码", width: 120, isEllipsis: true},
        {
          field: "custOrgCode",
          filter: item => this.getDictText("custOrgCodeS", item),
          label: "客户所属营业部",
          width: 120,
          isEllipsis: true
        },
        {
          field: "bizWorkStatus",
          filter: item => this.getDictText("bizWorkStatusS", item),
          label: "业务状态",
          width: 120,
          isEllipsis: true
        },
        {
          field: "channel",
          filter: item => this.getDictText("channelS", item),
          label: "引流渠道",
          width: 120,
          isEllipsis: true
        },
        {
          field: "opClient",
          filter: item => this.getDictText("opClientS", item),
          label: "操作终端",
          width: 120,
          isEllipsis: true
        },
        {field: "acceptEndTime", label: "受理完成时间", width: 180, isEllipsis: true, isSorter: true},
        {field: "handleEndTime", label: "办理完成时间", width: 180, isEllipsis: true, isSorter: true},
        {field: "workUpdateTime", label: "流水更新时间", width: 180, isEllipsis: true, isSorter: true},
        {field: "workCreateTime", label: "流水创建时间", width: 180, isEllipsis: true, isSorter: true},
        {field: "operation", label: "操作", width: 120, isEllipsis: true}
      ],
    };
  },
  methods: {
    //查询字典值
    queryDictMap() {
      return new Promise((resolve) => {
        [
          {dataIndex: 'bizTypeS', key: 'ge.comprehensive.bizType'},
          {dataIndex: 'custTypeS', key: 'ge.comprehensive.custType'},
          {dataIndex: 'idTypeS', key: 'ge.comprehensive.idType'},
          {dataIndex: 'custOrgCodeS', key: 'wa.common.branchList'},
          {dataIndex: 'bizWorkStatusS', key: 'ge.comprehensive.bizWorkStatus'},
          {dataIndex: 'channelS', key: 'ge.comprehensive.channel'},
          {dataIndex: 'opClientS', key: 'ge.comprehensive.opClient'},
          {dataIndex: 'pageTemplateCodeS', key: 'ge.comprehensive.pageTemplateCode'},
        ].forEach((item) => {
          this.$dict.dictContent(item.key).then(data => {
            this[item.dataIndex] = (data || []).map((data) => {
              return {label: data.dictLabel, value: data.dictValue};
            });
            resolve();
          })
        });
      });
    },
    /**
     * way:查看详情准备跳转
     * */
    checkDetail(val) {
      let processingUrl;
      class jump {
        constructor(path = '') {
          if (!path) return
          this.url = path
          this.parameters = {}
          this.parseQuery()
          this.stringifyQuery()
        }
        //处理解析url后的参数集合为一个对象
        parseQuery = ()=>{
              var str = this.url.split("?")[1],    //通过?得到一个数组,取?后面的参数
              items = str.split("&");    //分割成数组
              var arr, name,key;
              for (var i = 0; i < items.length; i++) {
              arr = items[i].split("=");
              name = arr[0];
              key = items[i]?.split('${')[1]?.split('}')[0]
              this.parameters[name] = val[key]
            }
        }
        stringifyQuery = ()=>{
          var str = this.url.split("?")[0]
          let parameters = ''
             Object.keys(this.parameters).map(name=>{
                 let item = this.parameters[name]
               if (!parameters){
                 parameters = parameters+`${name}=${item}`
               }else {
                 parameters = `${parameters} & ${name}=${item}`
               }
             })
          processingUrl = `${str}?${parameters}`
        }
      }
      let urObj = this.pageTemplateCodeS.find((item)=>{return item.value = val.pageTemplateCode})
      new jump(urObj?.label,this)
      window.open(processingUrl)
    },
    /**
     * way:查询表单
     * */
    query() {
      this.copySearchFrom = JSON.parse(JSON.stringify(this.searchFrom))
      this.copySearchFrom.custOrgCode = this.copySearchFrom.custOrgCode.join(',')
      this.$nextTick(() => {
        this.$refs.table.getTableData()
      });
    },
    /**
     * way:表格处理字典映射
     * */
    getDictText(key, value) {
      let results = this[key] || []
      results = results.filter((item) => {
        return item.value == value
      })
      return results && results.length && results[0].label || value || '';
    },
    /**
     * way:表单选择器默认过滤
     * */
    filterOption(input, option) {
      return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    /**
     * way:重置搜索内容
     * */
    reset() {
      this.searchFrom = JSON.parse(JSON.stringify(defaultForm))
    },
  },
}
</script>


<style lang="scss" rel="stylesheet/scss" scoped>
</style>

