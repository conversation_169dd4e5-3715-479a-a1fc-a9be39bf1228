<template>
  <a-modal :title="`${parameterData.id ? '修改' : '添加'}`" class="data-ant-module" v-model="showPop" @cancel="closePop"
    :width="700" :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :rules="rules">
      <a-form-model-item label="业务名称" prop="bizType">
        <a-select v-model="form.bizType" placeholder="请选择业务" show-search option-filter-prop="children" :allowClear="true"
          @change="changeBizType">
          <a-select-option v-for="item in parameterData.businessList" :value="item.bizType" :key="item.id">
            {{ item.bizName }}({{ item.bizType }})
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="业务风险等级" prop="bizRiskLevel" class="risk-class">
        <a-select v-model="form.bizRiskLevel" placeholder="请选择风险等级" show-search option-filter-prop="children"
          :allowClear="true">
          <a-select-option v-for="item in parameterData.bizRiskLevelList" :value="item.dictValue" :key="item.dictValue">
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
        <a-radio-group name="bizRiskMatch" v-model="form.bizRiskMatch" class="match-class">
          <a-radio value="1" key="1">强匹配</a-radio>
          <a-radio value="2" key="2">弱匹配</a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="投资期限">
        <a-select v-model="form.investTerm" placeholder="请选择投资期限" show-search option-filter-prop="children"
          :allowClear="true">
          <a-select-option v-for="item in parameterData.investTermList" :value="item.dictValue" :key="item.dictValue">
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="投资品种">
        <a-select v-model="form.investType" placeholder="请选择投资品种" show-search option-filter-prop="children"
          :allowClear="true">
          <a-select-option v-for="item in parameterData.investTypeList" :value="item.dictValue" :key="item.dictValue">
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="预期收益">
        <a-select v-model="form.incomeType" placeholder="请选择预期收益" show-search option-filter-prop="children"
          :allowClear="true">
          <a-select-option v-for="item in parameterData.incomeTypelList" :value="item.dictValue" :key="item.dictValue">
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="准入风险等级" prop="corpRiskLevel">
        <a-select v-model="form.corpRiskLevel" placeholder="请选择准入风险等级" show-search option-filter-prop="children"
          :allowClear="true">
          <a-select-option v-for="item in parameterData.corpRiskLevelList" :value="item.dictValue" :key="item.dictValue">
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>
    <template slot="footer">
      <a-button key="back" @click="reset">重置</a-button>
      <a-button type="primary" key="submit" @click="submit">保存</a-button>
    </template>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  bizName: "",
  bizType: undefined,
  bizRiskLevel: undefined,
  bizRiskMatch: "1",
  investTerm: undefined,
  investType: undefined,
  incomeType: undefined,
  corpRiskLevel: undefined,
};

// 注册当前请求对应的上下文请求组
export default {
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        bizType: [
          { required: true, message: "请选择业务", trigger: "blur" },
        ],
        bizRiskLevel: [
          { required: true, message: "请选择风险等级", trigger: "blur" },
        ]
      },
      confirmLoading: false,
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => { },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    changeBizType(e) {
      let biz = this.parameterData.businessList.find(item => item.bizType === e)
      if (biz) {
        this.form.bizName = biz.bizName
      }
    },
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    query() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.api
        .queryBcBusinessEgliDetail({
          bcBusinessEgliId: this.parameterData.id,
        })
        .then((res) => {
          if (res.code != 0) return;
          this.form = res.data;
        });
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        if (this.parameterData.id) {
          param.id = this.parameterData.id
        }
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `业务适当性${this.parameterData.id ? "修改" : "新增"}失败：${msg}`
            );
          this.$message.success(
            `业务适当性${this.parameterData.id ? "修改" : "新增"}成功！`
          );
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        this.parameterData.id
          ? this.api.editBcBusinessEgli(param).then(callback)
          : this.api.addBcBusinessEgli(param).then(callback);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .risk-class .ant-form-item-children {
  display: flex;
  align-items: center;

  .match-class {
    display: flex;
    margin-left: 20px;
  }
}
</style>
