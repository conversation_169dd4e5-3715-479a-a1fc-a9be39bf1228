// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

    /**
    * 新增业务适当性
    * @param {Object} param
    * - id {String} 唯一标识(UUID)
    * - bizType {String} 业务类型编号
    * - bizName {String} 业务类型名
    * - bizRiskLevel {String} 业务风险等级
    * - investTerm {String} 服务期限（1:无固定期限、2:0到1年、3:1到3年、4:3到5年、5:5年以上、6:无特别要求）
    * - investType {String} 投资类别（1:固定收益类、2:权益类、3:期货融资融券类、4:复杂高风险类、5:其他）
    * - incomeType {String} 收益类型（1:保本固定收益、2:保本浮动收益、3:非保本浮动收益、4:非保本固定收益、9:其他）
    * - remark {String} 备注信息
    */
    @Parameters(['_data'])
    addBcBusinessEgli() {
      return this.services.initPost({ reqUrl:"bcBusinessEgli/add", param: this.param});
    }

    /**
    * 修改业务适当性
    * @param {Object} param
    * - id {String} 唯一标识(UUID)
    * - bizType {String} 业务类型编号
    * - bizName {String} 业务类型名
    * - bizRiskLevel {String} 业务风险等级
    * - investTerm {String} 服务期限（1:无固定期限、2:0到1年、3:1到3年、4:3到5年、5:5年以上、6:无特别要求）
    * - investType {String} 投资类别（1:固定收益类、2:权益类、3:期货融资融券类、4:复杂高风险类、5:其他）
    * - incomeType {String} 收益类型（1:保本固定收益、2:保本浮动收益、3:非保本浮动收益、4:非保本固定收益、9:其他）
    * - remark {String} 备注信息
    * @returns {Promise}
    */
    @Parameters(['_data'])
    editBcBusinessEgli() {
     return this.services.initPost({ reqUrl:"bcBusinessEgli/edit", param: this.param});
    }

    /**
    * 删除单个业务适当性
    * @param {Object} param
    * - bcBusinessEgliId {String} 参数主键ID
    */
    @Parameters(["bcBusinessEgliIds"])
    deleteBcBusinessEgli() {
      return this.services.initPost({ reqUrl:"bcBusinessEgli/delete", param: this.param});
    }

    /**
    * 删除多个业务适当性
    * @param {Object} param
    * - bcBusinessEgliIds {String} 参数主键ID列表，多个以“，”隔开
    *
    */
    @Parameters(["bcBusinessEgliIds"])
    deletesBcBusinessEgli() {
      return this.services.initPost({ reqUrl:"bcBusinessEgli/deletes", param: this.param});
    }

    /**
    * 查询业务适当性翻页列表
    * @param {Object} param
    * - beginTime {String} 开始时间
    * - endTime {String} 结束时间
    * - pageSize {integer} 每页数量
    * - pageNumber {integer} 当前页码
    * - orderBy {String} 排序字段
    * - id {String} 唯一标识(UUID)
    */
    @Parameters(["beginTime", "endTime", "pageSize", "pageNum", "orderBy", "id"])
    queryBcBusinessEgliPage() {
      return this.services.initGet({ reqUrl: "bcBusinessEgli/page", param: this.param });
    }

    /**
    * 查询指定业务适当性详情
    * @param {Object} param
    * - bcBusinessEgliId {String} 参数主键ID
    */
    @Parameters(["bcBusinessEgliId"])
    queryBcBusinessEgliDetail() {
      return this.services.initGet({ reqUrl: "bcBusinessEgli/query", param: this.param});
    }

    /**
    * 查询新网厅首页列表
    * @param {Object} param
    * - status {String} 是否有效
    */
   @Parameters(["status"])
   queryBcClientPortalList() {
     return this.services.initGet({ reqUrl: "bcClientPortal/list", param: this.param});
   }

   /**
   * 查询业务定义列表
   * @param {Object} param
   */
    @Parameters(["_data"])
    getBcBusinessList() {
      return this.services.initGet({
        reqUrl: "bcBusiness/list",
        param: this.param
      });
    }

}

export default new api();