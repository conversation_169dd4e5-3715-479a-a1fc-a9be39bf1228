<template>
  <a-card title="业务适当性列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset(0)">
          <a-form-model-item label="业务名称">
            <a-select v-model="tableForm.bizType" placeholder="请选择业务名称" show-search option-filter-prop="children"
              allowClear>
              <a-select-option v-for="item in businessList" :key="item.id" :value="item.bizType">
                {{ item.bizName }}({{ item.bizType }})
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="业务风险等级">
            <a-select v-model="tableForm.bizRiskLevel" placeholder="请选择业务风险等级" show-search option-filter-prop="children"
              allowClear>
              <a-select-option v-for="item in bizRiskLevelList" :key="item.dictValue" :value="item.dictValue">
                {{ item.dictLabel }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table ref="table" :intercept-response="intercept_response" :tableData.sync="columns"
        :tableFromFilter="tableFormFilter" getMethods="bc-manage-server/bcBusinessEgli/page" :isSelected="true"
        :isPaging="true" :tableFrom="tableForm" :selectedRowKeys.sync="selectedRowKeys" tableId="id">
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add">添加</a-button>
          <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove">
            删除
          </a-button>
          <!-- <a-button type="primary" icon="redo" @click="reset(1)">刷新</a-button> -->
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="modify(data)">修改</a-button>
        </template>
      </tk-table>
      <addOrEdit :isPopShow.sync="isPopShow" @success="query" :parameterData="selectData"></addOrEdit>
    </div>
  </a-card>
</template>

<script>
import api from "./api";
import addOrEdit from "./module/addOrEdit";

// 默认表单属性
const defaultForm = {
  bizType: undefined,
  bizRiskLevel: undefined,
};

export default {
  name: "business_egli",
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        // 循环
        // {
        //   field: "id",
        //   label: "序号",
        //   isSorter: true,
        // },
        {
          field: "bizName",
          label: "业务名称",
          isSorter: false,
        },
        {
          field: "bizRiskLevel",
          label: "业务风险等级",
          isSorter: false,
          filter: (item) => this.getDictText("bizRiskLevelList", item),
        },
        {
          field: "investType",
          label: "投资品种",
          isSorter: false,
          filter: (item) => this.getDictText("investTypeList", item),
        },
        {
          field: "investTerm",
          label: "投资期限",
          isSorter: false,
          filter: (item) => this.getDictText("investTermList", item),
        },
        {
          field: "incomeType",
          label: "预期收益",
          isSorter: false,
          filter: (item) => this.getDictText("incomeTypelList", item),
        },
        {
          field: "corpRiskLevel",
          label: "准入风险等级",
          isSorter: false,
          filter: (item) => this.getDictText("corpRiskLevelList", item),
        },
        {
          field: "createDate",
          label: "创建时间",
          isSorter: true,
        },
        {
          field: "updateDate",
          label: "修改时间",
          isSorter: true,
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      tableForm: {
        bizType: undefined,
        bizRiskLevel: undefined,
      },
      selectedRowKeys: [], // Check here to configure the default column
      isPopShow: false,
      selectData: {},
      businessList: [],
      bizRiskLevelList: this.$dict.dictTypeList("bc.common.prodriskLevel"),
      investTypeList: this.$dict.dictTypeList("bc.common.prInvestKind"),
      investTermList: this.$dict.dictTypeList("bc.common.investTerm"),
      incomeTypelList: this.$dict.dictTypeList("bc.common.incomeType"),
      corpRiskLevelList: this.$dict.dictTypeList("bc.common.corpRiskLevel"),
    };
  },
  provide: { api: api },
  created() {
    this.queryBusinessList();
  },
  components: { addOrEdit },
  methods: {
    queryBusinessList() {
      api.getBcBusinessList().then((res) => {
        this.businessList = res.data.filter((item) => item.bizType);
      });
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset(type) {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
      if (type) {
        this.$nextTick(() => {
          this.$refs.table.getTableData();
        });
      }
    },
    getDictText(key, value) {
      let results = this[key].find((item) => {
        return item.dictValue == value;
      });
      return results ? results.dictLabel : value;
    },
    // 点击新增按钮
    add() {
      this.isPopShow = true;
      this.selectData = {
        businessList: this.businessList,
        bizRiskLevelList: this.bizRiskLevelList,
        investTypeList: this.investTypeList,
        investTermList: this.investTermList,
        incomeTypelList: this.incomeTypelList,
        corpRiskLevelList: this.corpRiskLevelList,
      };
    },
    // 点击修改按钮
    modify(data) {
      this.isPopShow = true;
      this.selectData = {
        ...data,
        businessList: this.businessList,
        bizRiskLevelList: this.bizRiskLevelList,
        investTypeList: this.investTypeList,
        investTermList: this.investTermList,
        incomeTypelList: this.incomeTypelList,
        corpRiskLevelList: this.corpRiskLevelList,
      };
    },
    // 搜索框参数
    tableFormFilter(param) {
      return param;
    },
    // 删除
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: "业务适当性",
          content: () => <p>确定删除当前业务适当性数据?</p>,
          okText: "确定",
          cancelText: "取消",
          onOk: () => {
            api
              .deletesBcBusinessEgli({
                bcBusinessEgliIds: this.selectedRowKeys.join(","),
              })
              .then(({ code, msg }) => {
                if (code != 0)
                  return this.$message.error(`删除业务适当性失败：${msg}`);
                this.$message.success("删除业务适当性成功！");
                this.$refs.table.getTableData();
                this.selectedRowKeys = [];
              });
          },
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
