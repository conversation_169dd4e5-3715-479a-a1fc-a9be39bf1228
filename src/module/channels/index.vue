<!--
 * Generate by tech-manage-server-v1.0.0#work-code-generator-v1.0.2
 * @Author: admin
 * @Date: 2022-03-02 14:03:43
 * @LastEditTime: 2022-03-02 14:03:43
 * @LastEditors: admin
 * @Description: 渠道管理首页
 * @FilePath: \sysRole\src\module\sysRole\index.vue
-->
<template>
  <a-card title="渠道管理" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="渠道类型">
            <a-select v-model="tableForm.channelType" placeholder="请选择渠道类型">
              <a-select-option :value="item.value" v-for="(item,index) in channelTypeList" :key="index">
                {{item.label}}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="渠道名称">
            <a-input v-model="tableForm.channelName" placeholder="请输入渠道名称"></a-input>
          </a-form-model-item>
          <a-form-model-item label="渠道标识">
            <a-input v-model="tableForm.channelNo" placeholder="请输入渠道标识"></a-input>
          </a-form-model-item>
          <a-form-model-item label="开户方式">
            <a-select v-model="tableForm.openAccType" placeholder="请选择开户方式">
              <a-select-option :value="item.value" v-for="(item,index) in accountTypeList" :key="index">
                {{item.label}}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="营业部">
            <a-input allowClear ref="yyds" @click="yyb" v-model="branchName" placeholder="请输入营业部"></a-input>
          </a-form-model-item>
          <a-form-model-item label="渠道状态">
            <a-select v-model="tableForm.state" placeholder="请选择渠道状态">
              <a-select-option :value="item.value" v-for="(item,index) in stateList" :key="index">
                {{item.label}}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table ref="table"
        :tableData.sync="columns"
        getMethods="khyx-manage-server/channelInfo/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        :selectedRows.sync="selectedRows"
        :loading="loadings"
        tableId="channelNo">
        <template slot="channelType" slot-scope="text">
          <span v-if="text.channelType=='1'">期货公司营业部</span>
          <span v-if="text.channelType=='2'">证券引流</span>
          <span v-if="text.channelType=='3'">合作银行</span>
          <span v-if="text.channelType=='4'">三方渠道</span>
          <span v-if="text.channelType=='5'">其他</span>
        </template>
        <template slot="openAccType" slot-scope="text">
          <span v-if="text.openAccType=='1'">H5开户</span>
          <span v-if="text.openAccType=='2'">app开户</span>
          <span v-if="text.openAccType=='3'">SDK开户</span>
        </template>
        <template slot="state" slot-scope="text">
          <span v-if="text.state=='1'">启用</span>
          <span v-if="text.state=='2'">停用</span>
        </template>
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add"> 新增 </a-button>
          <a-button icon="sync" type="primary" @click="reset"> 刷新 </a-button>
          <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove"> 删除 </a-button>
          <a-button icon="primary" type="primary" :disabled="selectedRowKeys.length <= 0" @click="invoke"> 启用 </a-button>
          <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="disable"> 停用 </a-button>
          <a-button icon="vertical-align-bottom" type="primary" @click="derive"> 导出 </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <!-- <a type="link" @click.stop="look(data)"> 二维码 </a> -->
          <a type="link" style="margin-left:10px" @click.stop="modify(data)"> 修改 </a>
        </template>
      </tk-table>
      <add :isPopShow.sync="isAddPopShow" v-if="isAddPopShow" @success="query" :parameterData="selectData" />
      <edit :isPopShow.sync="isEditPopShow" @success="query" ref="dszx" :parameterData="selectData" />
      <yyb :isPopShow.sync="isYybPopShow" @success="querys" />
      <enableStop :isPopShow.sync="isqtyPopShow" v-if="isqtyPopShow" ref="qyty" @success="query" :parameterData="selectDatasc" :selectDatascType="selectDatascType" />
      <LookComponent :isPopShow.sync="isLookPopShow" :parameterData="selectData" />
    </div>
  </a-card>
</template>

<script>
// 引入添加和编辑弹窗
import yyb from './module/yyb';
import add from './module/add';
import edit from './module/edit';
import enableStop from './module/enableStop';
// 引入查看弹窗
import LookComponent from './module/detail';
import api from './api';

// 默认表单属性
const defaultForm = {
  channelType: "",
  channelName: "",
  channelNo: "", 
  branchNo: "", 
  openAccType: "", // 开户方式
  state: "", 
};

export default {
  data() {
    return {
      channelTypeList: [],
      accountTypeList: [],
      stateList: [],
      selectedRows: [],
      branchName: "",
      isYybPopShow: false,
      isqtyPopShow: false,
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        // 循环
        {
          field: "channelNo",
          label: "渠道标识",
          isSorter: false,
        },
        {
          field: "channelName",
          label: "渠道名称",
          isSorter: false,
          isEllipsis: true,
        },
        {
          field: "channelType",
          dataIndex: "channelType",
          scopedSlots: {customRender:"channelType"},
          label: "渠道类型",
          isSorter: false,
        },
        {
          field: "branchName",
          label: "指定营业部",
          isSorter: false,
          isEllipsis: true,
        },
        {
          field: "openAccType",
          dataIndex: "openAccType",
          scopedSlots: {customRender:"openAccType"},
          label: "开户方式",
          isSorter: false,
        },
        {
          field: "createdBy",
          label: "创建人",
          isSorter: false,
        },
        {
          field: "createdDate",
          label: "创建时间",
          isSorter: false,
          filter: item => !item ? '--' : this.DateFormat(item).format("yyyy-MM-dd"),
        },
        {
          field: "updatedBy",
          label: "修改人",
          isSorter: false,
        },
        {
          field: "updatedDate",
          label: "修改时间",
          isSorter: false,
          filter: item => !item ? '--' : this.DateFormat(item).format("yyyy-MM-dd"),
        },
        {
          field: "state",
          dataIndex: "state",
          scopedSlots: {customRender:"state"},
          label: "状态",
          isSorter: false,
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          width: 120,
          fixed: 'right'
        }
      ],
      tableForm: {
        channelType: "",
        channelName: "",
        channelNo: "", 
        branchNo: "", 
        openAccType: "", // 开户方式
        state: "", 
      },
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isAddPopShow: false,   //营业部弹窗
      isEditPopShow: false,   //修改弹窗是否显示
      //查看弹窗
      isLookPopShow: false,
      selectData: {},
      selectDatasc: {},
      selectDatascType: 1,
      loadings: true
    }
  },
  provide: { "api": api, },
  components: {add, edit, yyb, enableStop, LookComponent},
  created() {
    this.queryDictMap()
    console.log(this.channelTypeList)
  },
  methods:{
    queryDictMap() {
      return new Promise((resolve) => {
        [
          { dataIndex: "channelTypeList", key: "bc.futures.channelType" },
          { dataIndex: "accountTypeList", key: "bc.futures.account" },
          { dataIndex: "stateList", key: "bc.futures.state" },
        ].forEach((item) => {
          this.$dict.dictContent(item.key).then((data) => {
            this[item.dataIndex] = (data || []).map((data) => {
              return { label: data.dictLabel, value: data.dictValue };
            });
            resolve();
          });
        });
      });
    },
    // 点击营业部弹窗
    yyb(){
      this.$refs.yyds.blur()
      this.isYybPopShow = true;
      this.selectData={}
    },
    querys(data) {
      this.branchName = data.branchName
      this.tableForm.branchNo = data.branchNo
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm.createTimeBeginTime = "";
      this.tableForm.createTimeEndTime = "";
      this.tableForm.updateTimeBeginTime = "";
      this.tableForm.updateTimeEndTime = "";
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    // 点击新增按钮
    add(){
      this.isAddPopShow = true;
      this.selectData={}
    },
    // 查看
    look(data) {
      this.isLookPopShow = true;
      this.selectData = data;
    },
    // 点击修改按钮
    modify(data) {
      this.isEditPopShow = true;
      this.selectData = data;
      this.$refs.dszx.initData()
    },
    //导出
    derive() {

    },
    // 删除
    remove() {
      if (this.selectedRowKeys.length > 0) {
        // let staffIds = []
        // for(let i=0;i<this.selectedRows.length;i++){
        //   staffIds.push(this.selectedRows[i].channelNo)
        // }
        this.$confirm({
          title: '渠道信息管理',
          content: () => <p>确定删除当前经纪人数据?</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () =>  {
            api.deleteSysRole({channelNos: this.selectedRowKeys}).then(({code, msg}) => {
              if (code != 0) return this.$message.error(`删除渠道失败：${msg}`);
              this.$message.success('删除渠道成功！');
              this.$refs.table.getTableData();
              this.selectedRowKeys = [];
            })
          },
        });
      }
    },
    //启用
    invoke() {
      console.log(this.selectedRowKeys)
      this.isqtyPopShow = true
      this.selectDatasc = this.selectedRows
      this.selectDatascType = 1
      this.$refs.qyty.initData()
    },
    // 停用
    disable() {
      this.isqtyPopShow = true
      this.selectDatasc = this.selectedRows
      console.log(this.selectDatasc)
      this.selectDatascType = 2
      this.$refs.qyty.initData()
    },
  }
}
</script>

<style lang="scss" scoped>
</style>