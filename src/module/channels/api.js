// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

    /**
    * 新增角色信息表
    * @param {Object} param
    * - roleId {Long} 客户姓名
    * - roleName {String} 手机号码
    * - roleKey {String} 营业部
    * - roleSort {Integer} 起始日期
    * - dataScope {String} 流程状态
    * - status {String} 操作来源
    * - delFlag {String} 流程类型
    * - createBy {String} 结束日期
    * - createTime {Date} 前端步骤
    * - updateBy {String} 认证方式
    * - updateTime {Date} 用户状态
    * - remark {String} 审核人姓名
    */
    @Parameters(["channelNo", "channelName", "channelType", "openAccType", "branchNo", "state", "introducerNo", "intermediator", "appLink", "bannerKey", "remark", "stopContext","bankNo", "isShowBranch", "isShowIntroducer", "isShowIntermediator", "logo"])
    addSysRole() {
      return this.khServices.initPost({ reqUrl:"channelInfo/add", param: this.param});
    }

    /**
    * 修改角色信息表
    * @param {Object} param
    * - roleId {Long} 客户姓名
    * - roleName {String} 手机号码
    * - roleKey {String} 营业部
    * - roleSort {Integer} 起始日期
    * - dataScope {String} 流程状态
    * - status {String} 操作来源
    * - delFlag {String} 流程类型
    * - createBy {String} 结束日期
    * - createTime {Date} 前端步骤
    * - updateBy {String} 认证方式
    * - updateTime {Date} 用户状态
    * - remark {String} 审核人姓名
    * @returns {Promise}
    */
    @Parameters(["channelNo", "channelName", "channelType", "openAccType", "branchNo", "state", "introducerNo", "intermediator", "appLink", "bannerKey", "remark", "stopContext","bankNo", "isShowBranch", "isShowIntroducer", "isShowIntermediator", "logo"])
    editSysRole() {
     return this.khServices.initPost({ reqUrl:"channelInfo/edit", param: this.param});
    }

    /**
    * 删除单个角色信息表
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
    @Parameters(["channelNos"])
    deleteSysRole() {
      return this.khServices.initPost({ reqUrl:"channelInfo/delete", param: this.param});
    }

    /**
    * 启用单个角色信息表
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
     @Parameters(["channelNos"])
     invokeSysRole() {
       return this.khServices.initPost({ reqUrl:"channelInfo/startChannel", param: this.param});
     }

     /**
    * 生成二维码
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
      @Parameters(["content","logo","size"])
      qrCodeSysRole() {
        return this.khServices.initGet({ reqUrl: "base/qrCode", param: this.param});
      }

     /**
    * 停用单个角色信息表
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
    @Parameters(["channelNos"])
    disableSysRole() {
      return this.khServices.initPost({ reqUrl:"channelInfo/stopChannel", param: this.param});
    }

    /**
    * 删除多个角色信息表
    * @param {Object} param
    * - sysRoleIds {String} 参数主键ID列表，多个以“，”隔开
    *
    */
    @Parameters(["sysRoleIds"])
    deletesSysRole() {
      return this.khServices.initPost({ reqUrl:"sysRole/deletes", param: this.param});
    }

    /**
    * 查询角色信息表翻页列表
    * @param {Object} param
    * - beginTime {String} 开始时间
    * - endTime {String} 结束时间
    * - pageSize {integer} 每页数量
    * - pageNumber {integer} 当前页码
    * - orderBy {String} 排序字段
    * - roleId {Long} 客户姓名
    * - roleName {String} 手机号码
    * - roleKey {String} 营业部
    * - roleSort {Integer} 起始日期
    * - dataScope {String} 流程状态
    * - status {String} 操作来源
    * - delFlag {String} 流程类型
    * - createBy {String} 结束日期
    * - createTime {Date} 前端步骤
    * - updateBy {String} 认证方式
    * - updateTime {Date} 用户状态
    * - remark {String} 审核人姓名
    */
    @Parameters(["beginTime", "endTime", "pageSize", "pageNum", "orderBy", "roleId", "roleName", "roleKey", "roleSort", "dataScope", "status", "delFlag", "createBy", "createTime", "updateBy", "updateTime", "remark"])
    querySysRolePage() {
      return this.khServices.initGet({ reqUrl: "sysRole/page", param: this.param });
    }

    /**
    * 查询指定角色信息表详情
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
    @Parameters(["sysRoleId"])
    querySysRoleDetail() {
      return this.khServices.initGet({ reqUrl: "sysRole/query", param: this.param});
    }

    /**
    * 查询营业部列表
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
     @Parameters(["branchNo","branchName","pageNum","pageSize","sortField","sortOrder"])
    yybSysRole() {
      return this.khServices.initGet({ reqUrl: "branchInfo/page", param: this.param});
    }

    /**
    * 查询推荐人列表
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
     @Parameters(["staffName","mobileNo","staffType","pageNum","pageSize","sortField","sortOrder"])
    tjrSysRole() {
      return this.khServices.initGet({ reqUrl: "staffinfo/page", param: this.param});
    }

    /**
    * 查询居间人列表
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
     @Parameters(["staffName","mobileNo","staffType","pageNum","pageSize","sortField","sortOrder"])
    jjrSysRole() {
      return this.khServices.initGet({ reqUrl: "staffinfo/page", param: this.param});
    }

    /**
    * 查询app列表
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
    @Parameters(["linkId","linkName","pageNum","pageSize","sortField","sortOrder"])
    applinkSysRole() {
      return this.khServices.initGet({ reqUrl: "appLink/page", param: this.param});
    }

    /**
    * 查询银行列表
    * @param {Object} param
    * - sysRoleId {String} 参数主键ID
    */
     @Parameters(["bankNo","bankName","pageNum","pageSize","sortField","sortOrder"])
     bankSysRole() {
       return this.khServices.initGet({ reqUrl: "bankInfo/page", param: this.param});
     }

}

export default new api();