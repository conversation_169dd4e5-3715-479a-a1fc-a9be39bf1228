<!--
 * @Author: liu quan
 * @Date: 2021-07-13 21:12:28
 * @LastEditors: liu quan
 * @LastEditTime: 2021-07-14 17:23:50
 * @FilePath: \bus-child-view\src\module\config\module\detail.vue
-->
<template>
  <a-modal
    :title="`查看角色信息表`"
    :width="500"
    v-model="showPop"
    :maskClosable="false"
  >
    <a-descriptions bordered :column="1">
      <a-descriptions-item label="客户姓名" prop="roleId">
        {{ form.roleId }}
      </a-descriptions-item>
      <a-descriptions-item label="手机号码" prop="roleName">
        {{ form.roleName }}
      </a-descriptions-item>
      <a-descriptions-item label="营业部" prop="roleKey">
        {{ form.roleKey }}
      </a-descriptions-item>
      <a-descriptions-item label="起始日期" prop="roleSort">
        {{ form.roleSort }}
      </a-descriptions-item>
      <a-descriptions-item label="任务状态" prop="dataScope">
        {{ form.dataScope }}
      </a-descriptions-item>
      <a-descriptions-item label="操作来源" prop="status">
        {{ form.status }}
      </a-descriptions-item>
      <a-descriptions-item label="流程类型" prop="delFlag">
        {{ form.delFlag }}
      </a-descriptions-item>
      <a-descriptions-item label="结束日期" prop="createBy">
        {{ form.createBy }}
      </a-descriptions-item>
      <a-descriptions-item label="前端步骤" prop="createTime">
        {{ form.createTime }}
      </a-descriptions-item>
      <a-descriptions-item label="认证方式" prop="updateBy">
        {{ form.updateBy }}
      </a-descriptions-item>
      <a-descriptions-item label="用户状态" prop="updateTime">
        {{ form.updateTime }}
      </a-descriptions-item>
      <a-descriptions-item label="审核人姓名" prop="remark">
        {{ form.remark }}
      </a-descriptions-item>
    </a-descriptions>
    <template slot="footer">
      <a-button type="primary" @click="ok">确定</a-button>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: "SysRole_detail",
  inject: ["api"],
  data() {
    return {
      form: {}, //表单数据,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.roleId) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    ok() {
      this.showPop = false;
      this.reset()
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    query() {
      this.reset();
      return this.api
        .querySysRoleDetail({ sysRoleId: this.parameterData.roleId })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          this.form = data;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
  .ant-descriptions-bordered.ant-descriptions-item-content,
  .ant-descriptions-bordered .ant-descriptions-item-label{
    min-width: 110px;
  }
  .ant-descriptions-item-content{
    word-break: break-all;
  }
</style>