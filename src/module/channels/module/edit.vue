<template>
  <a-modal
    :title="`${typeTitle}经纪人信息`"
    :width="1100"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="ok"
    @cancel="reset"
    :maskClosable="false"
    :footer="null"
  >
  <a-row>
  <a-col :span="13">
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
   <h3>营销属性</h3>
    <a-form-model-item label="渠道类型" prop="channelType">
        <a-select v-model="form.channelType" placeholder="请选择经纪人类型">
          <a-select-option :value="item.value" v-for="(item,index) in channelTypeList" :key="index">
            {{item.label}}
          </a-select-option>
        </a-select>
     </a-form-model-item>
    <a-form-model-item label="渠道名称" prop="channelName">
       <a-input v-model="form.channelName" :maxLength=50 placeholder="请输入渠道名称" ></a-input>
     </a-form-model-item>
     <a-form-model-item label="渠道编号" prop="channelNo">
       <a-input v-model="form.channelNo" :maxLength=20 placeholder="请输入渠道编号" ></a-input>
     </a-form-model-item>
     <!-- <a-form-model-item label="营业部类型" prop="staffType">
        <a-select v-model="form.staffType" placeholder="请选择营业部类型">
          <a-select-option :value="item.value" v-for="(item,index) in agentlList" :key="index">
            {{item.label}}
          </a-select-option>
        </a-select>
        <a-checkbox @change="onChange">
          不显示营业部选项
        </a-checkbox>
     </a-form-model-item> -->
     <a-form-model-item label="营业部">
      <a-select
        mode="multiple"
        style="width: 100%"
        placeholder="请输入"
        @search="searchYyb"
        @focus="searchYyb"
        :filter-option="filterOption"
        v-model="form.branchNo"
      >
        <a-select-option :value="item.branchNo" v-for="(item,index) in yybList" :key="index">
          {{ item.branchName }}
        </a-select-option>
      </a-select>
      <a-checkbox @change="onChange1">
        不显示营业部选项
      </a-checkbox>
    </a-form-model-item>
     <a-form-model-item label="推荐人">
       <a-select
        mode="multiple"
        style="width: 100%"
        placeholder="请输入"
        @search="searchTjr"
        @focus="searchTjr"
        :filter-option="filterOption"
        v-model="form.introducerNo"
      >
        <a-select-option :value="item.staffId" v-for="(item,index) in tjrList" :key="index">
          {{ item.staffName }}
        </a-select-option>
      </a-select>
       <a-checkbox @change="onChange2">
        不显示经纪人选项
      </a-checkbox>
     </a-form-model-item>
      <a-form-model-item label="居间人">
       <a-select
        mode="multiple"
        style="width: 100%"
        placeholder="请输入"
        @search="searchJjr"
        @focus="searchJjr"
        :filter-option="filterOption"
        v-model="form.intermediator"
      >
        <a-select-option :value="item.staffId" v-for="(item,index) in jjrList" :key="index">
          {{ item.staffName }}
        </a-select-option>
      </a-select>
       <a-checkbox @change="onChange3">
        不显示居间人选项
      </a-checkbox>
     </a-form-model-item>
     <a-form-model-item label="合作银行">
        <a-select
          mode="multiple"
          style="width: 100%"
          placeholder="请输入"
          @search="searchBank"
          @focus="searchBank"
          :filter-option="filterOption"
          v-model="form.bankNo"
        >
          <a-select-option :value="item.bankNo" v-for="(item,index) in bankList" :key="index">
            {{ item.bankName }}
          </a-select-option>
        </a-select>
     </a-form-model-item>
     <h3>渠道页配置</h3>
    
     <a-form-model-item label="开户方式" prop="openAccType">
      <a-radio-group v-decorator="['radio-group']" v-model="form.openAccType" @change="onChangeOpenAccType">
        <a-radio :value="item.value" v-for="(item,index) in accountTypeList" :key="index">
          {{item.label}}
        </a-radio>
      </a-radio-group>
     </a-form-model-item>
     <a-form-model-item label="APP下载地址" prop="appLink" v-if="togAppLink">
        <a-select v-model="form.appLink" placeholder="请选择营业部类型">
          <a-select-option :value="item.value" v-for="(item,index) in appLinkList" :key="index">
            {{item.label}}
          </a-select-option>
        </a-select>
     </a-form-model-item>
     <a-form-model-item label="二维码中的logo">
      <a-upload
        name="file"
        :multiple="true"
        action="/khyx-manage-server/base/uploadFile"
        :headers="headers"
        @change="handleChange1"
        accept=".jpg, .jpeg, .png"
      >
        <a-button> <a-icon type="upload" /> 上传 </a-button>
      </a-upload>
     </a-form-model-item>
     <a-form-model-item label="活动页图片">
      <a-upload
        name="file"
        :multiple="true"
        action="/khyx-manage-server/base/uploadFile"
        :headers="headers"
        @change="handleChange2"
        accept=".jpg, .jpeg, .png"
      >
        <a-button> <a-icon type="upload" /> 上传 </a-button>
      </a-upload>
     </a-form-model-item>
     <a-form-model-item label="活动说明">
       <a-textarea v-model="form.remark" placeholder="请输入" auto-size />
     </a-form-model-item>
     <a-form-model-item label="渠道状态" prop="state">
      <a-radio-group v-decorator="['radio-group']" v-model="form.state" @change="onChangeStopContext">
        <a-radio :value="item.value" v-for="(item,index) in stateList" :key="index">
          {{item.label}}
        </a-radio>
      </a-radio-group>
     </a-form-model-item>
      <a-form-model-item label="停用说明" prop="stopContext" v-if="togStopContext">
       <a-textarea v-model="form.stopContext" placeholder="请输入" auto-size />
     </a-form-model-item>
     <a-button @click="submit" type="primary" style="margin-left:30%">
      修改并生成新的二维码
    </a-button>
    <a-button @click="ok" style="margin-left:5%">
      关闭
    </a-button>
    </a-form-model>
    </a-col>
      <a-col :span="11">
        <div class="p_con_item">
          <h3 class="title">渠道地址</h3>
          <div class="p_con_form">
            <div class="p_inputbox">
              <div class="ct">
                <span class="p_link_info">{{channelLink?channelLink:'待生成'}}</span>
                <textarea id="input" style="opacity: 0;position: absolute;z-index:-10"></textarea>
                <a href="javascript:void(0);" @click="copyLink" v-if="channelLink">复制地址</a>
              </div>
            </div>
          </div>
        </div>
        <div class="p_con_item">
          <h3 class="title">渠道二维码</h3>
          <div class="p_con_form">
            <div class="p_inputbox">
              <div class="ct">
                <span class="p_code_box"><div class="pic"><img style="width:200px" download="chart-download" :src="imgSrc" /></div></span>
                <a href="javascript:void(0)" @click="downImg" v-if="imgSrc">下载二维码</a>
              </div>
            </div>
          </div>
        </div>
        <!-- <h3 style="margin-left:10%">渠道地址</h3>
        <span style="height:30px;lin-height:30px;border:1px solid font-size:12px;margin-left:10%;min-width:200px">{{channelLink?channelLink:'暂无'}}</span><a href="">复制链接</a>
        <h3 style="margin-top:20px;margin-left:10%">渠道二维码<a href="">下载二维码</a></h3>
        <img alt="暂无图片" style="width:50%;display: block;margin-left:10%" :src="imgSrc" /> -->
      </a-col>
    </a-row>
    <!-- <yyb :isPopShow.sync="isYybPopShow" @success="querys" /> -->
  </a-modal>
</template>

<script>
// import yyb from './yyb';
// 默认表单属性
const defaultForm = {
  channelNo: "",  //渠道编号
  channelName: "", // 渠道名称
  channelType: "", // 渠道类型
  openAccType: "1", // 开户方式
  branchNo: "",  //营业部
  state: "1", // 渠道状态
  introducerNo: "", // 经纪人编号
  intermediator: "", // 居间人
  appLink: "",  //下载地址
  bannerKey: "", // 活动图片
  remark: "", // 活动说明
  stopContext: "", //停用说明
  bankNo: "", //银行编号
  logo: "", // logo
  isShowBranch: 1,  //是否显示营业部
  isShowIntroducer: 1,  //是否显示推荐人
  isShowIntermediator: 1,  //是否显示居间人
};

export default {
  name: "add",
  inject: ["api"],
  // components: {yyb},
  data() {
    return {
      agentlList: [],
      channelTypeList: [],
      accountTypeList: [],
      stateList: [],
      togAppLink: false,
      togStopContext: false,
      isYybPopShow: false,
      headers: {
        authorization: 'authorization-text',
      },
      form: Object.assign({}, defaultForm, {channelType: undefined,bankNo: undefined, branchNo: undefined}), //表单数据,
      rules: {
        channelNo: [
            { required: true, message: "渠道编号不能为空", trigger: "blur"}
        ],
        channelName: [
            { required: true, message: "渠道名称不能为空", trigger: "blur"}
        ],
        channelType: [
            { required: true, message: "渠道类型不能为空", trigger: "blur"}
        ],
        openAccType: [
            { required: true, message: "开户方式不能为空", trigger: "blur"}
        ],
        state: [
            { required: true, message: "渠道状态不能为空", trigger: "blur"}
        ],
        appLink: [
            { required: true, message: "下载地址不能为空", trigger: "blur"}
        ],
        stopContext: [
            { required: true, message: "停用说明不能为空", trigger: "blur"}
        ],
      },
      // 异步加载
      confirmLoading: false,
      selectData: {},
      branchName: "",
      tjrName: "",
      jjrName: "",
      hzyhName: "",
      yybList: [],
      tjrList: [],
      jjrList: [],
      appLinkList: [],
      bankList: [],
      imgSrc: require("../imges/ewm.png"),
      channelLink: "",
      ewmUrl: "",
      hdUrl: ""
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: ()=>{}
    },
  },
  computed: {
    showPop: {
      get() {
          return this.isPopShow;
      },
      set(val) {
          this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      }
    },
    typeTitle (){
      return "添加";
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.sysRoleId) {
        this.query();
      }else{
        this.reset();
      }
      this.$nextTick(()=> {
      });
    },
    parameterData(){
      this.initData()
    }
  },
  created() {
    this.searchYyb('')
    this.searchTjr('')
    this.searchJjr('')
    this.searchBank('')
    this.queryDictMap()
    this.appLinks()
  },
  methods: {
    initData(){
      let parameterData = this.parameterData
      this.form.channelType = parameterData.channelType
      this.form.channelName = parameterData.channelName
      this.form.channelNo = parameterData.channelNo
      console.log(this.yybList)
      if(parameterData.branchNo){
        this.form.branchNo = parameterData.branchNo.split(',')
      }else{
        this.form.branchNo = undefined
      }
      if(parameterData.introducerNo){
        this.form.introducerNo = parameterData.introducerNo.split(',')
      }else{
        this.form.introducerNo = undefined
      }
      if(parameterData.intermediator){
        this.form.intermediator = parameterData.intermediator.split(',')
      }else{
        this.form.intermediator = undefined
      }
      if(parameterData.bankNo){
        this.form.bankNo = parameterData.bankNo.split(',')
      }else{
        this.form.bankNo = undefined
      }
      // this.form.introducerNo = parameterData.introducerNo
      // this.form.intermediator = parameterData.intermediator
      // this.form.bankNo = parameterData.bankNo
      // this.branchName = parameterData.branchName.split(',')
      // this.tjrName = parameterData.staffName
      // this.jjrName = parameterData.intermediatorName
      // this.hzyhName = parameterData.bankName
      this.form.openAccType = parameterData.openAccType
      this.form.appLink = parameterData.appLink
      this.form.logo = parameterData.logo
      this.form.bannerKey = parameterData.bannerKey
      this.form.remark = parameterData.remark
      this.form.state = parameterData.state
      this.form.stopContext = parameterData.stopContext
      if(parameterData.openAccType == 2){
        // this.togAppLink = true
      }
      if(parameterData.state == 2){
        this.togStopContext = true
      }
      this.channelLink = parameterData.content
      this.qrCodeImg(parameterData.content)
    },
    //点击复制链接
    copyLink() {
      var input = document.getElementById("input");
      input.value = this.channelLink; // 修改文本框的内容
      input.select(); // 选中文本
      document.execCommand("copy"); // 执行浏览器复制命令
    },
    //点击图片下载
    downImg() {
      let a = document.createElement("a");
      a.href = this.imgSrc;
      a.setAttribute("download", "chart-download");
      a.click();
    },
    qrCodeImg(value) {
      let callback = ({ code, msg, data }) => {
        console.log(data)
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`生成二维码失败：${msg}`);
          // this.$message.success(`生成二维码成功！`);
          this.imgSrc = "data:image/png;base64," + data.img
      };
      this.api.qrCodeSysRole({content:value}).then(callback);
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    queryDictMap() {
      return new Promise((resolve) => {
        [
          { dataIndex: "channelTypeList", key: "bc.futures.channelType" },
          { dataIndex: "accountTypeList", key: "bc.futures.account" },
          { dataIndex: "stateList", key: "bc.futures.state" },
        ].forEach((item) => {
          this.$dict.dictContent(item.key).then((data) => {
            this[item.dataIndex] = (data || []).map((data) => {
              return { label: data.dictLabel, value: data.dictValue };
            });
            resolve();
          });
        });
      });
    },
    appLinks() {
      let parme = {
        linkId:"",
        linkName:"",
        pageNum:1,
        pageSize:10,
        sortField:"",
        sortOrder:""
      }
      let that = this
      let callback = ({ code, msg, data }) => {
          that.confirmLoading = false;
          console.log(msg)
          console.log(code)
          // if (code != 0) return this.$message.error(`app链接获取失败`);
          that.appLinkList = data.list
        };
      this.api.applinkSysRole(parme).then(callback);
    },
     ok(){
      // 关闭弹窗
      this.showPop = false;
      // 通知操作成功
      this.$emit("success");
      // 重置表单
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // query() {
    //   this.reset();
    //   return this.api.querySysRoleDetail({ sysRoleId: this.parameterData.sysRoleId}).then((res) => {
    //     if (res.code != 0) return;
    //     let data = res.data;
    //     this.form = data;
    //   });
    // },
    // querys(data) {
    //   this.branchName = data.branchName
    //   this.form.branchNo = data.branchNo
    // },
    // 提交角色信息表创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        if(param.branchNo&&(typeof param.branchNo=='object')&&param.branchNo.constructor==Array){
          param.branchNo = param.branchNo.join(",")
        }
        if(param.introducerNo&&(typeof param.introducerNo=='object')){
          param.introducerNo = param.introducerNo.join(",")
        }
        if(param.intermediator&&(typeof param.intermediator=='object')){
          param.intermediator = param.intermediator.join(",")
        }
        if(param.bankNo&&(typeof param.bankNo=='object')){
          param.bankNo = param.bankNo.join(",")
        }
        let callback = ({ code, msg, data }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`经纪人${this.typeTitle}失败：${msg}`);
          this.$message.success(`经纪人${this.typeTitle}成功！`);
          this.channelLink = data.content
          this.qrCodeImg(data.content)
        };
        this.api.editSysRole(param).then(callback);
      });
    },
    handleChange1(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        this.ewmUrl = info.file.response.data.uploadPath
        this.form.logo = info.file.response.data.uploadPath
        this.$message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} file upload failed.`);
      }
    },
    handleChange2(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        this.hdUrl = info.file.response.data.uploadPath
        this.form.bannerKey = info.file.response.data.uploadPath
        this.$message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} file upload failed.`);
      }
    },
    onChange1(e) {
      console.log(`checked = ${e.target.checked}`);
      if(e.target.checked){
        this.form.isShowBranch = 0
      }else{
        this.form.isShowBranch = 1
      }
    },
    onChange2(e) {
      console.log(`checked = ${e.target.checked}`);
      if(e.target.checked){
        this.form.isShowIntroducer = 0
      }else{
        this.form.isShowIntroducer = 1
      }
    },
    onChange3(e) {
      console.log(`checked = ${e.target.checked}`);
      if(e.target.checked){
        this.form.isShowIntermediator = 0
      }else{
        this.form.isShowIntermediator = 1
      }
    },
    onChangeOpenAccType(e) {
      console.log(e.target.value);
      // if(e.target.value == 2){
      //   this.togAppLink = true
      // }else{
      //   this.togAppLink = false
      // }
    },
    onChangeStopContext(e) {
      console.log(e.target.value);
      if(e.target.value == 2){
        this.togStopContext = true
      }else{
        this.togStopContext = false
      }
    },
    searchYyb(value) {
      console.log(`selected ${value}`);
      let parme = {
        branchNo:"",
        branchName:value,
        pageNum:1,
        pageSize:10,
        sortField:"",
        sortOrder:""
      }
      let that = this
      let callback = ({ code, msg, data }) => {
          that.confirmLoading = false;
          if (code != 0) return this.$message.error(`经纪人${this.typeTitle}失败：${msg}`);
          that.yybList = data.list
        };
      this.api.yybSysRole(parme).then(callback);
    },
    // handleChangeYyb(value,option) {
    //   let options = []
    //   option.forEach((item)=>{
    //     options.push(item.componentOptions.propsData.value)
    //   })
    //   console.log(options.join());
    //   this.form.branchNo = options.join()
    // },
    searchTjr(value) {
      console.log(`selected ${value}`);
      let parme = {
        staffName:value,
        mobileNo:"",
        staffType:1,
        pageNum:1,
        pageSize:10,
        sortField:"",
        sortOrder:""
      }
      let that = this
      let callback = ({ code, msg, data }) => {
          that.confirmLoading = false;
          if (code != 0) return this.$message.error(`经纪人${this.typeTitle}失败：${msg}`);
          that.tjrList = data.list
        };
      this.api.tjrSysRole(parme).then(callback);
    },
    // handleChangeTjr(value,option) {
    //   let options = []
    //   option.forEach((item)=>{
    //     options.push(item.componentOptions.propsData.value)
    //   })
    //   console.log(options.join());
    //   this.form.introducerNo = options.join()
    // },
    searchJjr(value) {
      console.log(`selected ${value}`);
      let parme = {
        staffName:value,
        mobileNo:"",
        staffType:2,
        pageNum:1,
        pageSize:10,
        sortField:"",
        sortOrder:""
      }
      let that = this
      let callback = ({ code, msg, data }) => {
          that.confirmLoading = false;
          if (code != 0) return this.$message.error(`经纪人${this.typeTitle}失败：${msg}`);
          that.jjrList = data.list
        };
      this.api.jjrSysRole(parme).then(callback);
    },
    // handleChangeJjr(value,option) {
    //   let options = []
    //   option.forEach((item)=>{
    //     options.push(item.componentOptions.propsData.value)
    //   })
    //   console.log(options.join());
    //   this.form.intermediator = options.join()
    // },
    searchBank(value) {
      console.log(`selected ${value}`);
      let parme = {
        bankNo:"",
        bankName:value,
        pageNum:1,
        pageSize:10,
        sortField:"",
        sortOrder:""
      }
      let that = this
      let callback = ({ code, msg, data }) => {
          that.confirmLoading = false;
          if (code != 0) return this.$message.error(`经纪人${this.typeTitle}失败：${msg}`);
          that.bankList = data.list
        };
      this.api.bankSysRole(parme).then(callback);
    },
    // handleChangeBank(value,option) {
    //   let options = []
    //   option.forEach((item)=>{
    //     options.push(item.componentOptions.propsData.value)
    //   })
    //   console.log(options.join());
    //   this.form.bankNo = options.join()
    // },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .ant-modal-body {
  max-height: 600px !important;
  padding: 17px 24px !important;
}
.p_con_item .title{
	// font-size: 14px;
	// line-height: 20px;
	color: #292C2E;
	font-weight: 700;
	// padding: 20px 0 10px;
}
.p_inputbox{
	padding: 8px 0;
}
.p_inputbox .tit{
	display: block;
	float: left;
	line-height: 32px;
	font-size: 14px;
	width: 150px;
	color: #666666;
}
.p_inputbox .tit .imp{
	color: #E02020 ;
}
.p_inputbox .ct{
	min-height: 32px;
	line-height: 32px;
	font-size: 14px;
	color: #1E1E1E;
}
.p_inputbox.text .ct{
	margin-left: 150px;
}
.p_inputbox .ct > p{
	line-height: 20px;
	padding: 6px 0;
	width: 332px;
}
.p_link_info{
  display: inline-block;
    vertical-align: top;
    max-width: 320px;
     min-width: 300px;
    margin-right: 16px;
    padding: 5px 10px;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    font-size: 14px;
    color: #999999;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>