<!--
 * @Author: liu quan
 * @Date: 2021-07-13 21:12:28
 * @LastEditors: liu quan
 * @LastEditTime: 2021-07-14 17:23:50
 * @FilePath: \bus-child-view\src\module\config\module\detail.vue
-->
<template>
  <a-modal
    :title="`渠道信息${typeTitle}确认`"
    :width="800"
    v-model="showPop"
    :maskClosable="false"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
  >
    <div v-for="(item,index) in List" :key="index">
        <a-descriptions>
            <a-descriptions-item label="渠道编号">
            {{item.channelNo}}
            </a-descriptions-item>
            <a-descriptions-item label="渠道名称">
            {{item.channelName}}
            </a-descriptions-item>
            <a-descriptions-item label="渠道类型">
            {{item.channelType}}
            </a-descriptions-item>
        </a-descriptions>
    </div>
  </a-modal>
</template>
<script>
export default {
  name: "SysRole_detail",
  inject: ["api"],
  data() {
    return {
      List: []
    };
  },
  props: ["isPopShow","parameterData","selectDatascType"],
//   props: {
//     isPopShow: {
//       type: Boolean,
//       default: false,
//     },
//     parameterData: {
//       type: Object,
//       default: ()=>[]
//     },
//     selectDatascType: {
//       type: Number,
//       default: 1
//     },
//   },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
    typeTitle (){
        if(this.selectDatascType == 1){
            return "启用";
        }else{
            return "停用";
        }
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.roleId) {
        this.query();
      } else {
        this.reset();
      }
    },
    parameterData(){
      this.initData()
    }
  },
  mounted() {
    console.log(this.parameterData)
      this.initData()
  },
  methods: {
    initData() {
        this.List = this.parameterData
    },
    reset() {
      this.showPop = false;
    },
    submit() {
        let channelNo = []
        for(let i=0;i<this.parameterData.length;i++){
          channelNo.push(this.parameterData[i].channelNo)
        }
        let that = this
        let callback = ({ code, msg }) => {
          that.confirmLoading = false;
          if (code != 0) return this.$message.error(`渠道信息${this.typeTitle}失败：${msg}`);
          this.$message.success(`渠道信息${this.typeTitle}成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
        };
        if(this.List.type == 1){
            this.api.invokeSysRole({channelNos: channelNo}).then(callback);
        }else{
            this.api.disableSysRole({channelNos: channelNo}).then(callback);
        }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .ant-modal-body {
  max-height: 100% !important;
  padding: 17px 24px !important;
}
::v-deep .ant-table-body,
::v-deep .ant-table-placeholder {
  display: none !important;
}
</style>