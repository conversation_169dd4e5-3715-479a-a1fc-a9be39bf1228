// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
	 * 查询协议分组列表
	 * @description:
	 * @param {*}
	 */
	@Parameters(['searchValue'])
	getBcAgreeGroupList() {
		return this.services.initGet({
			reqUrl: 'bcAgreeGroup/list',
			param: this.param,
		});
	}

	/**
	 * 删除协议分组
	 * @param {Object} param
	 * - bcAgreeGroupId {String} 参数主键ID
	 */
	@Parameters(['bcAgreeGroupId'])
	deleteBcAgreeGroup() {
		return this.services.initPost({
			reqUrl: 'bcAgreeGroup/delete',
			param: this.param,
		});
	}

	/**
	 * 删除多个协议分组
	 * @param {Object} param
	 * - bcAgreeGroupIds {String} 参数主键ID列表，多个以“，”隔开
	 *
	 */
	@Parameters(['bcAgreeGroupIds'])
	deletesBcAgreeGroup() {
		return this.services.initPost({
			reqUrl: 'bcAgreeGroup/deletes',
			param: this.param,
		});
	}

	/**
	 * 新增协议分组
	 * @description:
	 * @param {*}
	 * - groupCode {String} 分组编码
	 * - groupName {String} 分组名称
	 * - parentId {Integer} 父节点ID
	 * - route {String} 路由信息
	 * - createBy {String} 创建用户的帐号
	 * - updateBy {String} 修改用户的帐号
	 */
	@Parameters([
		'groupCode',
		'groupName',
		'parentId',
		'route',
		'createBy',
		'updateBy',
	])
	addBcAgreeGroup() {
		return this.services.initPost({
			reqUrl: 'bcAgreeGroup/add',
			param: this.param,
		});
	}

	/**
	 * 修改协议分组
	 * @description:
	 * @param {*}
	 * - groupId {Integer} 分组ID
	 * - groupCode {String} 分组编码
	 * - groupName {String} 分组名称
	 * - parentId {Integer} 父节点ID
	 * - route {String} 路由信息
	 * - updateBy {String} 修改用户的帐号
	 * @return {*}
	 */
	@Parameters([
		'groupId',
		'groupCode',
		'groupName',
		'parentId',
		'route',
		'updateBy',
	])
	setBcAgreeGroup() {
		return this.services.initPost({
			reqUrl: 'bcAgreeGroup/edit',
			param: this.param,
		});
	}

	/**
	 * 删除单个协议
	 * @description:
	 * @param {*}
	 * @return {*}
	 */
	@Parameters(['bcAgreeId'])
	deleteBcAgree() {
		return this.services.initPost({
			reqUrl: 'bcAgree/delete',
			param: this.param,
		});
	}

	/**
	 * 删除多个协议
	 * @param {Object} param
	 * - bcAgreeIds {String} 参数主键ID列表，多个以“，”隔开
	 *
	 */
	@Parameters(['bcAgreeIds'])
	deletesBcAgree() {
		return this.services.initPost({
			reqUrl: 'bcAgree/deletes',
			param: this.param,
		});
	}

	/**
	 * 新增协议
	 * @description:
	 * @param {Object} param
	 * - groupId {Integer} 协议分组ID
	 * - agreeCode {String} 协议档案编号
	 * - title {String} 协议标题
	 * - version {String} 版本号
	 * - createBy {String} 创建用户的帐号
	 * - publishBy {String} 发布人
	 * - updateBy {String} 修改用户的帐号
	 * @return {*}
	 */
	@Parameters([
		'groupId',
		'agreeCode',
		'title',
		'version',
		'createBy',
		'publishBy',
		'updateBy',
	])
	addBcAgree() {
		return this.services.initPost({
			reqUrl: 'bcAgree/add',
			param: this.param,
		});
	}

	/**
	 * 修改协议
	 * @description:
	 * @param {Object} param
	 * - agreeId {Integer} 协议ID
	 * - groupId {Integer} 协议分组ID
	 * - agreeCode {String} 协议档案编号
	 * - title {String} 协议标题
	 * - version {String} 版本号
	 * - publishBy {String} 发布人
	 * - updateBy {String} 修改用户的帐号
	 * @return {*}
	 */
	@Parameters([
		'agreeId',
		'groupId',
		'agreeCode',
		'title',
		'version',
		'publishBy',
		'updateBy',
	])
	editBcAgree() {
		return this.services.initPost({
			reqUrl: 'bcAgree/edit',
			param: this.param,
		});
	}

	/**
	 * 查询指定协议详情
	 * @param {Object} param
	 * - bcAgreeId {String} 参数主键ID
	 */
	@Parameters(['bcAgreeId'])
	queryBcAgreeDetail() {
		return this.services.initGet({
			reqUrl: 'bcAgree/query',
			param: this.param,
		});
	}

	/**
	 * 查看历史最新的协议草稿
	 * @param {Object} param
	 * - agreeId {String} 协议Id
	 */
	@Parameters(['agreeId'])
	queryAgreeDraft() {
		return this.services.initGet({
			reqUrl: 'bcAgreeDraft/query',
			param: this.param,
		});
	}

	/**
	 * 修改协议草稿
	 * @description:
	 * @param {Object} param
	 * - agreeId {Number} 协议ID
	 * - title {String} 协议标题
	 * - signType {String} 签署类型
	 * - content {String} 协议内容
	 * - publishBy {String} 发布人
	 * - updateBy {String} 修改用户的帐号
	 * @return {*}
	 */
	@Parameters(['agreeId', 'title', 'signType', 'content'])
	saveAgreeDraft() {
		return this.services.initPost({
			reqUrl: 'bcAgreeDraft/save',
			param: this.param,
		});
	}

	/**
	 * 批量发布协议
	 * @description:
	 * @param {Object} param
	 * - bcAgreeIds {Number} 协议IDs
	 * @return {*}
	 */
	@Parameters([
		'bcAgreeIds',
	])
	batchPublic() {
		return this.services.initPost({
			reqUrl: 'bcAgree/batchPublic',
			param: this.param,
		});
	}

	/**
	 * 发布协议
	 * @description:
	 * @param {Object} param
	 * - agreeId {Number} 协议ID
	 * - title {String} 协议标题
	 * - signType {String} 签署类型
	 * - content {String} 协议内容
	 * - publishBy {String} 发布人
	 * - updateBy {String} 修改用户的帐号
	 * @return {*}
	 */
	@Parameters([
		'agreeId',
		'title',
		'signType',
		'content',
		'publishBy',
		'updateBy',
	])
	publishAgree() {
		return this.services.initPost({
			reqUrl: 'bcAgree/publish',
			param: this.param,
		});
	}

	/**
	 * 查看历史版本的详情信息
	 * @param {Object} param
	 * - agreeId {String} 协议Id
	 */
	@Parameters(['bcAgreeContentVersionId'])
	queryAgreeContent() {
		return this.services.initGet({
			reqUrl: 'bcAgreeContentVersion/query',
			param: this.param,
		});
	}
}

export default new api();
