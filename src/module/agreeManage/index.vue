<template>
  <a-layout class="menu-content">
    <a-layout-sider class="menu-tree-sider">
      <a-card :bordered="false">
        <template slot="title">
          <h4>协议分组列表</h4>
          <a-layout-header style="padding:0px 0px; background: #FFFFFF;">
            <a-form-item prop="searchValue">
              <a-input-search
                v-model="searchValue"
                placeholder="请输入协议分组名称"
                @change="search"
                style="width: 150px"
                allowClear
              />
            </a-form-item>
          </a-layout-header>
        </template>
        <div class="menu-left-tree">
          <tkTightTree @select="select" @rightClick="rightClick">
            <tk-tree
              class="ant-tree-switcher-no"
              :treeData="cacheTreeData"
              :replaceFields="replaceFields"
              :isIcon="false"
              :selectedKeys.sync="selectedKeys"
            ></tk-tree>
            <a-menu-item
              key="1"
              @click="(treeOperationShow = true), (operationType = 'add')"
            >
              添加协议分组
            </a-menu-item>
            <a-menu-item
              key="2"
              @click="(treeOperationShow = true), (operationType = 'set')"
              v-show="Object.keys(rightMenu).length > 0"
            >
              修改协议分组
            </a-menu-item>
            <!-- <a-menu-item
                key="3"
                @click="riskMenu('remove')"
                v-show="Object.keys(rightMenu).length > 0"
              >
                删除协议分组
              </a-menu-item>
              <a-menu-item key="4" @click="getTreeData">
                刷新协议分组
              </a-menu-item> -->
          </tkTightTree>
          <treeOper
            :isPopShow.sync="treeOperationShow"
            @success="getTreeData"
            :parameterData="rightMenu"
            :operationType="operationType"
          />
        </div>
      </a-card>
    </a-layout-sider>
    <a-layout-content class="menu-right-content">
      <a-card title="协议列表" :bordered="false">
        <dataTable :groupId="groupId" />
      </a-card>
    </a-layout-content>
  </a-layout>
</template>

<script>
import treeOper from './module/treeOper'; // 引入分组右击组件
import dataTable from './module/table'; // 引入右侧表格
import api from './api';

export default {
  name: 'agreeManage',
  components: { dataTable, treeOper },
  provide: { api: api },
  data() {
    return {
      searchValue: '', //左侧菜单搜索值
      groupId: undefined, // 用户选中项菜单
      cacheTreeData: [], //缓存查询结果集
      treeData: [], // 树状列表填充数据
      rightMenu: {}, // 右击对象
      // 树状列表调整对应的key
      replaceFields: {
        children: 'children',
        title: 'groupName',
        key: 'groupId'
      },
      selectedKeys: [],
      treeOperationShow: false, // 协议分组添加弹窗是否显示
      operationType: 'add'
    };
  },
  watch: {
    treeData(val) {
      this.cacheTreeData = val.filter((v) =>
        this.searchValue ? v.groupName.includes(this.searchValue) : true
      );
    }
  },
  created() {
    this.getTreeData();
  },
  methods: {
    search() {
      this.cacheTreeData = this.treeData.filter((v) =>
        this.searchValue ? v.groupName.includes(this.searchValue) : true
      );
    },
    select({ groupId }) {
      // 赋值
      this.groupId = groupId + '';
    },
    rightClick(data) {
      this.rightMenu = data;
    },
    // 查询当前菜单栏树状列表
    getTreeData() {
      api.getBcAgreeGroupList().then(({ code, data }) => {
        if (code != 0) return;
        this.treeData = data.map((item) => this.formatConversion(item));
        this.treeData = data;
      });
    },
    formatConversion(item) {
      item['scopedSlots'] = { icon: 'custom' };
      item['itemIcon'] = 'file';
      return item;
    }
    // 点击右击菜单
    // riskMenu(type) {
    //   if (type == 'remove') {
    //     let { groupId, groupName } = this.rightMenu;
    //     this.$confirm({
    //       title: '删除菜单',
    //       content: () => <p>确定删除指定的“{groupName}”协议分组？</p>,
    //       okText: '确定',
    //       cancelText: '取消',
    //       onOk: () => {
    //         api
    //           .deleteBcAgreeGroup({ bcAgreeGroupId: groupId })
    //           .then(({ code, msg }) => {
    //             if (code != 0)
    //               return this.$message.error(`删除协议分组失败：${msg}`);
    //             this.$message.success('删除协议分组成功！');
    //             this.getTreeData();
    //           });
    //       }
    //     });
    //   }
    // }
  }
};
</script>

<style lang="scss" scoped></style>
