<template>
  <a-modal
    title="新增协议"
    class="data-ant-module"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="协议分组ID" prop="groupId" v-show="false">
        <a-input v-model="form.groupId" disabled></a-input>
      </a-form-model-item>
      <a-form-model-item label="协议档案编号" prop="agreeCode">
        <a-input
          v-model="form.agreeCode"
          placeholder="请输入协议档案编号"
          allowClear
        ></a-input>
      </a-form-model-item>
      <a-form-model-item label="协议标题" prop="title">
        <a-input
          v-model="form.title"
          placeholder="请输入协议标题"
          allowClear
        ></a-input>
      </a-form-model-item>
      <!-- <a-form-model-item label="版本号" prop="version">
        <a-input v-model="form.version" placeholder="请输入版本号" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="创建用户的帐号" prop="createBy">
        <a-input v-model="form.createBy" placeholder="请输入创建用户的帐号" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="发布人" prop="publishBy">
        <a-input v-model="form.publishBy" placeholder="请输入发布人" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="修改用户的帐号" prop="updateBy">
        <a-input v-model="form.updateBy" placeholder="请输入修改用户的帐号" ></a-input>
      </a-form-model-item> -->
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  groupId: '', // 协议分组ID
  agreeCode: '', // 协议档案编号
  title: '' // 协议标题
  // version: "", // 版本号
  // createBy: "", // 创建用户的帐号
  // publishBy: "", // 发布人
  // updateBy: "", // 修改用户的帐号
};

// 注册当前请求对应的上下文请求组
export default {
  name: 'agree_add',
  inject: ['api'],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        groupId: [{ required: true }],
        agreeCode: [
          { required: true, message: '协议档案编号不能为空', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '协议标题不能为空', trigger: 'blur' }
        ]
        // version: [
        //   { required: true, message: "版本号不能为空", trigger: "blur"}
        // ],
        // createBy: [
        //   { required: true, message: "创建用户的帐号不能为空", trigger: "blur"}
        // ],
        // publishBy: [
        //   { required: true, message: "发布人不能为空", trigger: "blur"}
        // ],
        // updateBy: [
        //   { required: true, message: "修改用户的帐号不能为空", trigger: "blur"}
        // ],
      },
      // 异步加载
      confirmLoading: false
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    groupId: String, // 数据权限类型组编号
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {}
    },
    // 对应的操作表单
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      }
    },
    typeTitle() {
      return '添加';
    }
  },
  watch: {
    isPopShow(n) {
      // if (n && this.parameterData.groupId) {
      //   this.modify();
      // } else {
      //   }
      if (n) {
        this.reset();
      }
      // this.$nextTick(()=> {
      // });
    }
  },
  methods: {
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.form.groupId = this.groupId;
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = Object.assign({}, this.parameterData);
      // this.form.status = this.form.status == 0 ? true : false;
    },
    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`协议添加成功！：${msg}`);
          this.$message.success('协议添加成功!');
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit('success');
          // 重置表单
          this.reset();
        };
        this.api.addBcAgree(param).then(callback);
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
