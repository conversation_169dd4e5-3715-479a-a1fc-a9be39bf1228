<template>
  <a-modal
    title="协议草稿"
    class="data-ant-module"
    v-model="showPop"
    @cancel="reset"
    :width="750"
    :maskClosable="false"
  >
    <template slot="footer">
      <a-button @click="reset">取消</a-button>
      <a-button type="primary" @click="saveAndPublish('保存')">保存</a-button>
      <!-- <a-button type="primary" @click="saveAndPublish('发布')">发布</a-button> -->
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
      :rules="rules"
    >
      <a-form-model-item label="协议ID" prop="agreeId" v-show="false">
        <a-input v-model="form.agreeId" disabled />
      </a-form-model-item>
      <!-- <a-form-model-item label="协议草稿ID" prop="agreeDraftId">
        <a-input v-model="form.agreeDraftId" disabled />
      </a-form-model-item> -->
      <a-form-model-item label="协议标题" prop="title">
        <a-input v-model="form.title" placeholder="请输入协议标题" />
      </a-form-model-item>
      <!-- <a-form-model-item label="签署类型" prop="signType">
        <a-input v-model="form.signType" />
      </a-form-model-item> -->
      <a-form-model-item label="签署类型" prop="signType">
        <a-select v-model="form.signType">
          <a-select-option
            v-for="item in signTypeList"
            :key="item.dictValue"
            :value="item.dictValue"
          >
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <!-- <a-form-model-item label="是否生效" prop="status">
        <a-switch v-model="form.status" />
      </a-form-model-item> -->
      <a-form-model-item label="协议内容" prop="content">
        <tkEditor
          v-if="editorShow"
          ref="tinymce"
          v-model="form.content"
          :height="300"
          :disabled="false"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import tkEditor from '@c/tinymceEditor';

export default {
  name: 'agree_edit',
  inject: ['api'],
  components: { tkEditor },
  data() {
    return {
      // 协议修改表单 - 默认
      form: {
        agreeDraftId: 0,
        agreeId: 0,
        title: '',
        signType: '1',
        content: ''
      },
      // 表单权限验证
      rules: {
        title: [
          { required: true, message: '协议标题不能为空', trigger: 'blur' }
        ],
        signType: [
          { required: true, message: '签署类型不能为空', trigger: 'blur' }
        ]
      },
      // 异步加载
      confirmLoading: false,
      editorShow: false,
      signTypeList: [] // 签署类型数组
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    roleDataGroupId: String, // 数据权限类型组编号
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      }
    },
    typeTitle() {
      return this.operationType == 'add' ? '添加' : '修改';
    }
  },
  watch: {
    isPopShow(n) {
      // if (n && this.operationType != 'add' && this.parameterData.roleDataType) {
      //   this.modify();
      // }else{
      //   this.reset();
      // }
      if (n) {
        this.query();
      }
      this.$nextTick(() => (this.editorShow = n));
    }
  },
  methods: {
    query() {
      this.api
        .queryAgreeDraft({ agreeId: this.parameterData.agreeId })
        .then((res) => {
          if (res.data) {
            this.form = res.data;
          } else {
            Object.assign(this.form, this.parameterData);
          }
        });
      this.$dict.dictContent('sign_type').then((res) => {
        this.signTypeList = res;
      });
    },
    // 重置对应的表单
    reset() {
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {
        agreeDraftId: 0,
        agreeId: 0,
        title: '',
        signType: '1',
        content: ''
      };
      this.confirmLoading = false;
      this.showPop = false;
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = Object.assign({}, this.parameterData);
      this.form.status = this.form.status == 0 ? true : false;
    },
    publish() {},
    // changeSignType(value) {
    //   this.form.signType = value;
    // },
    // 提交权限创建
    saveAndPublish(option) {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let callBack = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(`协议${option}失败：${msg}`);
          this.$message.success(`协议${option}成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit('success');
          // 重置表单
          this.reset();
        };
        option === '保存'
          ? this.api.saveAgreeDraft(param).then(callBack)
          : this.api.publishAgree(param).then(callBack);
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
