<template>
  <a-modal
    title="协议详情"
    class="data-ant-module"
    v-model="showPop"
    @cancel="reset"
    :width="750"
    :maskClosable="false"
    :footer="null"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-model-item label="协议内容" prop="content">
        <tkEditor
          v-if="editorShow"
          ref="tinymce"
          v-model="form.content"
          :height="500"
          :disabled="true"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import tkEditor from '@c/tinymceEditor';

export default {
  name: 'data_detail',
  inject: ['api'],
  components: { tkEditor },
  data() {
    return {
      // 协议修改表单 - 默认
      form: {
        content: ''
      },
      // 异步加载
      confirmLoading: false,
      editorShow: false
    };
  },
  props: {
    isPopShow: <PERSON>ole<PERSON>, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      }
    }
  },
  watch: {
    isPopShow(n) {
      // if (n && this.operationType != 'add' && this.parameterData.roleDataType) {
      //   this.modify();
      // }else{
      //   this.reset();
      // }
      if (n) {
        this.query();
      }
      this.$nextTick(() => (this.editorShow = n));
    }
  },
  methods: {
    query() {
      this.api
        .queryAgreeContent({ bcAgreeContentVersionId: this.parameterData.id })
        .then((res) => {
          if (res.data) {
            this.form = res.data;
          }
        });
    },
    // 重置对应的表单
    reset() {
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {
        content: ''
      };
      this.confirmLoading = false;
      this.showPop = false;
    }
  }
};
</script>
<style scoped>
.data-ant-module >>> .ant-modal-body {
  max-height: unset;
  overflow-y: unset;
}
</style>
