<template>
  <a-modal
    title="协议历史版本"
    :width="1000"
    v-model="showPop"
    :footer="null"
    :maskClosable="false"
  >
    <div class="access-tabl">
      <tk-table
        ref="table"
        :intercept-response="intercept_response"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcAgreeVersion/page"
        :isSelected="false"
        :isPaging="true"
        :tableFrom="tableForm"
        tableId="version"
        :isTableLoading="true"
        style="min-height:300px;"
      >
        <!-- <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add"> 新增 </a-button>
          <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove"> 删除 </a-button>
        </div> -->
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="look(data)"> 查看详情 </a-button>
          <!-- <a-button type="link" @click.stop="modify(data)"> 修改 </a-button> -->
        </template>
      </tk-table>
      <dataDetail :isPopShow.sync="showDetailPop" :parameterData="selectData" />
    </div>
    <!-- <template slot="footer">
      <a-button type="primary" @click="ok">确定</a-button>
    </template> -->
  </a-modal>
</template>
<script>
import dataDetail from './dataDetail';
export default {
  name: 'version_list',
  inject: ['api'],
  data() {
    return {
      form: {}, //表单数据,
      columns: [
        // 循环
        {
          field: 'agreeId',
          label: '协议ID',
          isSorter: false,
          width: 100
        },
        // {
        //   field: 'agreeCode',
        //   label: '协议档案编号',
        //   isSorter: false,
        //   width: 150
        // },
        {
          field: 'title',
          label: '协议标题',
          isSorter: false,
          width: 150
        },
        {
          field: 'signType',
          label: '签约类型',
          isSorter: false,
          width: 120,
          filter: (item) => this.getDictText('sign_type', item)
        },
        {
          field: 'version',
          label: '版本号',
          isSorter: false,
          width: 120
        },
        {
          field: 'createBy',
          label: '创建用户的帐号',
          isSorter: false,
          width: 150
        },
        {
          field: 'createDate',
          label: '创建时间',
          isSorter: true,
          // filter: (item) =>
          //   !item ? '--' : this.DateFormat(item).format('yyyy-MM-dd'),
          width: 180
        },
        {
          field: 'updateBy',
          label: '修改用户的帐号',
          isSorter: false,
          width: 150
        },
        {
          field: 'updateDate',
          label: '修改时间',
          isSorter: false,
          // filter: (item) =>
          //   !item ? '--' : this.DateFormat(item).format('yyyy-MM-dd'),
          width: 180
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          width: 100,
          fixed: 'right'
        }
      ],
      tableForm: {
        agreeId: '' // 协议档案编号
      },
      showDetailPop: false, // 是否展示详情弹框
      selectData: {}, // 选择查看的数据对象
      dictMap: {
        sign_type: []
      }
    };
  },
  components: { dataDetail },
  props: {
    isPopShow: {
      type: Boolean,
      default: false
    },
    // 传入参数
    agreeId: {
      type: Number
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // 改变的时候通知父组件
      }
    }
  },
  watch: {
    isPopShow() {
      // if (n && this.parameterData.agreeId) {
      //   this.query();
      // } else {
      //   this.reset();
      // }
      // this.$refs.table && this.$refs.table.getTableData();
    },
    agreeId: {
      handler(val) {
        // 具有菜单编号时查询对应的权限列表
        this.tableForm['agreeId'] = val;
        this.$refs.table && this.$refs.table.getTableData();
        // 初始话选中
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    Promise.all([this.queryDictMap()]).finally();
  },
  methods: {
    ok() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    look(data) {
      this.showDetailPop = true;
      this.selectData = data;
    },
    queryDictMap() {
      return new Promise((resolve) => {
        Object.keys(this.dictMap).forEach((item) => {
          this.$dict.dictContent(item).then((data) => {
            this.dictMap[item] = (data || []).map((data) => {
              return { label: data.dictLabel, value: data.dictValue };
            });
            resolve();
          });
        });
      });
    },
    getDictText(key, value) {
      let results = this.dictMap[key] || [];
      results = results.filter((item) => {
        return item.value == value;
      });
      return (results && results.length && results[0].label) || value || '';
    }
    // query() {
    //   this.reset();
    //   return this.api
    //     .queryBcAgreeDetail({ bcAgreeId: this.parameterData.agreeId })
    //     .then((res) => {
    //       if (res.code != 0) return;
    //       let data = res.data;
    //       this.form = data;
    //     });
    // },
  }
};
</script>

<style lang="scss" scoped></style>
