<template>
	<div class="menu-right-limits">
		<div class="searchFrom">
			<a-form-model layout="inline" :model="tableForm">
				<tkSelectForm @query="query">
					<a-form-model-item label="协议档案编号">
						<a-input
							v-model="tableForm.agreeCode"
							placeholder="请输入协议档案编号"
							allowClear
						></a-input>
					</a-form-model-item>
					<a-form-model-item label="协议标题">
						<a-input
							v-model="tableForm.title"
							placeholder="请输入协议标题"
							allowClear
						></a-input>
					</a-form-model-item>
				</tkSelectForm>
			</a-form-model>
		</div>
		<!-- 添加修改 -->
		<dataAdd
			:isPopShow.sync="isAddPopShow"
			:groupId="groupId"
			@success="query"
		/>
		<dataEdit
			:isPopShow.sync="isEditPopShow"
			:parameterData="selectData"
			@success="query"
		/>
		<versionList :isPopShow.sync="isLookPopShow" :agreeId="agreeId" />
		<tk-table
			ref="table"
			:tableData.sync="comlun"
			:intercept-response="intercept_response"
			getMethods="bc-manage-server/bcAgree/page"
			:isSelected="true"
			:isPaging="true"
			:tableFrom="tableForm"
			:selectedRowKeys.sync="selectedRowKeys"
			tableId="agreeId"
			:isTableLoading="false"
		>
			<div class="table-buttun-area" slot="tableHeader">
				<a-button
					:disabled="!groupId"
					type="primary"
					icon="plus"
					@click="add"
				>
					新增
				</a-button>
				<a-button
					:disabled="selectedRowKeys.length <= 0"
					type="primary"
					@click="publish"
				>
					发布
				</a-button>
			</div>
			<template slot="operation" slot-scope="data">
				<a-button type="link" @click.stop="look(data)"> 查看 </a-button>
				<a-button type="link" @click.stop="modify(data)">
					修改
				</a-button>
			</template>
		</tk-table>
	</div>
</template>

<script>
	import dataAdd from "./addAndEdit/dataAdd";
	import dataEdit from "./addAndEdit/dataEdit";
	// // 引入查看弹窗
	import versionList from "./version/versionList";
	export default {
		data() {
			return {
				// 表格展示字段
				comlun: [
					// 循环
					{
						field: "agreeId",
						label: "协议ID",
						width: 120,
						isSorter: false
					},
					// {
					//   field: 'groupId',
					//   label: '协议分组ID',
					//   width: 130,
					//   isSorter: false
					// },
					{
						field: "agreeCode",
						label: "协议档案编号",
						width: 120,
						isSorter: false
					},
					{
						field: "title",
						label: "协议标题",
						width: 120,
						isSorter: false
					},
					{
						field: "version",
						label: "版本号",
						width: 120,
						isSorter: false
					},
					{
						field: "createBy",
						label: "创建用户的帐号",
						width: 150,
						isSorter: false
					},
					{
						field: "createDate",
						label: "创建时间",
						isSorter: true,
						width: 150,
						isEllipsis: true
						// filter: (item) =>
						//   !item ? '--' : this.DateFormat(item).format('yyyy-MM-dd')
					},
					{
						field: "publishBy",
						label: "发布人",
						width: 120,
						isSorter: false
					},
					{
						field: "publishDate",
						label: "发布时间",
						isSorter: true,
						width: 150,
						isEllipsis: true
						// filter: (item) =>
						//   !item ? '--' : this.DateFormat(item).format('yyyy-MM-dd')
					},
					{
						field: "updateBy",
						label: "修改用户的帐号",
						width: 150,
						isSorter: false
					},
					{
						field: "updateDate",
						label: "修改时间",
						isSorter: false,
						width: 150,
						isEllipsis: true
						// filter: (item) =>
						//   !item ? '--' : this.DateFormat(item).format('yyyy-MM-dd')
					},
					{
						field: "operation",
						label: "操作",
						align: "center",
						width: 160,
						fixed: "right"
					}
				],
				// 查询接口所需字段
				tableForm: {
					agreeCode: "", // 协议档案编号
					title: "" // 协议标题
				},
				operationType: "add",
				// 是否显示弹窗
				isAddPopShow: false,
				isEditPopShow: false,
				//查看弹窗
				isLookPopShow: false,
				selectData: {}, // 所要修改的权限
				selectedRowKeys: [], // 当前用户选中参数
				agreeId: undefined
			};
		},
		inject: ["api"],
		components: { dataAdd, dataEdit, versionList },
		props: {
			// 数据权限分组编号
			groupId: {
				type: String,
				default: ""
			}
		},
		created() {
			// 初始给与值
			this.tableForm["groupId"] = this.groupId;
		},
		watch: {
			// 用户传入菜单编号
			groupId: {
				handler(val) {
					// 具有菜单编号时查询对应的权限列表
					this.tableForm["groupId"] = val;
					this.$refs.table.getTableData();
					// 初始话选中
					this.selectedRowKeys = [];
				},
				deep: true
			}
		},
		methods: {
			add() {
				//点击新增按钮
				this.isAddPopShow = true;
				this.selectData = {};
			},
			publish() {
				// todo 点击发布按钮
				this.$confirm({
					title: "发布协议",
					content: () => <p>确定发布当前选择协议？</p>,
					okText: "确定",
					cancelText: "取消",
					onOk: () => {
						this.api
							.batchPublic({
								bcAgreeIds: this.selectedRowKeys.join(",")
							})
							.then(({ code, msg }) => {
								if (code != 0)
									return this.$message.error(
										`发布协议失败：${msg}`
									);
								this.$message.success("发布协议成功！");
								this.$refs.table.getTableData();
								this.selectedRowKeys = [];
							});
					}
				});
			},
			// 点击修改按钮
			modify(data) {
				this.isEditPopShow = true;
				this.selectData = JSON.parse(JSON.stringify(data));
			},
			// 查看
			look(data) {
				this.isLookPopShow = true;
				this.agreeId = data.agreeId;
			},
			// 操作成功
			query() {
				this.selectedRowKeys = [];
				this.$refs.table.getTableData();
			}
			// 删除对应的权限
			// remove() {
			//   if (this.selectedRowKeys.length > 0) {
			//     this.$confirm({
			//       title: '删除协议',
			//       content: () => <p>确定删除当前勾选的协议？</p>,
			//       okText: '确定',
			//       cancelText: '取消',
			//       onOk: () => {
			//         this.api
			//           .deletesBcAgree({ bcAgreeIds: this.selectedRowKeys.join(',') })
			//           .then(({ code, msg }) => {
			//             if (code != 0)
			//               return this.$message.error(`删除协议失败：${msg}`);
			//             this.$message.success('删除协议成功！');
			//             this.$refs.table.getTableData();
			//             this.selectedRowKeys = [];
			//           });
			//       }
			//     });
			//   }
			// }
		}
	};
</script>

<style lang="scss" scoped></style>
