<template>
  <a-modal
    :title="`${typeTitle}协议分组`"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 10 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="父节点ID" prop="groupId" v-show="false">
        <a-input v-model="form.groupId" disabled />
      </a-form-model-item>
      <a-form-model-item label="协议分组编码" prop="groupCode">
        <a-input v-model="form.groupCode" placeholder="请输入协议分组编码" />
      </a-form-model-item>
      <a-form-model-item label="协议分组名称" prop="groupName">
        <a-input v-model="form.groupName" placeholder="请输入协议分组名称" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
export default {
  name: 'treeOper',
  inject: ['api'],
  data() {
    return {
      form: {}, // 协议分组添加表单
      // 表单权限验证
      rules: {
        groupCode: [
          {
            required: true,
            message: '协议分组编码不能为空',
            trigger: 'blur'
          }
        ],
        groupName: [
          {
            required: true,
            message: '协议分组名称不能为空',
            trigger: 'blur'
          }
        ]
      },
      // 异步加载
      confirmLoading: false
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {}
    },
    // 操作类型
    operationType: {
      type: String,
      default: 'add'
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      }
    },
    typeTitle() {
      return this.operationType == 'add' ? '添加' : '修改';
    }
  },
  watch: {
    isPopShow(n) {
      if (n && this.operationType != 'add' && this.parameterData.groupCode) {
        this.modify();
      } else if (this.parameterData.groupId) {
        this.form.groupId = this.parameterData.groupId;
      } else if (this.operationType == 'add' && !this.parameterData.groupId) {
        this.form.groupId = 0;
      } else {
        this.reset();
      }
    }
  },
  methods: {
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = Object.assign({}, this.parameterData);
    },
    // 提交协议分组创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        if (this.operationType == 'add') {
          this.form.parentId = this.form.groupId;
        }
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(`协议分组${this.typeTitle}失败：${msg}`);
          this.$message.success(`协议分组${this.typeTitle}成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit('success');
          // 重置表单
          this.reset();
        };
        this.operationType == 'add'
          ? this.api.addBcAgreeGroup(param).then(callback)
          : this.api.setBcAgreeGroup(param).then(callback);
      });
    }
  }
};
</script>
