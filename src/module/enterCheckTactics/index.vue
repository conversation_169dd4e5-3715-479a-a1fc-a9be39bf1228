<!--  -->
<template>
  <a-card :title="`${typeTitle}规则管理`"
    :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline"
        :model="tableFrom">
        <tkSelectForm @query="query"
          @reset="reset">
          <a-form-model-item label="规则名称">
            <a-input placeholder="请输入规则名称"
              v-model="tableFrom.strategyName"
              :allowClear="true"></a-input>
          </a-form-model-item>
          <a-form-model-item label="规则编号">
            <a-input placeholder="请输入规则编号"
              v-model="tableFrom.strategyNo"
              :allowClear="true"></a-input>
          </a-form-model-item>
          <a-form-model-item label="业务类型">
            <a-select v-model="tableFrom.bizType"
              placeholder="请选择业务类型"
              show-search
              :filter-option="filterOption"
              :allowClear="true">
              <a-select-option v-for="item in businessList"
                :key="item.id"
                :value="item.bizType">
                {{ item.bizName }}({{ item.bizType }})
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table ref="table"
        :intercept-response="intercept_response"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/preEntryRuleConfig/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableFrom"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id">
        <div class="table-button-area"
          slot="tableHeader">
          <a-button icon="plus"
            type="primary"
            @click="add">
            新增
          </a-button>
          <a-button icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove">
            删除
          </a-button>
            <a-button icon="download"
            type="primary"
            @click="uploadShow = true">
            导入
          </a-button>
        </div>
        <template slot="operation"
          slot-scope="data">
          <span>
            <a @click.stop="modify(data)">编辑</a>
            <a-divider type="vertical" />
            <a @click.stop="look(data)">查看规则</a>
            <!-- <a-divider type="vertical" />
            <a-dropdown>
              <a @click.stop>更多
                <a-icon type="down" />
              </a>
              <template #overlay>
                <a-menu class="menu_list">
                  <a-menu-item class="menu_item"
                    @click="onClick(data)">
                    <a href="javascript:;">设计</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown> -->
          </span>
        </template>
      </tk-table>
    </div>
    <addOrEdit :isPopShow.sync="isPopShow"
      @success="query"
      :parameterData="selectData"
      :strategyType="strategyType"
      :businessList="businessList"
      :strategyList="strategyList"></addOrEdit>

    <graph :isPopShow.sync="graphShow"
      :parameterData="selectData"></Graph>

      <upload :visible.sync="uploadShow"
      downloadeUrl="/bc-manage-view/策略导入模板.xlsx"
      uploadUrl="inner/qcrule/v2/strategy/importExcel"
      baseUrl="/qc-bizengine-server"
      downloadeTitle="策略导入模板"
      @success="query" />
  </a-card>
</template>

<script>
import api from './api';
import addOrEdit from './components/addOrEdit';
import graph from './components/graph';
import upload from '@c/upload/upload';
export default {
  data() {
    return {
      columns: [
        // 循环
        {
          field: 'bizType',
          label: '业务编号',
          isSorter: false,
          width: 100,
        },
        {
          field: 'bizTypeName',
          label: '业务类型',
          align: 'center',
          isSorter: false,
          width: 150,
        },
        {
          field: 'strategyNo',
          label: '规则编号',
          align: 'center',
          isSorter: false,
          width: 150,
        },
        {
          field: 'strategyName',
          label: '规则名称',
          align: 'center',
          width: 180,
        },
        {
          field: 'strategyType',
          label: '规则类型',
          align: 'center',
          width: 180,
          filter: (item) =>
            item.trim() === '1' ? '前置条件检查' : '准入条件检查',
        },
        {
          field: 'updateTime',
          label: '修改时间',
          align: 'center',
          width: 180,
        },
        {
          field: 'state',
          label: '是否生效',
          align: 'center',
          width: 180,
          filter: (item) => (item.trim() === '1' ? '是' : '否'),
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          width: 220,
          fixed: 'right',
        },
      ],
      businessList: [],
      strategyList: [],
      tableFrom: {
        strategyName: '',
        strategyNo: '',
        bizType: '',
        strategyType: '',
      },
      selectData: {},
      selectedRowKeys: [], // Check here to configure the default column

      //新增弹窗
      isPopShow: false, //弹窗是否显示
      graphShow: false,
      strategyType: '',
      uploadShow: false
    };
  },

  provide: { api: api },

  components: {
    addOrEdit,
    graph,
    upload
  },

  computed: {
    gData() {
      return this.graphData;
    },
    typeTitle() {
      return this.strategyType == '1'
        ? '前置'
        : this.strategyType == '2'
        ? '准入'
        : '';
    },
  },

  created() {
    let type = this.$route.query.type;
    this.strategyType = type;
    this.tableFrom.strategyType = type;
    Promise.all([
      api.getBusinessList(),
      // api.getStrategyList(),
    ]).then((res) => {
      this.businessList = res[0].data;
      // this.strategyList = res[1].data;
    });
  },

  watch: {
    $route: {
      handler(e) {
        if (e.path == '/enterCheckTactics' && e.query.type) {
          let type = e.query.type;
          this.strategyType = type;
          this.tableFrom.strategyType = this.strategyType;
          this.$nextTick(() => {
            this.$refs.table.getTableData();
          });
        }
      },
      deep: true,
    },
  },

  methods: {
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    reset() {
      this.tableFrom.strategyName = '';
      this.tableFrom.strategyNo = '';
      this.tableFrom.bizType = '';
      this.$refs.table.getTableData();
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    add() {
      this.isPopShow = true;
      this.selectData = {};
    },
    remove() {
      let _this = this;
      _this.$confirm({
        title: '温馨提示',
        content: `是否确认删除?`,
        okType: 'danger',
        onOk() {
          _this.deletePre();
        },
      });
    },

    deletePre() {
      let _this = this;
      let bcBcPreEntryRuleConfigIds = this.selectedRowKeys.join(',');
      api
        .deletePreEntryRule({ bcBcPreEntryRuleConfigIds })
        .then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.query();
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          _this.$message.error(e.message);
        });
    },

    onClick(data) {
      let href = '/ui-manage-view/rules-design?id=' + data.strategyId;
      window.open(href, '_blank');
    },
    modify(data) {
      this.isPopShow = true;
      this.selectData = data;
    },

    look(data) {
      this.selectData = data;
      this.graphShow = true;
    },
    // 搜索框参数
    tableFormFilter(param) {
      if(sessionStorage.getItem('vuex') && JSON.parse(sessionStorage.getItem('vuex')).userInfo){
				param.merchantId = JSON.parse(sessionStorage.getItem('vuex')).userInfo.merchantId
			}
      return param;
    },
  },
};
</script>
<style lang='scss' scoped>
.menu_item a {
  color: #1890ff;
}
</style>