// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 分页查询可办理业务配置
   * @param {Object} param
   */
  @Parameters(["_data"])
  getEnterList() {
    return this.services.initGet({
      reqUrl: "preEntryRuleConfig/page",
      param: this.param,
    });
  }

  /**
   * 新增业务前置条件和准入条件检查策略关联配置
   * @param {Object} param
   */
  @Parameters(["_data"])
  addPreEntryRule() {
    return this.services.initPost({
      reqUrl: "preEntryRuleConfig/add",
      param: this.param,
    });
  }

  /**
   * 更新可办理业务配置
   * @param {Object} param
   */
  @Parameters(["_data"])
  editPreEntryRule() {
    return this.services.initPost({
      reqUrl: "preEntryRuleConfig/edit",
      param: this.param,
    });
  }

  /**
   * 删除可办理业务配置
   * @param {Object} param
   */
  @Parameters(["_data"])
  deletePreEntryRule() {
    return this.services.initPost({
      reqUrl: "preEntryRuleConfig/delete",
      param: this.param,
    });
  }

  /**
   * 查询规则策略表列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getStrategyList() {
    return this.qcBizServices.initGet({
      reqUrl: "inner/qcrule/v2/strategy/list",
      param: this.param,
    });
  }

  /**
   * 查询规则策略表数据
   * @param {Object} param
   */
  @Parameters(["_data"])
  getDesignNodeInfo() {
    return this.qcBizServices.initGet({
      reqUrl: "inner/qcrule/v2/strategy/designNodeInfo",
      param: this.param,
    });
  }

  /**
   * 查询业务定义列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBusinessList() {
    return this.services.initGet({
      reqUrl: "bcBusiness/list",
      param: this.param,
    });
  }
}

export default new api();
