<template>
  <a-modal :title="`${typeTitle}规则`"
    class="data-ant-module"
    :width="700"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false">
    <a-form-model ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules">
      <a-form-model-item label="业务类型"
        prop="bizType">
        <a-select v-model="form.bizType"
          placeholder="请选择业务类型"
          show-search
          :filter-option="filterOption"
          :allowClear="true">
          <a-select-option v-for="item in businessList"
            :value="item.bizType"
            :key="item.id">
            {{ item.bizName }}({{ item.bizType }})
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="规则名称"
        prop="strategyNo">
        <a-select v-model="form.strategyNo"
          placeholder="请选择规则"
          show-search
          :filter-option="filterOption"
          @change="selectStrategy"
          :allowClear="true">
          <a-select-option v-for="item in strategyList"
            :value="item.strategyNo"
            :key="item.id">
            {{ item.strategyName }}({{ item.strategyNo }})
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="是否生效">
        <a-radio-group v-model="form.state">
          <a-radio value="0"
            key="0">不生效</a-radio>
          <a-radio value="1"
            key="1">生效</a-radio>
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  bizType: '', // 业务类型
  strategyName: '', // 策略名称
  strategyNo: '', // 策略编号
  state: '1', // 状态
};

// 注册当前请求对应的上下文请求组
export default {
  inject: ['api'],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        bizType: [
          { required: true, message: '业务类型不能为空', trigger: 'blur' },
        ],
        strategyNo: [
          { required: true, message: '规则名称不能为空', trigger: 'blur' },
        ],
      },
      confirmLoading: false,
      strategyList: []
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
    strategyType: String,
    businessList: Array,
    // strategyList: Array,
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
    typeTitle() {
      let title;
      if (this.strategyType == '1') {
        title =
          Object.keys(this.parameterData).length <= 0 ? '添加前置' : '修改前置';
      } else if (this.strategyType == '2') {
        title =
          Object.keys(this.parameterData).length <= 0 ? '添加准入' : '修改准入';
      }

      return title;
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.api.getStrategyList().then(res=>{
        this.strategyList = res.data
      })
      }
      if (n && this.parameterData.groupNo) {
        this.modify();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    selectStrategy(e) {
      this.strategyList.forEach((item) => {
        if (e == item.strategyNo) {
          this.form.strategyName = item.strategyName;
        }
      });
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      let { state, strategyType } = this.parameterData;
      this.form = Object.assign({}, this.parameterData, {
        state: state.trim(),
        strategyType: strategyType.trim(),
      });
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(
          JSON.stringify(
            Object.assign({}, this.form, { strategyType: this.strategyType })
          )
        );
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `业务准入规则${this.typeTitle}失败：${msg}`
            );
          this.$message.success(`业务准入规则${this.typeTitle}成功！`);
          // 通知操作成功
          this.$emit('success');
          // 关闭弹窗 重置表单
          this.closePop();
        };
        let _this = this;
        if (_this.parameterData.groupNo) {
          _this.api.editPreEntryRule(param).then(callback);
        } else {
          _this.api.addPreEntryRule(param).then(callback);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
