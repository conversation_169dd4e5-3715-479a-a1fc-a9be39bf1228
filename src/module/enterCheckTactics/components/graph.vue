<!--  -->
<template>
  <a-modal title="规则详情"
    class="data-ant-module"
    :width="750"
    :height="800"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="closePop"
    @cancel="closePop"
    :maskClosable="false">
    <div style="height: 100vh">
      <GraphPage ref="graph"
        class="content-wrap"
        :graph-data="graphData"></GraphPage>
    </div>
  </a-modal>
</template>

<script>
import GraphPage from '@/components/graph/index';
export default {
  inject: ['api'],
  data() {
    return {
      graphData: [],
    };
  },

  components: { GraphPage },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.strategyId) {
        this.getDesignNodeInfo(this.parameterData.strategyId);
      }
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
    },
    getDesignNodeInfo(id) {
      this.api.getDesignNodeInfo({ strategyId: id }).then((res) => {
        this.graphData = res.data.strategyGroups;
      });
    },
  },
};
</script>
<style lang='scss' scoped>
.content-wrap {
  height: 100%;
}
</style>