<template>
  <div>
    <a-modal
      :title="(requestId ? '编辑' : '添加') + '银行信息'"
      :visible="showPop"
      :destroyOnClose="true"
      @ok="getValue"
      @cancel="closePop"
      class="ant_modal_bigtable"
    >
      <template slot="footer">
        <a-button key="back" @click="resetForm"> 重置 </a-button>
        <a-button type="primary" key="submit" @click="getValue">
          {{ requestId ? '修改' : '添加' }}
        </a-button>
      </template>
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="银行简称" prop="bankName">
          <a-input
            placeholder="请输入银行简称"
            v-model="form.bankName"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item label="银行全称" prop="bankFullName">
          <a-input
            placeholder="请输入银行全称"
            v-model="form.bankFullName"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item label="银行标识" prop="bankNo">
          <a-input placeholder="请输入银行标识" v-model="form.bankNo"></a-input>
        </a-form-model-item>
        <a-form-model-item label="排序值" prop="orderline">
          <a-input-number id="inputNumber" v-model="form.orderline" :min="0" />
        </a-form-model-item>
        <a-form-model-item label="银行图标logo">
          <a-upload
            name="file"
            list-type="picture-card"
            :multiple="true"
            :data="updataType"
            :file-list="PCFileList"
            action="/bc-manage-server/file/upload"
            @preview="handlePreview($event, 'PC')"
            @change="handleChange($event, 'PC')"
          >
            <div v-if="PCFileList.length < 1">
              <a-icon type="plus" />
              <div class="ant-upload-text">logo上传</div>
            </div>
          </a-upload>
        </a-form-model-item>
        <a-form-model-item label="缩略图logo">
          <a-upload
            name="file"
            list-type="picture-card"
            :multiple="false"
            :data="updataType"
            :file-list="APPFileList"
            action="/bc-manage-server/file/upload"
            @preview="handlePreview($event, 'APP')"
            @change="handleChange($event, 'APP')"
          >
            <div v-if="APPFileList.length < 1">
              <a-icon type="plus" />
              <div class="ant-upload-text">logo上传</div>
            </div>
          </a-upload>
        </a-form-model-item>
        <a-form-model-item label="是否启用" prop="state">
          <a-radio-group name="state" v-model="form.state">
            <a-radio value="1" key="1">是</a-radio>
            <a-radio value="0" key="0">否</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="营销话术" prop="marketTip">
          <a-input
            placeholder="请输入20个字以内的营销话术"
            v-model="form.marketTip"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item label="跳转银行连接" prop="bankUrl">
          <a-input
            placeholder="请输入银行开通养老金银行卡连接"
            v-model="form.bankUrl"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item label="绑卡提示信息" prop="bindTip">
          <a-input
            placeholder="请输入绑卡提示信息"
            v-model="form.bindTip"
          ></a-input>
        </a-form-model-item>
      </a-form-model>
      <a-modal
        :visible="previewVisible"
        :footer="null"
        @cancel="previewVisible = false"
      >
        <img alt="example" style="width: 100%" :src="previewImage" />
      </a-modal>
    </a-modal>
  </div>
</template>
<script>
export default {
  name: '',
  inject: ['api'],
  data() {
    return {
      updataType: {
        type: 'image',
      },
      PCFileList: [],
      APPFileList: [],
      isBuffetList: this.$baseDict.base.isBuffetList, //方式列表
      passwordTypeList: this.$dict.dictTypeList('bc.common.clientPwdType'),
      counterKindList: [
        { key: '0', value: '普通柜台' },
        { key: '1', value: '信用柜台' },
      ],
      idKindList: this.$dict.dictTypeList('bc.common.idKind'),
      previewVisible: false,
      previewImage: '',
      form: {
        bankNo: '', //Y
        bankName: '', //Y
        bankFullName: '', //Y
        bankLogo: '',
        bankSimpleLogo: '',
        orderline: '0',
        绑卡提示信息: '',
        state: '1',
        marketTip: '',
        bankUrl: '',
      },
      rules: {
        bankName: [
          {
            required: true,
            message: '银行简称不能为空',
            trigger: 'blur',
          },
        ],
        bankFullName: [
          {
            required: true,
            message: '银行全称不能为空',
            trigger: 'blur',
          },
        ],
        bankNo: [
          {
            required: true,
            message: '英文标识不能为空',
            trigger: 'blur',
          },
        ],
        marketTip: [
          {
            message: '最多只能输入20个字',
            max: 20,
          },
        ],
        bankUrl: [
          {
            required: true,
            message: '跳转银行连接不能为空',
            trigger: 'blur',
          },
        ],
        bindTip: [
          {
            message: '最多只能输入300个字',
            max: 300,
          },
        ],
      },
    }
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    requestId: {
      type: String,
      default: '',
    },
    parameterData: {
      type: Object,
    },
  },
  mounted() {},
  computed: {
    showPop: {
      get() {
        return this.isPopShow
      },
      set(val) {
        this.$emit('update:isPopShow', val) // visible 改变的时候通知父组件
      },
    },
  },
  methods: {
    updateList() {
      this.closePop()
      this.$emit('success')
    },
    // 重置表单
    resetForm() {
      this.form = {
        bankNo: '', //Y
        bankName: '', //Y
        bankFullName: '', //Y
        bankLogo: '',
        bankSimpleLogo: '',
        orderline: '0',
        bindTip: '',
        state: '1',
        marketTip: '',
        bankUrl: '',
      }
    },
    //关闭
    closePop() {
      this.resetForm()
      ;(this.PCFileList = []), (this.APPFileList = []), (this.showPop = false)
    },
    //提交
    getValue() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        let param = JSON.parse(JSON.stringify(this.form))
        let callback = ({ code, msg }) => {
          if (code != 0)
            return this.$message.error(
              `银行信息${this.requestId ? '修改' : '添加'}失败：${msg}`
            )
          this.$message.success(
            `银行信息${this.requestId ? '修改' : '添加'}成功！`
          )
          this.updateList()
        }
        this.requestId
          ? this.api.bcBankEdit(param).then(callback)
          : this.api.bcBankAdd(param).then(callback)
      })
    },
    handlePreview(info) {
      if (info.url) {
        this.previewImage = info.url
        this.previewVisible = true
      }
      if (info.response && info.response.data) {
        this.previewImage =
          window.$hvue.customConfig.fileUrl + info.response.data
        this.previewVisible = true
      }
    },
    // 选择图片修改
    handleChange(info, type) {
      let fileList = [...info.fileList]
      fileList = fileList.slice(-2)
      let resImg = info.file.response && info.file.response.data
      fileList = fileList.map((file) => {
        if (file.response) {
          file.url = window.$hvue.customConfig.fileUrl + file.response.data
        }
        return file
      })
      if (type === 'PC') {
        this.PCFileList = fileList
        this.form.bankLogo = resImg
      } else if (type === 'APP') {
        this.APPFileList = fileList
        this.form.bankSimpleLogo = resImg
      }
    },

    getDetail() {
      this.PCFileList = this.parameterData.bankLogo
        ? [
            {
              uid: '1',
              name: 'image.png',
              status: 'done',
              url:
                window.$hvue.customConfig.fileUrl + this.parameterData.bankLogo,
            },
          ]
        : []
      this.APPFileList = this.parameterData.bankSimpleLogo
        ? [
            {
              uid: '2',
              name: 'image.png',
              status: 'done',
              url:
                window.$hvue.customConfig.fileUrl +
                this.parameterData.bankSimpleLogo,
            },
          ]
        : []
      this.form = {
        ...this.parameterData,
      }
    },
  },
  watch: {
    isPopShow(n) {
      let _this = this
      if (n) {
        if (_this.requestId != '') {
          _this.$nextTick(() => {
            _this.getDetail()
          })
        }
      }
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .idKind .ant-checkbox-wrapper {
  display: block;
}

::v-deep .idKind .ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0;
}
</style>
