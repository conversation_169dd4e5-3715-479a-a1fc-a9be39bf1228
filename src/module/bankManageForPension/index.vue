<template>
  <a-card title="养老金存管银行管理" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset(0)">
          <a-form-model-item label="银行标识">
            <a-input
              placeholder="请输入银行标识"
              v-model="tableForm.bankNo"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="银行名称">
            <a-input
              placeholder="请输入银行名称"
              v-model="tableForm.keyWord"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="是否启用">
            <a-select v-model="tableForm.state" placeholder="请选择是否启用">
              <a-select-option
                v-for="(item, index) in ['否', '是']"
                :key="index"
                :value="index + ''"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :intercept-response="intercept_response"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/csdcBank/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="bankId"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add"> 添加 </a-button>
          <a-button
            icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove"
          >
            删除
          </a-button>
          <a-button type="primary" icon="redo" @click="reset(1)">刷新</a-button>
        </div>
        <template slot="bankSimpleLogo" slot-scope="data">
          <viewer v-if="data.bankSimpleLogo">
            <img
              style="cursor: pointer"
              :src="'/bc-manage-server/file/downloadImg?path=' + data.bankSimpleLogo"
              class="img-table"
            />
          </viewer>
          <span v-else>暂无图片</span>
        </template>
        <template slot="bankLogo" slot-scope="data">
          <viewer v-if="data.bankLogo">
            <img
              style="cursor: pointer"
              :src="'/bc-manage-server/file/downloadImg?path=' + data.bankLogo"
              class="img-table"
            />
          </viewer>
          <span v-else>暂无图片</span>
        </template>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="modify(data)"> 修改 </a-button>
        </template>
      </tk-table>
    </div>

    <addOrEdit
      :isPopShow.sync="isPopShow"
      @updateList="updateList"
      @success="query"
      :parameterData="selectData"
      :requestId="requestId"
    />
    <upload :visible.sync="uploadShow" @success="query" />
  </a-card>
</template>

<script>
// 引入添加和编辑弹窗
import addOrEdit from './components/addOrEdit'
import upload from './components/upload'
import api from './api'

export default {
  data() {
    return {
      columns: [
        // 循环
        {
          field: 'bankNo',
          label: '银行标识',
          isSorter: false,
        },
        {
          field: 'bankName',
          label: '银行名称',
          isSorter: false,
        },
        {
          field: 'orderline',
          label: '排序值',
          isSorter: true,
        },
        {
          field: 'bankSimpleLogo',
          label: '银行图标logo',
          isSorter: false,
        },
        {
          field: 'bankLogo',
          label: '缩略图logo',
          isSorter: false,
        },
        {
          field: 'state',
          label: '是否启用',
          isSorter: false,
          filter: (item) => (item === '1' ? '是' : '否'),
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          fixed: true,
        },
      ],
      isBuffetList: this.$baseDict.base.isBuffetList, //方式列表
      tableForm: {
        bankNo: '',
        keyWord: '',
        state: undefined,
      },
      selectData: {},
      selectedRowKeys: [], // Check here to configure the default column

      //新增弹窗
      isPopShow: false, //弹窗是否显示
      requestId: '', //id

      uploadShow: false,
    }
  },
  provide: { api: api },
  components: { addOrEdit, upload },
  methods: {
    // 导入downloadExcel
    downloadExcel() {
      this.uploadShow = true
    },

    // 导出exportExcel
    exportExcel() {
      window.location.href = '/bc-manage-server/bcBank/export'
    },

    // 搜索框参数
    tableFormFilter(param) {
      return param
    },

    query() {
      this.$refs.table.getTableData()
      this.selectedRowKeys = []
      this.requestId = ''
    },

    updateList() {},

    // 点击刷新按钮
    reset(type) {
      this.tableForm = {
        bankNo: '',
        keyWord: '',
        state: undefined,
      }
      if (type) {
        this.$nextTick(() => {
          this.$refs.table.getTableData()
        })
      }
    },

    // 点击新增按钮
    add() {
      this.isPopShow = true
      this.selectData = {}
      this.requestId = ''
    },

    // 点击修改按钮
    modify(data) {
      this.queryBankById(data)
    },

    // 点击删除按钮
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: '存管银行',
          content: () => <p>确定删除当前数据?</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            api
              .bcBankDeletes({
                bcBankIds: this.selectedRowKeys.join(','),
              })
              .then(({ code, msg }) => {
                if (code != 0) return this.$message.error(`删除失败：${msg}`)
                this.$message.success('删除成功！')
                this.$refs.table.getTableData()
                this.selectedRowKeys = []
              })
          },
        })
      }
    },

    queryBankById(data) {
      api.queryBankById({ bcBankId: data.bankId }).then((res) => {
        this.isPopShow = true
        this.selectData = res.data
        this.requestId = data.bankId
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .img-table {
  max-height: 23px;
}
</style>
