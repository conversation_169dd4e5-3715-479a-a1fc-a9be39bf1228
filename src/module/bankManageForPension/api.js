// 加载对应的装饰器
import { Parameters } from '@u/decorator'

class api {
  /**
   * 根据ID查询银行基本信息
   * @param {Object} param
   * - bcBankId {Number} 银行ID
   */
  @Parameters(['bcBankId'])
  queryBankById() {
    return this.services.initGet({
      reqUrl: '/csdcBank/query',
      param: this.param,
    })
  }

  /**
   * 新增银行基本信息
   * @param {Object} param
   */
  @Parameters(['_data'])
  bcBankAdd() {
    return this.services.initPost({
      reqUrl: '/csdcBank/add',
      param: this.param,
    })
  }

  /**
   * 修改银行基本信息
   * @param {Object} param
   */
  @Parameters(['_data'])
  bcBankEdit() {
    return this.services.initPost({
      reqUrl: '/csdcBank/edit',
      param: this.param,
    })
  }

  /**
   * 删除银行基本信息
   * @param {Object} param
   */
  @Parameters(['_data'])
  bcBankDeletes() {
    return this.services.initPost({
      reqUrl: '/csdcBank/deletes',
      param: this.param,
    })
  }
}

export default new api()
