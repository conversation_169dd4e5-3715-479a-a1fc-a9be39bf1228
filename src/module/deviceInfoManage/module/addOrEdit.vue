<template>
  <a-modal
    :title="`${parameterData.id ? '修改' : '新增'}设备信息`"
    class="data-ant-module"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
      :rules="rules"
    >
      <a-form-model-item label="营业部" prop="branchNo">
        <a-select
          v-model="form.branchNo"
          placeholder="请选择营业部"
          show-search
          option-filter-prop="children"
          :allowClear="true"
        >
          <a-select-option
            v-for="item in parameterData.branchList"
            :value="item.branchNo"
            :key="item.branchId"
          >
            {{ item.branchName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="设备号" prop="deviceId">
        <a-input v-model="form.deviceId" placeholder="请输入设备号"> </a-input>
      </a-form-model-item>
      <a-form-model-item label="设备名称">
        <a-input v-model="form.deviceName" placeholder="请输入设备名称">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="设备型号">
        <a-input v-model="form.deviceModel" placeholder="请输入设备型号">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="是否有效" prop="state">
        <a-radio-group v-model="form.state">
          <a-radio
            v-for="(item, index) in ['无效', '有效']"
            :key="index"
            :value="index + ''"
          >
            {{ item }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="使用状态" prop="usingState">
        <a-radio-group v-model="form.usingState">
          <a-radio
            v-for="(item, index) in ['未使用', '使用中']"
            :key="index"
            :value="index + ''"
          >
            {{ item }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  branchNo: undefined,
  deviceId: "",
  deviceName: "",
  deviceModel: "",
  state: "1",
  usingState: "1"
};

// 注册当前请求对应的上下文请求组
export default {
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        branchNo: [
          { required: true, message: "请选择营业部", trigger: "blur" },
        ],
        deviceId: [
          { required: true, message: "请输入设备号", trigger: "blur" },
        ]
      },
      confirmLoading: false,
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    query() {
      // 获取修改属性前先进行表单重置
      this.reset();
      let {
        branchNo,
        deviceId,
        deviceModel,
        deviceName,
        state,
        usingState,
        id
      } = this.parameterData;
      this.form = {
        branchNo,
        deviceId,
        deviceModel,
        deviceName,
        id,
        state,
        usingState,
      };
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `设备信息${
                this.parameterData.id ? "修改" : "新增"
              }失败：${msg}`
            );
          this.$message.success(
            `设备信息${this.parameterData.id ? "修改" : "新增"}成功！`
          );
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        this.parameterData.id
          ? this.api.editPubDeviceInfo(param).then(callback)
          : this.api.addPubDeviceInfo(param).then(callback);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
