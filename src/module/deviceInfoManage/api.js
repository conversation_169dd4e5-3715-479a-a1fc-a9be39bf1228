// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {

  /**
   * 查询营业部列表
   * @description:
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBranchInfo() {
    return this.services.initGet({
      reqUrl: "bcBranchInfo/list",
      param: this.param,
    });
  }

  /**
   * 查询指定设备信息信息
   * @param {Object} param
   */
  @Parameters(["_data"])
  getPubDeviceInfo() {
    return this.services.initGet({
      reqUrl: "pubDeviceInfo/query",
      param: this.param
    });
  }

  /**
	 * 新增设备信息
	 * @param {*}
	 */
	@Parameters(['_data'])
	addPubDeviceInfo() {
		return this.services.initPost({
			reqUrl: 'pubDeviceInfo/add',
			param: this.param
		});
	}

	/**
	 * 修改设备信息
	 * @param {*}
	 */
   @Parameters(['_data'])
   editPubDeviceInfo() {
     return this.services.initPost({
       reqUrl: 'pubDeviceInfo/edit',
       param: this.param
     });
   }

	/**
	 * 删除多个设备信息
	 * @param {Object} param
	 * - pubDeviceInfoIds {String} 参数主键ID列表，多个以“，”隔开
	 *
	 */
	@Parameters(['pubDeviceInfoIds'])
	deletesPubDeviceInfo() {
		return this.services.initPost({
			reqUrl: 'pubDeviceInfo/deletes',
			param: this.param
		});
	}
}

export default new api();
