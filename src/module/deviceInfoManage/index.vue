<template>
  <a-card title="设备信息管理" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query(1)" @reset="reset">
          <a-form-model-item label="营业部">
            <a-select
              v-model="tableForm.branchNo"
              placeholder="请选择营业部，支持多选"
              allowClear
              show-search
              option-filter-prop="children"
              mode="multiple"
            >
              <a-select-option
                v-for="item in branchList"
                :key="item.branchId"
                :value="item.branchNo"
              >
                {{ item.branchName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="设备号">
            <a-input
              v-model="tableForm.deviceId"
              placeholder="请输入设备号"
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/pubDeviceInfo/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="reqTableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
      <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add">
            新增
          </a-button>
          <a-button
            icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove"
          >
            删除
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a @click.stop="modify(data)">修改</a>
        </template>
      </tk-table>
      <addOrEdit
      :isPopShow.sync="isPopShow"
      @success="query"
      :parameterData="selectData"
    ></addOrEdit>
    </div>
  </a-card>
</template>

<script>
import addOrEdit from "./module/addOrEdit";
import api from "./api";

// 默认表单属性
const defaultForm = {
  branchNo: undefined,
  deviceId: "",
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "branchNo",
          label: "营业部名称",
          isSorter: false,
          filter: item=>this.exchangeBranchNo(item)
        },
        {
          field: "deviceId",
          label: "设备号",
          isSorter: false,
        },
        {
          field: "state",
          label: "是否有效",
          isSorter: false,
          filter: item=>item==='1'?'有效':'无效'
        },
        {
          field: "deviceName",
          label: "设备名称",
          isSorter: false,
        },
        {
          field: "deviceModel",
          label: "设备型号",
          isSorter: false,
        },
        {
          field: "createDate",
          label: "创建时间",
          isSorter: false,
        },
        {
          field: "usingState",
          label: "使用状态",
          isSorter: false,
          filter:item=>item==='1'?'使用中':'未使用',
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed:'right',
        },
      ],
      tableForm: {
        branchNo: undefined,
        deviceId: "",
      },
      reqTableForm: {},
      selectedRowKeys: [], // Check here to configure the default column
      branchList:[],
      isPopShow: false,
      selectData: {},
    };
  },
  provide: { api: api },
  components: { addOrEdit },
  created() {
    api.getBranchInfo().then((res) => {
      this.branchList = res.data;
    });
  },
  methods: {
    exchangeBranchNo(v){
        let obj = this.branchList.find((item) => item.branchNo === v);
        return obj ? obj.branchName : v;
      },
    add() {
      this.isPopShow = true;
      this.selectData = {
        branchList: this.branchList,
      };
    },
    remove() {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deletePre();
        },
      });
    },
    deletePre() {
      let pubDeviceInfoIds = this.selectedRowKeys.join(",");
      api
        .deletesPubDeviceInfo({ pubDeviceInfoIds })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.query();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    modify(data) {
      this.isPopShow = true;
      this.selectData = {
        ...data,
        branchList: this.branchList,
      };
    },
    tableFormFilter(param) {
      return param;
    },
    query(type) {
      if (type) {
        this.reqTableForm = JSON.parse(JSON.stringify(this.tableForm));
        this.reqTableForm.branchNo =
          this.reqTableForm.branchNo && this.reqTableForm.branchNo.join(",");
      }
      this.$nextTick(() => {
        this.$refs.table.getTableData();
        this.selectedRowKeys = [];
      });
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
  }
};
</script>

<style lang="scss" scoped></style>
