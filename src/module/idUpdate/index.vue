<template>
  <a-card title="身份证更新" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="handleReset">
          <a-form-model-item label="客户申请时间">
            <a-range-picker
              v-model="tableForm.applyTime"
              @change="onChangeApply"
            />
          </a-form-model-item>
          <a-form-model-item label="手机号">
            <a-input
              v-model="tableForm.mobileNo"
              placeholder="请输入手机号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户编号">
            <a-input
              v-model="tableForm.clientId"
              placeholder="请输入客户编号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="完成审核时间">
            <a-range-picker
              v-model="tableForm.auditedTime"
              @change="onChangeAudited"
            />
          </a-form-model-item>
          <a-form-model-item label="证件号码">
            <a-input
              v-model="tableForm.idNo"
              placeholder="请输入证件号码"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户姓名">
            <a-input
              v-model="tableForm.clientName"
              placeholder="请输入客户姓名"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="审核结果">
            <a-select
              v-model="tableForm.status"
              placeholder="请选择审核结果"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in statusList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="审核坐席">
            <a-input
              v-model="tableForm.operatorName"
              placeholder="请输入审核坐席"
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="page_main">
      <a-divider />
      <a-row class="btn_row">
        <a-col>
          <a-button type="primary" icon="redo" @click="query">刷新 </a-button>
        </a-col>
      </a-row>
      <div class="access-table">
        <tk-table
          ref="table"
          :tableData.sync="columns"
          getMethods="bc-manage-server/gjV2/audit/idcardupdate/page"
          :isSelected="true"
          :isPaging="true"
          :tableFrom="tableForm"
          :selectedRowKeys.sync="selectedRowKeys"
          tableId="id"
        >
          <template slot="operation" slot-scope="data">
            <a class="action-btn" @click="showDetail(data)"> 查看详情 </a>
          </template>
        </tk-table>
      </div>
    </div>
  </a-card>
</template>

<script>
import moment from 'moment'

const defaultForm = {
  bizType: undefined,
  name: '',
  state: undefined,
}

export default {
  name: 'idUpdate',
  data() {
    return {
      tableForm: {
        applyTime: '', //申请时间
        auditedTime: '', //审核时间
        applyTimeStart: '', //客户申请开始时间
        applyTimeEnd: '', //客户申请结束时间
        mobileNo: '', //手机号
        clientId: '', //客户编号
        auditedBeginTime: '', //完成审核开始时间
        auditedEndTime: '', //完成审核结束时间
        idNo: '', //证件号码
        clientName: '', //客户姓名
        status: '', //审核结果
        operatorName: '', //审核坐席
      },
      statusList: [
        { value: '1', name: '待审核' },
        { value: '2', name: '初审中' },
        { value: '3', name: '初审通过' },
        { value: '4', name: '初审驳回' },
        { value: '5', name: '复核中' },
        { value: '6', name: '复核驳回' },
        { value: '7', name: '复核通过' },
        { value: '8', name: '待操作员处理' },
        { value: '9', name: '操作员处理中' },
        { value: '10', name: '完成' },
      ], //审核结果列表
      data: [],
      //表头数据
      columns: [
        {
          label: '客户编号',
          field: 'clientId',
          isSorter: false,
        },
        {
          label: '客户姓名',
          field: 'clientName',
          isSorter: false,
        },
        {
          label: '审核结果',
          field: 'result',
          isSorter: false,
          filter: (item) => this.getTaskStatusList(item),
        },
        {
          label: '手机号码',
          field: 'mobileNo',
          isSorter: false,
          filter: (item) => this.getMosaicNum(item,4,4),
        },
        {
          label: '证件号码',
          field: 'submitIdNo',
          isSorter: false,
          filter: (item) => this.getMosaicNum(item,4,4),
        },
        {
          label: '申请时间',
          field: 'applyTime',
          isSorter: false,
          filter: (item) => moment(item).format('YYYY-MM-DD HH:MM:SS'),
        },
        {
          label: '完成审核时间',
          field: 'lastUpdateTime',
          isSorter: false,
          filter: (item) => moment(item).format('YYYY-MM-DD HH:MM:SS'),
        },
        {
          label: '审核坐席',
          field: 'operatorName',
          isSorter: false,
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: 220,
        },
      ],
      selectedRowKeys: [], // Check here to configure the default column
    }
  },
  components: {},
  methods: {
    onChangeApply(date, dateString) {
      this.tableForm.applyTimeStart = dateString[0]
      this.tableForm.applyTimeEnd = dateString[1]
    },
    onChangeAudited(date, dateString) {
      this.tableForm.auditedBeginTime = dateString[0]
      this.tableForm.auditedEndTime = dateString[1]
    },
    //处理按钮
    query() {
      this.$refs.table.getTableData()
    },
    handleReset() {
      this.tableForm = JSON.parse(JSON.stringify(defaultForm))
    },
    getTaskStatusList(val) {
      const filterData = this.statusList.filter(({ value }) => value == val)[0]
      return filterData ? filterData.name : ''
    },
    showDetail(data) {
      let href = `/bc-manage-view/idUpdateDetailInfo?id=${data.id}`
      window.open(href, '_blank')
    },
    getMosaicNum(str, beginLength, endLength) {
      beginLength = beginLength || 0
      endLength = endLength || 0
      if (str == null || str == undefined) {
        return str
      } else {
        str = str.toString()
        if (str.length <= beginLength + endLength) {
          return str
        } else {
          return (
            str.slice(0, beginLength) +
            '*'.repeat(str.length - beginLength - endLength) +
            (endLength > 0 ? str.slice(-endLength) : '')
          )
        }
      }
    },
  },
}
</script>

<style>
.alert-test {
  text-align: center;
  padding: 5px;
}
</style>
