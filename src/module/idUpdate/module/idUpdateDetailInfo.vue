<template>
  <div>
    <a-layout>
      <a-layout-content
        style="background: #f9f9f9; padding: 10px 25px 90px 25px"
      >
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">审核详情</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="业务类型">
                  {{ userInfo?.auditDetails?.businessType }}
                </a-descriptions-item>
                <a-descriptions-item label="客户号">
                  {{ userInfo?.auditDetails?.clientId }}
                </a-descriptions-item>
                <a-descriptions-item label="申请时间">
                  {{ formatDateTime(userInfo?.auditDetails?.applyTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="审核时间">
                  {{ formatDateTime(userInfo?.auditDetails?.auditEndTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="审核人员">
                  {{ userInfo?.auditDetails?.operatorId }}
                </a-descriptions-item>
                <a-descriptions-item label="复核时间">
                  {{ formatDateTime(userInfo?.auditDetails?.fhAuditEndTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="复核人员">
                  {{ userInfo?.auditDetails?.fhOperetorId }}
                </a-descriptions-item>
                <a-descriptions-item label="柜台操作时间">
                  {{
                    formatDateTime(userInfo?.auditDetails?.accountOperateTime)
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="柜台操作人员">
                  {{ userInfo?.auditDetails?.accountOperetorId }}
                </a-descriptions-item>
              </a-descriptions>
              <div>
                <a-row :style="{ marginTop: 15 + 'px' }">
                  <div class="content-border">
                    <div class="list_header">
                      <a-row>
                        <a-col :span="6" class="pop-title">客户柜台档案</a-col>
                      </a-row>
                    </div>
                    <viewer
                      class="idcardImg"
                      v-for="(item, index) in userInfo?.clientCounterfile
                        ?.customerImageDownloadVOS"
                      :key="index"
                    >
                      <h4>{{ clientCounterList[item.imageNo] }}</h4>
                      <img
                        width="100%"
                        style="cursor: pointer"
                        :src="'data:image/jpeg;base64,' + item.imageData"
                      />
                    </viewer>
                    <div
                      v-if="
                        !userInfo?.clientCounterfile?.customerImageDownloadVOS
                          ?.length
                      "
                      class="placeholder"
                    ></div>
                  </div>
                </a-row>
              </div>
              <div>
                <a-row :style="{ marginTop: 15 + 'px' }">
                  <div class="content-border">
                    <div class="list_header">
                      <a-row>
                        <a-col :span="6" class="pop-title">客户上传档案</a-col>
                      </a-row>
                    </div>
                    <viewer
                      class="idcardImg"
                      v-for="(item, index) in clientImgArr"
                      :key="index"
                    >
                      <h4>{{ archiveTypeList[item.archiveType] }}</h4>
                      <img
                        width="100%"
                        style="cursor: pointer"
                        :src="item.imgUrl"
                      />
                    </viewer>
                    <div v-if="!clientImgArr?.length" class="placeholder"></div>
                  </div>
                </a-row>
              </div>
            </div>
          </a-row>
        </div>
        <div>
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">公安信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="客户姓名">
              {{ userInfo?.clientInfo?.clientName }}
            </a-descriptions-item>
            <a-descriptions-item label="身份证号">
              {{ getMosaicNum(userInfo?.clientInfo?.idNo, 4, 4) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件开始日期">
              {{ formatDate(userInfo?.clientInfo?.idBegindate) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件结束日期">
              {{ formatDate(userInfo?.clientInfo?.idEnddate) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件地址">
              {{ userInfo?.clientInfo?.idAddress }}
            </a-descriptions-item>
            <a-descriptions-item label="证件签发机关">
              {{ userInfo?.clientInfo?.issuedDepart }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">客户审核信息</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="审核类型">
                  {{ userInfo?.auditInfo?.auditType }}
                </a-descriptions-item>
                <a-descriptions-item label="状态">
                  {{ getTaskStatusList(userInfo?.auditInfo?.status) }}
                </a-descriptions-item>
                <a-descriptions-item label="审核意见">
                  {{ userInfo?.auditInfo?.resultRemark }}
                </a-descriptions-item>
                <a-descriptions-item label="手机号码">
                  {{ getMosaicNum(userInfo?.auditInfo?.mobileNo, 4, 4) }}
                </a-descriptions-item>
                <a-descriptions-item label="备注">
                  {{ userInfo?.auditInfo?.remark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>
<script>
import api from '../api'
import moment from 'moment'
export default {
  name: 'idUpdateDetailInfo',
  data() {
    return {
      userInfo: {},
      clientImgArr: [],
      statusList: [
        { value: '1', name: '待审核' },
        { value: '2', name: '初审中' },
        { value: '3', name: '初审通过' },
        { value: '4', name: '初审驳回' },
        { value: '5', name: '复核中' },
        { value: '6', name: '复核驳回' },
        { value: '7', name: '复核通过' },
        { value: '8', name: '待操作员处理' },
        { value: '9', name: '操作员处理中' },
        { value: '10', name: '完成' },
      ],
      archiveTypeList: {
        1: '原身份证正面',
        2: '原身份证反面',
        3: '客户头像',
        4: '视频见证录像',
        5: '辅证图片',
        7: '单项视频',
        8: '客户公安头像',
        9: '净资产证明档案',
        10: '总资产证明档案',
        11: '近三年年均收入证明档案',
        12: '投资经验证明档案',
        13: '无持仓证明',
      },
      clientCounterList: {
        '6A': '新身份证正面',
        '6B': '新身份证反面',
        80: '客户头像',
        82: '公安头像',
      },
    }
  },
  provide: [api],
  components: {},
  created() {
    if (this.$route.query.id) {
      this.queryClientDetail()
    }
  },
  methods: {
    // 查询客户资料详情
    queryClientDetail() {
      api
        .queryItem({
          id: this.$route.query.id,
        })
        .then((res) => {
          if (res.code == 0) {
            if (res.data.clientBusinessfile.length > 0) {
              res.data.clientBusinessfile.forEach((item) => {
                if (item.fileIdStr) {
                  this.queryfileBytes(
                    item.fileIdStr,
                    item.archiveType,
                    (imgFileBytes) => {
                      this.clientImgArr.push({
                        imgUrl:
                          'data:image/jpeg;base64,' + imgFileBytes.fileBytes,
                        archiveType: imgFileBytes.fileType,
                      })
                    }
                  )
                }
              })
            }
            setTimeout(() => {
              console.log('this.clientImgArr', this.clientImgArr)
              this.userInfo = res.data
            }, 0)
          } else {
            this.$message.error(res.message)
          }
        })
    },
    queryfileBytes(fileId, fileType, callback) {
      let fileBytes = ''
      api
        .queryImgfileBytes({
          fileId,
        })
        .then((res) => {
          if (res.code == 0) {
            fileBytes = res.data.fileBytes
          } else {
            fileBytes = ''
          }
          callback && callback({ fileBytes, fileType })
        })
    },
    formatDate(date) {
      if (!date) {
        return ''
      }
      return date.slice(0, 4) + '-' + date.slice(4, 6) + '-' + date.slice(6, 8)
    },
    getTaskStatusList(val) {
      const filterData = this.statusList.filter(({ value }) => value == val)[0]
      return filterData ? filterData.name : ''
    },
    formatDateTime(date) {
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:MM:SS')
    },
    getMosaicNum(str, beginLength, endLength) {
      beginLength = beginLength || 0
      endLength = endLength || 0
      if (str == null || str == undefined) {
        return str
      } else {
        str = str.toString()
        if (str.length <= beginLength + endLength) {
          return str
        } else {
          return (
            str.slice(0, beginLength) +
            '*'.repeat(str.length - beginLength - endLength) +
            (endLength > 0 ? str.slice(-endLength) : '')
          )
        }
      }
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: '#282828';
}

.flow_col {
  word-break: break-all;
  min-height: 45px;
  margin: 20px 0px;
  padding-right: 25px;
}

.flow_col span {
  float: left;
  margin-right: 20px;
}

.flow_row .flow_col:not(:last-child):after {
  content: '';
  min-width: 1px;
  min-height: 45px;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: '';
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}

.idcardImg {
  display: inline-block;
  margin-right: 10px;
  margin-top: 10px;
}

.idcardImg img {
  width: 335px;
  height: 200px;
}
.idcardImg h4 {
  text-align: center;
  background-color: rgb(217, 233, 245);
}
.placeholder {
  height: 40px;
}
</style>
