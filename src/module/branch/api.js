// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {

  /**
   * 查询营业部详细信息
   * @description:
   * @param {*}
   * @return {*}
   */
  @Parameters(["bcBranchInfoId"])
  queryBranch() {
    return this.services.initGet({
      reqUrl: "bcBranchInfo/query",
      param: this.param,
    });
  }

  /**
   * 营业部信息修改
   * @description:
   * @param {*}
   * @return {*}
   */
  @Parameters([
    "branchId",
    "lat",
    "lot",
    "branchIp"
  ])
  branchEdit() {
    return this.services.initPost({
      reqUrl: "bcBranchExtinfo/edit",
      param: this.param,
    });
  }


  /**
   * 查询省市区树
   * @description:
   * @param {*}
   * @return {*}
   */
  @Parameters(["addressName"])
  queryTree() {
    return this.workServices.initGet({
      reqUrl: "address/list",
      param: this.param,
    });
  }

}

export default new api();
