<template>
  <a-modal :title="`${this.dataType == 'add' ? '新增' : '修改'}营业部`" :width="700" v-model="isvisible" ok-text="确认" cancel-text="取消" @ok="submit" @cancel="getModuleReset"  :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :rules="rules">
      <a-form-model-item label="营业部IP" prop="branchIp">
        <a-input v-model="form.branchIp" placeholder="请输入营业部IP" />
      </a-form-model-item>
      <a-form-model-item label="纬度" prop="lat">
        <a-input v-model="form.lat" placeholder="请输入纬度" />
      </a-form-model-item>
      <a-form-model-item label="经度" prop="lot">
        <a-input v-model="form.lot" placeholder="请输入经度" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
// 表单默认参数
const defaultForm = {
  branchId: undefined,    //Y 营业部id
  lat: undefined,      //N 纬度
  lot: undefined,    //N 经度
  branchIp: undefined
};
import modalMixed from '@u/modalMixed';
export default {
  data() {
    return {
      // 表单对象
      form: JSON.parse(JSON.stringify(defaultForm)),
      rules: {
        branchIp: [{ required: true, message: '营业部IP不能为空', trigger: 'blur' }],
      },
      filedShow: false,
      // 异步加载
      confirmLoading: false
    }
  },
  props: {
    // 是否显示弹窗
    visible: {
      type: Boolean,
      default: false
    },
    branchId: {
      type: Number,
      default: null
    }
  },
  inject: ["api"],
  mixins: [modalMixed],
  watch: {
    isvisible(newVal) {
      this.form.branchId = this.branchId;
      if (newVal && this.branchId != '') {
        this.queryBranch();
      }
    }
  },
  methods: {
    // 重置对应的表单
    getModuleReset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {
        branchId: undefined,    //Y 营业部id
        lat: undefined,      //N 纬度
        lot: undefined,    //N 经度
        branchIp: undefined
      }
    },
    queryBranch() {
      let _this = this;
      this.api.queryBranch({ bcBranchInfoId: this.branchId }).then(res => {
        if (res.code != 0) return;
        _this.form = res.data;
      });
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let func = ({ code, msg }) => {
          if (code != 0) {
            this.confirmLoading = false;
            return this.$message.error(`营业部信息修改失败：${msg}`);
          }
          this.$message.success(`营业部信息修改成功！`);
          this.confirmLoading = false;
          // 关闭弹窗
          this.isvisible = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.getModuleReset();

        }
        this.api.branchEdit(param).then(func);
      })
    }
  },
}
</script>

<style lang="scss" scoped>
</style>