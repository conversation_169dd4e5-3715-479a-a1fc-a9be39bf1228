<template>
  <a-card title="营业部列表" class="management" :bordered="false">
    <div class="searchFrom">
      <a-form-model layout="inline" :model="searchFrom">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="营业部编号">
            <a-input
              v-model="searchFrom.branchNo"
              placeholder="请输入营业部编号"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item label="营业部名称">
            <a-input
              v-model="searchFrom.branchName"
              placeholder="请输入营业部名称"
            />
          </a-form-model-item>
          <a-form-model-item label="省市选择">
            <a-tree-select
              :treeData="areaList"
              :replaceFields="replaceFields"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder="请选择省份或城市"
              allow-clear
              v-model="treeValue"
            >
            </a-tree-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>

    <div class="access-table">
      <tk-table
        :tableData.sync="comlun"
        :intercept-response="intercept_response"
        :getMethods="'bc-manage-server/bcBranchInfo/page'"
        :isSelected="true"
        tableId="branchId"
        :isPaging="true"
        ref="table"
        :selectedRowKeys.sync="selectedRowKeys"
        :tableFrom="searchFrom"
      >
        <template slot="branchId" slot-scope="data">
          <a-button type="link" @click="click(data)"> 编辑 </a-button>
        </template>
      </tk-table>
    </div>
    <!-- 弹窗存放元素 -->
    <div>
      <!-- 修改 -->
      <dataForm
        :visible.sync="setShow"
        :branchId="branchId"
        @success="refresh"
      />
    </div>
  </a-card>
</template>

<script>
import api from "./api";
import dataForm from "./module/addOrEditBranch";
export default {
  components: { dataForm},
  provide: { api: api },
  data() {
    return {
      regionList: undefined, //地区列表
      provinceList: undefined, //省列表
      cityList: undefined, //市区列表
      uploadShow: false, //判断是否导入
      addShow: false, // 显示新增弹窗
      setShow: false, // 显示修改弹窗
      // 用户管理是否显示
      userShow: false,

      branchId: undefined, // 营业部id
      val: "",
      searchFrom: {}, // 搜索条件
      selectedRowKeys: [], // 筛选条件
      // 展示列字段
      comlun: [
        { field: "branchNo", label: "营业部编号", isSorter: true },
        { field: "branchName", label: "营业部名称",  },
        {
          field: "provinceId",
          label: "所属省份",
          filter: (item) =>
            item ? this.dataFilter(this.regionList, item) : "",
          
        },
        {
          field: "cityId",
          label: "所属城市",
          filter: (item) =>
            item ? this.dataFilter(this.provinceList, item) : "",
          
        },
        { field: "address", label: "地址", isEllipsis: true },
        { field: "branchIp", label: "营业部IP" },
        { field: "lot", label: "经度" },
        { field: "lat", label: "纬度" },
        { field: "branchId", label: "操作", align: "center", fixed: "right" },
      ],
      replaceFields: {
        children: "children",
        title: "label",
        key: "id",
        value: "key",
      },
      areaList:[],
      treeValue:null
    };
  },
  created() {
    this.queryArea();
  },

  methods: {
    dataFilter(data, item) {
      if (!data || data.length <= 0) return "";
      let obj = data.filter((n) => n.key == item)[0];
      if (!obj) return "";
      return obj.value;
    },
    //获取省市区数据
    queryArea() {
      let _this = this;
      _this.regionList = [];
      _this.provinceList = [];
      _this.cityList = [];
      api.queryTree({ addressName: "" }).then((res) => {
        if (res.code == 0) {
          _this.areaList = res.data;
          _this.areaList.forEach((v) => {
            _this.regionList.push({ value: v.label, key: v.id });
            if (v.children && v.children.length > 0) {
              if (v.children == null) return;
              v.children.forEach((v1) => {
                _this.provinceList.push({
                  value: v1.label,
                  key: v1.id,
                })
                v1.children = []
                if (v1.children && v1.children.length > 0) {
                  v1.children.forEach((v2) => {
                    _this.cityList.push({
                      value: v2.label,
                      key: v2.id,
                    });
                  });
                }
              });
            } else {
              if (!_this.tableData.provinceId) {
                v.children.forEach((v1) => {
                  v1.children.forEach((v2) => {
                    _this.cityList.push({
                      value: v2.label,
                      key: v2.id,
                    });
                  });
                });
              }
            }
          });
        }
      });
    },

    refresh() {
      // 刷新列表
      this.$refs.table.getTableData(true);
    },
    query() {
      if(this.treeValue){
        let arr = this.treeValue.split('-')
        this.searchFrom.provinceId = Number(arr[0])
        this.searchFrom.cityId = Number(arr[1])
      }
      this.$refs.table.getTableData();
    },

    // 表单重置
    reset() {
      this.treeValue = null
      this.searchFrom.branchNo = null;
      this.searchFrom.branchName = null;
      this.searchFrom.provinceId = null
      this.searchFrom.cityId = null
      // this.$refs.table.getTableData();
    },
    click({ branchId }) {
      // 用户点击操作修改按钮
      this.branchId = branchId;
      this.setShow = true;
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
