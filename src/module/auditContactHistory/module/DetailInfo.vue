<template>
    <div>
        <a-layout>
            <a-layout-header style="background: #fff;">
                <a-row class="pg-header pg-header-top">
                    视频见证详情
                </a-row>
            </a-layout-header>
            <a-layout-content style="background: #f9f9f9; padding: 10px 25px 90px 25px">
                <div>
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="content-border">
                            <div class="list_header">
                                <a-row>
                                    <a-col :span="6" class="pop-title">证件信息</a-col>
                                </a-row>
                            </div>
                            <viewer class="idcardImg" v-for="(item, index) in clientImgArr" :key="index">
                                <h4>{{ archiveTypeList[item.archiveType] }}</h4>
                                <img width="100%" style="cursor: pointer" :src="item.imgUrl" />
                            </viewer>
                        </div>
                    </a-row>
                </div>
                <div>
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="content-border">
                            <div class="list_header">
                                <a-row>
                                    <a-col :span="6" class="pop-title">见证视频</a-col>
                                </a-row>
                            </div>
                            <a-row>
                                <a-col class="video_col">
                                    <a-card :bordered="false" class="self">
                                        <video width="600" height="400" controls="controls" crossorigin="anonymous"
                                            :src="clientSingleVideoUrl">
                                            <!-- src="http://172.28.129.177:8081/bc-manage-server/gjKhHis/archive/video/download?file_id=1129004291512733696" -->
                                            <source :src="clientSingleVideoUrl" type="video/mp4">
                                        </video>
                                    </a-card>
                                    <div class="content-borders">
                                        <a-table :dataSource="videoList" :columns="videoColumns" :pagination="false"
                                            bordered>
                                            <template slot="action" slot-scope="text, data">
                                                <span>
                                                    <a @click="playVideo(data)"> 播放</a>
                                                </span>
                                            </template>
                                        </a-table>
                                    </div>
                                </a-col>

                            </a-row>
                        </div>
                    </a-row>
                </div>
                <div>
                    <div class="list_header">
                        <a-row>
                            <a-col :span="6" class="pop-title">公安信息</a-col>
                        </a-row>
                    </div>
                    <a-descriptions bordered>
                        <a-descriptions-item label="客户姓名">
                            {{ userInfo.name }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件号码">
                            {{ userInfo.cardNum }}
                        </a-descriptions-item>
                        <a-descriptions-item label="客户性别">
                            {{ userInfo.gender === '0' ? '男' : '女' }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件地址">
                            {{ userInfo.idAddress }}
                        </a-descriptions-item>
                        <a-descriptions-item label="出生日期">
                            {{ userInfo.birthday }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件签发机关">
                            {{ userInfo.issuedDepart }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件开始日期">
                            {{ userInfo.idBegindate }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件结束日期">
                            {{ userInfo.idEnddate }}
                        </a-descriptions-item>
                        <a-descriptions-item label="流水号">
                            {{ userInfo.clientId }}
                        </a-descriptions-item>
                        <a-descriptions-item label="见证状态">
                            {{ getDictText("witnessStatus", detailInfo.witnessFlag) }}
                        </a-descriptions-item>
                        <a-descriptions-item label="见证方式">
                            {{ getDictText("openType", detailInfo.openType) || '离线' }}
                        </a-descriptions-item>
                        <a-descriptions-item label="备注">
                            {{ detailInfo.memo }}
                        </a-descriptions-item>
                    </a-descriptions>
                </div>
            </a-layout-content>
        </a-layout>
    </div>
</template>

<script>
import api from '../api'

export default {
    name: "AuditContactDetail",
    data() {
        return {
            userInfo: {},
            detailInfo: {},
            clientImgArr: [],
            clientSingleVideoUrl: '',
            videoList: [],
            videoColumns: [
                {
                    title: "见证坐席",
                    dataIndex: "agentName",
                    key: "agentName",
                    width: 100,
                },
                {
                    title: "文件名",
                    dataIndex: "filename",
                    key: "filename",
                    width: 200,
                },
                {
                    title: "文件时间",
                    dataIndex: "time",
                    key: "time",
                    width: 100,
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 60,
                    scopedSlots: { customRender: "action" },
                },
            ],
            dictArr: {
                resultOptions: [
                    { value: 1, label: '通过' },
                    { value: 2, label: '中断' },
                    { value: 3, label: '驳回' },
                    { value: 4, label: '复审通过' },
                    { value: 5, label: '复审驳回' },
                ],
                witnessStatus: [
                    { value: '0', label: '未通过' },
                    { value: '1', label: '通过' },
                    { value: '2', label: '中断' },
                ],
                openType: [
                    { value: '3', label: '在线' },
                    { value: '4', label: '离线' },

                ],

            },
            archiveTypeList: {
                1: '证件正面',
                2: '证件反面',
                3: '客户头像',
                4: '视频见证录像',
                5: '辅证图片',
                501: '营业执照',
                502: '就业证明',
                503: '居住证',
                7: '单向视频',
                8: '公安头像',
            },
        }
    },

    provide: [api],
    created() {
        if (this.$route.query.processId) {
            this.queryClientDetail()
        }
    },
    methods: {
        playVideo(record) {
            console.log('record====', record)
            this.clientSingleVideoUrl = '/bc-manage-server/gjKhHis/archive/video/download?file_id=' + record.archiveid;
        },
        queryClientDetail() {
            this.detailInfo = JSON.parse(this.$route.query.details);
            api
                .queryItem({
                    clientId: this.$route.query.processId,

                })
                .then((res) => {
                    if (res.code == 0) {
                        this.clientImgArr.push({ imgUrl: res.data.imageIdcardFront, archiveType: 1, })
                        this.clientImgArr.push({ imgUrl: res.data.imageIdcardBack, archiveType: 2, })
                        this.clientImgArr.push({ imgUrl: res.data.imageClientHead, archiveType: 3, })
                        this.clientImgArr.push({ imgUrl: res.data.imagePolice, archiveType: 8, })

                        this.videoList = res.data.videoList;
                        if (res?.data?.videoList?.length > 0) {
                            this.clientSingleVideoUrl = '/bc-manage-server/gjKhHis/archive/video/download?file_id=' + res.data.videoList[0].archiveid;
                        }
                        setTimeout(() => {
                            this.userInfo = res.data
                        }, 0)
                    } else {
                        this.$message.error(res.message)
                    }
                })
        },

        getDictText(sourceArray, idType) {
            let idTypeItem = this.dictArr[sourceArray].filter(
                (item) => item.value == idType
            )
            return idTypeItem[0]?.label;
        },

    },
}
</script>

<style lang="scss" scoped>
.video_col {
    display: flex;
}

.content-borders {
    width: 100%;
    margin-left: 10px;
}

.idcardImg {
    display: inline-block;
    margin-right: 10px;
    margin-top: 10px;
}

.idcardImg img {
    width: 335px;
    height: 200px;
}

.idcardImg h4 {
    text-align: center;
    background-color: rgb(217, 233, 245);
}

.self {
    text-align: center;
}
</style>

