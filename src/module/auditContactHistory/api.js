// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 视频见证详情
   * @param {Object} param
   * - clientId {String} 流水号-客户号
   */
  @Parameters(["clientId"])
  queryItem() {
    return this.services.initGet({
      reqUrl: "gjKhHis/margin/migration/contact/queryDetail",
      param: this.param,
    });
  }

  /**
   * 查询文件信息
   * @param {Object} param
   * - file_id {String} 文件id
   */
  @Parameters(["file_id"])
  queryImgfileUrl() {
    return this.services.initGet({
      reqUrl: "gjKhHis/archive/picture/download",
      param: this.param,
    });
  }
}

export default new api();
