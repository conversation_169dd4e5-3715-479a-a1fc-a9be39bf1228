<template>
  <a-modal
    :title="`${typeTitle}登记协议`"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 10 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="标签名" prop="regName">
        <a-input v-model="form.regName" placeholder="请输入标签名" />
      </a-form-model-item>
      <!-- <a-form-model-item label="备注" prop="remark">
        <a-input v-model="form.remark" placeholder="请输入备注" />
      </a-form-model-item> -->
    </a-form-model>
  </a-modal>
</template>
<script>
export default {
  name: 'treeOper',
  inject: ['api'],
  data() {
    return {
      form: {}, // 协议分组添加表单
      // 表单权限验证
      rules: {
        regName: [
          {
            required: true,
            message: '登记中文名不能为空',
            trigger: 'blur'
          }
        ],
        remark: [
          {
            required: true,
            message: '备注不能为空',
            trigger: 'blur'
          }
        ]
      },
      // 异步加载
      confirmLoading: false
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {}
    },
    // 操作类型
    operationType: {
      type: String,
      default: 'add'
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      }
    },
    typeTitle() {
      return this.operationType == 'add' ? '添加' : '修改';
    }
  },
  watch: {
    isPopShow(n) {
      if (n && this.operationType != 'add' && this.parameterData.regId) {
        this.modify();
      } else {
        this.reset();
      }
    }
  },
  methods: {
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = Object.assign({}, this.parameterData);
    },
    // 提交协议分组创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        if (this.operationType == 'add') {
          this.form.parentId = this.form.groupId;
        }
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(`${this.typeTitle}登记协议失败：${msg}`);
          this.$message.success(`${this.typeTitle}登记协议成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit('success');
          // 重置表单
          this.reset();
        };
        this.operationType == 'add'
          ? this.api.addAgreeRegister(param).then(callback)
          : this.api.editAgreeRegister(param).then(callback);
      });
    }
  }
};
</script>
