<template>
	<div>
		<div class="ant-table-title">
			<a-button type="primary" icon="plus" @click="add">新增</a-button>
			<a-button
				icon="delete"
				type="danger"
				:disabled="selectedRowKeys.length <= 0"
				@click.stop="remove"
				>删除</a-button
			>
		</div>
		<a-table
			ref="table"
			:row-selection="rowSelection"
			:columns="columns"
			:data-source="data"
			:pagination="false"
		>
			<a-switch
				slot="isNeed"
				slot-scope="item"
				v-model="item.isNeed"
				@change="submit"
			></a-switch>
			<span slot="resultSource" slot-scope="item">{{
				item.resultSource === "1" ? "受理上下文" : "任务出参"
			}}</span></a-table
		>
		<ContextTransfer
			:isPopShow.sync="isPopShowTransfer"
			:includeKeys="includeKeys"
			:data="transferData"
			@update="update"
		/>
	</div>
</template>

<script>
	import ContextTransfer from "./ContextTransfer";
	export default {
		components: {
			ContextTransfer
		},
		inject: ["api"],
		data() {
			return {
				isPopShowTransfer: false,
				selectedRowKeys: [],
				columns: [
					{
						key: "resultKey",
						title: "参数key",
						dataIndex: "resultKey"
					},
					{
						key: "resultDes",
						title: "参数名",
						dataIndex: "resultDes"
					},
					{
						key: "isNeed",
						title: "是否必填",
						scopedSlots: { customRender: "isNeed" }
					},
					{
						key: "resultSource",
						title: "入参来源",
						scopedSlots: { customRender: "resultSource" }
					},
					{
						key: "bizName",
						title: "入参任务",
						dataIndex: "bizName"
					}
				],
				data: [],
				transferData: [],
				includeKeys: []
			};
		},
		props: {
			// 数据权限分组编号
			regId: {
				type: String
			}
		},
		computed: {
			rowSelection() {
				return {
					onChange: (selectedRowKeys, selectedRows) => {
						this.selectedRowKeys = selectedRows.map(item => item.id);
					}
				};
			}
		},
		watch: {
			regId() {
				this.getTransferDate();
				this.getTableDate();
			}
		},
		methods: {
			getTableDate() {
				this.api
					.getBcBatchResultList({ bizType: this.regId })
					.then(data => {
						this.data = data.data;
						this.data.forEach(item => {
							if (item.isNeed === "0") {
								item.isNeed = false;
							} else if (item.isNeed === "1") {
								item.isNeed = true;
							}
						});
						this.includeKeys = this.data.map(item => item.resultKey);
					});
			},

			getTransferDate() {
				this.api
					.getBcBatchTaskOutAllParams({
						bizType: this.regId,
					})
					.then(data => {
						this.transferData = data.data;
					});
			},

			update(data) {
				let params = data.map(item => ({
					bizType: this.regId,
					resultKey: item.key,
					resultDes: item.title,
					resultSource: "1",
					isNeed: "1"
				}));
				this.api.getBcBatchResultAdds(params).then(() => {
					this.getTableDate();
				});
			},

			add() {
				this.isPopShowTransfer = true;
			},

			remove() {
				this.$confirm({
					title: "删除参数",
					content: () => <p>确定删除？</p>,
					okText: "确定",
					cancelText: "取消",
					onOk: () => {
						let callBack = ({ code, msg }) => {
							if (code != 0)
								return this.$message.error(`删除失败：${msg}`);
							this.$message.success("删除成功！");
							this.getTableDate();
							this.selectedRowKeys = [];
						};
						this.api
							.getBcBatchResultDeletes({
								ids: this.selectedRowKeys.join(",")
							})
							.then(callBack);
					}
				});
			},

			submit() {
				let params = this.data.map(item => ({
					id: item.id,
					bizType: this.regId,
					resultKey: item.resultKey,
					resultDes: item.resultDes,
					resultSource: item.resultSource,
					isNeed: item.isNeed ? "1" : "0"
				}));
				this.api.getBcBatchResultEdits(params).then(() => {
					this.getTableDate();
				});
			}
		}
	};
</script>