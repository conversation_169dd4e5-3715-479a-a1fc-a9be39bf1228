// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
	 * 获取业务类型名称list
	 * @param {Object} param
	 */
	@Parameters()
	getBcBizTypeList() {
		return this.services.initGet({
			reqUrl: 'bcBusiness/list',
			param: this.param,
		});
	}

	/**
	 * 查询已上架的业务
	 * @description:
	 * @param {*}
	 */
	@Parameters(['isShelfed'])
	getBcBusinessShelfList() {
		return this.services.initGet({
			reqUrl: 'bcBusinessShelf/list',
			param: this.param,
		});
	}

	/**
	 * 全量元数据
	 * @description:
	 * @param {*}
	 */
	@Parameters([])
	getBcBatchTaskOutAllParams() {
		return this.services.initGet({
			reqUrl: 'bcBatchTaskOut/allParams',
			param: this.param,
		});
	}

	/**
	 * 当前业务所有上下文及taskSort前置任务出参(默认上下文中所有)
	 * @description:
	 * @param {*}
	 */
	@Parameters(['taskSort', 'bizType'])
	getBcBatchTaskOutParamList() {
		return this.services.initGet({
			reqUrl: 'bcBatchTaskOut/paramList',
			param: this.param,
		});
	}

	/**
	 * 批量添加结果定义
	 * @description:
	 * @param {*}
	 */
	@Parameters(['_data'])
	getBcBatchResultAdds() {
		return this.services.initPost({
			reqUrl: 'bcBatchResult/adds',
			param: this.param,
		});
    }
    
    /**
	 * 批量修改结果定义
	 * @description:
	 * @param {*}
	 */
	@Parameters(['_data'])
	getBcBatchResultEdits() {
		return this.services.initPost({
			reqUrl: 'bcBatchResult/edits',
			param: this.param,
		});
	}

	/**
	 * 批量删除结果定义
	 * @description:
	 * @param {*}
	 */
	@Parameters(['ids'])
	getBcBatchResultDeletes() {
		return this.services.initPost({
			reqUrl: 'bcBatchResult/deletes',
			param: this.param,
		});
	}

	/**
	 * 任务结果定义列表
	 * @description:
	 * @param {*}
	 */
	@Parameters(['bizType'])
	getBcBatchResultList() {
		return this.services.initGet({
			reqUrl: 'bcBatchResult/list',
			param: this.param,
		});
	}
}

export default new api();
