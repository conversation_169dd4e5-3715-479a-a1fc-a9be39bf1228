<template>
  <a-card title="档案信息列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="档案编号">
            <a-input
              v-model="tableForm.archivesNo"
              placeholder="请输入档案编号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="档案名称">
            <a-input
              v-model="tableForm.name"
              placeholder="请输入档案名称"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="档案类型">
            <a-select
              v-model="tableForm.archivesType"
              placeholder="请选择档案类型"
              allowClear
            >
              <a-select-option
                v-for="item in archivesTypeList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.key }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="状态">
            <a-select v-model="tableForm.status" placeholder="请选择状态">
              <a-select-option
                v-for="(item, index) in ['无效', '有效']"
                :key="index"
                :value="index"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcArchives/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add">
            新增
          </a-button>
          <a-button
            icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove"
          >
            删除
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a @click.stop="modify(data)">修改</a>
        </template>
      </tk-table>
    </div>
    <addOrEdit
      :isPopShow.sync="isPopShow"
      @success="query"
      :parameterData="selectData"
    ></addOrEdit>
  </a-card>
</template>

<script>
import api from "./api";
import addOrEdit from "./module/addOrEdit";

// 默认表单属性
const defaultForm = {
  archivesNo: "",
  name: "",
  archivesType: undefined,
  status: undefined,
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "archivesNo",
          label: "档案编号",
          isSorter: false,
        },
        {
          field: "name",
          label: "档案名称",
          isSorter: false,
        },
        {
          field: "archivesType",
          label: "档案类型",
          isSorter: false,
          filter: (item) => this.filterValue(item),
        },
        {
          field: "collectCondition",
          label: "采集条件",
          isSorter: false,
        },
        {
          field: "pageMax",
          label: "最大页数",
          isSorter: true,
        },
        {
          field: "pageMin",
          label: "最小页数",
          isSorter: true,
        },
        {
          field: "status",
          label: "状态",
          isSorter: false,
          filter: (item) => (item === 1 ? "有效" : "无效"),
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      tableForm: {
        archivesNo: "",
        name: "",
        archivesType: undefined,
        status: undefined,
      },
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isPopShow: false, //弹窗是否显示
      selectData: {},
      archivesTypeList: [
        {
          key: "证件",
          value: "01",
        },
        {
          key: "证明文件",
          value: "02",
        },
        {
          key: "协议",
          value: "03",
        },
        {
          key: "流程",
          value: "04",
        },
      ],
    };
  },
  provide: { api: api },
  components: {
    addOrEdit,
  },
  methods: {
    filterValue(value) {
      let obj = this.archivesTypeList.find((item) => item.value === value);
      return obj ? obj.key : value;
    },
    add() {
      this.isPopShow = true;
      this.selectData = { archivesTypeList: this.archivesTypeList };
    },
    remove() {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deletePre();
        },
      });
    },
    deletePre() {
      let bcArchivesIds = this.selectedRowKeys.join(",");
      api
        .deletesArchivesl({ bcArchivesIds })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.query();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    modify(data) {
      this.isPopShow = true;
      this.selectData = { ...data, archivesTypeList: this.archivesTypeList };
    },
    tableFormFilter(param) {
      return param;
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
  },
};
</script>

<style lang="scss" scoped></style>
