// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

	/**
	 * 新增档案信息
	 * @param {*}
	 */
	@Parameters(['_data'])
	addArchives() {
		return this.services.initPost({
			reqUrl: 'bcArchives/add',
			param: this.param,
		});
	}

	/**
	 * 修改档案信息
	 * @param {*}
	 */
   @Parameters(['_data'])
   editArchives() {
     return this.services.initPost({
       reqUrl: 'bcArchives/edit',
       param: this.param,
     });
   }

	/**
	 * 删除多个档案信息
	 * @param {Object} param
	 * - bcArchivesIds {String} 参数主键ID列表，多个以“，”隔开
	 *
	 */
	@Parameters(['bcArchivesIds'])
	deletesArchivesl() {
		return this.services.initPost({
			reqUrl: 'bcArchives/deletes',
			param: this.param,
		});
	}

	/**
	 * 查询指定档案信息
	 * @description:
	 * @param {Object} param
	 * - id {String} 参数主键ID
	 */
	@Parameters(['id'])
	getArchives() {
		return this.services.initGet({
			reqUrl: 'bcArchives/query',
			param: this.param,
		});
	}

}

export default new api();
