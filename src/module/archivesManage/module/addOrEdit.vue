<template>
  <a-modal
    :title="`${parameterData.id ? '修改' : '新增'}档案信息`"
    class="data-ant-module"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
      :rules="rules"
    >
      <a-form-model-item label="档案编号" prop="archivesNo">
        <a-input v-model="form.archivesNo" placeholder="请输入档案编号">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="档案名称" prop="name">
        <a-input v-model="form.name" placeholder="请输入档案名称"> </a-input>
      </a-form-model-item>
      <a-form-model-item label="档案类型" prop="archivesType">
        <a-select
          v-model="form.archivesType"
          placeholder="请选择档案类型"
          show-search
          option-filter-prop="children"
          :allowClear="true"
        >
          <a-select-option
            v-for="item in parameterData.archivesTypeList"
            :value="item.value"
            :key="item.value"
          >
            {{ item.key }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="最小页数" prop="pageMin">
        <a-input
          v-model="form.pageMin"
          placeholder="请输入最小页数"
          @input="format($event, 'pageMin')"
        >
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="最大页数" prop="pageMax">
        <a-input
          v-model="form.pageMax"
          placeholder="请输入最大页数"
          @input="format($event, 'pageMax')"
        >
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="采集条件">
        <a-input v-model="form.collectCondition" placeholder="请输入采集条件">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="状态" prop="status">
        <a-radio-group v-model="form.status">
          <a-radio
            v-for="(item, index) in ['无效', '有效']"
            :key="index"
            :value="index"
          >
            {{ item }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="档案信息示例图">
        <a-upload
          name="file"
          list-type="picture-card"
          :multiple="true"
          :data="updataType"
          :file-list="fileList"
          action="/bc-manage-server/file/upload"
          @preview="handlePreview"
          @change="handleChange"
        >
          <div v-if="fileList.length < 1">
            <a-icon type="plus" />
            <div class="ant-upload-text">
              图片上传
            </div>
          </div>
        </a-upload>
      </a-form-model-item>
    </a-form-model>
    <a-modal
      :visible="previewVisible"
      :footer="null"
      @cancel="previewVisible = false"
    >
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  archivesNo: "",
  name: "",
  archivesType: undefined,
  collectCondition: "",
  pageMin: "",
  pageMax: "",
  status: 1,
  exampleDiagram: "",
};

// 注册当前请求对应的上下文请求组
export default {
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        archivesNo: [
          { required: true, message: "档案编号不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "档案名称不能为空", trigger: "blur" },
        ],
        archivesType: [
          { required: true, message: "请选择档案类型", trigger: "blur" },
        ],
        pageMin: [
          { required: true, message: "最小页数不能为空", trigger: "blur" },
        ],
        pageMax: [
          { required: true, message: "最大页数不能为空", trigger: "blur" },
        ],
      },
      confirmLoading: false,
      updataType: {
        type: "image",
      },
      fileList: [],
      previewVisible: false,
      previewImage: "",
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    handlePreview(info) {
      if (info.url) {
        this.previewImage = info.url;
        this.previewVisible = true;
      }
      if (info.response && info.response.data) {
        this.previewImage =
          window.$hvue.customConfig.fileUrl + info.response.data;
        this.previewVisible = true;
      }
    },
    // 选择图片修改
    handleChange(info) {
      let fileList = [...info.fileList];
      fileList = fileList.slice(-2);
      let resImg = info.file.response && info.file.response.data;
      fileList = fileList.map((file) => {
        if (file.response) {
          file.url = window.$hvue.customConfig.fileUrl + file.response.data;
        }
        return file;
      });
      this.fileList = fileList;
      this.form.exampleDiagram = resImg;
    },
    format(value, type) {
      this.form[type] = this.form[type].replace(/[^\d]/g, "");
      if (this.form[type] === "0") {
        this.form[type] = this.form[type].replace("0", "");
      }
    },
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
      this.fileList = [];
    },
    // 用户点击修改进入
    query() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.api
        .getArchives({
          id: this.parameterData.id,
        })
        .then((res) => {
          if (res.code != 0) return;
          this.form = res.data;
          this.fileList = this.form.exampleDiagram
            ? [
                {
                  uid: "1",
                  name: "image.png",
                  status: "done",
                  url:
                    window.$hvue.customConfig.fileUrl +
                    this.form.exampleDiagram,
                },
              ]
            : [];
        });
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        param.pageMin = Number(param.pageMin);
        param.pageMax = Number(param.pageMax);
        if (this.fileList.length === 0) {
          param.exampleDiagram = "";
        }
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `档案信息${this.parameterData.id ? "修改" : "新增"}失败：${msg}`
            );
          this.$message.success(
            `档案信息${this.parameterData.id ? "修改" : "新增"}成功！`
          );
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        this.parameterData.id
          ? this.api.editArchives(param).then(callback)
          : this.api.addArchives(param).then(callback);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
