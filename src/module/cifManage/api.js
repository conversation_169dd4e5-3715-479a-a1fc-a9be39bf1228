// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询客户信息表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifClientInfo() {
    return this.services.initGet({
      reqUrl: "cifClientInfo/list",
      param: this.param,
    });
  }

  /**
   * 修改客户信息表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifClientInfo() {
    return this.services.initPost({
      reqUrl: "cifClientInfo/edit",
      param: this.param,
    });
  }

  /**
   * 查询账户资产信息表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifAssets() {
    return this.services.initGet({
      reqUrl: "cifAssets/list",
      param: this.param,
    });
  }

  /**
   * 修改账户资产信息表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifAssets() {
    return this.services.initPost({
      reqUrl: "cifAssets/edit",
      param: this.param,
    });
  }

  /**
   * 查询客户适当性表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifClientElig() {
    return this.services.initGet({
      reqUrl: "cifClientElig/list",
      param: this.param,
    });
  }

  /**
   * 修改客户适当性表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifClientElig() {
    return this.services.initPost({
      reqUrl: "cifClientElig/edit",
      param: this.param,
    });
  }

  /**
   * 查询客户适当性匹配关系表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifClientProdEligRel() {
    return this.services.initGet({
      reqUrl: "cifClientProdEligRel/list",
      param: this.param,
    });
  }

  /**
   * 修改客户适当性匹配关系表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifClientProdEligRel() {
    return this.services.initPost({
      reqUrl: "cifClientProdEligRel/edit",
      param: this.param,
    });
  }

  /**
   * 查询客户权限
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifClientRight() {
    return this.services.initGet({
      reqUrl: "cifClientRight/list",
      param: this.param,
    });
  }

  /**
   * 修改客户权限
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifClientRight() {
    return this.services.initPost({
      reqUrl: "cifClientRight/edit",
      param: this.param,
    });
  }

  /**
   * 查询证券账户信息表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifStockAccount() {
    return this.services.initGet({
      reqUrl: "cifStockAccount/list",
      param: this.param,
    });
  }

  /**
   * 修改证券账户信息表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifStockAccount() {
    return this.services.initPost({
      reqUrl: "cifStockAccount/edit",
      param: this.param,
    });
  }

  /**
   * 查询查询首次交易日信息表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifStockFirstDate() {
    return this.services.initGet({
      reqUrl: "cifStockFirstDate/list",
      param: this.param,
    });
  }

  /**
   * 修改查询首次交易日信息表列表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifStockFirstDate() {
    return this.services.initPost({
      reqUrl: "cifStockFirstDate/edit",
      param: this.param,
    });
  }

  /**
   * 查询日日均信息
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifAverageAsset() {
    return this.services.initGet({
      reqUrl: "cifAverageAsset/list",
      param: this.param,
    });
  }

  /**
   * 修改日日均信息
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifAverageAsset() {
    return this.services.initPost({
      reqUrl: "cifAverageAsset/edit",
      param: this.param,
    });
  }

  /**
   * 查询受益人
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifBenefitPerson() {
    return this.services.initGet({
      reqUrl: "cifBenefiter/list",
      param: this.param,
    });
  }

  /**
   * 修改受益人
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifBenefitPerson() {
    return this.services.initPost({
      reqUrl: "cifBenefiter/edit",
      param: this.param,
    });
  }

  /**
   * 查询受益人
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifBlacklist() {
    return this.services.initGet({
      reqUrl: "cifBlacklist/list",
      param: this.param,
    });
  }

  /**
   * 修改受益人
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifBlacklist() {
    return this.services.initPost({
      reqUrl: "cifBlacklist/edit",
      param: this.param,
    });
  }

  /**
   * 查询中登
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getCifBusinessTraceZd() {
    return this.services.initGet({
      reqUrl: "cifBusinessTraceZd/list",
      param: this.param,
    });
  }

  /**
   * 修改中登
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editCifBusinessTraceZd() {
    return this.services.initPost({
      reqUrl: "cifBusinessTraceZd/edit",
      param: this.param,
    });
  }

  /**
   * 查询控制人
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getcifControlPerson() {
    return this.services.initGet({
      reqUrl: "cifControllerInfo/list",
      param: this.param,
    });
  }

  /**
   * 修改控制人
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editcifControlPerson() {
    return this.services.initPost({
      reqUrl: "cifControllerInfo/edit",
      param: this.param,
    });
  }

  /**
   * 查询诚信记录
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getcifCreditRecord() {
    return this.services.initGet({
      reqUrl: "cifCreditRecord/list",
      param: this.param,
    });
  }

  /**
   * 修改诚信记录
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editcifCreditRecord() {
    return this.services.initPost({
      reqUrl: "cifCreditRecord/edit",
      param: this.param,
    });
  }

  /**
   * 查询资金账户信息
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getcifFundAccount() {
    return this.services.initGet({
      reqUrl: "cifFundAccount/list",
      param: this.param,
    });
  }

  /**
   * 修改资金账户信息
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editcifFundAccount() {
    return this.services.initPost({
      reqUrl: "cifFundAccount/edit",
      param: this.param,
    });
  }

  /**
   * 查询第二联系人
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getcifLinkmanInfo() {
    return this.services.initGet({
      reqUrl: "cifSecRelationInfo/list",
      param: this.param,
    });
  }

  /**
   * 修改第二联系人
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editcifLinkmanInfo() {
    return this.services.initPost({
      reqUrl: "cifSecRelationInfo/edit",
      param: this.param,
    });
  }

  /**
   * 查询税收信息
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getcifRevenueResidentType() {
    return this.services.initGet({
      reqUrl: "cifRevenueResidentType/list",
      param: this.param,
    });
  }

  /**
   * 修改税收信息
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editcifRevenueResidentType() {
    return this.services.initPost({
      reqUrl: "cifRevenueResidentType/edit",
      param: this.param,
    });
  }

  /**
   * 查询客户密码
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getcifClientPwd() {
    return this.services.initGet({
      reqUrl: "cifClientPwd/list",
      param: this.param,
    });
  }

  /**
   * 修改客户密码
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editcifClientPwd() {
    return this.services.initPost({
      reqUrl: "cifClientPwd/edit",
      param: this.param,
    });
  }

  /**
   * 查询客户账户资金信息
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getcifFund() {
    return this.services.initGet({
      reqUrl: "cifFund/list",
      param: this.param,
    });
  }

  /**
   * 修改客户账户资金信息
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  editcifFund() {
    return this.services.initPost({
      reqUrl: "cifFund/edit",
      param: this.param,
    });
  }

  /**
   * 新增客户号
   * @description:
   * @param {*}
   */
   @Parameters(["_data"])
   addcifClientInfo() {
     return this.services.initPost({
       reqUrl: "cifClientInfo/add",
       param: this.param,
     });
   }
}

export default new api();
