<template>
	<a-layout class="menu-content">
		<a-card title="CIF管理" :bordered="false">
			<div class="menu-right-limits">
				<div class="searchForm">
					<a-form-model layout="inline" :model="tableForm">
						<tkSelectForm @query="query">
							<a-form-model-item label="客户号">
								<a-input
									v-model="tableForm.client_id"
									:allowClear="true"
									placeholder="请输入客户号"
									@keyup.enter.native="query"
								>
								</a-input>
							</a-form-model-item>
							<!-- <a-form-model-item label="客户姓名">
								<a-input
									v-model="tableForm.client_name"
									:allowClear="true"
									placeholder="请输入客户姓名"
									@keyup.enter.native="query"
								>
								</a-input>
							</a-form-model-item> -->
						</tkSelectForm>
					</a-form-model>
				</div>
				<div class="table-content" style="display:flex">
					<div style="width:90%">
					<a-button style="margin-top:10px !important" type="primary" @click="isPopShow=true">新增客户号</a-button>  
						<basic
							ref="basic"
							v-if="selectTab === '1'"
							:tableForm="tableForm"
						/>
						<account
							ref="account"
							v-if="selectTab === '2'"
							:tableForm="tableForm"
						/>
						<suitability
							ref="suitability"
							v-if="selectTab === '3'"
							:tableForm="tableForm"
						/>
						<suitabilityMatch
							ref="suitabilityMatch"
							v-if="selectTab === '4'"
							:tableForm="tableForm"
						/>
						<rule
							ref="rule"
							v-if="selectTab === '5'"
							:tableForm="tableForm"
						/>
						<securityAccount
							ref="securityAccount"
							v-if="selectTab === '6'"
							:tableForm="tableForm"
						/>
						<marketDay
							ref="marketDay"
							v-if="selectTab === '7'"
							:tableForm="tableForm"
						/>
						<aveAset
							ref="aveAset"
							v-if="selectTab === '8'"
							:tableForm="tableForm"
						/>
						<benPerson
							ref="benPerson"
							v-if="selectTab === '9'"
							:tableForm="tableForm"
						/>
						<blackList
							ref="blackList"
							v-if="selectTab === '10'"
							:tableForm="tableForm"
						/>
						<ZDbusiness
							ref="ZDbusiness"
							v-if="selectTab === '11'"
							:tableForm="tableForm"
						/>
						<conPerson
							ref="conPerson"
							v-if="selectTab === '12'"
							:tableForm="tableForm"
						/>
						<creRecord
							ref="creRecord"
							v-if="selectTab === '13'"
							:tableForm="tableForm"
						/>
                        <foundAccount
							ref="foundAccount"
							v-if="selectTab === '14'"
							:tableForm="tableForm"
						/>
                        <linkmanInfo
							ref="linkmanInfo"
							v-if="selectTab === '15'"
							:tableForm="tableForm"
						/>
                        <revType
							ref="revType"
							v-if="selectTab === '16'"
							:tableForm="tableForm"
						/>
						<password
							ref="password"
							v-if="selectTab === '17'"
							:tableForm="tableForm"
						/>
						<accountAssets
							ref="accountAssets"
							v-if="selectTab === '18'"
							:tableForm="tableForm"
						/>
					</div>
					<div>
						<tkTightTree @select="select">
							<tk-tree
								class="ant-tree-switcher-no"
								:treeData="treeData"
								:selectedKeys.sync="selectedKeys"
							></tk-tree>
						</tkTightTree>
					</div>
				</div>
				<add :isPopShow.sync="isPopShow" @success="query"/>
			</div>
		</a-card>
	</a-layout>
</template>

<script>
	import api from "./api";
	import basic from "./module/basic";
	import account from "./module/account";
	import suitability from "./module/suitability";
	import suitabilityMatch from "./module/suitabilityMatch";
	import rule from "./module/rule";
	import securityAccount from "./module/securityAccount";
	import marketDay from "./module/marketDay";
	import aveAset from "./module/aveAset";
	import benPerson from "./module/benPerson";
	import blackList from "@/module/cifManage/module/blackList";
	import ZDbusiness from "@/module/cifManage/module/ZDbusiness";
	import conPerson from "@/module/cifManage/module/conPerson";
	import creRecord from "@/module/cifManage/module/creRecord";
    import foundAccount from "@/module/cifManage/module/foundAccount";
    import linkmanInfo from '@/module/cifManage/module/linkmanInfo'
    import revType from '@/module/cifManage/module/revType'
    import password from '@/module/cifManage/module/password'
    import accountAssets from '@/module/cifManage/module/accountAssets'
	import add from './module/add';
	export default {
		data() {
			return {
				selectTab: null,
				tableForm: {
					client_id: "",
					client_name: ""
				},
				selectedKeys: [],
				treeData: [
					{
						title: "客戶信息",
						key: "1"
					},
					{
						title: "账户资产信息",
						key: "2"
					},
					{
						title: "客户适当性",
						key: "3"
					},
					{
						title: "客户业务适当性匹配关系",
						key: "4"
					},
					// {
					// 	title: "客户权限",
					// 	key: "5"
					// },
					{
						title: "证券账户信息",
						key: "6"
					},
					// {
					// 	title: "首次交易日信息",
					// 	key: "7"
					// },
					{
						title: "日均资产信息",
						key: "8"
					},
					{
						title: "受益人信息",
						key: "9"
					},
					// {
					// 	title: "黑名单",
					// 	key: "10"
					// },
					// {
					// 	title: "中登业务操作留痕",
					// 	key: "11"
					// },
					{
						title: "控制人信息",
						key: "12"
					},
					{
						title: "客户诚信记录",
						key: "13"
					},
					{
						title: "资金账户信息",
						key: "14"
                    },
                    {
						title: "第二联系人资料",
						key: "15"
                    },
                    {
						title: "税收信息",
						key: "16"
					},
					{
						title: "客户密码",
						key: "17"
					},
					{
						title: "账户资金信息",
						key: "18"
					}
				],
				isPopShow:false
			};
		},
		provide: { api: api },
		components: {
			basic,
			account,
			suitability,
			suitabilityMatch,
			rule,
			securityAccount,
			marketDay,
			aveAset,
			benPerson,
			blackList,
			ZDbusiness,
			conPerson,
			creRecord,
            foundAccount,
            linkmanInfo,
            revType,
			password,
			accountAssets,
			add
		},
		methods: {
			select(data) {
				this.selectTab = data.key;
				this.$nextTick(() => {
					if (this.selectTab === "1") {
						this.$refs.basic && this.$refs.basic.init();
					} else if (this.selectTab === "2") {
						this.$refs.account && this.$refs.account.init();
					} else if (this.selectTab === "3") {
						this.$refs.suitability && this.$refs.suitability.init();
					} else if (this.selectTab === "4") {
						this.$refs.suitabilityMatch &&
							this.$refs.suitabilityMatch.init();
					} else if (this.selectTab === "5") {
						this.$refs.rule && this.$refs.rule.init();
					} else if (this.selectTab === "6") {
						this.$refs.securityAccount &&
							this.$refs.securityAccount.init();
					} else if (this.selectTab === "7") {
						this.$refs.marketDay && this.$refs.marketDay.init();
					} else if (this.selectTab === "8") {
						this.$refs.aveAset && this.$refs.aveAset.init();
					} else if (this.selectTab === "9") {
						this.$refs.benPerson && this.$refs.benPerson.init();
					} else if (this.selectTab === "10") {
						this.$refs.blackList && this.$refs.blackList.init();
					} else if (this.selectTab === "11") {
						this.$refs.ZDbusiness && this.$refs.ZDbusiness.init();
					} else if (this.selectTab === "12") {
						this.$refs.conPerson && this.$refs.conPerson.init();
					} else if (this.selectTab === "13") {
						this.$refs.creRecord && this.$refs.creRecord.init();
					} else if (this.selectTab === "14") {
						this.$refs.foundAccount && this.$refs.foundAccount.init();
					} else if (this.selectTab === "15") {
						this.$refs.linkmanInfo && this.$refs.linkmanInfo.init();
					} else if (this.selectTab === "16") {
						this.$refs.revType && this.$refs.revType.init();
					} else if (this.selectTab === "17") {
						this.$refs.password && this.$refs.password.init();
					} else if (this.selectTab === "18") {
						this.$refs.accountAssets && this.$refs.accountAssets.init();
					}
				});
			},

			query() {
				if (
					(this.tableForm.client_id || this.tableForm.client_name) &&
					!this.selectTab
				) {
					this.selectTab = "1";
				}
				this.$nextTick(() => {
					this.$refs.basic && this.$refs.basic.init();
					this.$refs.account && this.$refs.account.init();
					this.$refs.suitability && this.$refs.suitability.init();
					this.$refs.suitabilityMatch &&
						this.$refs.suitabilityMatch.init();
					this.$refs.rule && this.$refs.rule.init();
					this.$refs.securityAccount && this.$refs.securityAccount.init();
					this.$refs.marketDay && this.$refs.marketDay.init();
					this.$refs.aveAset && this.$refs.aveAset.init();
					this.$refs.benPerson && this.$refs.benPerson.init();
					this.$refs.blackList && this.$refs.blackList.init();
					this.$refs.ZDbusiness && this.$refs.ZDbusiness.init();
					this.$refs.conPerson && this.$refs.conPerson.init();
                    this.$refs.creRecord && this.$refs.creRecord.init();
                    this.$refs.foundAccount && this.$refs.foundAccount.init();
                    this.$refs.linkmanInfo && this.$refs.linkmanInfo.init();
                    this.$refs.revType && this.$refs.revType.init();
                    this.$refs.password && this.$refs.password.init();
					this.$refs.accountAssets && this.$refs.accountAssets.init();
				});
			}
		}
	};
</script>

<style lang="scss" scoped></style>
