<template>
	<!-- 证券账户信息 -->
	<div>
		<a-card title="证券账户信息" :bordered="false">
			<a-table
				ref="table"
				:columns="columns"
				:data-source="tableData"
				:pagination="false"
				:scroll="{ x: 1300 }"
			>
				<a
					slot="action"
					slot-scope="item"
					href="javascript:;"
					@click="toEdit(item)"
					>修改</a
				>
			</a-table>
		</a-card>

		<dataEdit :isPopShow.sync="isEditPopShow" :formData="formData" @query="success" />
	</div>
</template>

<script>
	import dataEdit from "./editAccount";
	export default {
		inject: ["api"],
		components: { dataEdit },
		data() {
			return {
                formData: {},
				selectedRowKeys: [],
				isEditPopShow: false,
				columns: [
					{
						key: "clientId",
						title: "客户号",
						width: 140,
						dataIndex: "clientId"
					},
					{
						key: "exchangeType",
						title: "交易板块类别",
						width: 150,
						dataIndex: "exchangeType"
					},
					{
						key: "fundAccount",
						title: "资金账号",
						width: 120,
						dataIndex: "fundAccount"
					},
					{
						key: "stockAccount",
						title: "证券账号",
						width: 120,
						dataIndex: "stockAccount"
					},
					{
						key: "mainFlag",
						title: "主账标志",
						width: 120,
						dataIndex: "mainFlag"
					},
					{
						key: "holderKind",
						title: "账户类别",
						width: 120,
						dataIndex: "holderKind"
					},
					{
						key: "assetProp",
						title: "资产属性",
						width: 120,
						dataIndex: "assetProp"
					},
					{
						key: "holderStatus",
						title: "股东状态",
						width: 120,
						dataIndex: "holderStatus"
					},
					{
						key: "holderRights",
						title: "股东权限",
						width: 120,
						dataIndex: "holderRights"
					},
					{
						key: "register",
						title: "是否指定",
						width: 120,
						dataIndex: "register"
					},
					{
						key: "acodeAccount",
						title: "一码通账户号码",
						width: 180,
						dataIndex: "acodeAccount"
					},
					{
						key: "seatNo",
						title: "席位编号",
						width: 180,
						dataIndex: "seatNo"
					},
					{
						key: "holderName",
						title: "账户姓名",
						width: 180,
						dataIndex: "holderName"
					},
					{
						key: "firstExchdate",
						title: "首次交易日期",
						width: 180,
						dataIndex: "firstExchdate"
					},
					// {
					// 	key: "branchNo",
					// 	title: "分支机构",
					// 	width: 120,
					// 	dataIndex: "branchNo"
					// },
					// {
					// 	key: "clientName",
					// 	title: "客户姓名",
					// 	width: 120,
					// 	dataIndex: "clientName"
					// },
					// {
					// 	key: "createDate",
					// 	title: "创建日期时间",
					// 	width: 200,
					// 	dataIndex: "createDate"
					// },
					// {
					// 	key: "createPeople",
					// 	title: "创建人",
					// 	width: 90,
					// 	dataIndex: "createPeople"
					// },
					// {
					// 	key: "id",
					// 	title: "自增编号",
					// 	width: 120,
					// 	dataIndex: "id"
					// },
					// {
					// 	key: "idKind",
					// 	title: "证件类别",
					// 	width: 120,
					// 	dataIndex: "idKind"
					// },
					// {
					// 	key: "idNo",
					// 	title: "证件号码",
					// 	width: 120,
					// 	dataIndex: "idNo"
					// },
					// {
					// 	key: "opEntrustWay",
					// 	title: "委托方式",
					// 	width: 120,
					// 	dataIndex: "opEntrustWay"
					// },
					// {
					// 	key: "tradePassword",
					// 	title: "交易密码",
					// 	width: 120,
					// 	dataIndex: "tradePassword"
					// },
					// {
					// 	key: "updateDate",
					// 	title: "修改日期时间",
					// 	width: 200,
					// 	dataIndex: "updateDate"
					// },
					// {
					// 	key: "updatePeople",
					// 	title: "修改人",
					// 	width: 90,
					// 	dataIndex: "updatePeople"
					// },
					{
						title: "操作",
						key: "action",
						fixed: "right",
						width: 80,
						scopedSlots: { customRender: "action" }
					}
				],
				tableData: []
			};
		},
		props: {
			tableForm: {
				default: () => {},
				type: Object
			}
		},
		methods: {
			success(){
				this.getTableDate();
			},
			init() {
                if (!this.tableForm.client_id && !this.tableForm.client_name) {
                    return
                }
				this.getTableDate();
			},

			toEdit(data) {
                this.isEditPopShow = true
                this.formData = data
			},

			query() {
                this.getTableDate()
            },

			getTableDate() {
				this.api
					.getCifStockAccount({
						clientId: this.tableForm.client_id,
						clientName: this.tableForm.client_name
					})
					.then(data => {
						this.tableData = data.data;
					});
			},
		}
	};
</script>