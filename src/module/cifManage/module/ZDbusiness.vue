<template>
	<!-- 中登业务操作留痕 -->
	<div>
		<a-card title="中登业务操作留痕" :bordered="false">
			<div style="margin-bottom:20px">
				<a-button v-if="!canEdit" type="primary" @click="modify">
					修改
				</a-button>
				<a-button v-if="canEdit" @click="disModify">
					取消
				</a-button>
				<a-button type="primary" v-if="canEdit" @click="submit">
					保存
				</a-button>
			</div>
			<a-form>
				<div style="display:flex;flex-wrap: wrap;">
					<a-form-item
						:label="item.label"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
						v-for="(item, index) in testData"
						:key="index"
					>
						<a-input
							v-if="canEdit"
							v-model="form[item.key]"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form[item.key] }}</span>
					</a-form-item>
				</div>
			</a-form>
		</a-card>
	</div>
</template>

<script>
	export default {
		inject: ["api"],
		data() {
			return {
				testData: [
					{
						key: "id",
						label: "自增编号",
					},
					{
						key: "opEntrustWay",
						label: "委托方式",
					},
					{
						key: "branchNo",
						label: "分支机构",
					},
					{
						key: "clientId",
						label: "客户号",
					},
					{
						key: "fundAccount",
						label: "资金账号",
					},
					{
						key: "tradePassword",
						label: "交易密码",
					},
					{
						key: "stockAccount",
						label: "证券账号",
					},
					{
						key: "exchangeType",
						label: "交易板块类别",
					},
					{
						key: "openType",
						label: "操作类别",
					},
					{
						key: "bizType",
						label: "业务类别",
					},
					{
						key: "clientName",
						label: "客户姓名",
					},
					{
						key: "idKind",
						label: "证件类别",
					},
					{
						key: "idNo",
						label: "证件号码",
					},
					{
						key: "createDate",
						label: "创建日期时间",
					},
					{
						key: "createPeople",
						label: "创建人",
					},
					{
						key: "updateDate",
						label: "修改日期时间",
					},
					{
						key: "updatePeople",
						label: "修改人",
					}
				],
				form: {},
				defaultData: {},
				canEdit: false
			};
		},
		props: {
			tableForm: {
				default: () => {},
				type: Object
			}
		},
		methods: {
			init() {
				if (!this.tableForm.client_id && !this.tableForm.client_name) {
					return;
				}
				this.api
					.getCifBusinessTraceZd({
						clientId: this.tableForm.client_id,
						clientName: this.tableForm.client_name
					})
					.then(data => {
						this.defaultData = data.data[0];
						this.form = JSON.parse(JSON.stringify(this.defaultData));
					});
			},

			disModify() {
				this.canEdit = false;
				this.form = JSON.parse(JSON.stringify(this.defaultData));
			},

			modify() {
				this.canEdit = true;
			},

			submit() {
				this.api
					.editCifBusinessTraceZd({
						...this.form
					})
					.then(data => {
						if (data.code === 0) {
							this.$message.success("修改成功！");
							this.init();
							this.canEdit = false;
						}
					});
			}
		}
	};
</script>