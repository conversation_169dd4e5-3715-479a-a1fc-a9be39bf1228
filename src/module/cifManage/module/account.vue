<template>
  <!-- 账户资产信息 -->
  <div>
    <a-card title="账户资产信息" :bordered="false">
      <div style="margin-bottom: 20px">
        <a-button v-if="!canEdit" type="primary" @click="modify">
          修改
        </a-button>
        <a-button v-if="canEdit" @click="disModify"> 取消 </a-button>
        <a-button type="primary" v-if="canEdit" @click="submit">
          保存
        </a-button>
      </div>
      <a-form>
        <div style="display: flex; flex-wrap: wrap">
          <a-form-item
            :label="item.label"
            style="width: 50%"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            v-for="(item, index) in testData"
            :key="index"
          >
            <a-input
              v-if="canEdit"
              v-model="form[item.key]"
              :disabled="item.key==='clientId'"
            />
            <span v-else>{{ form[item.key] }}</span>
          </a-form-item>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script>
export default {
  inject: ["api"],
  data() {
    return {
      testData: [
        {
          key: "clientId",
          label: "客户号"
        },
        {
          key: "fundAccount",
          label: "资金账号"
        },
        {
          key: "moneyType",
          label: "币种类别"
        },
        {
          key: "totalAsset",
          label: "总资产"
        },
        {
          key: "fundAsset",
          label: "现金资产"
        },
        {
          key: "enableBalance",
          label: "可用资金"
        },
        {
          key: "marketValue",
          label: "证券市值"
        },
        {
          key: "afundMarketValue",
          label: "普通A股市值"
        },
        {
          key: "hkfundMarketValue",
          label: "港股市值"
        },
        {
          key: "tfundMarketValue",
          label: "特转市值"
        },
        {
          key: "hstockMarketValue",
          label: "H股市值"
        },
        {
          key: "bfundMarketValue",
          label: "B股市值"
        },
        {
          key: "bondMarketValue",
          label: "债券市值"
        },
        {
          key: "stockMarketValue",
          label: "股票市值"
        },
        {
          key: "standMarketValue",
          label: "标准券市值"
        },
        {
          key: "qrpMarketValue",
          label: "报价回购市值"
        },
        {
          key: "ifundMarketValue",
          label: "场内基金市值"
        },
        {
          key: "opfundMarketValue",
          label: "开基市值"
        },
        {
          key: "crdtMarketValue",
          label: "信用证券账户市值"
        },
        {
          key: "totalDebit",
          label: "负债总额"
        },
        {
          key: "optCashBalance",
          label: "期权现金结算金额"
        },
        {
          key: "dynaMarketValue",
          label: "期权持仓动态市值"
        },
        {
          key: "finexeDebit",
          label: "融资行权负债总额"
        },
        {
          key: "arpDebit",
          label: "约定购回负债总额"
        },
      ],
      form: {},
      defaultData: {},
      canEdit: false,
    };
  },
  props: {
    tableForm: {
      default: () => {},
      type: Object,
    },
  },
  methods: {
    init() {
      if (!this.tableForm.client_id && !this.tableForm.client_name) {
        return;
      }
      this.api
        .getCifAssets({
          clientId: this.tableForm.client_id,
          clientName: this.tableForm.client_name,
        })
        .then((data) => {
          this.defaultData = data.data[0];
          this.form = JSON.parse(JSON.stringify(this.defaultData));
        });
    },

    disModify() {
      this.canEdit = false;
      this.form = JSON.parse(JSON.stringify(this.defaultData));
    },

    modify() {
      this.canEdit = true;
    },

    submit() {
      this.api
        .editCifAssets({
          ...this.form,
        })
        .then((data) => {
          if (data.code === 0) {
            this.$message.success("修改成功！");
            this.init();
            this.canEdit = false;
          }
        });
    },
  },
};
</script>
