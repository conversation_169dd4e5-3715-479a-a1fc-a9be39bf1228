<template>
	<!-- 用户适当性 -->
	<div>
		<a-card title="用户适当性" :bordered="false">
			<div style="margin-bottom:20px">
				<a-button v-if="!canEdit" type="primary" @click="modify">
					修改
				</a-button>
				<a-button v-if="canEdit" @click="disModify">
					取消
				</a-button>
				<a-button type="primary" v-if="canEdit" @click="submit">
					保存
				</a-button>
			</div>
			<a-form>
				<div style="display:flex;flex-wrap: wrap;">
					<!-- <a-form-item
						label="	分支机构"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.branchNo"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.branchNo }}</span>
					</a-form-item> -->
					<a-form-item
						label="客户号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.clientId"
							disabled
						/>
						<span v-else>{{ form.clientId }}</span>
					</a-form-item>
					<a-form-item
						label="资金账号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.fundAccount"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.fundAccount }}</span>
					</a-form-item>
					<a-form-item
						label="客户风险等级"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.corpRiskLevel"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.corpRiskLevel }}</span>
					</a-form-item>
					<a-form-item
						label="客户风险测评日"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.corpBeginDate"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.corpBeginDate }}</span>
					</a-form-item>
					<a-form-item
						label="客户风险到期日"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.corpEndDate"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.corpEndDate }}</span>
					</a-form-item>
					<a-form-item
						label="客户最低标识"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.minRankFlag"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.minRankFlag }}</span>
					</a-form-item>
					<a-form-item
						label="投资品种"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.enInvestKind"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.enInvestKind }}</span>
					</a-form-item>
					<a-form-item
						label="投资期限"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.enInvestTerm"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.enInvestTerm }}</span>
					</a-form-item>
					<a-form-item
						label="收益类型"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.incomeType"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.incomeType }}</span>
					</a-form-item>
					<a-form-item
						label="专业投资者标志"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.profFlag"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.profFlag }}</span>
					</a-form-item>
										<a-form-item
						label="专业投资者开始日期"
						style="width:50%"
						:label-col="{ span: 7 }"
						:wrapper-col="{ span: 17 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.profBeginDate"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.profBeginDate }}</span>
					</a-form-item>
					<a-form-item
						label="专业投资者结束日期"
						style="width:50%"
						:label-col="{ span: 7 }"
						:wrapper-col="{ span: 17 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.profEndDate"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.profEndDate }}</span>
					</a-form-item>
					<!-- <a-form-item
						label="	创建日期时间"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.createDate"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.createDate }}</span>
					</a-form-item>
					<a-form-item
						label="创建人"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.createPeople"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.createPeople }}</span>
					</a-form-item>
					<a-form-item
						label="自增编号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.id"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.id }}</span>
					</a-form-item>
					<a-form-item
						label="委托方式"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.opEntrustWay"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.opEntrustWay }}</span>
					</a-form-item>
					<a-form-item
						label="交易密码"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.tradePassword"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.tradePassword }}</span>
					</a-form-item>
					<a-form-item
						label="	修改日期时间"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.updateDate"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.updateDate }}</span>
					</a-form-item>
					<a-form-item
						label="修改人"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.updatePeople"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.updatePeople }}</span>
					</a-form-item> -->
				</div>
			</a-form>
		</a-card>
	</div>
</template>

<script>
	export default {
		inject: ["api"],
		data() {
			return {
				form: {},
				defaultData: {},
				canEdit: false
			};
		},
		props: {
			tableForm: {
				default: () => {},
				type: Object
			}
		},
		methods: {
			init() {
				if (!this.tableForm.client_id && !this.tableForm.client_name) {
					return;
				}
				this.api
					.getCifClientElig({
						clientId: this.tableForm.client_id,
						clientName: this.tableForm.client_name
					})
					.then(data => {
						this.defaultData = data.data[0];
						this.form = JSON.parse(JSON.stringify(this.defaultData));
					});
			},

			disModify() {
				this.canEdit = false;
				this.form = JSON.parse(JSON.stringify(this.defaultData));
			},

			modify() {
				this.canEdit = true;
			},

			submit() {
				this.api
					.editCifClientElig({
						...this.form
					})
					.then(data => {
						if (data.code === 0) {
							this.$message.success("修改成功！");
							this.init();
							this.canEdit = false;
						}
					});
			}
		}
	};
</script>