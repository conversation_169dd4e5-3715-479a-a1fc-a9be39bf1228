<template>
	<!-- 客户密码信息 -->
	<div>
		<a-card title="客户密码信息" :bordered="false">
			<a-table
				ref="table"
				:columns="columns"
				:data-source="tableData"
				:pagination="false"
			>
				<a
					slot="action"
					slot-scope="item"
					href="javascript:;"
					@click="toEdit(item)"
					>修改</a
				>
			</a-table>
		</a-card>

		<dataEdit :isPopShow.sync="isEditPopShow" :formData="formData" @query="success" />
	</div>
</template>

<script>
	import dataEdit from "./editPassword";
	export default {
		inject: ["api"],
		components: { dataEdit },
		data() {
			return {
                formData: {},
				selectedRowKeys: [],
				isEditPopShow: false,
				columns: [
					{
						key: "clientId",
						title: "客户号",
						width: 140,
						dataIndex: "clientId"
					},
					{
						key: "passwordType",
						title: "密码类别",
						width: 150,
						dataIndex: "passwordType"
					},
					{
						key: "password",
						title: "密码（SM3加密）",
						width: 120,
						dataIndex: "password",
					},
					{
						title: "操作",
						key: "action",
						fixed: "right",
						width: 80,
						scopedSlots: { customRender: "action" }
					}
				],
				tableData: []
			};
		},
		props: {
			tableForm: {
				default: () => {},
				type: Object
			}
		},
		methods: {
			success(){
				this.getTableDate();
			},
			init() {
                if (!this.tableForm.client_id && !this.tableForm.client_name) {
                    return
                }
				this.getTableDate();
			},

			toEdit(data) {
                this.isEditPopShow = true
                this.formData = data
			},

			query() {
                this.getTableDate()
            },

			getTableDate() {
				this.api
					.getcifClientPwd({
						clientId: this.tableForm.client_id,
						clientName: this.tableForm.client_name
					})
					.then(data => {
						this.tableData = data.data;
					});
			},
		}
	};
</script>