<template>
	<div>
		<a-modal
			title="修改"
			:visible="showPop"
			@ok="submit"
			@cancel="closePop"
			class="ant_modal_bigtable"
		>
			<a-form-model
				ref="form"
				:model="form"
				:label-col="{ span: 8 }"
				:wrapper-col="{ span: 14 }"
				:rules="rules"
			>
				<a-form-model-item label="自增编号" prop="id" v-show="false">
					<a-input v-model="form.id" placeholder="请输入自增编号" disabled ></a-input>
				</a-form-model-item>
				<a-form-model-item label="客户号" prop="clientId">
					<a-input v-model="form.clientId" placeholder="请输入客户号" disabled></a-input>
				</a-form-model-item>
				<a-form-model-item label="密码类别" prop="passwordType">
					<a-input v-model="form.passwordType" placeholder="请输入密码类别" disabled></a-input>
				</a-form-model-item>
				<a-form-model-item label="密码（SM3加密）" prop="password">
					<a-input v-model="form.password" placeholder="请输入密码（SM3加密）" ></a-input>
				</a-form-model-item>
			</a-form-model>
		</a-modal>
	</div>
</template>

<script>
	export default {
		inject: ["api"],
		data() {
			return {
				form: {},
				includeKeys: [],
				rules: {
					id: [
						{ required: true, message: "自增编号不能为空", trigger: "blur"}
					],
					clientId: [
						{ required: true, message: "客户号不能为空", trigger: "blur"}
					],
					passwordType: [
						{ required: true, message: "密码类别不能为空", trigger: "blur"}
					],
					password: [
						{ required: true, message: "密码（SM3加密）不能为空", trigger: "blur"}
					],
				}
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			formData: {
				default: () => {},
				type: Object
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		watch: {
			isPopShow() {
				this.updataData();
			}
		},
		methods: {
			updataData() {
				this.form = JSON.parse(JSON.stringify(this.formData));
			},
			closePop() {
				this.showPop = false;
			},
			submit() {
				this.$refs.form.validate((valid) => {
				if(!valid) return
				this.api
					.editcifClientPwd({
						...this.form
					})
					.then(data => {
                        if (data.code === 0) {
                            this.$message.success("修改成功！");
							this.showPop = false;
                            this.$emit('query')
                        }
					});
				});
			}
		}
	};
</script>

<style scoped>
	.ant-layout {
		background-color: #fff;
	}
	.ant-layout-header {
		background-color: #fff;
	}
</style>
