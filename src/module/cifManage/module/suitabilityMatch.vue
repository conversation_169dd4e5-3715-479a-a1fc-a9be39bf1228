<template>
	<!-- 用户适当性匹配关系 -->
	<div>
		<a-card title="用户适当性匹配关系" :bordered="false">
			<div style="margin-bottom:20px">
				<a-button v-if="!canEdit" type="primary" @click="modify">
					修改
				</a-button>
				<a-button v-if="canEdit" @click="disModify">
					取消
				</a-button>
				<a-button type="primary" v-if="canEdit" @click="submit">
					保存
				</a-button>
			</div>
			<a-form>
				<div style="display:flex;flex-wrap: wrap;">
					<a-form-item
						label="客户号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.clientId"
							disabled
						/>
						<span v-else>{{ form.clientId }}</span>
					</a-form-item>
					<a-form-item
						label="	业务类型"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.bizType"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.bizType }}</span>
					</a-form-item>
					<a-form-item
						label="	客户投资品种"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.investmentVarieties"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.investmentVarieties }}</span>
					</a-form-item>
					<a-form-item
						label="	客户投资期限"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.investmentTerm"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.investmentTerm }}</span>
					</a-form-item>
					<a-form-item
						label="	客户投资收益"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.investmentIncome"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.investmentIncome }}</span>
					</a-form-item>
					<a-form-item
						label="	客户风险等级"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.riskLevel"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.riskLevel }}</span>
					</a-form-item>
					<!-- <a-form-item
						label="分支机构"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.branchNo"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.branchNo }}</span>
					</a-form-item> -->
					<a-form-item
						label="	业务投资品种"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.businessInvestmentVarieties"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.businessInvestmentVarieties }}</span>
					</a-form-item>
					<a-form-item
						label="	业务投资期限"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.businessInvestmentTerm"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.businessInvestmentTerm }}</span>
					</a-form-item>
					<a-form-item
						label="业务投资收益"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.businessInvestmentIncome"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.businessInvestmentIncome }}</span>
					</a-form-item>
					<a-form-item
						label="业务风险等级"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.businessRiskLevel"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.businessRiskLevel }}</span>
					</a-form-item>
					<a-form-item
						label="	风险测评有效期"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.riskEndDate"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.riskEndDate }}</span>
					</a-form-item>
					<a-form-item
						label="	适当性匹配结果"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.matchingResults"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.matchingResults }}</span>
					</a-form-item>
					<!-- <a-form-item
						label="	创建日期时间"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.createDate"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.createDate }}</span>
					</a-form-item>
					<a-form-item
						label="	创建人"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.createPeople"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.createPeople }}</span>
					</a-form-item>
					<a-form-item
						label="资金账号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.fundAccount"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.fundAccount }}</span>
					</a-form-item>
					<a-form-item
						label="自增编号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.id"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.id }}</span>
					</a-form-item>
				
					<a-form-item
						label="	委托方式"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.opEntrustWay"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.opEntrustWay }}</span>
					</a-form-item>
					<a-form-item
						label="	交易密码"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.tradePassword"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.tradePassword }}</span>
					</a-form-item>
					<a-form-item
						label="修改日期时间"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.updateDate"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.updateDate }}</span>
					</a-form-item>
					<a-form-item
						label="修改人"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.updatePeople"
							:disabled="!canEdit"
						/>
						<span v-else>{{ form.updatePeople }}</span>
					</a-form-item> -->
				</div>
			</a-form>
		</a-card>
	</div>
</template>

<script>
	export default {
		inject: ["api"],
		data() {
			return {
				form: {},
				defaultData: {},
				canEdit: false
			};
		},
		props: {
			tableForm: {
				default: () => {},
				type: Object
			}
		},
		methods: {
			init() {
				if (!this.tableForm.client_id && !this.tableForm.client_name) {
					return;
				}
				this.api
					.getCifClientProdEligRel({
						clientId: this.tableForm.client_id,
						clientName: this.tableForm.client_name
					})
					.then(data => {
						this.defaultData = data.data[0];
						this.form = JSON.parse(JSON.stringify(this.defaultData));
					});
			},

			disModify() {
				this.canEdit = false;
				this.form = JSON.parse(JSON.stringify(this.defaultData));
			},

			modify() {
				this.canEdit = true;
			},

			submit() {
				this.api
					.editCifClientProdEligRel({
						...this.form
					})
					.then(data => {
						if (data.code === 0) {
							this.$message.success("修改成功！");
							this.init();
							this.canEdit = false;
						}
					});
			}
		}
	};
</script>