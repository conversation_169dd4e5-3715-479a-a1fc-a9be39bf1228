<template>
	<div>
		<a-modal
			title="修改"
			:visible="showPop"
			@ok="submit"
			@cancel="closePop"
			class="ant_modal_bigtable"
		>
			<a-form-model
				ref="form"
				:model="form"
				:label-col="{ span: 8 }"
				:wrapper-col="{ span: 14 }"
				:rules="rules"
			>
				<a-form-model-item label="自增编号" prop="id" v-show="false">
					<a-input v-model="form.id" placeholder="请输入自增编号" disabled ></a-input>
				</a-form-model-item>
				<a-form-model-item label="客户号" prop="clientId">
					<a-input v-model="form.clientId" placeholder="请输入客户号" disabled></a-input>
				</a-form-model-item>
				<a-form-model-item label="交易板块类别" prop="exchangeType">
					<a-input v-model="form.exchangeType" placeholder="请输入交易板块类别" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="资金账号" prop="fundAccount">
					<a-input v-model="form.fundAccount" placeholder="请输入资金账号" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="证券账号" prop="stockAccount">
					<a-input v-model="form.stockAccount" placeholder="请输入证券账号" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="主账标志" prop="mainFlag">
					<a-input v-model="form.mainFlag" placeholder="请输入主账标志" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="账户类别" prop="holderKind">
					<a-input v-model="form.holderKind" placeholder="请输入账户类别" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="资产属性" prop="assetProp">
					<a-input v-model="form.assetProp" placeholder="请输入资产属性" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="股东状态" prop="holderStatus">
					<a-input v-model="form.holderStatus" placeholder="请输入股东状态" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="股东权限" prop="holderRights">
					<a-input v-model="form.holderRights" placeholder="请输入股东权限" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="指定标志" prop="register">
					<a-input v-model="form.register" placeholder="请输入指定标志" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="一码通账户号码" prop="acodeAccount">
					<a-input v-model="form.acodeAccount" placeholder="请输入一码通账户号码" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="席位编号" prop="seatNo">
					<a-input v-model="form.seatNo" placeholder="请输入席位编号" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="账户姓名" prop="holderName">
					<a-input v-model="form.holderName" placeholder="请输入账户姓名" ></a-input>
				</a-form-model-item>
				<a-form-model-item label="首次交易日期" prop="firstExchdate">
					<a-input v-model="form.firstExchdate" placeholder="请输入首次交易日期" ></a-input>
				</a-form-model-item>
			</a-form-model>
		</a-modal>
	</div>
</template>

<script>
	export default {
		inject: ["api"],
		data() {
			return {
				form: {},
				includeKeys: [],
				rules: {
					id: [
						{ required: true, message: "自增编号不能为空", trigger: "blur"}
					],
					clientId: [
						{ required: true, message: "客户号不能为空", trigger: "blur"}
					],
					exchangeType: [
						{ required: true, message: "交易板块类别不能为空", trigger: "blur"}
					],
					fundAccount: [
						{ required: true, message: "资金账号不能为空", trigger: "blur"}
					],
					stockAccount: [
						{ required: true, message: "证券账号不能为空", trigger: "blur"}
					],
					mainFlag: [
						{ required: true, message: "主账标志不能为空", trigger: "blur"}
					],
					holderKind: [
						{ required: true, message: "账户类别不能为空", trigger: "blur"}
					],
					assetProp: [
						{ required: true, message: "资产属性不能为空", trigger: "blur"}
					],
					holderStatus: [
						{ required: true, message: "股东状态不能为空", trigger: "blur"}
					],
					holderRights: [
						{ required: true, message: "股东权限不能为空", trigger: "blur"}
					],
					register: [
						{ required: true, message: "指定标志不能为空", trigger: "blur"}
					],
					acodeAccount: [
						{ required: true, message: "一码通账户号码不能为空", trigger: "blur"}
					],
					seatNo: [
						{ required: true, message: "席位编号不能为空", trigger: "blur"}
					],
					holderName: [
						{ required: true, message: "账户姓名不能为空", trigger: "blur"}
					],
					firstExchdate: [
						{ required: true, message: "首次交易日期不能为空", trigger: "blur"}
					]
				}
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			formData: {
				default: () => {},
				type: Object
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		watch: {
			isPopShow() {
				this.updataData();
			}
		},
		methods: {
			updataData() {
				this.form = JSON.parse(JSON.stringify(this.formData));
			},
			closePop() {
				this.showPop = false;
			},
			submit() {
				this.$refs.form.validate((valid) => {
				if(!valid) return
				this.api
					.editCifStockAccount({
						...this.form
					})
					.then(data => {
                        if (data.code === 0) {
                            this.$message.success("修改成功！");
							this.showPop = false;
                            this.$emit('query')
                        }
					});
				});
			}
		}
	};
</script>

<style scoped>
	.ant-layout {
		background-color: #fff;
	}
	.ant-layout-header {
		background-color: #fff;
	}
</style>
