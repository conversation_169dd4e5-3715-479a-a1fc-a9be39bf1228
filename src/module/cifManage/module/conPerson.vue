<template>
	<!-- 控制人信息 -->
	<div>
		<a-card title="控制人信息" :bordered="false">
			<div style="margin-bottom:20px">
				<a-button v-if="!canEdit" type="primary" @click="modify">
					修改
				</a-button>
				<a-button v-if="canEdit" @click="disModify">
					取消
				</a-button>
				<a-button type="primary" v-if="canEdit" @click="submit">
					保存
				</a-button>
			</div>
			<a-form>
				<div style="display:flex;flex-wrap: wrap;">
					<a-form-item
						:label="item.label"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
						v-for="(item, index) in testData"
						:key="index"
					>
						<a-input
							v-if="canEdit"
							v-model="form[item.key]"
							:disabled="item.key==='clientId'"
						/>
						<span v-else>{{ form[item.key] }}</span>
					</a-form-item>
				</div>
			</a-form>
		</a-card>
	</div>
</template>

<script>
	export default {
		inject: ["api"],
		data() {
			return {
				testData: [
					{
						key: "clientId",
						label: "客户号",
					},
					{
						key: "isOneselfControl",
						label: "是否为本人"
					},
					{
						key: "controllerName",
						label: "客户姓名"
					},
					{
						key: "controllerGender",
						label: "客户性别"
					},
					{
						key: "controllerIdKind",
						label: "证件类别"
					},
					{
						key: "controllerIdNo",
						label: "证件号码"
					},
					{
						key: "controllerIdBegindate",
						label: "证件开始日期"
					},
					{
						key: "controllerIdEnddate",
						label: "证件结束日期"
					},
					{
						key: "controllerIdAddress",
						label: "证件地址"
					},
					{
						key: "controllerBirthday",
						label: "出生日期"
					},
					{
						key: "controllerMobileTel",
						label: "手机号码"
					},
					{
						key: "controllerZipCode",
						label: "邮政编码"
					},
					{
						key: "controllerEmail",
						label: "电子信箱"
					},
					{
						key: "controllerNationality",
						label: "国籍地区"
					},
					{
						key: "controllerCityNo",
						label: "城市编号"
					},
					{
						key: "controllerProfessionCode",
						label: "职业代码"
					},
				],
				form: {},
				defaultData: {},
				canEdit: false
			};
		},
		props: {
			tableForm: {
				default: () => {},
				type: Object
			}
		},
		methods: {
			init() {
				if (!this.tableForm.client_id && !this.tableForm.client_name) {
					return;
				}
				this.api
					.getcifControlPerson({
						clientId: this.tableForm.client_id,
						clientName: this.tableForm.client_name
					})
					.then(data => {
						this.defaultData = data.data[0];
						this.form = JSON.parse(JSON.stringify(this.defaultData));
					});
			},

			disModify() {
				this.canEdit = false;
				this.form = JSON.parse(JSON.stringify(this.defaultData));
			},

			modify() {
				this.canEdit = true;
			},

			submit() {
				this.api
					.editcifControlPerson({
						...this.form
					})
					.then(data => {
						if (data.code === 0) {
							this.$message.success("修改成功！");
							this.init();
							this.canEdit = false;
						}
					});
			}
		}
	};
</script>