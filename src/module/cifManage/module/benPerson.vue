<template>
	<!-- 受益人信息 -->
	<div>
		<a-card title="受益人信息" :bordered="false">
			<div style="margin-bottom:20px">
				<a-button v-if="!canEdit" type="primary" @click="modify">
					修改
				</a-button>
				<a-button v-if="canEdit" @click="disModify">
					取消
				</a-button>
				<a-button type="primary" v-if="canEdit" @click="submit">
					保存
				</a-button>
			</div>
			<a-form>
				<div style="display:flex;flex-wrap: wrap;">
					<a-form-item
						:label="item.label"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
						v-for="(item, index) in testData"
						:key="index"
					>
						<a-input
							v-if="canEdit"
							v-model="form[item.key]"
							:disabled="item.key==='clientId'"
						/>
						<span v-else>{{ form[item.key] }}</span>
					</a-form-item>
				</div>
			</a-form>
		</a-card>
	</div>
</template>

<script>
	export default {
		inject: ["api"],
		data() {
			return {
				testData: [
					{
						key: "clientId",
						label: "客户号"
					},
					{
						key: "isOneselfBenefit",
						label: "是否为本人"
					},
					{
						key: "benefiterName",
						label: "客户姓名"
					},
					{
						key: "benefiterGender",
						label: "客户性别"
					},
					{
						key: "benefiterIdKind",
						label: "证件类别"
					},
					{
						key: "benefiterIdNo",
						label: "证件号码"
					},
					{
						key: "benefiterIdBegindate",
						label: "证件开始日期"
					},
					{
						key: "benefiterIdEnddate",
						label: "证件结束日期"
					},
					{
						key: "benefiterIdAddress",
						label: "证件地址"
					},
					{
						key: "benefiterBirthday",
						label: "出生日期"
					},
					{
						key: "benefiterMobileTel",
						label: "手机号码"
					},
					{
						key: "benefiterZipCode",
						label: "邮政编码"
					},
					{
						key: "benefiterEmail",
						label: "电子信箱"
					},
					{
						key: "benefiterNationality",
						label: "国籍地区"
					},
					{
						key: "benefiterCityNo",
						label: "城市编号"
					},
					{
						key: "benefiterProfessionCode",
						label: "职业代码"
					},
				],
				form: {},
				defaultData: {},
				canEdit: false
			};
		},
		props: {
			tableForm: {
				default: () => {},
				type: Object
			}
		},
		methods: {
			init() {
				if (!this.tableForm.client_id && !this.tableForm.client_name) {
					return;
				}
				this.api
					.getCifBenefitPerson({
						clientId: this.tableForm.client_id,
						clientName: this.tableForm.client_name
					})
					.then(data => {
						this.defaultData = data.data[0];
						this.form = JSON.parse(JSON.stringify(this.defaultData));
					});
			},

			disModify() {
				this.canEdit = false;
				this.form = JSON.parse(JSON.stringify(this.defaultData));
			},

			modify() {
				this.canEdit = true;
			},

			submit() {
				this.api
					.editCifBenefitPerson({
						...this.form
					})
					.then(data => {
						if (data.code === 0) {
							this.$message.success("修改成功！");
							this.init();
							this.canEdit = false;
						}
					});
			}
		}
	};
</script>