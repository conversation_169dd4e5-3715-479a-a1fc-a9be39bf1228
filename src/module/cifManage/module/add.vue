<template>
  <a-modal
    title="新增客户号"
    :width="500"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="客户号" prop="clientId">
        <a-input v-model="form.clientId" placeholder="请输入客户号" />
      </a-form-model-item>
      <a-form-model-item label="姓名" prop="clientName">
        <a-input v-model="form.clientName" placeholder="请输入姓名" />
      </a-form-model-item>
      <a-form-model-item label="证件号码" prop="idNo">
        <a-input v-model="form.idNo" placeholder="请输入证件号码" />
      </a-form-model-item>
      <a-form-model-item label="手机号码" prop="mobileTel">
        <a-input v-model="form.mobileTel" placeholder="请输入手机号码" />
      </a-form-model-item>
     <a-form-model-item label="证件地址" prop="idAddress">
        <a-input v-model="form.idAddress" placeholder="请输入证件地址" />
      </a-form-model-item>
      <a-form-model-item label="性别" prop="clientGender">
        <a-radio-group name="clientGender" v-model="form.clientGender">
						<a-radio value="0" key="0">男</a-radio>
						<a-radio value="1" key="1">女</a-radio>
					</a-radio-group>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultFrom = {
  clientId: "",
  clientName: "",
  idNo: "",
  mobileTel: "",
  idAddress:"",
  clientGender: '0'
};

export default {
  name: "add",
  inject: ["api"],
  data() {
    return {
      form: Object.assign({}, defaultFrom), //表单数据,
      rules: {
        clientId: [
          { required: true, message: "客户号不能为空", trigger: "blur" },
        ],
        clientName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        idNo: [
          { required: true, message: "证件号码不能为空", trigger: "blur" },
        ],
        mobileTel: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
        ],
        idAddress: [
          { required: true, message: "证件地址不能为空", trigger: "blur" },
        ],
      },
      // 异步加载
      confirmLoading: false,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    }
  },
  watch: {
    isPopShow() {
      this.reset();
    },
  },
  methods: {
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultFrom);
      this.confirmLoading = false;
    },
    // 提交数据权限分组创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          if (code != 0) {
            this.confirmLoading = false;
            return this.$message.error(`客户号新增失败：${msg}`);
          }
          this.$message.success(`客户号新增成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
        };
          this.api.addcifClientInfo(param).then(callback)
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>