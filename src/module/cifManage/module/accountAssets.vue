<template>
  <!-- 账户资金信息 -->
  <div>
    <a-card title="账户资金信息" :bordered="false">
      <div style="margin-bottom: 20px">
        <a-button v-if="!canEdit" type="primary" @click="modify">
          修改
        </a-button>
        <a-button v-if="canEdit" @click="disModify"> 取消 </a-button>
        <a-button type="primary" v-if="canEdit" @click="submit">
          保存
        </a-button>
      </div>
      <a-form>
        <div style="display: flex; flex-wrap: wrap">
          <a-form-item
            :label="item.label"
            style="width: 50%"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            v-for="(item, index) in testData"
            :key="index"
          >
            <a-input
              v-if="canEdit"
              v-model="form[item.key]"
              :disabled="item.key==='clientId'"
            />
            <span v-else>{{ form[item.key] }}</span>
          </a-form-item>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script>
export default {
  inject: ["api"],
  data() {
    return {
      testData: [
        {
          key: "clientId",
          label: "客户号"
        },
        {
          key: "fundAccount",
          label: "资金账号"
        },
        {
          key: "moneyType",
          label: "币种类别"
        },
        {
          key: "totalAsset",
          label: "总资产"
        },
        {
          key: "currentBalance",
          label: "当前余额"
        },
        {
          key: "beginBalance",
          label: "期初余额"
        },
        {
          key: "enableBalance",
          label: "可用资金"
        },
        {
          key: "foregiftBalance",
          label: "禁取资金"
        },
        {
          key: "mortgageBalance",
          label: "禁取资产"
        },
        {
          key: "frozenBalance",
          label: "冻结资金"
        },
        {
          key: "unfrozenBalance",
          label: "解冻资金"
        },
        {
          key: "fetchBalance",
          label: "可取金额"
        },
        {
          key: "fetchCash",
          label: "可取现金"
        },
        {
          key: "entrustBuyBalance",
          label: "委托买入金额"
        },
        {
          key: "assetBalance",
          label: "资产值"
        },
        {
          key: "interest",
          label: "待入账利息"
        },
        {
          key: "integralBalance",
          label: "利息积数"
        },
        {
          key: "fineIntegral",
          label: "罚息积数"
        },
        {
          key: "preInterest",
          label: "预计利息"
        },
        {
          key: "preFine",
          label: "预计罚息"
        },
        {
          key: "preInterestTax",
          label: "预计利息税"
        },
        {
          key: "correctBalance",
          label: "资产修正金额"
        },
        {
          key: "fundBalance",
          label: "总资金余额"
        },
        {
          key: "rateKind",
          label: "利率类别"
        },
        {
          key: "realBuyBalance",
          label: "回报买入金额"
        },
        {
          key: "realSellBalance",
          label: "回报卖出金额"
        },
        {
          key: "netAsset",
          label: "净资产"
        },
        {
          key: "marketValue",
          label: "证券市值"
        },
        {
          key: "opfundMarketValue",
          label: "开基市值"
        },
        {
          key: "prodMarketValue",
          label: "多金融产品市值"
        },
      ],
      form: {},
      defaultData: {},
      canEdit: false,
    };
  },
  props: {
    tableForm: {
      default: () => {},
      type: Object,
    },
  },
  methods: {
    init() {
      if (!this.tableForm.client_id && !this.tableForm.client_name) {
        return;
      }
      this.api
        .getcifFund({
          clientId: this.tableForm.client_id,
          clientName: this.tableForm.client_name,
        })
        .then((data) => {
          this.defaultData = data.data[0];
          this.form = JSON.parse(JSON.stringify(this.defaultData));
        });
    },

    disModify() {
      this.canEdit = false;
      this.form = JSON.parse(JSON.stringify(this.defaultData));
    },

    modify() {
      this.canEdit = true;
    },

    submit() {
      this.api
        .editcifFund({
          ...this.form,
        })
        .then((data) => {
          if (data.code === 0) {
            this.$message.success("修改成功！");
            this.init();
            this.canEdit = false;
          }
        });
    },
  },
};
</script>
