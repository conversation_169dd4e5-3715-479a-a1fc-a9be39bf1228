<template>
	<!-- 用户基本信息 -->
	<div>
		<a-card title="用户基本信息" :bordered="false">
			<div style="margin-bottom:20px">
				<a-button v-if="!canEdit" type="primary" @click="modify">
					修改
				</a-button>
				<a-button v-if="canEdit" @click="disModify">
					取消
				</a-button>
				<a-button type="primary" v-if="canEdit" @click="submit">
					保存
				</a-button>
			</div>
			<a-form>
				<div style="display:flex;flex-wrap: wrap;">
					<a-form-item
						label="客户号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.clientId"
							disabled
						/>
						<span v-else>{{ form.clientId }}</span>
					</a-form-item>
					<a-form-item
						label="客户姓名"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.clientName"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.clientName }}</span>
					</a-form-item>
					<a-form-item
						label="客户状态"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.clientStatus"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.clientStatus }}</span>
					</a-form-item>
					<a-form-item
						label="客户权限"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.clientRights"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.clientRights }}</span>
					</a-form-item>
					<a-form-item
						label="客户性别"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.clientGender"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.clientGender }}</span>
					</a-form-item>
					<a-form-item
						label="联系地址"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.address"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.address }}</span>
					</a-form-item>
					<a-form-item
						label="反洗钱风险等级"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.amlRiskLevel"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.amlRiskLevel }}</span>
					</a-form-item>
					<a-form-item
						label="出生日期"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.birthday"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.birthday }}</span>
					</a-form-item>
					<a-form-item
						label="分支机构"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.branchNo"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.branchNo }}</span>
					</a-form-item>
					<!-- <a-form-item
						label="创建日期时间"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.createDate"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.createDate }}</span>
					</a-form-item> -->
					<!-- <a-form-item
						label="创建人"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.createPeople"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.createPeople }}</span>
					</a-form-item> -->
					<a-form-item
						label="学历代码"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.degreeCode"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.degreeCode }}</span>
					</a-form-item>
					<a-form-item
						label="电子信箱"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.email"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.email }}</span>
					</a-form-item>
					<a-form-item
						label="传真号码"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.fax"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.fax }}</span>
					</a-form-item>
					<!-- <a-form-item
						label="资金账号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.fundAccount"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.fundAccount }}</span>
					</a-form-item> -->
					<!-- <a-form-item
						label="自增编号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.id"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.id }}</span>
					</a-form-item> -->
					<a-form-item
						label="身份证地址"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.idAddress"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.idAddress }}</span>
					</a-form-item>
					<a-form-item
						label="证件开始日期"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.idBegindate"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.idBegindate }}</span>
					</a-form-item>
					<a-form-item
						label="证件结束日期"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.idEnddate"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.idEnddate }}</span>
					</a-form-item>
					<a-form-item
						label="证件类别"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.idKind"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.idKind }}</span>
					</a-form-item>
					<a-form-item
						label="证件号码"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.idNo"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.idNo }}</span>
					</a-form-item>
					<a-form-item
						label="年收入"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.income"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.income }}</span>
					</a-form-item>
					<a-form-item
						label="行业类别"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.industryType"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.industryType }}</span>
					</a-form-item>
					<a-form-item
						label="签发机关"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.issuedDepart"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.issuedDepart }}</span>
					</a-form-item>
					<a-form-item
						label="手机号码"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.mobileTel"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.mobileTel }}</span>
					</a-form-item>
					<a-form-item
						label="民族编号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.nationId"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.nationId }}</span>
					</a-form-item>
					<a-form-item
						label="国籍地区"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.nationality"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.nationality }}</span>
					</a-form-item>
					<a-form-item
						label="城市编号"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.cityNo"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.cityNo }}</span>
					</a-form-item>
					<a-form-item
						label="委托方式"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.enEntrustWay"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.enEntrustWay }}</span>
					</a-form-item>
					<a-form-item
						label="开户日期"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.openDate"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.openDate }}</span>
					</a-form-item>
					<a-form-item
						label="机构标志"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.organFlag"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.organFlag }}</span>
					</a-form-item>
					<a-form-item
						label="机构名称"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.organName"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.organName }}</span>
					</a-form-item>
					<a-form-item
						label="联系电话"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.phoneCode"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.phoneCode }}</span>
					</a-form-item>
					<a-form-item
						label="职业代码"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.professionCode"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.professionCode }}</span>
					</a-form-item>
					<!-- <a-form-item
						label="交易密码"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.tradePassword"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.tradePassword }}</span>
					</a-form-item> -->
					<!-- <a-form-item
						label="修改日期时间"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.updateDate"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.updateDate }}</span>
					</a-form-item> -->
					<!-- <a-form-item
						label="修改人"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.updatePeople"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.updatePeople }}</span>
					</a-form-item> -->
					<a-form-item
						label="工作单位"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.workUnit"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.workUnit }}</span>
					</a-form-item>
					<a-form-item
						label="职位"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.jobTitle"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.jobTitle }}</span>
					</a-form-item>
					<a-form-item
						label="邮政编码"
						style="width:50%"
						:label-col="{ span: 6 }"
						:wrapper-col="{ span: 18 }"
					>
						<a-input
							v-if="canEdit"
							v-model="form.zipCode"
							:disabled="!canEdit"
						/>
                        <span v-else>{{ form.zipCode }}</span>
					</a-form-item>
				</div>
			</a-form>
		</a-card>
	</div>
</template>

<script>
	export default {
		inject: ["api"],
		data() {
			return {
				form: {},
				defaultData: {},
				canEdit: false
			};
		},
		props: {
			tableForm: {
				default: () => {},
				type: Object
			}
		},
		methods: {
			init() {
				if (!this.tableForm.client_id && !this.tableForm.client_name) {
					return;
				}
				this.api
					.getCifClientInfo({
						clientId: this.tableForm.client_id,
						clientName: this.tableForm.client_name
					})
					.then(data => {
						this.defaultData = data.data[0];
						this.form = JSON.parse(JSON.stringify(this.defaultData));
					});
			},

			disModify() {
				this.canEdit = false;
				this.form = JSON.parse(JSON.stringify(this.defaultData));
			},

			modify() {
				this.canEdit = true;
			},

			submit() {
				this.api
					.editCifClientInfo({
						...this.form
					})
					.then(data => {
						if (data.code === 0) {
							this.$message.success("修改成功！");
							this.init();
							this.canEdit = false;
						}
					});
			}
		}
	};
</script>