// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

    /**
    * 查询已上架业务类型
    * @param {Object} param
    * - isShelfed {String} 是否上架
    */
     @Parameters(["isShelfed"])
     queryBusinessList() {
       return this.services.initGet({ reqUrl: "bcBusiness/list", param: this.param});
     }
    /**
    * 查询短信模板列表
    * @param {Object} param
    */
     @Parameters(["_data"])
     querySmsSendTemplateList() {
       return this.services.initGet({ reqUrl: "bcSmsSendTemplate/list", param: this.param});
     }

}

export default new api();