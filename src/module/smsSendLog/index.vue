<template>
  <a-card title="短信发送记录列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <!-- <a-form-model-item label="客户姓名">
            <a-input
              v-model="tableForm.clientName"
              placeholder="请输入客户姓名"
            ></a-input>
          </a-form-model-item> -->
          <!-- <a-form-model-item label="业务流水号">
            <a-input v-model="tableForm.businessSerialNo" placeholder="请输入"></a-input>
          </a-form-model-item>
          <a-form-model-item label="业务名称">
            <a-select v-model="tableForm.businessCode" placeholder="请选择" show-search option-filter-prop="children"
              allowClear>
              <a-select-option v-for="item in businessList" :key="item.id" :value="item.bizType">
                {{ item.bizName }}({{ item.bizType }})
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item label="客户名称">
            <a-input v-model="tableForm.customerName" placeholder="请输入"></a-input>
          </a-form-model-item>

          <a-form-model-item label="客户类别">
            <a-select v-model="tableForm.customerType" placeholder="请选择" allowClear>
              <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.clientType']" :key="i">
                {{ v.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item> -->

          <a-form-model-item label="手机号">
            <a-input
              v-model="tableForm.mobileTel"
              placeholder="请输入手机号"
            ></a-input>
          </a-form-model-item>

          <a-form-model-item label="模板名称">
            <a-select
              v-model="tableForm.smsModelId"
              show-search
              option-filter-prop="children"
              placeholder="请选择模板名称"
              allowClear
            >
              <a-select-option
                v-for="item in smsSendTemplateList"
                :key="item.id"
                :value="item.id"
              >
                {{ item.name }}({{ item.smsNo }})
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item label="发送状态">
            <a-select
              v-model="tableForm.sendState"
              placeholder="请选择短信发送状态"
            >
              <a-select-option
                v-for="(item, index) in ['发送失败', '发送成功']"
                :key="index"
                :value="index + ''"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item label="发送起止时间">
            <a-range-picker v-model="tableForm.rangeDate" @change="onChange" />
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcSmsSendBusinessLog/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
      </tk-table>
    </div>
  </a-card>
</template>

<script>
import api from "./api";

// 默认表单属性
const defaultForm = {
  businessSerialNo: "", // 业务流水号
  sendState: undefined, // 短信发送状态（0发送失败；1发送成功）
  sendDate: "", // 短信发送时间
  mobileTel: "", // 接收短信的手机号
  clientId: "", // 客户编号
  customerName: "", // 客户姓名
  bizName: "", // 业务流程名称
  businessCode: undefined, // 业务流程编号
  sendDateBeginTime: "",
  sendDateEndTime: "",
  smsModelId: undefined,
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        // {
        //   field: "businessSerialNo",
        //   label: "业务流水号",
        //   isSorter: false,
        // },
        // {
        //   field: "businessName",
        //   label: "业务名称",
        //   isSorter: false,
        // },
        // {
        //   field: "customerName",
        //   label: "客户名称",
        //   isSorter: false,
        // },
        // {
        //   field: "customerType",
        //   label: "客户类别",
        //   isSorter: false,
        //   filter: item => this.getDictText('bc.common.clientType', item)
        // },
        {
          field: "mobileTel",
          label: "手机号",
          isSorter: false,
        },
        {
          field: "templateName",
          label: "模板名称",
          isSorter: false,
        },
        {
          field: "smsContent",
          label: "短信内容",
          isSorter: false,
        },
        {
          field: "sendState",
          label: "发送状态",
          isSorter: false,
          filter: (item) => (item === "1" ? "发送成功" : "发送失败"),
        },
        {
          field: "sendDate",
          label: "发送时间",
          isSorter: true,
        },
      ],
      tableForm: {
        businessSerialNo: "",
        sendState: undefined, // 短信发送状态（0发送失败；1发送成功）
        sendDate: "", // 短信发送时间
        mobileTel: "", // 接收短信的手机号
        clientId: "", // 客户编号
        customerName: "", // 客户姓名
        customerType: "", // 客户类别
        bizName: "", // 业务流程名称
        businessCode: undefined, // 业务流程编号
        sendDateBeginTime: "",
        sendDateEndTime: "",
        smsModelId: undefined,
      },
      selectedRowKeys: [], // Check here to configure the default column
      businessList: [],
      smsSendTemplateList: [],
      dictMap: {
        "bc.common.clientType": [],
      },
    };
  },
  provide: { api: api },
  created() {
    this.querySmsSendTemplateList();
    this.queryBusinessList();

    Promise.all([this.queryDictMap()]).finally();
  },
  methods: {
    querySmsSendTemplateList() {
      api.querySmsSendTemplateList({ state: "1" }).then((res) => {
        this.smsSendTemplateList = res.data;
      });
    },
    async queryBusinessList() {
      const res = await this.$dict.dictContent("ge.comprehensive.bizTypeMain");
      if (!res) {
        api.queryBusinessList({ isShelfed: "1" }).then((res) => {
          this.businessList = res.data;
        });
      } else {
        this.businessList = res.map((item) => ({
          id: item.dictValue,
          bizType: item.dictValue,
          bizName: item.dictLabel,
        }));
      }
    },
    queryDictMap() {
      return new Promise((resolve) => {
        Object.keys(this.dictMap).forEach((item) => {
          this.$dict.dictContent(item).then((data) => {
            this.dictMap[item] = (data || []).map((data) => {
              return { label: data.dictLabel, value: data.dictValue };
            });
            resolve();
          });
        });
      });
    },
    onChange(date, dateString) {
      this.tableForm.sendDateBeginTime = dateString[0];
      this.tableForm.sendDateEndTime = dateString[1];
    },
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableForm.sendDateBeginTime)) >
        Date.parse(this.DateFormat(this.tableForm.sendDateEndTime))
      ) {
        param["sendDateBeginTime"] = "";
        param["sendDateEndTime"] = "";
      }
      delete param.rangeDate;
      return param;
    },
    getDictText(key, value) {
      let results = this.dictMap[key] || [];
      results = results.filter((item) => {
        return item.value == value;
      });
      return (results && results.length && results[0].label) || value || "";
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
  },
};
</script>

<style lang="scss" scoped></style>
