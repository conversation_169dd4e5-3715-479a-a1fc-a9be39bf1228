// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询可办理业务父节点列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBusinessList() {
    return this.services.initGet({
      reqUrl: "businessCan/list",
      param: this.param,
    });
  }

  /**
   * 查询可办理业务子节点列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBusinessPage() {
    return this.services.initPost({
      reqUrl: "businessCan/page",
      param: this.param,
    });
  }
  /**
   * 新增可办理业务配置
   * @param {Object} param
   */
  @Parameters(["_data"])
  addBusiness() {
    return this.services.initPost({
      reqUrl: "businessCan/add",
      param: this.param,
    });
  }
  /**
   * 修改可办理业务配置
   * @param {Object} param
   */
  @Parameters(["_data"])
  editBusiness() {
    return this.services.initPost({
      reqUrl: "businessCan/edit",
      param: this.param,
    });
  }
  /**
   * 删除可办理业务配置
   * @param {Object} param
   */
  @Parameters(["_data"])
  deleteBusiness() {
    return this.services.initPost({
      reqUrl: "businessCan/delete",
      param: this.param,
    });
  }
    /**
   * 查询业务定义列表
   * @param {Object} param
   */
     @Parameters(["_data"])
     getBcBusinessList() {
       return this.services.initGet({
         reqUrl: "bcBusiness/list",
         param: this.param,
       });
     }
}

export default new api();
