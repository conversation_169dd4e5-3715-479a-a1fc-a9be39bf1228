<template>
  <a-modal :title="`${typeTitle}可办理子业务`"
    class="data-ant-module"
    :width="700"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :confirm-loading="confirmLoading"
    :maskClosable="false">
    <a-form-model ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules">
      <a-form-model-item label="业务名称"
        prop="bizAlias">
        <a-input v-model="form.bizAlias"
          placeholder="请输入业务名称"
          allowClear></a-input>
      </a-form-model-item>
      <a-form-model-item label="业务描述"
        prop="bizTip">
        <a-input v-model="form.bizTip"
          placeholder="请输入业务描述"
          allowClear></a-input>
      </a-form-model-item>
      <a-form-model-item label="可办理业务编号"
        prop="relateBizType">
        <a-select v-model="form.relateBizType"
          placeholder="请选择业务编号"
          show-search
          :filter-option="filterOption"
          :allowClear="true">
          <a-select-option v-for="item in businessList"
            :key="item.id"
            :value="item.bizType">
            {{ item.bizName }}({{ item.bizType }})
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="密码操作类型"
        prop="pwdOperateType">
        <a-select v-model="form.pwdOperateType">
          <a-select-option v-for="item in typeList"
            :value="item.key"
            :key="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="是否生效"
        prop="state">
        <a-radio-group v-model="form.state">
          <a-radio value="0"
            key="0">不生效</a-radio>
          <a-radio value="1"
            key="1">生效</a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="配置JSON"
        prop="propertyConf">
        <AceEditor style="height: 400px"
          ref="AceEditor"
          :showMode="false"
          v-model="form.propertyConf" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import AceEditor from '@c/aceEditor';
// 默认表单属性
const defaultForm = {
  bizAlias: '', // 业务名称
  bizTip: '', // 业务描述
  relateBizType: '', // 业务编号
  pwdOperateType: '0', // 密码操作类型
  state: '1', // 是否生效
  propertyConf: '', // 配置JSON
};

// 注册当前请求对应的上下文请求组
export default {
  name: 'agree_add',
  inject: ['api'],
  components: { AceEditor },
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        bizAlias: [
          { required: true, message: '业务名称不能为空', trigger: 'blur' },
        ],
        bizTip: [
          { required: true, message: '业务描述不能为空', trigger: 'blur' },
        ],
        relateBizType: [
          { required: true, message: '业务编号不能为空', trigger: 'blur' },
        ],
        status: [
          { required: true, message: '请选择是否生效', trigger: 'blur' },
        ],
      },
      typeList: this.$baseDict.base.pwdTypeList,
      confirmLoading: false,
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    groupId: String,
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
    businessList: {
      type: Array,
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
    typeTitle() {
      return Object.keys(this.parameterData).length <= 0 ? '添加' : '修改';
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.parentGroupNo) {
        this.modify();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.$nextTick(() => {
        this.$refs.AceEditor &&
          this.$refs.AceEditor.setValue(defaultForm.propertyConf);
      });
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      let { state } = this.parameterData;
      this.form = Object.assign({}, this.parameterData, {
        state: state.trim(),
      });
      let _this = this;
      setTimeout(() => {
        _this.$nextTick(() => {
          _this.$refs.AceEditor.resize(true);
          if (_this.form.propertyConf) {
            _this.$refs.AceEditor.setValue(_this.form.propertyConf);
          }
        });
      }, 300);
    },

    // 提交创建
    submit() {
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        console.log(this.form);
        let param = JSON.parse(JSON.stringify(this.form));
        console.log(param);
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(`子业务${this.typeTitle}失败：${msg}`);
          this.$message.success(`子业务${this.typeTitle}成功！`);
          // 通知操作成功
          this.$emit('success');
          // 关闭弹窗 重置表单
          this.closePop();
        };
        let _this = this;
        if (_this.parameterData.parentGroupNo) {
          _this.api.editBusiness(param).then(callback);
        } else {
          _this.api
            .addBusiness(
              Object.assign({}, param, { parentGroupNo: _this.groupId })
            )
            .then(callback);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
