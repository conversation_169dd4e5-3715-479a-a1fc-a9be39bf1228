<template>
  <div class="menu-right-limits">
    <!-- 添加修改 -->
    <dataAdd :isPopShow.sync="isAddPopShow"
      :groupId="groupId"
      :parameterData="selectData"
      :businessList="businessList"
      @success="query" />
    <tk-table ref="table"
      :tableData.sync="comlun"
      :intercept-response="intercept_response"
      getMethods="bc-manage-server/businessCan/page"
      :isSelected="true"
      :isPaging="true"
      :tableFrom="tableForm"
      :selectedRowKeys.sync="selectedRowKeys"
      tableId="id"
      :isTableLoading="false">
      <div class="table-buttun-area"
        slot="tableHeader">
        <a-button :disabled="!groupId"
          type="primary"
          icon="plus"
          @click="add">
          新增
        </a-button>
        <a-button :disabled="selectedRowKeys.length <= 0"
          type="danger"
          icon="delete"
          @click="remove">
          删除
        </a-button>
      </div>
      <template slot="operation"
        slot-scope="data">
        <a-button type="link"
          @click.stop="edit(data)"> 修改 </a-button>
      </template>
    </tk-table>
  </div>
</template>

<script>
import dataAdd from './addAndEdit/dataAdd';
// import dataEdit from "./addAndEdit/dataEdit";
// // // 引入查看弹窗
// import versionList from "./version/versionList";
export default {
  data() {
    return {
      // 表格展示字段
      comlun: [
        // 循环
        {
          field: 'bizAlias',
          label: '业务名称',
          width: 180,
          isSorter: false,
        },
        {
          field: 'bizTip',
          label: '业务描述',
          width: 180,
          isSorter: false,
        },
        {
          field: 'relateBizType',
          label: '可办关联业务编号',
          align: 'center',
          width: 150,
          isSorter: false,
        },
        {
          field: 'state',
          label: '状态',
          align: 'center',
          width: 120,
          isSorter: false,
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          width: 160,
          fixed: 'right',
        },
      ],
      // 查询接口所需字段
      tableForm: {
        parentGroupNo: '',
      },
      operationType: 'add',
      // 是否显示弹窗
      isAddPopShow: false,
      isEditPopShow: false,
      //查看弹窗
      selectData: {}, // 所要修改的权限
      selectedRowKeys: [], // 当前用户选中参数
      agreeId: undefined,
    };
  },
  inject: ['api'],
  components: { dataAdd },
  props: {
    // 数据权限分组编号
    groupId: {
      type: String,
      default: '',
    },
    businessList: {
      type: Array
    }
  },
  created() {
    // 初始给与值
    this.tableForm['parentGroupNo'] = this.groupId;
  },
  watch: {
    // 用户传入菜单编号
    groupId: {
      handler(val) {
        // 具有菜单编号时查询对应的权限列表
        this.tableForm['parentGroupNo'] = val;
        this.$refs.table.getTableData();
        // 初始话选中
        this.selectedRowKeys = [];
      },
      deep: true,
    },
  },
  methods: {
    add() {
      //点击新增按钮
      this.isAddPopShow = true;
      this.selectData = {};
    },
    remove() {
      let _this = this;
      _this.$confirm({
        title: '温馨提示',
        content: `是否确认删除?`,
        okType: 'danger',
        onOk() {
          _this.deleteBus();
        },
      });
    },
    deleteBus() {
      let _this = this;
      let bcBusinessCanIds = this.selectedRowKeys.join(',');
      this.api
        .deleteBusiness({ bcBusinessCanIds })
        .then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.query();
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          _this.$message.error(e.message);
        });
    },
    // 修改
    edit(data) {
      this.selectData = data;
      this.isAddPopShow = true;
    },
    // 操作成功
    query() {
      this.selectedRowKeys = [];
      this.$refs.table.getTableData();
    },
  },
};
</script>

<style lang="scss" scoped></style>
