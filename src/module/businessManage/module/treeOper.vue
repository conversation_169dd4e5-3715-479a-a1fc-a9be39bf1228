<!--  -->
<template>
  <div>
    <a-modal :title="`${typeTitle}项目组`"
      :visible="showPop"
      :ok-text="typeTitle"
      cancel-text="取消"
      @ok="submit"
      @cancel="closePop"
      :maskClosable="false">
      <a-form-model ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 14 }">
        <a-form-model-item label="项目组名称"
          prop="groupName">
          <a-input placeholder="请输入项目组名称"
            v-model="form.groupName"></a-input>
        </a-form-model-item>
        <a-form-model-item label="项目业务编号"
          prop="bizType">
          <a-input placeholder="请输入项目业务编号"
            v-model="form.bizType"></a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'treeOper',
  inject: ['api'],
  data() {
    return {
      form: {},
      rules: {
        groupName: [
          {
            required: true,
            message: '分组名称不能为空',
            trigger: 'blur',
          },
        ],
        bizType: [
          {
            required: true,
            message: '业务编号不能为空',
            trigger: 'blur',
          },
        ],
      },
    };
  },

  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
    // 操作类型
    operationType: {
      type: String,
      default: 'add',
    },
  },

  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
    typeTitle() {
      return this.operationType == 'add' ? '添加' : '修改';
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.operationType != 'add' && this.parameterData.id) {
        this.modify();
      } else if (this.parameterData.id) {
        this.form.id = this.parameterData.id;
      } else if (this.operationType == 'add' && !this.parameterData.id) {
        this.form.id = null;
      } else {
        this.reset();
      }
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = Object.assign({}, this.parameterData);
    },
    submit() {
      //新增;
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if (this.operationType == 'set') {
          let param = {
            id: this.parameterData.id,
            groupName: this.form.groupName,
            bizType: this.form.bizType,
          };
          this.api.editBusiness(param).then((res) => {
            if (res.code === 0) {
              this.$message.success(res.msg);
              this.$emit('updateList', true);
              this.closePop();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          let param = {
            groupName: this.form.groupName,
            bizType: this.form.bizType,
          };
          this.api.addBusiness(param).then((res) => {
            if (res.code === 0) {
              this.$message.success(res.msg);
              this.$emit('updateList', true);
              this.closePop();
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      });
    },
  },
};
</script>
<style lang='scss' scoped>
</style>