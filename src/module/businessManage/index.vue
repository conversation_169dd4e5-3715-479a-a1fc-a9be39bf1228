<!--  -->
<template>
  <a-layout class="menu-content">
    <a-layout-sider class="menu-tree-sider">
      <a-card title="可办理业务列表"
        :bordered="false">
        <div class="menu-left-tree"
          style="height: calc(100% - 32px)">
          <a-button type="primary"
            icon="plus"
            @click="(isPopShow = true), (operationType = 'add')"
            style="margin-left:10px">新增</a-button>
          <tkTightTree @select="select"
            @rightClick="rightClick">
            <tk-tree :treeData="treeData"
              :selectedKeys.sync="selectedKeys"
              class="ant-tree-switcher-no"
              :replaceFields="replaceFields"
              :isIcon="true" />
            <a-menu-item key="0"
              v-show="Object.keys(rightMenu).length == 0"
              @click="(isPopShow = true), (operationType = 'add')"> 新增项目组 </a-menu-item>
            <a-menu-item key="1"
              v-show="Object.keys(rightMenu).length > 0"
              @click="(isPopShow = true), (operationType = 'set')"> 修改项目组 </a-menu-item>
            <a-menu-item key="2"
              v-show="Object.keys(rightMenu).length > 0"
              @click="remove"> 删除项目组 </a-menu-item>
            <a-menu-item key="3"
              v-show="Object.keys(rightMenu).length > 0"
              @click="downloadExcel"> 导入项目组 </a-menu-item>
            <a-menu-item key="4"
              v-show="Object.keys(rightMenu).length > 0"
              @click="exportExcel"> 导出项目组 </a-menu-item>
            <a-menu-item key="5"
              v-show="Object.keys(rightMenu).length == 0"
              @click="updateList"> 刷新项目组 </a-menu-item>
          </tkTightTree>
        </div>
      </a-card>
    </a-layout-sider>
    <a-layout-content class="menu-right-content">
      <a-card title="子业务列表"
        :bordered="false">
        <dataTable ref="dataTable"
          :groupId="groupId" 
          :businessList="businessList"/>
        <treeOper :isPopShow.sync="isPopShow"
          @updateList="updateList"
          :parameterData="rightMenu"
          :operationType="operationType"></treeOper>
      </a-card>
      <upload :visible.sync="uploadShow"
        :parentNo="rightMenu.groupNo"
        @success="query" />
    </a-layout-content>
  </a-layout>
</template>

<script>
import api from './api';
import dataTable from './module/table';
import treeOper from './module/treeOper';
import upload from './module/upload';
export default {
  provide() {
    return { api: api };
  },
  data() {
    return {
      groupId: undefined, // 用户选中项菜单
      treeData: [], // 树状列表填充数据
      businessList: [], // 业务列表 
      rightMenu: {}, // 右击对象
      operationType: '',
      replaceFields: {
        children: 'children',
        title: 'groupName',
        key: 'id',
      },
      selectData: {},
      selectedKeys: [''],
      isPopShow: false, //弹窗是否显示
      uploadShow: false, // 导入弹框
    };
  },

  components: { dataTable, treeOper, upload },

  computed: {},

  created() {
    this.getTreeData();
    api.getBcBusinessList().then((res) => {
      this.businessList = res.data;
    });
  },

  methods: {
    // 导入downloadExcel
    downloadExcel() {
      this.uploadShow = true;
    },

    // 导出exportExcel
    exportExcel() {
      window.location.href = `/bc-manage-server/businessCan/export?parentGroupNo=${this.rightMenu.groupNo}`;
    },
    query() {
      this.$refs.dataTable.$refs.table.getTableData();
    },
    remove() {
      let _this = this;
      _this.$confirm({
        title: '温馨提示',
        content: `是否确认删除?`,
        okType: 'danger',
        onOk() {
          _this.deleteBus();
        },
      });
    },
    deleteBus() {
      let _this = this;
      api
        .deleteBusiness({ bcBusinessCanIds: this.rightMenu.id })
        .then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.getTreeData();
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          _this.$message.error(e.message);
        });
    },
    updateList() {
      this.getTreeData();
    },
    rightClick(data) {
      this.rightMenu = data;
    },
    select(data) {
      // 赋值
      this.groupId = data.groupNo;
      this.selectData = data;
      this.selectedKeys = [data.id];
    },
    // 表单重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.operationType = '';
      this.selectData = {};
      this.selectedKeys = [];
    },
    // 查询当前菜单栏树状列表
    getTreeData() {
      api.getBusinessList().then(({ code, data }) => {
        if (code != 0) return;
        this.treeData = data.map((item) => this.formatConversion(item));
      });
    },
    formatConversion(item) {
      item['scopedSlots'] = { icon: 'custom' };
      item['itemIcon'] = 'file';
      return item;
    },
  },
};
</script>
<style lang='scss' scoped>
</style>