<template>
    <div>
        <a-layout>
            <a-layout-header style="background: #fff;">
                <a-row class="pg-header pg-header-top">
                    单向审核记录详情
                </a-row>
            </a-layout-header>
            <a-layout-content style="background: #f9f9f9; padding: 10px 25px 90px 25px">
                <div>
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="content-border">
                            <div class="list_header">
                                <a-row>
                                    <a-col :span="6" class="pop-title">客户上传档案</a-col>
                                </a-row>
                            </div>
                            <a-descriptions bordered>
                                <a-descriptions-item label="业务类型">
                                    {{ getDictText('business_types', userInfo?.business_type) }}
                                </a-descriptions-item>
                                <a-descriptions-item label="客户号">
                                    {{ userInfo?.preengage_id }}
                                </a-descriptions-item>
                                <a-descriptions-item label="审核申请时间">
                                    {{ userInfo?.apply_time }}
                                </a-descriptions-item>
                                <a-descriptions-item label="审核完成时间">
                                    {{ userInfo?.examine_end_time }}
                                </a-descriptions-item>
                                <a-descriptions-item label="审核人员">
                                    {{ userInfo?.operator_name }}
                                </a-descriptions-item>
                                <a-descriptions-item label="审核结果">
                                    {{ getDictText('apply_statuss', userInfo?.apply_status) }}
                                </a-descriptions-item>
                                <a-descriptions-item label="复核人员" v-if="userInfo?.review_operatro_name != null">
                                    {{ userInfo?.review_operatro_name }}
                                </a-descriptions-item>
                                <a-descriptions-item label="复核时间" v-if="userInfo.review_time != null">
                                    {{ userInfo?.review_time }}
                                </a-descriptions-item>
                                <a-descriptions-item label="身份确认方式">
                                    {{ getDictText('identity_types', userInfo?.identity_type) }}
                                </a-descriptions-item>

                            </a-descriptions>
                            <viewer class="idcardImg" v-for="(item, index) in clientImgArr" :key="index">
                                <h4>{{ archiveTypeList[item.archiveType] }}</h4>
                                <img width="100%" style="cursor: pointer" :src="item.imgUrl" />
                            </viewer>
                        </div>
                    </a-row>
                </div>
                <div>
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="content-border">
                            <div class="list_header">
                                <a-row>
                                    <a-col :span="6" class="pop-title">单向视频</a-col>
                                </a-row>
                            </div>
                            <a-row>
                                <a-col>
                                    <a-card :bordered="false" class="self">
                                        <video width="600" height="400" controls="controls" crossorigin="anonymous"
                                            :src="clientSingleVideoUrl">
                                            <source :src="clientSingleVideoUrl" type="video/mp4">
                                        </video>
                                    </a-card>
                                </a-col>
                            </a-row>
                        </div>
                    </a-row>
                </div>
                <div>
                    <div class="list_header">
                        <a-row>
                            <a-col :span="6" class="pop-title">公安信息</a-col>
                        </a-row>
                    </div>
                    <a-descriptions bordered>
                        <a-descriptions-item label="客户姓名">
                            {{ userInfo?.client_name }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件号码">
                            {{ userInfo?.id_no }}
                        </a-descriptions-item>
                        <a-descriptions-item label="性别">
                            {{ userInfo?.gender === '0' ? '男' : '女' }}
                        </a-descriptions-item>
                        <a-descriptions-item label="出生日期">
                            {{ userInfo?.birthday }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件类别">
                            {{ getDictText("id_kinds", userInfo?.id_kind) }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件地址">
                            {{ userInfo?.id_address }}
                        </a-descriptions-item>
                        <!-- <a-descriptions-item label="国籍名称" v-if="userInfo?.id_kind == 'I'">
                            {{ userInfo?.nationalityName }}
                        </a-descriptions-item> -->
                        <a-descriptions-item label="联系地址">
                            {{ userInfo?.address }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件签发机关">
                            {{ userInfo?.issued_depart }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件开始日期">
                            {{ userInfo?.id_begin_date }}
                        </a-descriptions-item>
                        <a-descriptions-item label="证件结束日期">
                            {{ userInfo?.id_end_date }}
                        </a-descriptions-item>
                        <a-descriptions-item label="公安匹配分值" v-if="userInfo?.id_kind == '0'">
                            {{ getDictText("similar_check_results", userInfo?.similar_check_result) || '(未进行人脸比对)' }}
                        </a-descriptions-item>
                    </a-descriptions>
                </div>
                <div>
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="content-border">
                            <div class="list_header">
                                <a-row>
                                    <a-col :span="6" class="pop-title">客户见证信息</a-col>
                                </a-row>
                            </div>
                            <a-descriptions bordered>
                                <a-descriptions-item label="手机号码">
                                    {{ userInfo?.mobile_no }}
                                </a-descriptions-item>
                                <a-descriptions-item label="产品名称">
                                    {{ getDictText("product_ids", userInfo?.product_id) }}
                                </a-descriptions-item>
                                <a-descriptions-item label="推荐人编码">
                                    {{ userInfo?.input_code }}
                                </a-descriptions-item>
                                <a-descriptions-item label="终端类型">
                                    {{ getDictText("terminal_types", userInfo?.terminal_type) || '未知' }}
                                </a-descriptions-item>
                                <a-descriptions-item label="审核次数">
                                    {{ userInfo?.audit_count }}
                                </a-descriptions-item>
                                <a-descriptions-item label="审核结果">
                                    {{ getDictText("apply_statuss", userInfo?.apply_status) }}
                                </a-descriptions-item>
                                <a-descriptions-item label="见证备注">
                                    {{ userInfo?.audit_remark }}
                                </a-descriptions-item>
                            </a-descriptions>
                        </div>
                    </a-row>
                </div>
                <div v-show="userInfo?.gat_auxiliary_type == '0'">
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="content-border">
                            <div class="list_header">
                                <a-row>
                                    <a-col :span="6" class="pop-title">居住证信息</a-col>
                                </a-row>
                            </div>
                            <a-descriptions bordered>
                                <a-descriptions-item label="姓名">
                                    {{ userInfo?.gat_name }}
                                </a-descriptions-item>
                                <a-descriptions-item label="证件类别">
                                    居住证
                                </a-descriptions-item>
                                <a-descriptions-item label="证件号码">
                                    {{ userInfo?.gat_id_no }}
                                </a-descriptions-item>
                                <a-descriptions-item label="证件开始日期">
                                    {{ userInfo?.gat_validity_term_start }}
                                </a-descriptions-item>
                                <a-descriptions-item label="证件结束日期">
                                    {{ userInfo?.gat_validity_term_end }}
                                </a-descriptions-item>
                                <a-descriptions-item label="签发机关">
                                    {{ userInfo?.gat_issuance_department }}
                                </a-descriptions-item>
                                <a-descriptions-item label="证件地址">
                                    {{ userInfo?.gat_address }}
                                </a-descriptions-item>
                            </a-descriptions>
                        </div>
                    </a-row>
                </div>
            </a-layout-content>
        </a-layout>
    </div>
</template>

<script>
import api from '../api'
import moment from 'moment'

export default {
    name: "AuditRecordsDetail",
    data() {
        return {
            userInfo: {},
            clientImgArr: [],
            clientSingleVideoUrl: '',
            dictArr: {
                identity_types: [
                    { value: '2', label: '银证鉴权' },
                    { value: '4', label: '活体检测' },
                ],
                id_kinds: [
                    { value: '0', label: '身份证' },
                    { value: 'G', label: '港澳居民来往内地通行证' },
                    { value: 'H', label: '台湾居民来往内地通行证' },
                    // { value: 'I', label: '外国人永久居留身份证' },

                ],
                product_ids: [
                    { value: '1', label: '佣金宝' },
                    { value: '2', label: '投资宝' },
                ],
                apply_statuss: [
                    { value: 2, label: '审核不通过' },
                    { value: 3, label: '审核通过' },
                    { value: 4, label: '复核通过' },
                    { value: 5, label: '复核驳回' }
                ],
                business_types: [
                    { value: '1001', label: '开股东户' },
                    { value: '1002', label: '开股东户' },
                    { value: '1003', label: '开股东户（微信）' },
                    { value: '1004', label: '1.0迁移3.0' },
                ],
                terminal_types: [
                    { value: '1', label: 'ios' },
                    { value: '2', label: 'android' },
                    { value: '3', label: '微信' },
                    { value: '4', label: 'pc' },

                ],
                similar_check_results: [
                    { value: '0', label: '-200(客户无公安头像)' },
                    { value: '1', label: '200.00' },
                    { value: '2', label: '0(系统认为客户非本人) ' },
                ],

            },
            archiveTypeList: {
                1: '证件正面',
                2: '证件反面',
                3: '客户头像',
                4: '视频见证录像',
                5: '辅证图片',
                501: '营业执照',
                502: '就业证明',
                503: '居住证',
                7: '单向视频',
                8: '公安头像',
            },
        };
    },
    provide: [api],
    created() {
        if (this.$route.query.processId) {
            this.queryClientDetail()
        }
    },
    methods: {
        queryClientDetail() {
            const params = new URLSearchParams(window.location.search);
            api
                .queryItem({
                    id: this.$route.query.processId,
                    dataTm: params.get('dataTm'),
                })
                .then((res) => {
                    if (res.code == 0) {
                        // 正面
                        if (res.data.image_idcard_front) {
                            this.queryfileBytes(res.data.image_idcard_front, (res) => {
                                this.clientImgArr.unshift({ imgUrl: res, archiveType: 1, })
                            })
                        }
                        // 反面
                        if (res.data.image_idcard_back) {
                            this.queryfileBytes(res.data.image_idcard_back, (res) => {
                                this.clientImgArr.unshift({ imgUrl: res, archiveType: 2, })
                            })
                        }
                        // 头像
                        if (res.data.image_client_head) {
                            this.queryfileBytes(res.data.image_client_head, (res) => {
                                this.clientImgArr.unshift({ imgUrl: res, archiveType: 3, })
                            })
                        }
                        // 公安头像(证件类型为身份证专有字段)
                        if (res.data.image_police) {
                            this.queryfileBytes(res.data.image_police, (res) => {
                                this.clientImgArr.unshift({ imgUrl: res, archiveType: 8, })
                            })
                        }
                        // 辅证(证件类型为身份证专有字段)
                        if (res.data.image_auxiliary) {
                            let temp = res.data.image_auxiliary.split(",");
                            temp.forEach(item => {
                                this.queryfileBytes(item, (res) => {
                                    this.clientImgArr.unshift({ imgUrl: res, archiveType: 5, })
                                })
                            })
                        }
                        // 居住证(证件类型为港澳居民来往内地通行证/台湾居民来往内地通行证专有字段)
                        if (res.data.image_residence) {
                            let temp = res.data.image_residence.split(",");
                            temp.forEach(item => {
                                this.queryfileBytes(item, (res) => {
                                    this.clientImgArr.unshift({ imgUrl: res, archiveType: 503, })
                                })
                            })
                        }
                        // 营业执照
                        if (res.data.image_business_license) {
                            this.queryfileBytes(res.data.image_business_license, (res) => {
                                this.clientImgArr.unshift({ imgUrl: res, archiveType: 501, })
                            })
                        }
                        // 就业证明
                        if (res.data.image_job_prove) {
                            this.queryfileBytes(res.data.image_job_prove, (res) => {
                                this.clientImgArr.unshift({ imgUrl: res, archiveType: 502, })
                            })
                        }
                        this.clientSingleVideoUrl = "/bc-manage-server/gjKhHis/archive/video/download?file_id=" + res.data.video_single
                        setTimeout(() => {
                            this.userInfo = res.data
                        }, 0)
                    } else {
                        this.$message.error(res.message)
                    }
                })
        },

        queryfileBytes(file_id, callback) {
            api
                .queryImgfileUrl({
                    file_id,
                })
                .then((res) => {
                    let fileBytes = ''
                    if (res.code == 0) {
                        fileBytes = res.data
                    }
                    callback && callback(fileBytes)
                })
        },
        getDictText(sourceArray, idType) {
            let idTypeItem = this.dictArr[sourceArray].filter(
                (item) => item.value == idType
            )
            return idTypeItem[0]?.label;
        },
        formatDate(date) {
            if (!date) {
                return ''
            }
            return date.slice(0, 4) + '-' + date.slice(4, 6) + '-' + date.slice(6, 8)
        },
        formatDateTime(date) {
            return moment(date).format('YYYY-MM-DD HH:MM:SS')
        },

    },
};
</script>

<style lang="scss" scoped>
.idcardImg {
    display: inline-block;
    margin-right: 10px;
    margin-top: 10px;
}

.idcardImg img {
    width: 335px;
    height: 200px;
}

.idcardImg h4 {
    text-align: center;
    background-color: rgb(217, 233, 245);
}

.self {
    text-align: center;
}
</style>
