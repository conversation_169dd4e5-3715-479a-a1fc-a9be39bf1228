<template>
    <a-card title="单向审核记录" class="management" :bordered="false">
        <div class="searchFrom">
            <a-form-model layout="inline" :model="tableFrom">
                <tkSelectForm @query="success" @reset="reset">
                    <a-form-model-item label="客户申请时间">
                        <a-range-picker v-model="tableFrom.applyTime" valueFormat="YYYY-MM-DD" />
                    </a-form-model-item>
                    <a-form-model-item label="手机号">
                        <a-input v-model="tableFrom.mobileNo" placeholder="请输入手机号" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="客户编号">
                        <a-input v-model="tableFrom.preengageId" placeholder="请输入客户编号" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="完成审核时间">
                        <a-range-picker v-model="tableFrom.examineEndTime" valueFormat="YYYY-MM-DD" />
                    </a-form-model-item>
                    <a-form-model-item label="证件号码">
                        <a-input v-model="tableFrom.idNo" placeholder="请输入证件号码" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="客户姓名">
                        <a-input v-model="tableFrom.clientName" placeholder="请输入客户姓名" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="审核结果">
                        <a-select v-model="tableFrom.applyStatus" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.optionsSource']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="审核座席">
                        <a-input v-model="tableFrom.operatorName" placeholder="请输入审核座席" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="推荐人编码">
                        <a-input v-model="tableFrom.cooperationCode" placeholder="请输入推荐人编码" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="身份确认方式">
                        <a-select v-model="tableFrom.identityType" placeholder="全部" show-search
                            option-filter-prop="children" allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.identitySource']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="开户证件">
                        <a-select v-model="tableFrom.idKind" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.cardSource']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="资金账号">
                        <a-input v-model="tableFrom.clientId" placeholder="请输入资金账号" allowClear />
                    </a-form-model-item>
                </tkSelectForm>
            </a-form-model>
        </div>
        <div class="access-table">
            <tk-table ref="table" :tableData.sync="comlun" getMethods="bc-manage-server/gjKhHis/single/queryAuditList"
                :intercept-response="intercept_response" :isPaging="true" :tableFromFilter="tableFormFilter"
                :tableFrom="tableFrom" tableId="id">
                <div class="table-button-area" slot="tableHeader">
                    <a-button icon="upload" type="primary" @click="exportExcel">
                        导出
                    </a-button>
                </div>
                <template slot="operation" slot-scope="data">
                    <a class="action-btn" @click="showDetail(data)"> 查看详情 </a>
                </template>
            </tk-table>

        </div>

    </a-card>
</template>

<script>
// import api from './api';

export default {
    name: 'auditRecords',
    data() {
        return {
            comlun: [
                { field: 'preengageId', label: '客户编号', width: 180 },
                { field: 'clientName', label: '客户姓名', width: 100 },
                { field: 'mobileNo', label: '手机号', width: 120 },
                { field: 'idNo', label: '证件号码', width: 180 },
                {
                    field: 'applyStatus', label: '审核结果', width: 150,
                    filter: item => this.getDictText('bc.common.optionsSource', item)
                },
                { field: 'count', label: '审核次数', width: 100 },
                {
                    field: 'identityType', label: '身份确认方式', width: 150,
                    filter: item => this.getDictText('bc.common.identitySource', item)
                },
                {
                    field: 'idKind', label: '开户证件', width: 150,
                    filter: item => this.getDictText('bc.common.cardSource', item)
                },
                { field: 'operatorName', label: '审核座席', width: 150 },
                { field: 'cooperationCode', label: '推荐人编码', width: 150 },
                { field: 'clientId', label: '资金账号', width: 150 },
                { field: 'applyTime', label: '申请时间', width: 180 },
                { field: 'examineEndTime', label: '完成审核时间', width: 180 },
                {
                    field: 'specicalFlag', label: '特殊标示', width: 150,
                    filter: item => this.getDictText('bc.common.specicalSource', item)
                },
                { field: 'refuseReason', label: '见证备注', width: 200, isEllipsis: true },
                { field: 'operation', label: '操作', align: 'center', fixed: 'right', width: 100 }

            ],
            dictMap: {
                'bc.common.optionsSource': [
                    { value: 3, label: '审核通过' },
                    { value: 2, label: '审核不通过' },
                    { value: 4, label: '复核通过' },
                    { value: 5, label: '复核驳回' }
                ],
                'bc.common.cardSource': [
                    { value: '0', label: '身份证' },
                    { value: 'G', label: '港澳居民来往内地通行证' },
                    { value: 'H', label: '台湾居民来往内地通行证' },
                    { value: 'I', label: '外国人永久居留身份证' }
                ],
                'bc.common.identitySource': [
                    { value: 2, label: '银证鉴权' },
                    { value: 4, label: '活体检测' },
                ],
                'bc.common.specicalSource': [
                    { value: '0', label: '--' },
                    { value: '1', label: '待回访' },
                    { value: '2', label: '进行中' },
                    { value: '3', label: '已回访' },
                    { value: '4', label: '失败' },
                    { value: '5', label: '未接通' },
                ]
            },
            tableFrom: {
                applyTime:['2024-04-26','']
            },
            exportForm: {},
        }
    },
    methods: {
        getDictText(key, value) {
            let results = this.dictMap[key] || [];
            results = results.filter((item) => {
                return item.value == value;
            });
            return (results && results.length && results[0].label) || '';
        },
        success() {
            this.$refs.table.getTableData();
        },
        tableFormFilter(param) {
            console.log('this.tableFrom?.applyTime', this.tableFrom?.applyTime)
            // 申请时间
            if (this.tableFrom?.applyTime && this.tableFrom.applyTime?.length > 0) {
                param["applyTimeStart"] = this.tableFrom.applyTime[0];
                param["applyTimeEnd"] = this.tableFrom.applyTime[1];
            }
            delete param.applyTime
            // 完成审核时间
            if (this.tableFrom?.examineEndTime && this.tableFrom.examineEndTime?.length > 0) {
                param["examineEndTimeStart"] = this.tableFrom.examineEndTime[0];
                param["examineEndTimeEnd"] = this.tableFrom.examineEndTime[1];
            }
            // 是否脱敏
            const params = new URLSearchParams(window.location.search);
            param['dataTm'] = params.get('dataTm');
            delete param.examineEndTime
            this.exportForm = param;
            return param;
        },
        // 重置
        reset() {
            this.tableFrom = {};
        },
        // 查看详情
        showDetail(data) {
            console.log('data', data)
            const params = new URLSearchParams(window.location.search);
            let href = `/bc-manage-view/auditRecords/DetailInfo?processId=${data.id}&dataTm=${params.get('dataTm')}`
            window.open(href, '_blank')
        },
        objectToQueryString(obj) {
            return Object.keys(obj)
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
                .join('&');
        },
        // 导出exportExcel
        exportExcel() {
            const searchs = this.objectToQueryString(this.exportForm);
            console.log('searchs===========', searchs)
            window.location.href = "/bc-manage-server/gjKhHis/single/exportAuditList?" + searchs;
        },
    },
    created() {

    }
}
</script>