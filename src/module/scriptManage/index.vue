<!--  -->
<template>
  <div class="base_sty_column_box"
    ref="content"
    @click="hideRightMenu">
    <div class="base_tree_nav_box"
      ref="addEditTree">
      <a-layout style="height: 100%;background: #fff;">
        <a-page-header title="脚本组"
          style="background: #fff;" />
        <a-layout-header>
          <a-button type="link"
            icon="plus"
            size="small"
            @click="dealBtn('add')">新增</a-button>
        </a-layout-header>
        <a-tree v-if="treeData.length>0"
          style="background: #fff;"
          :show-icon="showIcon"
          :treeData="treeData"
          @select="onSelect"
          @rightClick="rightClick">
          <a-icon slot="file"
            type="copy" />
        </a-tree>
        <div class="base_right_click_menu"
          v-if="isPopShow"
          id="rightClickMenu"
          ref="rightMenu"
          :style="tmpStyle">
          <a-button type="link"
            size="small"
            @click="dealBtn('edit')">修改脚本组名称</a-button>
          <a-button type="link"
            size="small"
            @click="dealBtn('remove')">删除脚本组</a-button>
        </div>
      </a-layout>
    </div>
    <div style="width: 1px;height: 100%;background: rgb(221 221 221);float: left;position: relative;"></div>
    <div class="base_sty_column_right"
      ref="addEditContent"
      v-show="displayTree">
      <rightManage :id="leftInfo.uuid"
        :leftInfo="leftInfo"
        @uploadSuccess="query"></rightManage>
    </div>
    <addOrEdit v-show="addOrEditPop"
      :isPopShow.sync="addOrEditPop"
      :requestId="requestId"
      :leftInfo="leftInfo"
      @updateList="updateList"></addOrEdit>
  </div>
</template>

<script>
import api from './api';
export default {
  data() {
    return {
      displayTree: true, //默认显示右侧菜单树
      treeData: [], //树形数据
      requestId: '', //选中的左侧id
      showIcon: true, //是否展示 TreeNode title 前的图标，没有默认样式，如设置为 true，需要自行定义图标相关样式
      addOrEditPop: false, //是否显示新增区域弹窗
      leftInfo: {}, //左侧选中信息
      currentLeftKey: null, //左键选中Id
      NodeTreeItem: {
        id: '',
      }, // 右键菜单数据
      tmpStyle: '', //触发右键style
      currentRightKey: null, //右键选中Id
      isPopShow: false, //是否显示树形右击弹窗
    };
  },

  components: {
    addOrEdit: () =>
      import(/* webpackChunkName: "addOrEdit" */ './components/addOrEdit.vue'),
    rightManage: () =>
      import(
        /* webpackChunkName: "addOrEdit" */ './components/rightManage.vue'
      ),
  },

  computed: {},

  mounted() {
    this.queryAreaTree();
  },

  methods: {
    // 隐藏右键弹框
    hideRightMenu() {
      this.isPopShow = false;
    },
    //刷新树形
    updateList(param) {
      if (param) {
        this.queryAreaTree();
      }
    },
    query() {
      this.queryAreaTree();
    },
    //获取树形数据
    queryAreaTree() {
      let _this = this;
      api.getScriptGroupList().then((res) => {
        if (res.code === 0) {
          res.data.forEach((v) => {
            v.slots = {
              icon: 'file',
            };
            _this.dealTreeData(v);
          });
          _this.treeData = res.data;
        } else {
          _this.treeData = [];
        }
      });
    },
    //处理树形数据
    dealTreeData(v) {
      v.title = v.scriptName;
      v.key = v.id;
      if (v.children) {
        v.children.forEach((v1) => {
          this.dealTreeData(v1);
        });
      }
    },
    //左侧点击
    onSelect(selectedKeys, info) {
      this.currentLeftKey = selectedKeys;
      this.leftInfo = info.node.dataRef;
    },
    // 右击事件
    rightClick({ event, node }) {
      const x = event.x;
      const y = event.y;
      this.NodeTreeItem = {
        pageX: x,
        pageY: y,
        id: node._props.eventKey,
        title: node._props.title,
        parentOrgId: node._props.dataRef.parentOrgId || null,
        dataRef: node._props.dataRef || {},
        type: node._props.dataRef.parentId,
      };
      let scroll = this.$refs.addEditTree.scrollTop;
      this.tmpStyle = {
        position: 'absolute',
        maxHeight: 40,
        textAlign: 'left',
        left: '30px',
        top: `${y - 160 + scroll}px`,
        display: 'flex',
        flexDirection: 'column',
      };
      this.currentRightKey = node._props.eventKey;
      this.isPopShow = true;
      this.$forceUpdate();
    },
    dealBtn(type) {
      let _this = this;
      if (type == 'add') {
        _this.requestId = '';
        _this.addOrEditPop = true;
      } else if (type == 'edit') {
        _this.requestId = _this.NodeTreeItem.dataRef.uuid;
        _this.leftInfo = _this.NodeTreeItem.dataRef;
        _this.addOrEditPop = true;
      } else if (type == 'remove') {
        let _this = this;
        _this.$confirm({
          title: '温馨提示',
          content: `是否确认删除?`,
          okType: 'danger',
          onOk() {
            _this.deleteScript();
          },
        });
      }
    },
    addScript() {
      api.scriptAdd({});
    },
    deleteScript() {
      let _this = this;
      api
        .scriptDelete({
          bcSurveyAnswerScriptId: _this.NodeTreeItem.dataRef.id,
        })
        .then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.query();
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          _this.$message.error(e.message);
        });
    },
  },
};
</script>
<style scoped>
.base_right_click_menu {
  width: 152px;
}
/* .base_tree_nav_box /deep/ .ant-tree li span.ant-tree-switcher{
    display: none;
} */
.base_tree_nav_box .ant-layout-header {
  background: #fff;
  z-index: 2;
}

.base_sty_column_box .base_tree_nav_box {
  width: 180px;
}
.base_sty_column_right {
  margin-left: 184px;
}
.ant-page-header {
  padding: 12px 0px !important;
}
.base_tree_nav_box .ant-layout-header {
  padding: 0px 10px 0px 0px;
}
.base_right_click_menu .ant-btn-sm {
  padding: 0;
}
</style>