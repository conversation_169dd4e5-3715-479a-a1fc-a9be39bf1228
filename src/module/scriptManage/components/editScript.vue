<template>
  <div>
    <a-modal :visible="showPop"
      :width="770"
      :maskClosable="false"
      :title="scriptId ? '修改脚本' : '添加脚本'"
      @cancel="closePop">
      <template slot="footer">
        <a-button key="back"
          @click="closePop">取消</a-button>
        <a-button type="primary"
          @click="saveScript">保存</a-button>
      </template>
      <a-layout>
        <a-layout-content class="pop_content"
          style="min-height: calc(270px - 120px);">
          <a-row>
            <a-col :span="3"
              class="label_style">
              <label for="">脚本名称<a class="mark_style">*</a></label>
            </a-col>
            <a-col :span="6">
              <a-input v-model="info.scriptName"
                placeholder="" />
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="3"
              class="label_style">
              <label for="">是否生效<a class="mark_style">*</a></label>
            </a-col>
            <a-col :span="6">
              <a-select v-model="info.state"
                placeholder=""
                @change="handleChange"
                class="select_wid">
                <a-select-option :value="v.key"
                  v-for="v in stateList"
                  :key="v.key">
                  {{ v.value }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="3"
              class="label_style">
              <label for="">编辑脚本</label>
            </a-col>
            <a-col ::span="18">
              <AceEditor style="height: 400px"
                ref="AceEditor"
                :showMode="false"
                v-model="info.scriptContent" />
            </a-col>
          </a-row>
        </a-layout-content>
      </a-layout>

    </a-modal>
  </div>
</template>
<script>
import api from '../api';
import AceEditor from '@c/aceEditor';
export default {
  name: 'editScript',
  data() {
    return {
      info: {
        state: '',
        scriptName: '',
        scriptContent: '',
      },
      stateList: this.$baseDict.base.selectList, //状态列表
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    scriptId: {
      type: String,
      default: '',
    },
    scriptInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        console.log(val);
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
  },
  components: { AceEditor },
  methods: {
    //关闭
    closePop() {
      this.showPop = false;
      this.reset();
    },
    handleChange() {},
    saveScript() {
      let _this = this;
      let param = _this.info;

      if (_this.scriptId == '') {
        api.scriptAdd(param).then((res) => {
          if (res.code == 0) {
            _this.$message.success(res.msg);
            _this.$emit('updateList', true);
            _this.closePop();
          } else {
            _this.$message.error(res.msg);
          }
        });
      } else if (_this.scriptId != '') {
        api.scriptEdit(param).then((res) => {
          if (res.code == 0) {
            _this.$message.success(res.msg);
            _this.$emit('updateList', true);
            _this.closePop();
          } else {
            _this.$message.error(res.msg);
          }
        });
      }
    },
    //重置
    reset() {
      this.info = {
        state: '',
        scriptName: '',
        scriptContent: '',
      };
      this.$nextTick(() => {
        this.$refs.AceEditor &&
          this.$refs.AceEditor.setValue(this.info.scriptContent);
      });
    },
  },
  watch: {
    isPopShow(n) {
      let _this = this;
      if (n) {
        if (_this.scriptId != '') {
          _this.info = _this.scriptInfo;
          setTimeout(() => {
            _this.$nextTick(() => {
              _this.$refs.AceEditor.resize(true);
              if (_this.scriptInfo.scriptContent) {
                _this.$refs.AceEditor.setValue(_this.scriptInfo.scriptContent);
              }
            });
          }, 300);
        } else {
          _this.info = Object.assign(_this.info, _this.scriptInfo);
        }
      }
    },
  },
};
</script>
<style scoped>
.jc_pop_fixed {
  margin-top: -234px;
  margin-left: -500px;
  width: 1000px;
  height: 468px;
}
.jc_pop_fixed /deep/ .ant-table-thead > tr > th:nth-child(2),
.jc_pop_fixed /deep/ .ant-table-thead > tr > td:nth-child(2) {
  width: 120px;
}
.jc_pop_fixed /deep/ .ant-table-thead > tr > th,
.jc_pop_fixed /deep/ .ant-table-tbody > tr > td {
  padding: 16px 0;
}
</style>