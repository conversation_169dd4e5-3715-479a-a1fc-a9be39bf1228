<template>
  <div>
    <a-modal :title="requestId ? `修改脚本组` : `添加脚本组`"
      :visible="showPop"
      @ok="getValue"
      @cancel="closePop">
      <template slot="footer">
        <a-button key="back"
          @click="closePop">
          取消
        </a-button>
        <a-button type="primary"
          key="submit"
          @click="getValue">
          {{ requestId ? "修改" : "添加" }}
        </a-button>
      </template>
      <a-layout>
        <a-layout-content class="pop_content"
          style="min-height: calc(200px - 120px);">
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">脚本组名称<a class="mark_style">*</a></label>
            </a-col>
            <a-col :span="18">
              <a-input v-model="info.scriptName"
                placeholder="" />
            </a-col>
          </a-row>
         
        </a-layout-content>
      </a-layout>
    </a-modal>
  </div>
</template>
<script>
import api from '../api';
export default {
  name: 'addOrEdit',
  data() {
    return {
      info: {
        scriptName: '',
        state: '1',
      },
      stateList: this.$baseDict.base.selectList, //状态列表
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    leftInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    requestId: {
      type: String,
      default: '',
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        console.log(val);
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
  },
  components: {},
  methods: {
    //关闭
    closePop() {
      this.showPop = false;
      this.info = {
        name: '',
        subjectNo: '',
        // state: '',
        description: '',
        answerNumLimit: 2,
        // orderLine: ''
      };
    },
    //提交
    getValue() {
      let _this = this;
      if (_this.info.scriptName == '') {
        _this.$message.error('脚本名称不能为空');
        return false;
      }
      let param = _this.info;
      if (_this.requestId == '') {
        //新增;
        api.scriptAdd(param).then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.$emit('updateList', true);
            _this.closePop();
          } else {
            _this.$message.error(res.msg);
          }
        });
      } else if (_this.requestId != '') {
        //编辑
        let param1 = {
          id: _this.leftInfo.id,
        };
        param = Object.assign(param, param1);
        api.scriptEdit(param).then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.$emit('updateList', true);
            _this.closePop();
          } else {
            _this.$message.error(res.msg);
          }
        });
      }
    },
    handleChange() {},
  },
  watch: {
    isPopShow(n) {
      let _this = this;
      if (n) {
        if (_this.requestId != '') {
          _this.info = {
            scriptName: _this.leftInfo.scriptName,
            state: _this.leftInfo.state,
          };
        }
      }
    },
  },
};
</script>
<style scoped>
.jc_pop_fixed {
  margin-top: -100px;
  margin-left: -175px;
  width: 350px;
  height: 200px;
}
</style>