<template>
  <a-layout-content class="menu-right-content">
    <a-card title="脚本列表"
      :bordered="false">
      <div class="searchForm">
        <a-form-model layout="inline"
          :model="tableForm">
          <tkSelectForm @query="search"
            @reset="reset">
            <a-form-model-item label="脚本名称">
              <a-input placeholder="请输入脚本名称"
                v-model="tableForm.keyWord"></a-input>
            </a-form-model-item>
          </tkSelectForm>
        </a-form-model>
      </div>
      <a-row class="btn_row">
        <a-col :span="24">
          <a-button type="primary"
            icon="plus"
            @click="dealYb('add')"
            :disabled="!id">新增</a-button>
          <a-button type="danger"
            icon="delete"
            @click="dealYb('remove')"
            :disabled="selectedRowKeys.length == 0">删除</a-button>
          <a-button type="primary"
            icon="redo"
            @click="dealYb('redo')"
            :disabled="!id">刷新</a-button>
        </a-col>
      </a-row>
      <a-table :columns="columns"
        :data-source="data"
        :loading="loading"
        :pagination="pagination"
        :customRow="customRow"
        :row-selection="{
				fixed: true,
				selectedRowKeys: selectedRowKeys,
				onChange: onSelectChange
			}"
        @change="handleChange"
        row-key="key">
        <template slot="name"
          slot-scope="name">
          <p class="single_omit"
            :title="name">{{ name }}</p>
        </template>
        <template slot="description"
          slot-scope="description">
          <p class="single_omit"
            :title="description">
            {{ description }}
          </p>
        </template>
        <template slot="state"
          slot-scope="state">
          {{ state | dealType(stateList) }}
        </template>
        <a slot="action"
          slot-scope="text, record"
          @click.stop="() => toBusinessConfig(record)">编辑脚本</a>
      </a-table>
      <a-pagination style="margin-top: 10px;text-align: right;"
        :total="total"
        :current="current"
        :pageSize="pageSize"
        :pageSizeOptions="pageSizeOptions"
        show-size-changer
        show-quick-jumper
        :show-total="total => `共 ${total} 条`"
        @change="change"
        @showSizeChange="showSizeChange">
      </a-pagination>
      <editOrEditScript :isPopShow.sync="isScriptPopShow"
        @updateList="updateList"
        :scriptId="scriptId"
        :scriptInfo="scriptInfo"></editOrEditScript>
      <upload :visible.sync="uploadShow"
        downloadeTitle=""
        @success="query" />
    </a-card>
  </a-layout-content>
</template>

<script>
// import { surveyQuestionList,surveyQuestionDelete } from "@/service/service.js";
import upload from '@c/upload/upload';
import api from '../api';
export default {
  name: 'rightManage',
  data() {
    return {
      tableForm: {
        keyWord: '',
      },
      stateList: this.$baseDict.base.selectList, //状态列表
      //table start
      //表格数据
      data: [],
      //表头数据
      columns: [
        {
          title: '脚本名称',
          dataIndex: 'scriptName',
          key: 'script_name',
          sorter: true,
          ellipsis: true,
          scopedSlots: { customRender: 'scriptName' },
        },
        {
          title: '是否生效',
          dataIndex: 'state',
          key: 'state',
          sorter: true,
          scopedSlots: { customRender: 'state' },
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'create_time',
          sorter: true,
          scopedSlots: { customRender: 'createTime' },
        },
        {
          title: '更新时间',
          dataIndex: 'updateTime',
          key: 'update_time',
          sorter: true,
          scopedSlots: { customRender: 'updateTime' },
        },
        {
          title: '操作',
          key: 'operation',
          fixed: 'right',
          width: 200,
          scopedSlots: { customRender: 'action' },
        },
      ],

      selectedRowKeys: [], // Check here to configure the default column
      pagination: false, //是否显示table分页
      loading: false, //是否显示loading
      orderByColumn: '', //排序name
      isAsc: '', //排序方向
      scroll: { x: 900, y: 680 },
      //table end

      //pagination start
      total: 0, //table总页数
      current: 1, //当前页数
      pageSize: 10, //每页条数
      pageSizeOptions: ['10', '20', '50', '100'], //指定每页可以显示多少条
      //pagination end

      //编辑脚本弹窗
      isScriptPopShow: false, //弹窗是否显示
      scriptId: '', // 脚本Id
      scriptInfo: {}, //脚本信息

      uploadShow: false,
    };
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    leftInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  created() {},
  components: {
    upload,
    editOrEditScript: () =>
      import(
        /* webpackChunkName: "editOrEditScript" */ './editOrEditScript.vue'
      ),
  },
  methods: {
    query() {
      this.queryTable();
      this.$emit('uploadSuccess');
    },

    reset() {
      this.tableForm.keyWord = '';
      this.updateList();
    },

    search() {
      this.queryTable();
      this.selectedRowKeys = [];
    },
    //新增刷新
    updateList(param) {
      if (param) {
        this.current = 1;
        this.queryTable();
      }
    },
    //请求table
    queryTable() {
      let _this = this;
      this.loading = true;
      api
        .getScriptPage({
          scriptName: this.tableForm.keyWord,
          parentId: this.id,
          pageNum: this.current,
          pageSize: this.pageSize,
          orderBy: this.orderByColumn,
          isDesc: this.isAsc,
        })
        .then((res) => {
          this.loading = false;
          this.selectedRowKeys = [];
          if (res.code == 0) {
            if (res.data) {
              let data = res.data;
              this.total = data.total;
              this.current = data.pageNum;
              this.data = data.list;
            } else {
              this.data = [];
            }
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          _this.$message.error(e.message);
        });
    },
    //排序
    handleChange(pagination, filters, sorter) {
      this.orderByColumn = sorter.columnKey;
      this.isAsc = sorter.order && sorter.order == 'descend' ? '0' : '1';
      this.queryTable();
    },
    //单选全选
    onSelectChange(selectedRowKeys) {
      console.log('selectedRowKeys changed: ', selectedRowKeys);
      this.selectedRowKeys = selectedRowKeys;
    },
    //点击一行选中效果
    customRow(record, index) {
      let _this = this;
      return {
        on: {
          click: () => {
            if (!_this.selectedRowKeys.includes(index)) {
              _this.selectedRowKeys = [index];
            }
          },
        },
      };
    },
    //页码改变的回调，参数是改变后的页码及每页条数
    change(page, pageSize) {
      console.log(page);
      console.log(pageSize);
      this.current = page;
      this.pageSize = pageSize;
      this.queryTable();
    },
    //总数变化时回调
    showSizeChange(current, size) {
      this.current = current;
      this.pageSize = size;
      this.queryTable();
    },
    //处理
    dealYb(val) {
      let _this = this;
      if (val == 'add') {
        //添加
        _this.scriptInfo.parentId = _this.leftInfo.uuid;
        _this.scriptId = '';
        _this.isScriptPopShow = true;
      } else if (val == 'remove') {
        console.log(_this.selectedRowKeys);
        //删除
        if (_this.selectedRowKeys.length > 0) {
          _this.remove();
        }
      } else if (val == 'redo') {
        //刷新
        _this.queryTable();
      }
    },
    //删除
    remove() {
      let _this = this;
      _this.$confirm({
        title: '温馨提示',
        content: `是否确认删除?`,
        okType: 'danger',
        onOk() {
          _this.deleteApi();
        },
      });
    },
    //请求删除接口
    deleteApi() {
      let _this = this,
        arr = [],
        str = '';
      _this.selectedRowKeys.forEach((v) => {
        arr.push(_this.data[v].id);
      });
      str = arr.join(',');
      console.log(str);
      if (_this.selectedRowKeys.length === 1) {
        api
          .scriptDelete({
            bcSurveyAnswerScriptId: str,
          })
          .then((res) => {
            if (res.code == 0) {
              _this.$message.success(res.msg);
              _this.queryTable();
            } else {
              _this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            _this.$message.error(e.message);
          });
      } else {
        api
          .scriptListDelete({
            bcSurveyAnswerScriptIds: str
          })
          .then((res) => {
            if (res.code === 0) {
              _this.$message.success(res.msg);
              _this.queryTable();
            } else {
              _this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            _this.$message.error(e.message);
          });
      }
    },
    //编辑脚本
    toBusinessConfig(record) {
      let _this = this;
      _this.scriptId = record.uuid;
      _this.scriptInfo = record;
      _this.isScriptPopShow = true;
    },
  },
  watch: {
    id: {
      handler: function (n) {
        if (n) {
          this.isKey = '';
          this.$nextTick(() => {
            this.current = 1;
            this.queryTable();
          });
        }
      },
      deep: true,
    },
  },
  filters: {
    dealType(val, list) {
      if (!val) return false;
      let arr = [];
      arr = list.filter((v) => {
        return v.key == val;
      });
      return arr[0] ? arr[0].value : '';
    },
  },
};
</script>

<style>
</style>
