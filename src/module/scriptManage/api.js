// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /** 查询脚本分组
   * @param {Object} param
   */
  @Parameters(["_data"])
  getScriptGroupList() {
    return this.services.initGet({
      reqUrl: "bcSurveyAnswerScript/group/list",
      param: this.param,
    });
  }

  /** 查询脚本列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getScriptList() {
    return this.services.initGet({
      reqUrl: "bcSurveyAnswerScript/list",
      param: this.param,
    });
  }

  /** 查询脚本翻页列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getScriptPage() {
    return this.services.initGet({
      reqUrl: "bcSurveyAnswerScript/page",
      param: this.param,
    });
  }

  /** 查询指定脚本表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getScriptQuery() {
    return this.services.initGet({
      reqUrl: "bcSurveyAnswerScript/query",
      param: this.param,
    });
  }

  /** 查询指定脚本表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getScriptQueryByUid() {
    return this.services.initGet({
      reqUrl: "bcSurveyAnswerScript/queryByUid",
      param: this.param,
    });
  }

  /** 新增脚本表
   * @param {Object} param
   */
  @Parameters(["_data"])
  scriptAdd() {
    return this.services.initPost({
      reqUrl: "bcSurveyAnswerScript/add",
      param: this.param,
    });
  }

  /** 修改脚本表
   * @param {Object} param
   */
  @Parameters(["_data"])
  scriptEdit() {
    return this.services.initPost({
      reqUrl: "bcSurveyAnswerScript/edit",
      param: this.param,
    });
  }

  /** 删除指定脚本表
   * @param {Object} param
   */
  @Parameters(["_data"])
  scriptDelete() {
    return this.services.initPost({
      reqUrl: "bcSurveyAnswerScript/delete",
      param: this.param,
    });
  }

  /** 批量删除脚本表
   * @param {Object} param
   */
  @Parameters(["_data"])
  scriptListDelete() {
    return this.services.initPost({
      reqUrl: "bcSurveyAnswerScript/deletes",
      param: this.param,
    });
  }
}
export default new api();
