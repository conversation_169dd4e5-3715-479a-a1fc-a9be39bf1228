<!--
 * @Author:xuedongLi
 * @Date: 2021-04-06 10:17:21
 * @LastEditTime: 2021-09-30 17:46:04
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \work-manage-view\src\module\branch\module\upload.vue
-->
<template>
	<a-modal
		title="导入"
		:width="500"
		v-model="isvisible"
		ok-text="确认"
		cancel-text="取消"
		@ok="submit"
		@cancel="getModuleReset"
		:maskClosable="false"
	>
		<a-form-model
			ref="form"
			:model="form"
			:label-col="{ span: 8 }"
			:wrapper-col="{ span: 14 }"
		>
			<a-form-model-item label="导入文件">
				<a-upload
					:fileList.sync="fileList"
					name="file"
					:multiple="true"
					:beforeUpload="() => false"
					@change="handleChange"
					accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
				>
					<a-button> <a-icon type="upload" /> 上传文件 </a-button>
				</a-upload>
			</a-form-model-item>
			<a-form-model-item label="模板文件">
				<a @click="downloademon">
					<a-icon type="download" />下载存管银行模板文件
				</a>
			</a-form-model-item>
		</a-form-model>
	</a-modal>
</template>
<script>
	import modalMixed from "@u/modalMixed";
	import { request } from "bus-common-component/lib/extension";
	// import api from '../api';
	export default {
		data() {
			return {
				form: {}, // 权限添加表单
				dictFiledShow: false,
				// 异步加载
				confirmLoading: false,
				file: {},
				fileList: []
			};
		},
		// inject: ["api"],
		mixins: [modalMixed],
		inject: ["api"],
		watch: {
			isvisible(newVal) {
				if (newVal) {
					this.file = undefined;
					this.fileList = [];
				}
			}
		},
		methods: {
			downloademon() {
				window.location.href =
					window.$hvue.customConfig.fileUrl +
					"/bc/excel/********/92fa8df7cda24daf85bc8302ce1d1d9e.xlsx";
			},
			getModuleReset() {},
			// 提交数据权限分组创建
			submit() {
				if (!this.file || this.fileList.length <= 0)
					return this.$message.error(`上传文件不能为空`);
				let data = new FormData();
				data.set("file", this.file);
				new request({ address: "/bc-manage-server" })
					.upload({
						reqUrl: "bcBank/import",
						param: data
					})
					.then(({ code, msg }) => {
						if (code != 0)
							return this.$message.error(`上传失败：${msg}`);
						this.$message.success("上传成功！");
						this.isvisible = false;
						this.$emit("success");
					});
			},
			handleChange({ file, fileList }) {
				this.file = file;
				if (fileList.length >= 0 && fileList.length <= 1) {
					this.fileList = fileList;
				}
			}
		}
	};
</script>
