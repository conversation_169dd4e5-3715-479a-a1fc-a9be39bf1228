// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
	 * 查询已上架业务类型
	 * @param {Object} param
	 * - isShelfed {String} 是否上架
	 */
	@Parameters(['isShelfed'])
	queryBusinessList() {
		return this.services.initGet({
			reqUrl: 'bcBusinessShelf/list',
			param: this.param,
		});
	}

	/**
	 * 作废流水
	 * @param {Object} param
	 * - ids {String} 唯一标识
	 *
	 */
	@Parameters(['ids'])
	invalidForm() {
		return this.services.initPost({
			reqUrl: 'flowIns/invalid',
			param: this.param,
		});
	}

	/**
	 * 查询受理单详情
	 * @param {Object} param
	 * - bizType {String} 	业务类型
	 * - flowInsId {String} 	流程实例Id
	 * - formId {String} 	表单Id
	 */
	@Parameters(['bizType', 'flowInsId', 'formId'])
	queryFormDetail() {
		return this.services.initGet({
			reqUrl: 'flowIns/query',
			param: this.param,
		});
	}

	/**
	 * 查询问卷
	 * @param {Object} param
	 * - paperAnswer {String} 	问卷答案
	 * - paperNo {String} 	问卷id
	 */
	@Parameters(['paperAnswer', 'paperNo'])
	getSubjectInfoByUserAnswer() {
		return this.services.initPost({
			reqUrl: 'survey/getSubjectInfoByUserAnswer',
			param: this.param,
		});
	}

	/**
	 * 受理单流水步骤日志
	 * @param {Object} param
	 * - bizType {String} 	业务类型
	 * - flowInsId {String} 	流程实例Id
	 * - formId {String} 	表单Id
	 */
	@Parameters(['bizType', 'flowInsId', 'formId'])
	queryFlowStepLop() {
		return this.services.initGet({
			reqUrl: 'flowIns/queryFlowStepLop',
			param: this.param,
		});
	}

	/**
	 * 查询协议
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	queryAgreement() {
		return this.services.initGet({
			reqUrl: 'flowIns/queryAgreement',
			param: this.param,
		});
	}

	/**
	 * 查询中金受理单办理结果
	 * @param {Object} param
	 * - bizType {String} 	业务类型
	 * - formId {String} 	表单Id
	 */
	@Parameters(['bizType', 'formId'])
	zjHandleResult() {
		return this.services.initGet({
			reqUrl: 'bcBusinessHandleResult/list',
			param: this.param,
		});
	}

	/**
	 * 查询新网厅首页列表
	 * @param {Object} param
	 * - status {String} 是否有效
	 */
	@Parameters(['status'])
	queryBcClientPortalList() {
		return this.services.initGet({
			reqUrl: 'bcClientPortal/list',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters([])
	cancerTaskOpen() {
		return this.waServices.initGet({
			reqUrl: 'audit/cancerTaskOpen',
			param: this.param,
		});
	}

  /**
   * 作废受理单前同步审核任务
   * @param {Object} param
   * - formId {String} 	表单Id
   * - bizType {String} 	业务类型
   * - reason {String} 	原因
   */
   @Parameters(["formId", "bizType", "reason"])
   taskCancel() {
     return this.services.initPost({
       reqUrl: "flowIns/taskCancel",
       param: this.param,
     });
   }

	/**
	 * 查询规则引擎数据字典
	 * @param {Object} param
	 */
	@Parameters(["_data"])
	queryDataDictionaries() {
		return this.workServices.initGet({
			reqUrl: "dict/map",
			param: this.param,
		});
	}

	/**
	 * 查询历史受理单详情
	 * @param {Object} param
	 * - bizType {String} 	业务类型
	 * - flowInsId {String} 	流程实例Id
	 * - formId {String} 	表单Id
	 */
	@Parameters(['bizType', 'flowInsId', 'formId'])
	queryFormDetailHis() {
		return this.services.initGet({
			reqUrl: 'flowInsHis/query',
			param: this.param,
		});
	}
}

export default new api();
