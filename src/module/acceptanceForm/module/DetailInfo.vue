<template>
  <div>
    <a-layout>
      <a-layout-header style="background: #fff;">
        <a-row class="pg-header pg-header-top">
          <a-col :span="8"><a style="font-size: 18px;">
              <a-icon type="solution" /> &nbsp;{{ detailInfo.bizName }}({{
                detailInfo.bizType
              }})
            </a></a-col>
        </a-row>
      </a-layout-header>
      <a-layout-content style="background: #f9f9f9;padding: 10px 25px 90px 25px; ">
        <!-- 基本信息-->
        <basic-info v-if="JSON.stringify(detailInfo) !== '{}'"
          :detailInfo="detailInfo"
          :fundAccount="fundAccount" />
        <a-row v-if="showSpin"
          style="margin: 50px 45%;">
          <a-spin size="large" />
        </a-row>
        <!-- 基金公司信息 -->
        <detail-info-item v-if="fundCompanyInfo.length !== 0"
          type="fundCompanyInfo"
          areaTitle="基金公司信息"
          :sourceArray="fundCompanyInfo" />
        <!-- 引流信息 -->
        <detail-info-item v-if="channelInfo.length !== 0"
          type="channel"
          areaTitle="引流信息"
          :sourceArray="channelInfo" />
        <!-- 居间人信息 -->
        <detail-info-item v-if="intermediaryInfo.length!==0"
          type="intermediay"
          areaTitle="居间人信息"
          :sourceArray="intermediaryInfo" />
        <!-- 图片信息 -->
        <detail-info-item v-if="imageInfo.length!==0&&(imageInfo[0].value||imageInfo[1].value)"
          type="image"
          areaTitle="身份信息"
          :sourceArray="imageInfo" />
        <!-- ocr信息 -->
        <detail-info-item v-if="ocrInfo.length!==0"
          type="ocr"
          areaTitle="身份信息"
          :sourceArray="ocrInfo" />
        <!-- 辅助证件 -->
        <detail-info-item v-if="auxiImageInfo.length!==0"
          type="image"
          areaTitle="辅助证件"
          :sourceArray="auxiImageInfo" />
        <!-- 完善信息 -->
        <detail-info-item v-if="userChangeInfo.length!==0"
          type="userChangeInfo"
          areaTitle="完善信息"
          :sourceArray="userChangeInfo" />
        <!-- 单向视频信息 -->
        <detail-info-item v-if="oneWayVideoInfo.length!==0"
          type="oneWayVideo"
          areaTitle="视频信息"
          :sourceArray="oneWayVideoInfo" />
        <!-- 视频信息 -->
        <detail-info-item v-if="videoInfo.length!==0"
          type="video"
          areaTitle="视频信息"
          :sourceArray="videoInfo" />
        <!-- 账户信息 -->
        <detail-info-item v-if="accountInfo.length!==0"
          type="account"
          areaTitle="账户信息"
          :sourceArray="accountInfo" />
        <!-- 业务准入 -->
        <detail-info-item v-if="clientInfo.length!==0"
          type="clientInfo"
          areaTitle="客户修改信息"
          :sourceArray="clientInfo" />
        <!-- 业务准入 -->
        <detail-info-item v-if="accessInfo.length!==0"
          type="access"
          areaTitle="业务准入"
          :sourceArray="accessInfo" />
        <!-- 适当性信息 -->
        <detail-info-item v-if="suitInfo.length!==0"
          type="suit"
          areaTitle="适当性信息"
          :sourceArray="suitInfo" />
        <!-- 银行信息 -->
        <detail-info-item v-if="bankInfo.length!==0"
          type="bank"
          areaTitle="银行信息"
          :sourceArray="bankInfo" />
        <!-- 客户扩展信息 -->
        <detail-info-item v-if="extInfo.length!==0"
          type="ext"
          areaTitle="客户扩展信息"
          :sourceArray="extInfo" />
        <!-- 协议 -->
        <detail-info-item v-if="agreeInfo.length!==0"
          type="agree"
          areaTitle="协议"
          :sourceArray="agreeInfo" />
        <!-- 知识测评 -->
        <detail-info-item v-if="knowInfo.length!==0"
          type="know"
          areaTitle="知识测评"
          :sourceArray="knowInfo" />
        <!-- 征信测评 -->
        <detail-info-item v-if="creditInfo.length!==0"
          type="credit"
          areaTitle="征信测评"
          :sourceArray="creditInfo" />
        <!-- 回访问卷 -->
        <detail-info-item v-if="visitInfo.length!==0"
          type="visit"
          areaTitle="回访问卷"
          :sourceArray="visitInfo" />
        <!-- 特殊信息申报 -->
        <detail-info-item v-if="specialInfo.length!==0"
          type="specail"
          areaTitle="特殊信息申报"
          :sourceArray="specialInfo" />
        <!-- 纸质协议 -->
        <detail-info-item v-if="paperAgreeInfo.length!==0"
          type="paperAgree"
          areaTitle="纸质协议"
          :sourceArray="paperAgreeInfo" />
        <!-- 打印协议 -->
        <detail-info-item v-if="agreePrintInfo.length!==0"
          type="agreePrint"
          areaTitle="打印协议"
          :sourceArray="agreePrintInfo" />
        <!-- 拟接受服务风险等级 -->
        <detail-info-item v-if="corpRiskInfo.length!==0"
          type="corpRisk"
          areaTitle="拟接受服务风险等级"
          :sourceArray="corpRiskInfo" />
        <!-- 更多信息 -->
        <detail-info-item v-if="otherData.length!==0"
          type="other"
          areaTitle="更多表单项"
          :sourceArray="otherData" />
      </a-layout-content>

      <a-layout-footer class="btn-block"
        style="width: 100%">
        <a-button type="primary"
          @click.stop="closePop"
          style="width: 400px;height: 40px;font-size: 16px;">关闭</a-button>
      </a-layout-footer>
    </a-layout>
  </div>
</template>
<script>
import BasicInfo from './BasicInfo';
import api from '../api';
import DetailInfoItem from './DetailInfoItem';
import { uniq } from "lodash";
export default {
  name: 'DetailInfo',
  data() {
    return {
      detailInfo: sessionStorage.getItem('detailInfo')
        ? JSON.parse(sessionStorage.getItem('detailInfo'))
        : {},
      fundAccount: '',
      showSpin: true,
      channelInfo: [],
      intermediaryInfo: [],
      ocrInfo: [],
      imageInfo: [],
      auxiImageInfo: [],
      userChangeInfo: [],
      oneWayVideoInfo: [],
      videoInfo: [],
      accountInfo: [],
      accessInfo: [],
      suitInfo: [],
      bankInfo: [],
      extInfo: [],
      clientInfo: [],
      agreeInfo: [],
      knowInfo: [],
      creditInfo: [],
      visitInfo: [],
      specialInfo: [],
      otherData: [],
      paperAgreeInfo: [],
      corpRiskInfo: [],
      agreePrintInfo: [],
      fundCompanyInfo:[]
    };
  },
  provide: [api],
  components: {
    BasicInfo,
    DetailInfoItem,
  },
  created() {
    // this.queryUserInfo();
    this.queryClientDetail();
  },
  methods: {
    // 查询客户资料详情
    queryClientDetail() {
      let func = sessionStorage.getItem('fromHisPage') === '1' ? 'queryFormDetailHis' : 'queryFormDetail'
      api[func]({
          bizType: this.detailInfo.bizType,
          flowInsId: this.detailInfo.id,
          formId: this.detailInfo.formId,
        })
        .then((res) => {
          if (res.code == 0) {
            this.showSpin = false;
            this.sortData(res.data);
          } else {
            this.$message.error(res.message);
          }
        });
    },
    getValue(prop, data, type) {
      const obj = data.find((item, index) => {
        if (item.filedKey.toLowerCase() === prop) {
          this.otherData.splice(index, 1);
          return item;
        }
      });
      if (!obj || !obj.filedValue) return '';
      if (type === 'file') {
        try {
          let path = JSON.parse(obj.filedValue)
          if(path.src){
            return window.$hvue.customConfig.fileUrl + path.src
          }
          return path
        } catch (e) {
          return window.$hvue.customConfig.fileUrl + obj.filedValue;
        }
      }else if(type === 'video'){
        return '/bc-manage-server/file/rangeDownload?path=' + obj.filedValue;
      }else {
        return obj.filedValue;
      }
    },
    async sortData(data) {
      if (data.selectFundVoList) {
        this.fundCompanyInfo = [{ key: '已选择基金公司', filedValue: '已选择基金公司', value: data.selectFundVoList.map(item => item.fundCompanyName).join(',  ') }]
      }
      if (data.cliCompareVos) {
        this.clientInfo = data.cliCompareVos.map(item => {
          return {
            key:item.fieldName,
            value:`${item.sourceField} ==> ${item.targetField}`
          }
        })
      }
      let formData = await Promise.all(data.formDetailList.map(async (item) => {
        if (item.isEnum === '1' && item.filedValue) {
          let dictList = await this.queryDictProps(item.dictKey);
          let dictLabelList = [];
          dictList = dictList || []
          item.filedValue.split(',').forEach((item) => {
            let dictObj = dictList.find((itm) => itm.dictValue === item);
            dictLabelList.push(dictObj ? dictObj.dictLabel : item);
          });
          item.filedValue = dictLabelList.join('，');
          return item;
        } else {
          if (item.filedKey === 'UN_USER_SERVICES' || item.filedKey === 'UN_USER_ADVISER' || item.filedKey === 'UN_USER_PROD') {
            return {
              filedKey:item.filedKey,
              filedDesc: item.filedDesc,
              filedValue:JSON.parse(item.filedValue?item.filedValue:'[]').map(it => `${it.businessName}（${it.riskLvlName}）`).join(',')
            } 
          }else{
            return item;
          }
        }
      }))
      this.otherData = formData;
      let videoData = data.witnessInfoVO;
      let accessData = data.taskResultJson;
      let bpAccessData = data.bpResultJson
      let agreementData = data.agreementRecordList;
      this.fundAccount = this.getValue('fund_account', formData);
      this.getValue('update_time', formData);
      this.getValue('branch_no', formData);
      this.getValue('branch_name', formData);
      this.getValue('client_id', formData);
      this.getValue('full_name', formData);
      this.getValue('op_source', formData);
      this.getValue('nationality', formData);
      this.getValue('client_status', formData);
      this.getValue('epaper_sign_json', formData);
      const video_json = this.getValue('video_json', formData)
      const recommendNo = this.getValue('recommend_no', formData); // 推荐人编号
      const intermediary = this.getValue('intermediary', formData); // 居间人编号
      const idNo = this.getValue('id_no', formData); // 身份证号 用作年龄的判断
      let endDate = this.getValue('id_enddate', formData); // 证件结束日期
      endDate = endDate !== '********' ? endDate : '长期';
      // const riskScore = this.getValue('corp_risk_core', formData); // 风测的得分，用作是否查看风测问卷的条件
      const knowScore = this.getValue('knowledge_score', formData) || this.getValue('paper_score', formData); // 知识测评的得分，用作是否查看知识测评的条件
      const paperAns = this.getValue('paper_answer', formData); // 问卷的答案
      const creditScore = this.getValue('credit_score', formData); // 征信测评的得分
      const creditPaperAns = this.getValue('credit_paper_answer', formData); // 征信测评的答案
      const revisitPaperAns = this.getValue('revisit_paper_answer', formData); // 问卷回访的答案
      const bankJson = this.getValue('choose_bank_json', formData); // 银行JSON数据
      const accountJson = this.getValue('qry_account_param', formData) || this.getValue('selected_accounts_data', formData); // 证券账户JSON数据
      const paperAgreeJson = this.getValue('paper_pic_json', formData); // 纸质协议JSON数据
      const corpRiskJson = this.getValue('en_corp_risk_level_json', formData); // 拟接受服务风险等级JSON数据
      const agreePrintJson = this.getValue('agree_print_json', formData); // 打印协议JSON数据
      this.channelInfo =
        this.detailInfo.channelNo || recommendNo
          ? [
              { key: '引流渠道编号', value: this.detailInfo.channelNo },
              { key: '推荐人编号', value: recommendNo },
              { key: '推荐人姓名', value: '' },
              { key: '推广备注信息', value: '' },
            ]
          : [];
      this.intermediaryInfo =
        this.detailInfo.bizType === '017001' && intermediary
          ? [
              { key: '居间人编号', value: intermediary },
              { key: '居间人姓名', value: '' },
            ]
          : [];
      this.imageInfo =
        this.detailInfo.investorType === '01'
          ? [
              {
                key: '人像面',
                value: this.getValue('id_card_portrait', formData, 'file'),
              },
              {
                key: '国徽面',
                value: this.getValue('id_card_national', formData, 'file'),
              },
              {
                key: '大头照',
                value: this.getValue('bareheaded_pic', formData, 'file'),
              },
              {
                key: '公安照',
                value: this.getValue(
                  'public_security_avatar',
                  formData,
                  'file'
                ),
              },
              {
                key: '手持证件照',
                value: this.getValue('id_card_in_hand', formData, 'file'),
              },
              {
                key: '手写签名',
                value: this.getValue('sign_name_pic', formData, 'file'),
              },
              {
                key: '合照',
                value: this.getValue('group_pic', formData, 'file'),
              },
            ]
          : [
              {
                key: '经办人身份证人像面',
                value: this.getValue('contact_id_card_portrait',formData,'file'),
              },
              {
                key: '经办人身份证国徽面',
                value: this.getValue('contact_id_card_national',formData,'file'),
              },
              {
                key: '经办人免冠照',
                value: this.getValue('contact_bareheaded_pic',formData,'file'),
              },
              {
                key: '经办人公安照',
                value: this.getValue('contact_police_avatar',formData,'file'),
              },
              {
                key: '法人身份证人像面',
                value: this.getValue('instrepr_id_card_portrait',formData,'file')
              },
              {
                key: '法人身份证国徽面',
                value: this.getValue('instrepr_id_card_national',formData,'file'),
              },
              {
                key: '法人免冠照',
                value: this.getValue('instrepr_bareheaded_pic',formData,'file'),
              },
              {
                key: '法人公安照',
                value: this.getValue('instrepr_police_avatar',formData,'file'),
              },
              {
                key: '法定代表人证明书',
                value:  this.getValue('instrepr_certifiy_pic',formData,'file'),
              },
              {
                key: '授权委托书',
                value: this.getValue('instrepr_attorney_pic',formData,'file')
              },
              {
                key: '工商营业执照',
                value: this.getValue('business_id_pic', formData, 'file'),
              },
              {
                key: '电子营业执照',
                value: this.getValue('e_busi_pic', formData, 'file'),
              },
            ];
      this.auxiImageInfo = this.getValue('auxi_id_img_data', formData, 'file') ?
      [{
        key: '辅助证件',
        value: this.getValue('auxi_id_img_data', formData, 'file'),
      }] : [];
      this.oneWayVideoInfo = this.getValue('witness_way', formData).indexOf( '单向') != -1 ? [{
        key: '视频源',
        value: this.getValue('witness_video', formData, 'video'),
      },
      {
        id: 'videoType',
        key: '视频类型',
        value: '单向',
      },
      {
        id: 'videoLength',
        key: '视频大小(Byte)',
        value: this.getValue('video_size', formData) || '',
      },{
          id: 'totalTime',
          key: '视频时长(秒)',
          value:  this.getValue('video_length', formData) || ''
        },] : []
        if(this.getValue('changed_client_info_key', formData) == '1') {
          this.userChangeInfo = [{
            key: '常住地址',
            orgin: this.getValue('address', formData) || '--',
            modified: this.getValue('new_adress', formData) || '--'
          },{
            key: '手机号码',
            orgin: this.getValue('mobile_tel', formData) || '--',
            modified: this.getValue('new_mobile_tel', formData) || '--'
          },{
            key: '职业代码',
            orgin: this.getValue('profession_code', formData) || '--',
            modified: this.getValue('new_profession_code', formData) || '--',
          },{
            key: '邮编',
            orgin: this.getValue('zipcode', formData) || '--',
            modified: this.getValue('new_zipcode', formData) || '--'
          }];
        }
      this.ocrInfo = this.detailInfo.investorType === '01'?[
        { key: '姓名', value: this.getValue('client_name', formData) },
        { key: '性别', value: this.getValue('client_gender', formData) },
        { key: '年龄', value: this.getAge(idNo) },
        { key: '证件类型', value: this.getValue('id_kind', formData) },
        { key: '证件号码', value: idNo },
        {
          key: '有效期限',
          value: this.getValue('id_begindate', formData) + '  ~  ' + endDate,
        },
        { key: '民族', value: this.getValue('nation_id', formData) },
        { key: '生日', value: this.getValue('birthday', formData) },
        { key: '签发机关', value: this.getValue('issued_depart', formData) },
        { key: '证件地址', value: this.getValue('id_address', formData) },
        { key: '联系电话', value: this.getValue('mobile_tel', formData) },
        { key: '联系地址', value: this.getValue('address', formData) },
      ]:[{ key: '机构全称', value: this.getValue('client_name', formData) },
        { key: '证件类型', value: this.getValue('id_kind', formData) },
        { key: '证件号码', value: idNo },
        {
          key: '有效期限',
          value: this.getValue('id_begindate', formData) + '  ~  ' + endDate,
        },
        { key: '注册国家', value: this.getValue('register_country', formData) },
        { key: '注册币种', value: this.getValue('register_money_type', formData) },
        { key: '注册地', value: this.getValue('register_address', formData) },
        { key: '注册资金', value: this.getValue('register_fund', formData) },
        { key: '签发机关', value: this.getValue('business_id_depart', formData) },
        { key: '证件地址', value: this.getValue('id_address', formData) },
        { key: '联系电话', value: this.getValue('mobile_tel', formData) },
        { key: '联系地址', value: this.getValue('address', formData) },
        { key: '法人类别', value: this.getValue('shdc_instrepr_type', formData) },
      ]
      if(['010044', '010252'].includes(this.detailInfo.bizType)) {
        this.ocrInfo.push({
           key: '业务说明', value: this.getAccJsonValue(accountJson, 'operateType')
        })
      }
      this.videoInfo = videoData
        ? [
            {
              key: '视频源',
              value: this.getValue('witness_video', formData, 'file'),
            },
            {
              id: 'videoType',
              key: '视频类型',
              value: videoData.videoType
                ? videoData.videoType === '1'
                  ? '单向'
                  : '双向'
                : '',
            },
            {
              id: 'videoLength',
              key: '视频大小(Byte)',
              value: videoData.videoLength ? videoData.videoLength : '',
            },
            {
              id: 'receiveTime',
              key: '接入时间',
              value: videoData.receiveTime ? videoData.receiveTime : '',
            },
            {
              id: 'totalTime',
              key: '视频时长(秒)',
              value: videoData.totalTime ? videoData.totalTime : '',
            },
            {
              id: 'staffName',
              key: '见证人员',
              value: videoData.staffName ? videoData.staffName : '',
            },
            {
              id: 'tips',
              key: '聊天记录',
              value: videoData.tips ? videoData.tips : '',
            },
          ]
        : [];
      if(this.videoInfo.length > 0 && this.videoInfo[0].value === '') {
        try {
          const src = JSON.parse(JSON.parse(video_json).g_both_witness_video_url).src
          this.videoInfo[0].value = window.$hvue.customConfig.fileUrl + src
        }catch(e) {
          console.log(e)
        }
      }
      this.accountInfo =
        this.detailInfo.bizType === '017001'
          ? [
              {
                key: '期货账户',
                value: this.getValue('futures_markets', formData),
              },
            ]
          : [
              { key: '资金账号', value: this.fundAccount },
              { key: '市场', value: this.getValue('exchange_type', formData) },
              {
                key: '证券账户',
                value: accountJson,
                value1: accountJson ? this.handleAccount(accountJson) : '',
              },
            ];
       this.accessInfo = accessData
        ? !bpAccessData
          ? [
              {
                id: "taskResultJson",
                key: "客户准入检查报告",
                value: accessData,
              },
            ]
          : [
              {
                id: "taskResultJson",
                key: "客户准入检查报告",
                value: accessData,
              },
              {
                id: "bpResultJson",
                key: "跑批准入检查报告",
                value: bpAccessData,
              },
            ]
        : [];
      this.suitInfo = [
        {
          key: '专业投资者标识',
          value: this.getValue('prof_flag', formData)
        },
        {
          key: '适当性是否匹配',
          value: this.getValue('matching_result', formData)
        },
        {
          key: '业务风险等级',
          value: this.getValue('bus_suitability_grade', formData)
        },
        {
          key: '业务投资期限',
          value: this.getValue('pr_invest_term', formData)
        },
        {
          key: '业务投资品种',
          value: this.getValue('pr_invest_kind', formData)
        },
        {
          key: '客户投资期限',
          value: this.getValue('en_invest_term', formData)
        },
        {
          key: '客户投资品种',
          value: this.getValue('en_invest_kind', formData)
        },
        {
          key: '客户风险等级',
          value: this.getValue('corp_risk_level', formData)
        }
      ]
      // this.suitInfo = [
      //   {
      //     key: '投资者适当性分类',
      //     value: this.getValue('bus_suitability_grade', formData),
      //   }, // dict
      //   {
      //     key: '风险测评时间',
      //     value: this.getValue('corp_begin_date', formData),
      //   },
      //   {
      //     key: '风测评分',
      //     value: riskScore,
      //     value1: riskScore && this.getValue('paper_no', formData),
      //   },
      //   { key: '风险等级', value: this.getValue('corp_risk_level', formData) },
      //   {
      //     key: '风险测评到期日期',
      //     value: this.getValue('corp_end_date', formData),
      //   },
      //   { key: '投资品种', value: this.getValue('pr_invest_kind', formData) },
      //   { key: '投资期限', value: this.getValue('pr_invest_term', formData) },
      //   { key: '收益类型', value: this.getValue('pr_expect_profit', formData) },
      // ];
      this.bankInfo = bankJson
        ? JSON.parse(bankJson).map((item) => {
            return [
              { key: '银行名称', value: item['bank_name'] },
              { key: '银行卡号', value: item['bank_no'] },
              { key: '银行网点', value: item['bank_branch'] },
              {
                key: '银行卡照片',
                value:
                  window.$hvue.customConfig.fileUrl +
                  JSON.parse(item['bank_card_pic']).src,
              },
            ];
          })
        : [];
      this.extInfo = [
        // { key: "国籍", value: this.getValue("nationality",formData) },
        { key: '学历', value: this.getValue('degree_code', formData) },
        { key: '职业', value: this.getValue('profession_code', formData) },
        { key: '工作单位', value: this.getValue('work_unit', formData) },
        { key: 'E-mail', value: this.getValue('email', formData) },
        { key: '邮编', value: this.getValue('zipcode', formData) },
        {
          key: '是否本人为控制人',
          value: this.getValue('control_person', formData),
        },
        {
          key: '是否本人为受益人',
          value: this.getValue('benefit_person', formData),
        },
        {
          key: '是否有不良诚信记录',
          value: this.getValue('credit_record', formData),
        },
        {
          key: '税收身份类型',
          value: this.getValue('tax_resident_person', formData),
        },
        { key: '政要关系', value: this.getValue('gov_rel', formData) },
        {
          key: '反洗钱风险等级',
          value: this.getValue('aml_risk_level', formData),
        },
        { key: '年收入', value: this.getValue('income', formData) },
        { key: '自然人身份类别', value: this.getValue('identity_category', formData) },
      ];
      this.agreeInfo = agreementData
        ? [{ id: 'epaper_sign_json', key: '签署协议', value: agreementData }]
        : [];
      this.knowInfo =
        knowScore && paperAns
          ? [
              { key: '测评得分', value: knowScore },
              {
                key: '查看问卷',
                value: paperAns,
                value1: this.getValue('paper_no', formData),
              },
            ]
          : [];
      this.creditInfo =
        creditScore && creditPaperAns
          ? [
              { key: '测评得分', value: creditScore },
              {
                key: '查看问卷',
                value: creditPaperAns,
                value1: this.getValue('credit_paper_no', formData),
              },
            ]
          : [];
      this.visitInfo = revisitPaperAns
        ? [
            { id: 'paper_type', key: '回访状态', value: '成功' },
            {
              key: '查看问卷',
              value: revisitPaperAns,
              value1: this.getValue('revisit_paper_no', formData),
            },
          ]
        : [];
      this.specialInfo =
        this.detailInfo.bizType === '010174'
          ? [
              {
                key: '是否有关联人信息',
                value: this.getValue('is_personrelated', formData),
                value1: this.getValue('relation_context', formData),
              },
              {
                key: '是否为我公司股东或关联人',
                value: this.getValue('is_securities_company_holder', formData),
              },
              {
                key: '是否持有上市公司股份5%以上',
                value: this.getValue('is_market_company_holder', formData),
                value1: this.getValue('market_company_holder_json', formData),
              },
              {
                key: '是否持有上市公司限售股份',
                value: this.getValue('is_company_limitsell_holder', formData),
                value1: this.getValue(
                  'company_limitsell_holder_json',
                  formData
                ),
              },
              {
                key: '是否为上市公司董监高',
                value: this.getValue('is_market_company_execv', formData),
                value1: this.getValue('market_company_execv_json', formData),
              },
            ]
          : [];
      this.paperAgreeInfo = paperAgreeJson
        ? JSON.parse(paperAgreeJson).map((item) => {
            return {
              key: item.agreeName,
              value: item.picUrl
                ? window.$hvue.customConfig.fileUrl + item.picUrl
                : '',
            };
          })
        : [];
      this.corpRiskInfo = corpRiskJson
        ? [
            {
              key: '拟接受服务风险等级',
              value: corpRiskJson,
              value1: corpRiskJson ? this.handleCorpRisk(corpRiskJson) : '',
            },
          ]
        : [];
      this.agreePrintInfo = agreePrintJson
        ? [
            {
              key: '打印协议预览',
              value: agreePrintJson ? JSON.parse(agreePrintJson) : '',
            },
          ]
        : [];
    },
    //关闭
    closePop() {
      window.opener = null;
      window.open('', '_self');
      window.close();
    },
    handleCorpRisk(array) {
      return JSON.parse(array).map((item) => {
        return [
          { key: '开通权限名称', value: item['holderRight'] },
          { key: '匹配等级', value: item['level'] },
          { key: '等级匹配结果(匹配/不匹配)', value: item['isMatch'] },
          { key: '投资期限', value: item['term'] },
          { key: '期限匹配结果(匹配/不匹配)', value: item['trMatch'] },
          { key: '投资品种', value: item['variety'] },
          { key: '品种匹配结果(匹配/不匹配)', value: item['vrMatch'] },
        ];
      });
    },
    handleAccount(array) {
      return JSON.parse(array).map((item) => {
        return [
          { key: '客户号', value: item['clientId'] },
          { key: '资金账号', value: item['fundAccount'] },
          { key: '股东账号', value: item['stockAccount'] },
          {
            key: '交易类别',
            value: this.handleAccountDict(
              item['exchangeType'],
              'bc.common.exchangeType'
            ),
          },
          {
            key: '账户类别',
            value: this.handleAccountDict(
              item['holderKind'],
              'bc.common.holderKind'
            ),
          },
          {
            key: '账户状态',
            value: this.handleAccountDict(
              item['holderStatus'],
              'bc.common.accountStatus'
            ),
          },
          {
            key: '指定交易状态',
            value: this.handleAccountDict(
              item['regflag'],
              'bc.common.transStatus'
            ),
          },
          { key: '一码通账号', value: item['acodeAccount'] },
          {
            key: '股东权限',
            value: this.handleAccountDict(
              item['holderRights'],
              'bc.common.holderRights'
            ),
          },
          {
            key: '资产属性',
            value: this.handleAccountDict(
              item['assetProp'],
              'bc.common.assetProp'
            ),
          },
          {
            key: '主帐标志',
            value: this.handleAccountDict(
              item['mainFlag'],
              'bc.common.mainFlag'
            ),
          },
          {
            key: '操作类型',
            value: !item['operateType'] ? '' : item['operateType'] === '1' ? '增开' : '加挂'
          },
        ];
      });
    },
    handleAccountDict(origin, key) {
      if (!origin) return ''
      const dictList = this.$dict.dictTypeList(key);
      let dictLabelList = [];
      origin.split(',').forEach((item) => {
        let dictObj = dictList.find((itm) => itm.dictValue === item);
        dictLabelList.push(dictObj ? dictObj.dictLabel : item);
      });
      return dictLabelList.join('，');
    },
    getAge(a, curDate = '') {
      var birthDay = this.idCardToBirthday(a);
      var birthDate = new Date(birthDay);
      curDate = curDate.replace(
        /^(\d{4})[.\-/](\d{1,2})[.\-/](\d{1,2})$/,
        '$1-$2-$3'
      );
      var nowDateTime = curDate ? new Date(curDate) : new Date();
      var age = nowDateTime.getFullYear() - birthDate.getFullYear();
      // 再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
      if (
        nowDateTime.getMonth() < birthDate.getMonth() ||
        (nowDateTime.getMonth() === birthDate.getMonth() &&
          nowDateTime.getDate() < birthDate.getDate())
      ) {
        age--;
      }
      return age;
    },
    idCardToBirthday(c) {
      if (c) {
        if (c.length === 18) {
          return c.replace(/\d{6}(\d{4})(\d{2})(\d{2})\d{3}[\dXx]/, '$1-$2-$3');
        } else if (c.length === 15) {
          return c.replace(/\d{6}(\d{2})(\d{2})(\d{2})\d{3}/, '19$1-$2-$3');
        }
      }
    },
    /*
 * 部件枚举类型根据数据字典key获取高级属性过滤后的数组对象
 */
    queryDictProps(dictKey) {
      return new Promise((resolve, reject) => {
        api.queryDataDictionaries({
              dictType: dictKey
            }).then(({ code, data, msg }) => {
            if(code === 0) {
              resolve(data[dictKey]);
            }else{
              reject(msg)
            }
        })
      })
    },
    getAccJsonValue(accountJSON, k) {
      const operateTypeTransform = {
        '1': '客户新开账户',
        '2': '客户下挂账户'
      }
      try {
        const accList = JSON.parse(accountJSON);
        return uniq(accList.map((it) => {
          return operateTypeTransform[it[k]]
        })).join(',')
      } catch (error) {
        return ''
      }
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: '#282828';
}

.flow_col {
  word-break: break-all;
  min-height: 45px;
  margin: 20px 0px;
  padding-right: 25px;
}

.flow_col span {
  float: left;
  margin-right: 20px;
}

.flow_row .flow_col:not(:last-child):after {
  content: '';
  min-width: 1px;
  min-height: 45px;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: '';
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}
</style>
