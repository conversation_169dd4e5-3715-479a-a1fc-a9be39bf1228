<template>
    <div>
        <a-modal
              title="证券账户数据"
              :visible="showPop"
              @cancel="closePop"
            >
          <template slot="footer">
            <a-button key="back" @click="closePop">
              关闭
            </a-button>
          </template>
             <a-layout>
                <a-layout-content class="pop_content" style="min-height: calc(200px - 120px);">
					<div v-for="(item,index) in data" :key="index">
                       <div v-for="(info,ind) in item" :key="ind">
                           <span>{{info.key}}</span>：<span>{{info.value}}</span> 
                       </div>
                       <br>
                    </div>
                </a-layout-content>
            </a-layout>
        </a-modal>
    </div>
</template>
<script>

export default {
    name: 'AccountPop',
    data() {
        return {
        }
    },
    props: {
        isPopShow: {
            type: Boolean,
            default: false
        },
        data: {
            type: Array,
            default : ()=>{}
        }
    },
    computed: {
        showPop: {
            get() {
                return this.isPopShow;
            },
            set(val) {
                this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
            },
        },
    },
    methods: {
        //关闭
        closePop() {
            this.showPop = false
        },
    }
}
</script>
<style scoped>
</style>
