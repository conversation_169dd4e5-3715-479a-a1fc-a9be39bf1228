<template>
  <div>
    <div class="list_header">
      <a-row>
        <a-col :span="6" class="pop-title">受理单基本信息</a-col>
        <a-col style="font-weight: normal;text-align: right;">
          <a @click="isPopShow = true">受理单流水</a>
        </a-col>
      </a-row>
    </div>
    <a-descriptions bordered>
      <a-descriptions-item label="受理单号">
        {{ detailInfo.id }}
      </a-descriptions-item>
      <a-descriptions-item label="客户姓名">
        {{ detailInfo.customerName }}
      </a-descriptions-item>
      <a-descriptions-item label="手机号">
        {{ detailInfo.mobileNo }}
      </a-descriptions-item>
      <a-descriptions-item label="业务类型">
        {{ detailInfo.bizName }}
      </a-descriptions-item>
      <a-descriptions-item label="营业部">
        {{ detailInfo.branchName }}({{detailInfo.branchNo}})
      </a-descriptions-item>
      <a-descriptions-item label="操作终端">
        {{ detailInfo.opSourceName }}
      </a-descriptions-item>
      <a-descriptions-item label="客户号">
        {{ detailInfo.customerId }}
      </a-descriptions-item>
      <a-descriptions-item label="资金账号">
        {{ fundAccount }}
      </a-descriptions-item>
      <a-descriptions-item label="最近更新时间">
        {{ detailInfo.updateTime }}
      </a-descriptions-item>
    </a-descriptions>
    <accept-flow v-if="isPopShow" :isPopShow.sync="isPopShow" :data="detailInfo" />
  </div>
</template>

<script>
export default {
  name: 'BasicInfo',
  data() {
    return {
      isPopShow:false,
    };
  },
  props:{
    detailInfo:{
      type: Object,
      default: ()=>{}
    },
    fundAccount:{
      type: String,
      default: ''
    }
  },
  components:{
    AcceptFlow: () => import( /* webpackChunkName: "AcceptFlow" */ './AcceptFlow.vue'),
  },
  //   inject: ['api'],
  // mounted() {
  //   this.queryDictName();
  // },
  methods: {
    //查询字典名称
    // queryDictName() {
    //   api.queryDictName({ enumNo: 'ismp.sex', itemValue: this.userInfo.sex }).then(res => {
    //     if (res.code == 0) {
    //       this.$set(this.dictExchange, 'sex', res.data ? res.data : this.userInfo.sex);
    //     }
    //   });
    //   api
    //     .queryDictName({ enumNo: 'ismp.idtype', itemValue: this.userInfo.identityType })
    //     .then(res => {
    //       if (res.code == 0) {
    //         this.$set(
    //           this.dictExchange,
    //           'identityType',
    //           res.data ? res.data : this.userInfo.identityType
    //         );
    //       }
    //     });
    //   api
    //     .queryDictName({
    //       enumNo: 'ismp.nationality',
    //       itemValue: this.userInfo.nationality
    //     })
    //     .then(res => {
    //       if (res.code == 0) {
    //         this.$set(
    //           this.dictExchange,
    //           'nationality',
    //           res.data ? res.data : this.userInfo.nationality
    //         );
    //       }
    //     });
    //   api
    //     .queryDictName({
    //       enumNo: 'ismp.ethnicname',
    //       itemValue: this.userInfo.ethnicname
    //     })
    //     .then(res => {
    //       if (res.code == 0) {
    //         this.$set(
    //           this.dictExchange,
    //           'ethnicname',
    //           res.data ? res.data : this.userInfo.ethnicname
    //         );
    //       }
    //     });
    // }
  }
};
</script>

<style lang="scss" scoped>
.pop-title {
  border-left: 5px solid #1890ff;
  padding-left: 10px;
}
::v-deep .ant-descriptions-item-label {
  width: 10%;
}
::v-deep .ant-descriptions-item-content {
  width: 23.33%;
}
.list_header {
  padding: 10px 20px;
  background: #f9f9f9;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #e9e9e9;
}
</style>
