<template>
  <a-modal title="准入报告" :width="500" v-model="showPop" :footer="null">
    <a-list item-layout="horizontal" :data-source="JSON.parse(data)">
      <a-list-item slot="renderItem" slot-scope="item" v-if="JSON.parse(item.ruleResultDesc).title">
        <a-list-item-meta>
          <div slot="description">
            <span v-if="item.userInfo.markText" class="num" style="font-size: 18px; font-weight: 700">{{
              item.userInfo.markText }}</span>
            <p v-if="JSON.parse(item.ruleResultDesc).tips">
              <span v-html="JSON.parse(item.ruleResultDesc).tips"></span>
            </p>
          </div>
          <a slot="title">{{ JSON.parse(item.ruleResultDesc).title }}</a>
          <a-avatar slot="avatar" :icon="item.ruleELResults[0].passed ? 'check-circle' : 'close-circle'
            " :style="{
    backgroundColor: item.ruleELResults[0].passed
      ? '#2f54eb'
      : '#eb615d',
  }" />
        </a-list-item-meta>
      </a-list-item>
    </a-list>
  </a-modal>
</template>
<script>
export default {
  name: "AccessPop",
  data() {
    return {};
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    data: {
      type: String,
      default: "",
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  }
};
</script>

<style lang="scss" scoped></style>
