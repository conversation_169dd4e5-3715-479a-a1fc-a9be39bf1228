<template>
  <div>
    <a-layout>
      <a-layout-header style="background: #fff">
        <a-row class="pg-header pg-header-top">
          <a-col :span="8"><a style="font-size: 18px">
              <a-icon type="solution" /> &nbsp;{{ detailInfo.bizName }}({{
                detailInfo.bizType
              }})
            </a></a-col>
        </a-row>
      </a-layout-header>
      <a-layout-content style="background: #f9f9f9; padding: 10px 25px 90px 25px">
        <a-row v-if="showSpin"
          style="margin: 50px 45%">
          <a-spin size="large" />
        </a-row>
        <detail-info-item v-if="basicInfo.length !== 0"
          type="basic"
          areaTitle="客户基础信息"
          :sourceArray="basicInfo" />
        <!-- 图片信息 -->
        <detail-info-item v-if="
            imageInfo.length !== 0 && (imageInfo[0].value || imageInfo[1].value)
          "
          type="image"
          areaTitle="身份信息"
          :sourceArray="imageInfo" />
        <!-- 业务办理信息 -->
        <detail-info-item v-if="busHandleInfo.length !== 0"
          type="busHandle"
          areaTitle="业务办理信息"
          :sourceArray="busHandleInfo" />
        <!-- 视频信息 -->
        <detail-info-item v-if="videoInfo.length !== 0"
          type="video"
          areaTitle="视频信息"
          :sourceArray="videoInfo" />
        <!-- 业务办理信息 -->
        <detail-info-item v-if="handleInfo.length !== 0"
          type="handle"
          areaTitle="业务办理信息"
          :sourceArray="handleInfo" />
        <!-- 更多信息 -->
        <!-- <detail-info-item
          v-if="otherData.length !== 0"
          type="other"
          areaTitle="更多表单项"
          :sourceArray="otherData"
        /> -->
      </a-layout-content>

      <a-layout-footer class="btn-block"
        style="width: 100%">
        <a-button type="primary"
          @click.stop="closePop"
          style="width: 400px; height: 40px; font-size: 16px">关闭</a-button>
      </a-layout-footer>
    </a-layout>
  </div>
</template>
<script>
// import BasicInfo from "../BasicInfo";
import api from '../../api';
import DetailInfoItem from './ZjDetailInfoItem';
export default {
  name: 'DetailInfo',
  data() {
    return {
      detailInfo: sessionStorage.getItem('detailInfo')
        ? JSON.parse(sessionStorage.getItem('detailInfo'))
        : {},
      fundAccount: '',
      showSpin: true,
      basicInfo: [],
      imageInfo: [],
      videoInfo: [],
      otherData: [],
      busHandleInfo: [],
      handleInfo: [],
    };
  },
  provide: [api],
  components: {
    DetailInfoItem,
  },
  created() {
    this.queryClientDetail();
  },
  methods: {
    // 查询客户资料详情
    queryClientDetail() {
      api
        .queryFormDetail({
          bizType: this.detailInfo.bizType,
          flowInsId: this.detailInfo.id,
          formId: this.detailInfo.formId,
        })
        .then((res) => {
          if (res.code == 0) {
            this.showSpin = false;
            // console.log(res, "11111");
            this.sortData(res.data);
          } else {
            this.$message.error(res.message);
          }
        });
    },
    getValue(prop, data, type) {
      const obj = data.find((item, index) => {
        if (item.filedKey.toLowerCase() === prop) {
          this.otherData.splice(index, 1);
          return item;
        }
      });
      if (!obj || !obj.filedValue) return '';
      if (type === 'file') {
        try {
          return (
            window.$hvue.customConfig.fileUrl + JSON.parse(obj.filedValue).src
          );
        } catch (e) {
          return window.$hvue.customConfig.fileUrl + obj.filedValue;
        }
      } else {
        return obj.filedValue;
      }
    },
    sortData(data) {
      let formData = data.formDetailList.map((item) => {
        if (item.isEnum === '1' && item.filedValue) {
          const dictList = this.$dict.dictTypeList(item.dictKey);
          let dictLabelList = [];
          item.filedValue.split(',').forEach((item) => {
            let dictObj = dictList.find((itm) => itm.dictValue === item);
            dictLabelList.push(dictObj ? dictObj.dictLabel : item);
          });
          item.filedValue = dictLabelList.join('，');
          return item;
        } else {
          return item;
        }
      });
      this.otherData = formData;
      this.fundAccount = this.getValue('fund_account', formData);
      const idNo = this.getValue('id_no', formData); // 身份证号 用作年龄的判断
      let endDate = this.getValue('id_enddate', formData); // 证件结束日期
      endDate = endDate !== '********' ? endDate : '长期';
      this.basicInfo =
        this.detailInfo.bizType === '030001'
          ? [
              { key: '姓名', value: this.getValue('client_name', formData) },
              { key: '性别', value: this.getValue('client_gender', formData) },
              { key: '国籍', value: this.getValue('nationality', formData) },
              { key: '民族', value: this.getValue('nation_id', formData) },
              { key: '营业部', value: this.getValue('branch_name', formData) },
              { key: '证件类型', value: this.getValue('id_kind', formData) },
              { key: '证件号码', value: idNo },
              {
                key: '证件有效期（始）',
                value: this.getValue('id_begindate', formData),
              },
              { key: '证件有效期（终）', value: endDate },
              { key: '年龄', value: this.getAge(idNo) },
              { key: '出生日期', value: this.getValue('birthday', formData) },
              { key: '手机号码', value: this.getValue('mobile_tel', formData) },
              {
                key: '签发机关',
                value: this.getValue('issued_depart', formData),
              },
              { key: '证件地址', value: this.getValue('id_address', formData) },
              { key: '联系地址', value: this.getValue('address', formData) },
            ]
          : [
              {
                key: '机构名称',
                value: this.getValue('full_name', formData),
              },
              {
                key: '机构简称',
                value: this.getValue('organ_name', formData),
              },
              {
                key: '注册国家',
                value: this.getValue('nationality', formData),
              },
              { key: '营业部', value: this.getValue('branch_name', formData) },
              {
                key: '机构证件类型',
                value: this.getValue('business_id_kind', formData),
              },
              {
                key: '证件号码',
                value: this.getValue('business_licence', formData),
              },
              {
                key: '注册日期',
                value: this.getValue('register_date', formData),
              },
              {
                key: '注册资金',
                value: this.getValue('register_fund', formData),
              },
              //  {
              //   key: '注册币种',
              //   value: this.getValue('register_money_type', formData),
              // },
              {
                key: '证件起始日期',
                value: this.getValue('business_audit_date', formData),
              },
              {
                key: '公司注册地址',
                value: this.getValue('register_address', formData),
              },
              {
                key: '工商营业执照地址',
                value: this.getValue('business_id_address', formData),
              },

              {
                key: '工商营业执照发证机关',
                value: this.getValue('business_id_depart', formData),
              },
            ];
      this.imageInfo =
        this.detailInfo.bizType === '030001'
          ? [
              {
                key: '二代身份证',
                value: [
                  {
                    key: '身份证人像面',
                    value: this.getValue('id_card_portrait', formData, 'file'),
                  },
                  {
                    key: '身份证国徽面',
                    value: this.getValue('id_card_national', formData, 'file'),
                  },
                  // {
                  //   key: "公安照",
                  //   value: this.getValue(
                  //     "public_security_avatar",
                  //     formData,
                  //     "file"
                  //   ),
                  // },
                ],
              },
            ]
          : [
              {
                key: '经办人身份证',
                value: [
                  {
                    key: '身份证人像面',
                    value: this.getValue(
                      'contact_id_card_portrait',
                      formData,
                      'file'
                    ),
                  },
                  {
                    key: '身份证国徽面',
                    value: this.getValue(
                      'contact_id_card_national',
                      formData,
                      'file'
                    ),
                  },
                  // {
                  //   key: "公安照",
                  //   value: this.getValue(
                  //     "public_security_avatar",
                  //     formData,
                  //     "file"
                  //   ),
                  // },
                ],
              },
              {
                key: '法人身份证',
                value: [
                  {
                    key: '身份证人像面',
                    value: this.getValue(
                      'instrepr_id_card_portrait',
                      formData,
                      'file'
                    ),
                  },
                  {
                    key: '身份证国徽面',
                    value: this.getValue(
                      'instrepr_id_card_national',
                      formData,
                      'file'
                    ),
                  },
                  {
                    key: '身份证复印件',
                    value: this.getValue(
                      'instrepr_id_card_copy',
                      formData,
                      'file'
                    ),
                  },
                  {
                    key: '执行事务合伙人工商营业执照',
                    value: this.getValue(
                      'partner_business_license',
                      formData,
                      'file'
                    ),
                  },
                  // {
                  //   key: "公安照",
                  //   value: this.getValue(
                  //     "public_security_avatar",
                  //     formData,
                  //     "file"
                  //   ),
                  // },
                ],
              },
              {
                key: '法定代表人证明书',
                value: [
                  {
                    key: '',
                    value: this.getValue(
                      'instrepr_certifiy_pic',
                      formData,
                      'file'
                    ),
                    type: 'arr',
                  },
                ],
              },
              {
                key: '授权委托书',
                value: [
                  {
                    key: '',
                    value: this.getValue(
                      'instrepr_attorney_pic',
                      formData,
                      'file'
                    ),
                    type: 'arr',
                  },
                ],
              },
              {
                key: '工商营业执照',
                value: [
                  {
                    key: '',
                    value: this.getValue('business_id_pic', formData, 'file'),
                  },
                ],
              },
            ];
      this.busHandleInfo = [
        {
          key: '选择办理业务',
          value: this.getValue('choose_biz_type', formData),
        },
      ];
      this.videoInfo =
        this.detailInfo.bizType === '030001'
          ? [
              {
                key: '拍摄合照',
                value: [
                  {
                    key: '经办人与面见人合照',
                    value: this.getValue('group_pic', formData, 'file'),
                  },
                ],
              },
              {
                key: '见证视频',
                value:
                  this.getValue('witness_video_new', formData, 'file') ||
                  this.getValue('witness_video', formData, 'file'),
              },
            ]
          : [
              {
                key: '拍摄合照',
                value: [
                  {
                    key: '经办人免冠照',
                    value: this.getValue(
                      'contact_bareheaded_pic',
                      formData,
                      'file'
                    ),
                  },
                  {
                    key: '经办人与面见人合照',
                    value: this.getValue('group_pic', formData, 'file'),
                  },
                ],
              },
              {
                key: '见证视频',
                value:
                  this.getValue('witness_video_new', formData, 'file') ||
                  this.getValue('witness_video', formData, 'file'),
              },
            ];
      this.queryZjHandleResult();
    },
    queryZjHandleResult() {
      api
        .zjHandleResult({
          bizType: this.detailInfo.bizType,
          formId: this.detailInfo.formId,
        })
        .then((res) => {
          if (res.code === 0) {
            if (res.data.length !== 0) {
              this.handleInfo = [
                [
                  {
                    key: '资金账户',
                    value: this.fundAccount ? this.fundAccount : '--',
                  },
                ],
              ];
              res.data.forEach((item) => {
                this.handleInfo.push([
                  { key: '办理业务', value: item.bizName },
                  {
                    key: '办理结果',
                    value: `${item.handleStatus}${
                      item.failReason ? '(' + item.failReason + ')' : ''
                    }`,
                  },
                ]);
              });
            }
          } else {
            this.$message.error(res.message);
          }
        });
    },
    changeBizType(str) {
      let retName = [];
      str.split(',').forEach((item) => {
        const obj = this.detailInfo.businessList.find(
          (itm) => itm.bizType === item
        );
        retName.push(obj ? obj.bizName : item);
      });
      return retName.join(',');
    },
    getAge(a, curDate = '') {
      var birthDay = this.idCardToBirthday(a);
      var birthDate = new Date(birthDay);
      curDate = curDate.replace(
        /^(\d{4})[.\-/](\d{1,2})[.\-/](\d{1,2})$/,
        '$1-$2-$3'
      );
      var nowDateTime = curDate ? new Date(curDate) : new Date();
      var age = nowDateTime.getFullYear() - birthDate.getFullYear();
      // 再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
      if (
        nowDateTime.getMonth() < birthDate.getMonth() ||
        (nowDateTime.getMonth() === birthDate.getMonth() &&
          nowDateTime.getDate() < birthDate.getDate())
      ) {
        age--;
      }
      return age;
    },
    idCardToBirthday(c) {
      if (c) {
        if (c.length === 18) {
          return c.replace(/\d{6}(\d{4})(\d{2})(\d{2})\d{3}[\dXx]/, '$1-$2-$3');
        } else if (c.length === 15) {
          return c.replace(/\d{6}(\d{2})(\d{2})(\d{2})\d{3}/, '19$1-$2-$3');
        }
      }
    },
    //关闭
    closePop() {
      window.opener = null;
      window.open('', '_self');
      window.close();
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: '#282828';
}

.flow_col {
  word-break: break-all;
  min-height: 45px;
  margin: 20px 0px;
  padding-right: 25px;
}

.flow_col span {
  float: left;
  margin-right: 20px;
}

.flow_row .flow_col:not(:last-child):after {
  content: '';
  min-width: 1px;
  min-height: 45px;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: '';
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}
</style>
