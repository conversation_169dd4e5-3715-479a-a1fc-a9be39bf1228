<template>
  <div>
    <a-row :style="{ marginTop: type != 'ocr' ? 15 + 'px' : 0 }"
      v-if="sourceArray.length > 0">
      <div class="content-border">
        <div class="list_header"
          v-if="type != 'ocr'">
          <a-row>
            <a-col :span="6"
              class="pop-title">{{ areaTitle }}</a-col>
          </a-row>
        </div>
        <!-- 柜台基础信息 -->
        <a-row v-if="type == 'basic' && sourceArray.length > 0">
          <a-descriptions bordered
            :column="4">
            <a-descriptions-item :label="item.key"
              v-for="(item, index) in filterSourceArray"
              :key="index">
              {{ item.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>
        <!-- 图像信息 -->
        <template v-if="type == 'image' && filterImgSourceArray.length > 0">
          <a-descriptions bordered
            v-for="(item, index) in filterImgSourceArray"
            :key="index"
            class="avg-one">
            <a-descriptions-item :label="item.key">
              <div class="content-body">
                <div class="content-item"
                  v-for="(itm, ind) in item.value"
                  :key="ind">
                  <h3 style="margin-bottom: 10px">{{ itm.key }}</h3>
                  <viewer :class="
                      itm.key == '身份证人像面' || itm.key == '身份证国徽面'
                        ? 'idcardImg'
                        : 'actorImg'
                    ">
                    <div v-if="itm.type != 'arr'">
                      <img width="100%"
                        style="cursor: pointer"
                        :src="itm.value"
                        :key="itm.key" />
                    </div>
                    <div v-else
                      class="img_arr">
                      <img v-for="(pic, i) in itm.value"
                        width="100%"
                        style="cursor: pointer;"
                        :src="pic"
                        :key="i" />
                    </div>
                  </viewer>
                </div>
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </template>

        <!-- 业务办理信息 -->
        <a-row v-if="type == 'busHandle' && sourceArray.length > 0"
          class="avg-three">
          <a-descriptions bordered>
            <a-descriptions-item :label="item.key"
              v-for="(item, index) in sourceArray"
              :key="index">
              {{ item.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 视频影像 -->
        <a-row v-if="type == 'video' && sourceArray.length > 0">
          <a-descriptions bordered>
            <a-descriptions-item :label="sourceArray[0].key">
              <div class="content-body">
                <div class="content-item"
                  v-for="(item, index) in sourceArray[0].value"
                  :key="index">
                  <h3 style="margin-bottom: 10px">{{ item.key }}</h3>
                  <viewer :class="
                      item.key == '经办人与面见人合照'
                        ? 'idcardImg'
                        : 'actorImg'
                    ">
                    <img v-if="item.value"
                      width="100%"
                      style="cursor: pointer"
                      :src="item.value"
                      :key="item.key" />
                  </viewer>
                </div>
              </div>
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions v-if="filterVideoSourceArray.length"
            bordered>
            <a-descriptions-item label="见证视频">
              <video v-for="(item, index) in filterVideoSourceArray"
                :key="index"
                :label="item"
                width="500"
                height="400"
                style="margin: 0 10px"
                controls="controls">
                <source :src="item"
                  type="video/mp4" />
              </video>
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 业务办理信息 -->
        <a-row v-if="type == 'handle' && sourceArray.length > 0"
          class="avg-two">
          <a-descriptions bordered
            :column="2"
            v-for="(item, index) in sourceArray"
            :key="index">
            <a-descriptions-item :label="itm.key"
              v-for="(itm, ind) in item"
              :key="ind">
              {{ itm.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>
        <a-row v-if="type == 'other' && sourceArray.length > 0">
          <a-list bordered
            class="ant_list"
            :data-source="filterOriginSourceArray">
            <a-list-item class="ant_list_item"
              slot="renderItem"
              slot-scope="item">
              <a-col :span="6">{{ item.filedDesc }}</a-col>
              <a-col :span="18">{{
                item.filedValue ? item.filedValue : ""
              }}</a-col>
            </a-list-item>
          </a-list>
        </a-row>
      </div>
    </a-row>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    /* 父组件传递给子组件的，实际上只是一个引用地址，当子组件修改这个对象时，是真的修改了在堆空间中保存的数值，当然父组件中的值也会发生变化，但是引用地址没有进行修改，所以并没有报错。 */
    type: {
      type: String,
      default: '',
    },
    sourceArray: {
      type: Array,
      default: () => [],
    },
    areaTitle: {
      type: String,
      default: '',
    },
  },
  methods: {},

  computed: {
    filterImgSourceArray() {
      let arr = this.sourceArray.filter((item) => item.value);
      arr.forEach((data) => {
        if (Array.isArray(data.value)) {
          data.value.forEach((item) => {
            if (item.type == 'arr' && item.value) {
              item.value = item.value.toString().split(',');
              item.value.forEach((i, j) => {
                if (i && j != 0) {
                  return (item.value[j] =
                    window.$hvue.customConfig.fileUrl + i);
                }
              });
              return item.value;
            }
          });
        }
      });
      return arr;
    },
    filterVideoSourceArray() {
      let arr = this.sourceArray.filter((item) => item.value);
      let list = [];
      arr.forEach((data) => {
        if (data.key === '见证视频') {
          list = data.value.toString().split(',');
          list.forEach((i, j) => {
            if (i && j != 0) {
              return (list[j] = window.$hvue.customConfig.fileUrl + i);
            }
          });
          return list;
        }
      });
      return list;
    },
    filterSourceArray() {
      return this.sourceArray.map((item) => {
        if (item.value == '') {
          item.value = '--';
          return item;
        }
        return item;
      });
    },
    filterOriginSourceArray() {
      return this.sourceArray.map((item) => {
        if (!item.filedValue) {
          item.filedValue = '--';
          return item;
        }
        return item;
      });
    },
  },
};
</script>

<style lang="scss" scoped="scoped">
::v-deep .avg-one .ant-descriptions-item-label {
  width: 15%;
}
::v-deep .avg-two .ant-descriptions-item-label {
  width: 10%;
}
::v-deep .avg-two .ant-descriptions-item-content {
  width: 40%;
}
::v-deep .avg-three .ant-descriptions-item-label {
  width: 10%;
}
::v-deep .avg-three .ant-descriptions-item-content {
  width: 23.3%;
}
::v-deep .avg-four .ant-descriptions-item-label {
  width: 10%;
}
::v-deep .avg-four .ant-descriptions-item-content {
  width: 15%;
}
::v-deep .ocr .ant-descriptions-view {
  border: none;
}
::v-deep .ocr .ant-descriptions-item-label,
::v-deep .xh-reson .ant-descriptions-item-label {
  width: 10%;
}
::v-deep .ocr .ant-descriptions-item-content {
  width: 40%;
}
::v-deep .handle-result .ant-descriptions-item-label {
  width: 5%;
}
// ::v-deep .handle-result .ant-descriptions-item-content {
//   width: 8%;
// }
// ::v-deep .handle-result .ant-descriptions-item-content:first-child {
//   width: 10%;
// }
::v-deep .handle-result .ant-descriptions-item-content:last-child {
  width: 50%;
}

.ant-spin-container {
  background: #fff !important;
  padding: 10px 15px !important;
}

.content-border {
  border: 1px solid #e9e9e9;
  border-radius: 2px;
  background: #fff;
}

.list_header {
  padding: 10px 20px;
  background: #f9f9f9;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #e9e9e9;
}
.pop-title {
  border-left: 5px solid #1890ff;
  padding-left: 10px;
}

.content-body {
  background: #fff;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
}

.content-item {
  text-align: center;
  padding: 10px 0px;
  background: #fafafa;
  flex-grow: 1;
  margin-right: 30px;
}

.content-item:last-child {
  margin-right: 0px;
}

.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #fff;
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: '';
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  float: right;
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: relative;
  /* display: inline-block; */
  width: 125px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.wordsTitle {
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 2px solid #dfe1e6;
  background-color: #eeeff2;
  border-radius: 3px;
}
.wordsContent {
  padding: 15px 20px;
  height: 350px;
  overflow: auto;
}
.wordsContent p {
  font-weight: bold;
  font-size: 16px;
  line-height: 30px;
}
.idcardImg {
  overflow: hidden;
  text-align: center;
}
.idcardImg img {
  width: 335px;
  height: 200px;
}
.actorImg {
  overflow: hidden;
}
.actorImg img {
  width: 135px;
  height: 200px;
}
.ant-alert {
  margin: 0 10px;
}
.img_arr {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: center;
}
</style>
