// 注册对应的访问路由
export default [
  {
    path: '/acceptanceForm',
    component: (resolve) => require(['./index'], resolve),
    name: 'acceptanceForm',
    meta: { title: '受理单信息' }
  },
  {
    path: '/detailInfo',
    component: (resolve) => require(['./module/DetailInfo'], resolve),
    name: 'DetailInfo',
    meta: { title: '查看详情' }
  },{
    path: '/zjDetailInfo',
    component: (resolve) => require(['./module/zjSecond/ZjDetailInfo'], resolve),
    name: 'zjDetailInfo',
    meta: { title: '查看详情' }
  },{
    path: '/acceptanceFormHis',
    component: (resolve) => require(['./acceptanceFormHis'], resolve),
    name: 'acceptanceFormHis',
    meta: { title: '历史受理单信息' }
  }
];
