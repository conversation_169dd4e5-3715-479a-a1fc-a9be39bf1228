/*
 * @Author: your name
 * @Date: 2021-01-23 10:21:41
 * @LastEditTime: 2021-05-20 16:49:57
 * @LastEditors: liu quan
 * @Description: In User Settings Edit
 * @FilePath: \bus-child-view\src\module\index.js
 */
// 从全局公共方法中获取指定的方法
import { fileLoading } from '@utils';
import Vue from 'vue';
// 获取状态插件
import Vuex from 'vuex';
// 获取vuex持久化插件
import createPersistedState from 'vuex-persistedstate'
// 获取路由插件
import Router from 'vue-router';
// 注册当前请求对应的上下文请求组
import { request } from 'bus-common-component/lib/extension';
const services = new request();

export const InterceptResponse = (res) => {
  let { data: { code, msg }, headers } = res;
  if (code == 0) {
    services.setOption({ headers: { "tk-token-authorization": headers["tk-token-authorization"] } })
    window.$store.commit("SETAUTHORIZATION", headers["tk-token-authorization"]);
  }else{
    return window.alert(msg || '模拟登录失败！')
  }
  return true;
}

// 最终抛出的状态对象-数据
const storeData = {
  state: {
    userInfo: undefined, // 获取当前用户信息
    authorization: undefined, // 获取当前用户登录标识
    tkJwtAuthorization: undefined, //URL签名
  },
  mutations: {
    SETUSER5INFO: (state, userInfo) => state.userInfo = userInfo,
    SETAUTHORIZATION: (state, authorization) => state.authorization = authorization,
    SETTKJWTAUTHORIZATION: (state, tkJwtAuthorization) => state.tkJwtAuthorization = tkJwtAuthorization,
  },
  actions: {
    getUserInfo({ commit }, param){
      commit('SETAUTHORIZATION', undefined)
      sessionStorage.removeItem("reqOption")
      return services.initPost({ reqUrl: "work-manage-server/simulate/login",param, InterceptResponse}).then(({code, data}) => code == 0 && commit('SETUSER5INFO',data))
    },
  },
  getters:{
    // 获取当前用户信息
    userInfo: state => state.userInfo,
    // 获取当前登录标识
    authorization: state => state.authorization,
    // 获取当前URL签名
    tkJwtAuthorization: state => state.tkJwtAuthorization,
  },
  modules: {},
  plugins: [
    createPersistedState({
      storage: window.sessionStorage,
      reducer(val){
        return {
          userInfo: val.userInfo,
          authorization: val.authorization,
          tkJwtAuthorization: val.tkJwtAuthorization
        }
      }
    })
  ]
};
// 最终抛出的路由对象-数据
export const routerData = {
  routes: []
};
// 注册对应的状态插件
Vue.use(Vuex);
// 注册对应的路由插件
Vue.use(Router);
// 获取当前工程中已开发的功能模块
let data = fileLoading();
// 遍历添加各个模块的内容
data.map(item => {
  // 判断状态中是否已注册此模块已注册则不进行对应的状态注册操作
  if (!storeData.modules[item.file] || Object.keys(storeData.modules[item.file]).length <= 0) {
    if (item.isStoreDefault()) {
      // 未含有此模块进行状态注册操作
      storeData.modules[item.file] = item.getStoreDefault();
    }
    routerData.routes = routerData.routes.concat(item.getRouterDefault());
  }
});
// 对外输出状态对象
export const store = new Vuex.Store(storeData);