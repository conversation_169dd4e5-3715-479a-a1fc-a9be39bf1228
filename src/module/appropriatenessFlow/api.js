// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询业务定义列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBcBusinessList() {
    return this.services.initGet({
      reqUrl: "bcBusiness/list",
      param: this.param,
    });
  }
  /**
   * 查询营业部列表
   * @description:
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBranchInfo() {
    return this.services.initGet({
      reqUrl: "bcBranchInfo/list",
      param: this.param,
    });
  }
  /**
   * 查询短信模板列表
   * @param {Object} param
   * - bcAppropriatenessFlowId {String} 适当性流水记录ID
   */
  @Parameters(["bcAppropriatenessFlowId"])
  queryBcAppropriatenessFlow() {
    return this.services.initGet({
      reqUrl: "bcAppropriatenessFlow/query",
      param: this.param,
    });
  }
}

export default new api();
