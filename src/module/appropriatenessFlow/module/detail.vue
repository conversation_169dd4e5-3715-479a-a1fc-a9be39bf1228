<template>
  <a-modal
    :title="`适当性流水详情`"
    :width="800"
    v-model="showPop"
    :maskClosable="false"
  >
    <a-descriptions bordered :column="1">
      <a-descriptions-item label="业务流水号">
        {{form.businessSerialNo}}
      </a-descriptions-item>
      <a-descriptions-item label="业务名称">
        {{form.businessName}}
      </a-descriptions-item>
      <a-descriptions-item label="客户全称">
       {{form.customerName}}
      </a-descriptions-item>
      <a-descriptions-item label="资金账号">
       {{form.fundAccount}}
      </a-descriptions-item>
      <a-descriptions-item label="客户类别">
       {{form.customerType}}
      </a-descriptions-item>
      <a-descriptions-item label="证件类型">
       {{form.idKind}}
      </a-descriptions-item>
      <a-descriptions-item label="证件号码">
       {{form.idNo}}
      </a-descriptions-item>
      <a-descriptions-item label="所属营业部">
       {{form.branchName}}
      </a-descriptions-item>
      <a-descriptions-item label="匹配状态">
       {{form.matchState==='1'?'匹配':'不匹配'}}
      </a-descriptions-item>
      <a-descriptions-item label="客户风险等级">
       {{form.corpRiskLevelDesc}}
      </a-descriptions-item>
      <a-descriptions-item label="测评分数">
       {{form.evaluateScore}}
      </a-descriptions-item>
      <a-descriptions-item label="业务风险等级">
       {{form.busiRiskLevelDesc}}
      </a-descriptions-item>
      <a-descriptions-item label="适当性匹配时间">
       {{form.createTime}}
      </a-descriptions-item>
      <!-- <a-descriptions-item label="协议签署名称">
       {{form.agreeTitle}}
      </a-descriptions-item>
      <a-descriptions-item label="签署协议版本号">
       {{form.agreeVersion}}
      </a-descriptions-item>
      <a-descriptions-item label="协议内容">
       <iframe v-if="form.agreePath" :src="'/file-common-server/file'+form.agreePath" frameborder="0" width="100%" height="600px"></iframe>
      </a-descriptions-item> -->
    </a-descriptions>
    <br/>
    <template slot="footer">
      <a-button type="primary" @click="ok">确定</a-button>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: "appropriatenessFlow_detail",
  inject: ["api"],
  data() {
    return {
      form: {}, //表单数据,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    ok() {
      this.showPop = false;
      this.reset()
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    query() {
      this.reset();
      return this.api
        .queryBcAppropriatenessFlow({ bcAppropriatenessFlowId: this.parameterData.id })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          this.form = data;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.ant-descriptions-bordered.ant-descriptions-item-content, 
.ant-descriptions-bordered .ant-descriptions-item-label{
  min-width: 110px;
}
.ant-descriptions-item-content{
  word-break: break-all;
}
</style>