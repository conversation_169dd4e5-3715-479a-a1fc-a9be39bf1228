<template>
  <a-card title="适当性流水" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query(1)" @reset="reset">
          <a-form-model-item label="业务流水号">
            <a-input
              v-model="tableForm.businessSerialNo"
              placeholder="请输入业务流水号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="业务名称">
            <a-select
              v-model="tableForm.businessCode"
              placeholder="请选择业务名称"
              allowClear
              show-search
              option-filter-prop="children"
            >
              <a-select-option
                v-for="item in businessList"
                :key="item.id"
                :value="item.bizType"
              >
              {{ item.bizName }}({{ item.bizType }})
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="客户名称">
            <a-input
              v-model="tableForm.customerName"
              placeholder="请输入客户名称"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="资金账号">
            <a-input
              v-model="tableForm.fundAccount"
              placeholder="请输入资金账号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户类别">
            <a-select
              v-model="tableForm.customerType"
              placeholder="请选择客户类别"
            >
              <a-select-option
                v-for="(item, index) in ['个人', '机构','产品']"
                :key="index"
                :value="item"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="证件类型">
            <a-select
              v-model="tableForm.idKind"
              placeholder="请选择证件类型"
            >
              <a-select-option
                v-for="(item, index) in ['身份证号', '营业执照']"
                :key="index"
                :value="item"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="证件号码">
            <a-input
              v-model="tableForm.idNo"
              placeholder="请输入证件号码"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户营业部">
            <a-select
              v-model="tableForm.branchNos"
              placeholder="请选择客户营业部，支持多选"
              allowClear
              show-search
              option-filter-prop="children"
              mode="multiple"
            >
              <a-select-option
                v-for="item in branchList"
                :key="item.branchId"
                :value="item.branchNo"
              >
                {{ item.branchName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="流水创建起止时间">
            <a-range-picker v-model="tableForm.rangeDate" @change="onChange" />
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcAppropriatenessFlow/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="reqTableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
      <template slot="corpRiskLevel" slot-scope="data">
          <span>{{ data.corpRiskLevelDesc }}</span>
      </template>
      <template slot="busiRiskLevel" slot-scope="data">
          <span>{{ data.busiRiskLevelDesc }}</span>
      </template>
      <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="detail(data)"> 详情 </a-button>
      </template>
      </tk-table>
      <LookComponent :isPopShow.sync="isLookPopShow" :parameterData="selectData" />
    </div>
  </a-card>
</template>

<script>
// 引入查看弹窗
import LookComponent from './module/detail';
import api from "./api";

// 默认表单属性
const defaultForm = {
  businessSerialNo: "", 
  businessCode: undefined, 
  customerName: "",
  fundAccount: "",
  customerType: undefined, 
  idKind: undefined, 
  idNo: "",
  branchNo: undefined,
  createTimeBeginTime: "",
  createTimeEndTime: "",
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "businessSerialNo",
          label: "业务流水号",
          isSorter: false,
          width:150
        },
        {
          field: "businessName",
          label: "业务名称",
          isSorter: false,
          width:150
        },
        {
          field: "customerName",
          label: "客户名称",
          isSorter: false,
          width:180
        },
        {
          field: "fundAccount",
          label: "资金账号",
          isSorter: false,
          width:150
        },
        {
          field: "customerType",
          label: "客户类别",
          isSorter: false,
          width:120
        },
        {
          field: "idKind",
          label: "证件类型",
          isSorter: false,
          width:120
        },
        {
          field: "idNo",
          label: "证件号码",
          isSorter: false,
          width:180
        },
        {
          field: "branchName",
          label: "客户所属营业部",
          isSorter: false,
          width:180
        },
        {
          field: "matchState",
          label: "匹配状态",
          isSorter: false,
          filter:item=>item==='1'?'匹配':'不匹配',
          width:120
        },
        {
          field: "corpRiskLevel",
          label: "客户风险等级",
          isSorter: false,
          width:150
        },
        {
          field: "busiRiskLevel",
          label: "业务风险等级",
          isSorter: false,
          width:150
        },
        {
          field: "createTime",
          label: "适当性匹配时间",
          isSorter: true,
          width:180
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed:'right',
          width:120
        },
      ],
      tableForm: {
        businessSerialNo: "", 
        businessCode: undefined, 
        customerName: "",
        fundAccount: "",
        customerType: undefined, 
        idKind: undefined, 
        idNo: "",
        branchNo: undefined,
        createTimeBeginTime: "",
        createTimeEndTime: "",
      },
      reqTableForm: {},
      selectedRowKeys: [], // Check here to configure the default column
      businessList: [],
      branchList:[],
      isLookPopShow: false,
      selectData: {},
    };
  },
  provide: { api: api },
  components: { LookComponent },
  async created() {
    const res = await this.$dict.dictContent("ge.comprehensive.bizTypeMain")
    if (!res) {
      api.getBcBusinessList({ isShelfed: "1" }).then((res) => {
        this.businessList = res.data;
      });
    } else {
      this.businessList = res.map(item => ({
        id: item.dictValue,
        bizType: item.dictValue,
        bizName: item.dictLabel
      }))
    }
    api.getBranchInfo().then((res) => {
      this.branchList = res.data;
    });
  },
  methods: {
    detail(data){
      this.isLookPopShow = true;
      this.selectData = data;
    },
    querySmsSendTemplateList(){
      api.querySmsSendTemplateList({state:'1'}).then((res) => {
        this.smsSendTemplateList = res.data;
      });
    },
    queryBusinessList() {
      api.queryBusinessList({ isShelfed: "1" }).then((res) => {
        this.businessList = res.data;
      });
    },
    onChange(date, dateString) {
      this.tableForm.createTimeBeginTime = dateString[0];
      this.tableForm.createTimeEndTime = dateString[1];
    },
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableForm.createTimeBeginTime)) >
        Date.parse(this.DateFormat(this.tableForm.createTimeEndTime))
      ) {
        param["createTimeBeginTime"] = "";
        param["createTimeEndTime"] = "";
      }
      delete param.rangeDate;
      return param;
    },
    query(type) {
      if (type) {
        this.reqTableForm = JSON.parse(JSON.stringify(this.tableForm));
        this.reqTableForm.branchNos =
          this.reqTableForm.branchNos && this.reqTableForm.branchNos.join(",");
      }
      this.$nextTick(() => {
        this.$refs.table.getTableData();
        this.selectedRowKeys = [];
      });
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
  },
};
</script>

<style lang="scss" scoped></style>
