<template>
  <div>
    <a-layout>
      <a-layout-content
        style="background: #f9f9f9; padding: 10px 25px 90px 25px"
      >
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">客户上传档案</a-col>
                </a-row>
              </div>
              <viewer
                class="idcardImg"
                v-for="(item, index) in clientImgArr"
                :key="index"
              >
                <h4>{{ archiveTypeList[item.archiveType] }}</h4>
                <img width="100%" style="cursor: pointer" :src="item.imgUrl" />
              </viewer>
            </div>
          </a-row>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">单项视频</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card :bordered="false" class="self">
                    <video
                      width="600"
                      height="400"
                      controls="controls"
                      crossorigin="anonymous"
                      :src="clientSingleVideoUrl"
                    >
                    </video>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div>
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">公安信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="客户姓名">
              {{ userInfo?.clientInfo?.clientName }}
            </a-descriptions-item>
            <a-descriptions-item label="身份证号">
              {{ userInfo?.clientInfo?.idNo }}
            </a-descriptions-item>
            <a-descriptions-item label="性别">
              {{ userInfo?.clientInfo?.gender === '0' ? '男' : '女' }}
            </a-descriptions-item>
            <a-descriptions-item label="生日">
              {{ formatDate(userInfo?.clientInfo?.idNo?.slice(6, 14)) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件开始日期">
              {{ formatDate(userInfo?.clientInfo?.begindate) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件结束日期">
              {{ formatDate(userInfo?.clientInfo?.enddate) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件地址">
              {{ userInfo?.clientInfo?.idAddress }}
            </a-descriptions-item>
            <a-descriptions-item label="证件签发机关">
              {{ userInfo?.clientInfo?.issuedDepart }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">流程信息</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="流程ID">
                  {{ userInfo?.auditInfo?.processId }}
                </a-descriptions-item>
                <a-descriptions-item label="标识类型">
                  {{ getidentityType(userInfo?.auditInfo?.identityType) }}
                </a-descriptions-item>
                <a-descriptions-item label="标识">
                  {{ userInfo?.auditInfo?.identity }}
                </a-descriptions-item>
                <a-descriptions-item label="状态">
                  {{ getStatusName(userInfo?.auditInfo?.state) }}
                </a-descriptions-item>
                <a-descriptions-item label="上一状态">
                  {{ getStatusName(userInfo?.auditInfo?.lastState) }}
                </a-descriptions-item>
                <a-descriptions-item label="客户姓名">
                  {{ userInfo?.clientInfo?.clientName }}
                </a-descriptions-item>
                <a-descriptions-item label="自动审核结果">
                  {{ userInfo?.auditInfo?.faceRecongnitionScore }}
                </a-descriptions-item>
                <a-descriptions-item label="业务流水号">
                  {{ userInfo?.auditInfo?.busiSn }}
                </a-descriptions-item>
                <a-descriptions-item label="创建时间">
                  {{ formatDateTime(userInfo?.auditInfo?.createTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="更新时间">
                  {{ formatDateTime(userInfo?.auditInfo?.updateTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="操作员">
                  {{ userInfo?.auditInfo?.operator }}
                </a-descriptions-item>
                <a-descriptions-item label="操作备注">
                  {{ userInfo?.auditInfo?.optionRemark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>
<script>
import api from '../api'
import moment from 'moment'
export default {
  name: 'resetPwdInfo',
  data() {
    return {
      userInfo: {},
      clientImgArr: [],
      clientSingleVideoUrl: '',
      identityTypeList: [
        { value: '0', name: '资金账号' },
        { value: '10', name: '信用资金账号' },
        { value: '12', name: 'clientId' },
      ], //标识类型列表
      statusList: [
        { value: '0', name: '初始化' },
        { value: '1', name: '初审通过' },
        { value: '2', name: '自动审核不通过' },
        { value: '3', name: '审核通过' },
        { value: '4', name: '审核不通过' },
      ], //状态列表
      archiveTypeList: {
        1: '身份证照片正面',
        2: '身份证照片反面',
        3: '客户头像',
        4: '视频见证录像',
        5: '辅证图片',
        7: '单项视频',
        8: '公安身份证头像',
      },
    }
  },
  provide: [api],
  components: {},
  created() {
    if (this.$route.query.processId) {
      this.queryClientDetail()
    }
  },
  methods: {
    // 查询客户资料详情
    queryClientDetail() {
      api
        .queryItem({
          processId: this.$route.query.processId,
        })
        .then((res) => {
          if (res.code == 0) {
            this.temObj = res.data
            if (res.data.clientBusinessfile.length > 0) {
              res.data.clientBusinessfile.forEach((item) => {
                if (item.fileIdStr) {
                  this.queryfileBytes(
                    item.fileIdStr,
                    item.archiveType,
                    (imgFileBytes) => {
                      this.clientImgArr.push({
                        imgUrl:
                          'data:image/jpeg;base64,' + imgFileBytes.fileBytes,
                        archiveType: imgFileBytes.fileType,
                      })
                    }
                  )
                }
              })
            }
            if (res.data.singleVideoId) {
              this.queryfileBytes(
                res.data.singleVideoId,
                '',
                (singleVideoFileBytesObj) => {
                  this.clientSingleVideoUrl =
                    'data:video/mp4;base64,' + singleVideoFileBytesObj.fileBytes
                }
              )
            }
            setTimeout(() => {
              console.log('this.clientImgArr', this.clientImgArr)
              this.userInfo = res.data
            }, 0)
          } else {
            this.$message.error(res.message)
          }
        })
    },
    getidentityType(idType) {
      let idTypeItem = this.identityTypeList.filter(
        (item) => item.value == idType
      )
      return idTypeItem[0]?.name
    },
    getStatusName(status) {
      let statusItem = this.statusList.filter((item) => item.value == status)
      return statusItem[0]?.name
    },
    queryfileBytes(fileId, fileType, callback) {
      let fileBytes = ''
      api
        .queryImgfileBytes({
          fileId,
        })
        .then((res) => {
          if (res.code == 0) {
            fileBytes = res.data.fileBytes
          } else {
            fileBytes = ''
          }
          callback && callback({ fileBytes, fileType })
        })
    },
    formatDate(date) {
      if (!date) {
        return ''
      }
      return date.slice(0, 4) + '-' + date.slice(4, 6) + '-' + date.slice(6, 8)
    },
    formatDateTime(date) {
      return moment(date).format('YYYY-MM-DD HH:MM:SS')
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: '#282828';
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: '';
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

::v-deep .ant-card-body {
  padding: 15px;
  text-align: center;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}
.idcardImg {
  display: inline-block;
  margin-right: 10px;
  margin-top: 10px;
}

.idcardImg img {
  width: 335px;
  height: 200px;
}

.idcardImg h4 {
  text-align: center;
  background-color: rgb(217, 233, 245);
}
</style>
