<template>
  <a-card title="重置密码" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="handleReset">
          <a-form-model-item label="流程流水号">
            <a-input
              v-model="tableForm.processId"
              placeholder="请输入流程流水号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="标识类型">
            <a-select
              v-model="tableForm.identityType"
              placeholder="请选择标识类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in identityTypeList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="标识">
            <a-input
              v-model="tableForm.identity"
              placeholder="请输入标识"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="状态">
            <a-select
              v-model="tableForm.state"
              placeholder="请选择状态"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in statusList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="业务流水号">
            <a-input
              v-model="tableForm.busiSn"
              placeholder="请输入业务流水号"
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="page_main">
      <a-divider />
      <a-row class="btn_row">
        <a-col>
          <a-button type="primary" icon="redo" @click="query">刷新 </a-button>
        </a-col>
      </a-row>
      <div class="access-table">
        <tk-table
          ref="table"
          :tableData.sync="columns"
          getMethods="bc-manage-server/gjV2/audit/password/reset/page"
          :isSelected="true"
          :isPaging="true"
          :tableFrom="tableForm"
          tableId="id"
        >
          <template slot="operation" slot-scope="data">
            <a class="action-btn" @click="showDetail(data)"> 查看 </a>
          </template>
        </tk-table>
      </div>
    </div>
  </a-card>
</template>

<script>
import moment from 'moment'

const defaultForm = {
  bizType: '',
  name: '',
  state: '',
}

export default {
  name: 'resetPwd',
  data() {
    return {
      tableForm: {
        processType: '', //流程类型
        identityType: '', //标识类型
        state: '', //状态
        processId: '', //流程流水号
        identity: '', //标识
        busiSn: '', //业务流水号
      },
      identityTypeList: [
        { value: '0', name: '资金账号' },
        { value: '10', name: '信用资金账号' },
        { value: '12', name: 'clientId' },
      ], //标识类型列表
      statusList: [
        { value: '0', name: '初始化' },
        { value: '1', name: '初审通过' },
        { value: '2', name: '自动审核不通过' },
        { value: '3', name: '审核通过' },
        { value: '4', name: '审核不通过' },
      ], //状态列表
      data: [],
      //表头数据
      columns: [
        {
          label: '流程状态',
          field: 'auditType',
          isSorter: false,
          filter: (item) => (item == '0' ? '初审' : '复审'),
        },
        {
          label: '流程类型',
          field: 'processType',
          isSorter: false,
        },
        {
          label: '流程流水号',
          field: 'processId',
          isSorter: false,
        },
        {
          label: '标识类型',
          field: 'identityType',
          isSorter: false,
          filter: (item) => this.getIdentityTypeList(item),
        },
        {
          label: '标识',
          field: 'identity',
          isSorter: false,
        },
        {
          label: '客户姓名',
          field: 'clientName',
          isSorter: false,
        },
        {
          label: '自动审核结果',
          field: 'faceRecongnitionScore',
          isSorter: false,
        },
        {
          label: '状态',
          field: 'state',
          isSorter: false,
          filter: (item) => this.getTaskStatusList(item),
        },
        {
          label: '上一状态',
          field: 'lastState',
          isSorter: false,
          filter: (item) => this.getTaskStatusList(item),
        },
        {
          label: '操作备注',
          field: 'optionRemark',
          isSorter: false,
        },
        {
          label: '业务流水号',
          field: 'busiSn',
          isSorter: false,
        },
        {
          label: '创建时间',
          field: 'createTime',
          isSorter: false,
          filter: (item) => moment(item).format('YYYY-MM-DD HH:MM:SS'),
        },
        {
          label: '更新时间',
          field: 'updateTime',
          isSorter: false,
          filter: (item) => moment(item).format('YYYY-MM-DD HH:MM:SS'),
        },
        {
          label: '是否锁定',
          field: 'isLock',
          isSorter: false,
          filter: (item) => (item == '1' ? '已被其他用户锁定' : '未被其他用户锁定'),
        },
        {
          label: '锁定时间',
          field: 'lockTime',
          isSorter: false,
        },
        {
          label: '锁定人',
          field: 'lockUser',
          isSorter: false,
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: 220,
        },
      ],
    }
  },
  components: {},
  methods: {
    //处理按钮
    query() {
      this.$refs.table.getTableData()
    },
    handleReset() {
      this.tableForm = JSON.parse(JSON.stringify(defaultForm))
    },
    getTaskStatusList(val) {
      const filterData = this.statusList.filter(({ value }) => value === val)[0]
      return filterData ? filterData.name : ''
    },
    getIdentityTypeList(val) {
      const filterData = this.identityTypeList.filter(
        ({ value }) => value === val
      )[0]
      return filterData ? filterData.name : ''
    },
    showDetail(data) {
      console.log('data', data)
      let href = `/bc-manage-view/resetPwdInfo?processId=${data.processId}`
      window.open(href, '_blank')
    },
  },
}
</script>

<style>
.alert-test {
  text-align: center;
  padding: 5px;
}
</style>
