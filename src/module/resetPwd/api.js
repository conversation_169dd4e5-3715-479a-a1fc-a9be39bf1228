// 加载对应的装饰器
import { Parameters } from '@u/decorator'

class api {
  /**
   * 查询执行组织信息
   * @param {Object} param
   */
  @Parameters(['processId'])
  queryItem() {
    return this.services.initGet({
      reqUrl: 'gjV2/audit/password/reset/showDetail',
      param: this.param,
    })
  }

  /**
   * 查询文件信息
   * @param {Object} param
   */
  @Parameters(['fileId'])
  queryImgfileBytes() {
    return this.services.initGet({
      reqUrl: 'gj/download',
      param: this.param,
    })
  }
}

export default new api()
