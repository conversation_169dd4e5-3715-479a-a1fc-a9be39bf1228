<template>
  <a-card title="问卷统计" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query(1)" @reset="reset">
          <a-form-model-item label="查询时间">
            <a-range-picker v-model="tableForm.rangeDate" @change="onChange" />
          </a-form-model-item>
          <a-form-model-item label="问卷名称">
            <a-input
              v-model="tableForm.subjectName"
              placeholder="请输入问卷名称"
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcReturnVisitRecord/statistics"
        :isPaging="true"
        :tableFrom="reqTableForm"
        tableId="subjectNo"
        :tableFromFilter="tableFormFilter"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button type="primary" icon="redo" @click="reset(1)">刷新</a-button>
          <a-button icon="upload" type="primary" @click="exportExcel">
            导出
          </a-button>
        </div>
      </tk-table>
    </div>
  </a-card>
</template>

<script>
// 默认表单属性
const defaultForm = {
  createDateBeginTime: "",
  createDateEndTime: "",
  subjectName: "",
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "subjectName",
          label: "问卷名称",
          isSorter: false,
        },
        {
          field: "provideCount",
          label: "发放份数",
          isSorter: true,
        },
        {
          field: "recycleCount",
          label: "回收份数",
          isSorter: true,
        },
        {
          field: "egliCount",
          label: "合规份数",
          isSorter: true,
        },
        {
          field: "toVisitCount",
          label: "转人工份数",
          isSorter: true,
        },
        {
          field: "recycleRate",
          label: "回收率",
          isSorter: true,
        },
        {
          field: "egliRate",
          label: "合格率",
          isSorter: true,
        },
      ],
      tableForm: {
        subjectName: "",
        createDateBeginTime: "",
        createDateEndTime: "",
      },
      reqTableForm: {},
      selectedRowKeys: [], // Check here to configure the default column
    };
  },
  methods: {
    exportExcel() {
      let url = "/bc-manage-server/bcReturnVisitRecord/exportStatistics";
      if (this.selectedRowKeys.length !== 0) {
        url = url + "?subjectNo=" + this.selectedRowKeys.join(",");
      } else {
        Object.keys(this.reqTableForm).forEach((item) => {
          if (!this.reqTableForm[item]) {
            delete this.reqTableForm[item];
          }
        });
        if (Object.keys(this.reqTableForm).length !== 0) {
          Object.keys(this.reqTableForm).forEach((item, index) => {
            if (index === 0) {
              url = url + "?" + item + "=" + Object.values(this.reqTableForm)[index];
            } else {
              url = url + "&" + item + "=" + Object.values(this.reqTableForm)[index];
            }
          });
        }
      }
      window.location.href = url;
    },
    onChange(date, dateString) {
      this.tableForm.createDateBeginTime = dateString[0];
      this.tableForm.createDateEndTime = dateString[1];
    },
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableForm.createDateBeginTime)) >
        Date.parse(this.DateFormat(this.tableForm.createDateEndTime))
      ) {
        param["createDateBeginTime"] = "";
        param["createDateEndTime"] = "";
      }
      delete param.rangeDate;
      return param;
    },
    query(type) {
      if (type) {
        this.reqTableForm = JSON.parse(JSON.stringify(this.tableForm));
      }
      this.$nextTick(() => {
        this.$refs.table.getTableData();
        this.selectedRowKeys = [];
      });
    },
    // 重置
    reset(type) {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
      if (type) {
        this.$nextTick(() => {
          this.$refs.table.getTableData();
          this.selectedRowKeys = [];
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
