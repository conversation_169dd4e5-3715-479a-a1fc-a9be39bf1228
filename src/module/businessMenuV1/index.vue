<template>
	<a-layout class="menu-content">
		<a-layout-sider class="menu-tree-sider">
			<a-card title="业务分组列表" :bordered="false">
				<div class="menu-left-tree">
					<a-button type="primary"
						icon="plus"
						@click="(treeOperationShow = true), (operationType = 'add')"
						style="margin-left:10px">新增
					</a-button>
					<tkTightTree @select="select" @rightClick="rightClick">
						<tk-tree
							class="ant-tree-switcher-no"
							:treeData="cacheTreeData"
							:replaceFields="replaceFields"
							:isIcon="true"
							:selectedKeys.sync="selectedKeys"
						></tk-tree>
						<a-menu-item key="0"
							v-show="Object.keys(rightMenu).length == 0"
							@click="(treeOperationShow = true), (operationType = 'add')"> 新增业务分组 
						</a-menu-item>
						<a-menu-item key="1"
							v-show="Object.keys(rightMenu).length > 0&&rightMenu.id"
							@click="(treeOperationShow = true), (operationType = 'set')"> 修改业务分组 
						</a-menu-item>
						<a-menu-item key="2"
							v-show="Object.keys(rightMenu).length > 0&&rightMenu.id"
							@click="remove"> 删除业务分组
						</a-menu-item>
					</tkTightTree>
					<treeOper
						:isPopShow.sync="treeOperationShow"
						@success="getTreeData"
						:parameterData="rightMenu"
						:operationType="operationType"
					/>
				</div>
			</a-card>
		</a-layout-sider>
		<a-layout-content class="menu-right-content">
			<a-card title="业务列表" :bordered="false">
				<dataTable :regId="regId" :labelList="cacheTreeData" />
			</a-card>
		</a-layout-content>
	</a-layout>
</template>

<script>
	import treeOper from "./module/treeOper"; // 引入分组右击组件
	import dataTable from "./module/table"; // 引入右侧表格
	import api from "./api";

	export default {
		components: { treeOper, dataTable },
		provide() {
			return { api: api };
		},
		data() {
			return {
				searchValue: "", //左侧菜单搜索值
				regId: "", // 用户选中项菜单
				cacheTreeData: [], //缓存查询结果集
				treeData: [], // 树状列表填充数据
				rightMenu: {}, // 右击对象
				// 树状列表调整对应的key
				replaceFields: {
					children: "children",
					title: "regName",
					key: "regId"
				},
				selectedKeys: [''],
				treeOperationShow: false, // 协议分组添加弹窗是否显示
				operationType: "add"
			};
		},
		watch: {
			treeData(val) {
				this.cacheTreeData = val.filter(v =>
					this.searchValue ? v.regName.includes(this.searchValue) : true
				);
			}
		},
		created() {
			this.getTreeData();
		},
		methods: {
			search() {
				this.cacheTreeData = this.treeData.filter(v =>
					this.searchValue ? v.regName.includes(this.searchValue) : true
				);
			},
			select({ regId }) {
				// 赋值
				this.regId = regId;
			},
			rightClick(data) {
				this.rightMenu = data;
			},
			// 查询当前菜单栏树状列表
			getTreeData() {
				api.getBcAgreeRegList().then(({ code, data }) => {
					if (code != 0) return;
					this.treeData = data.map(item => this.formatConversion(item));
					this.treeData = [
						{
							scopedSlots: { icon: "custom" },
							itemIcon: "file",
							regId: "",
							regName: "全部业务"
						},
						...data
					];
				});
			},
			formatConversion(item) {
				item["scopedSlots"] = { icon: "custom" };
				item["itemIcon"] = "file";
				item.regName = item.labelName;
				item.regId = item.labelCode;
				return item;
			},
			remove() {
				let _this = this;
				_this.$confirm({
					title: '温馨提示',
					content: `是否确认删除?`,
					okType: 'danger',
					onOk() {
						_this.deleteBus();
					}
				});
			},
			deleteBus() {
				let _this = this;
				api.deleteBcBusinessLabel({ bcBusinessLabelId: this.rightMenu.id })
					.then((res) => {
					if (res.code === 0) {
						_this.$message.success(res.msg);
						_this.getTreeData();
					} else {
						_this.$message.error(res.msg);
					}
					}).catch((e) => {
						_this.$message.error(e.message);
					});
			}
		}
	};
</script>

<style lang="scss" scoped></style>
