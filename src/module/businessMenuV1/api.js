// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询业务标签
   * @description:
   * @param {*}
   */
  @Parameters(["searchValue"])
  getBcAgreeRegList() {
    return this.services.initGet({
      reqUrl: "bcBusinessLabel/list",
      param: this.param,
    });
  }

  /**
   * 获取业务类型名称list
   * @param {Object} param
   */
  @Parameters()
  getBcBizTypeList() {
    return this.bfServices.initGet({
      reqUrl: "/bf/flow/list",
      param: this.param,
    });
  }

  /**
   * 查询指定网厅首页配置
   * @param {Object} param
   */
  @Parameters(["bcClientPortalId"])
  getBcClientPortal() {
    return this.services.initGet({
      reqUrl: "bcClientPortal/query",
      param: this.param,
    });
  }

  /**
   * 新增业务
   * @param {Object} param
   * - appIds {String} 多个以“，”隔开
   */
  @Parameters([
    "appType",
    "bizDes",
    "bizLogoMobileImg",
    "bizLogoPcImg",
    "bizNickName",
    "bizType",
    "bizUrl",
    "flowNo",
    "isHot",
    "isShow",
    "labelCodes",
    "status",
    "viewSort"  
  ])
  addBcClientPortal() {
    return this.services.initPost({
      reqUrl: "bcClientPortal/add",
      param: this.param,
    });
  }

  /**
   * 批量上下架
   * @param {Object} param
   * @returns {Promise}
   */
  @Parameters(["bcBusinessShelfIds", "isShelfed"])
  batchChangeShelfState() {
    return this.services.initPost({
      reqUrl: "bcBusinessShelf/batchChangeShelfState",
      param: this.param,
    });
  }

  /**
   * 修改业务
   * @param {Object} param
   * - appIds {String} 多个以“，”隔开
   */
  @Parameters([
      "appType",
      "bizDes",
      "bizLogoMobileImg",
      "bizLogoPcImg",
      "bizNickName",
      "bizType",
      "bizUrl",
      "flowNo",
      "id",
      "isHot",
      "isShow",
      "labelCodes",
      "status",
      "viewSort" 
  ])
  editBcClientPortal() {
    return this.services.initPost({
      reqUrl: "bcClientPortal/edit",
      param: this.param,
    });
  }

  /**
   * 删除业务
   * @param {Object} param
   * - bcClientPortalIds {String} 参数主键ID
   */
  @Parameters(["bcClientPortalIds"])
  deleteBcClientPortal() {
    return this.services.initPost({
      reqUrl: "bcClientPortal/deletes",
      param: this.param,
    });
  }

  /**
   * 业务介绍
   * @param {Object} param
   */
  @Parameters(["bizType", "conditions", "tips", "version", "enableBottomLink", "bottomLinkType", "bottomLinkName", "bottomLinkUrl"])
  getBcBusinessIntroduceAdd() {
    return this.services.initPost({
      reqUrl: "bcBusinessIntroduce/add",
      param: this.param,
    });
  }

  /**
   * 查询业务介绍
   * @param {Object} param
   */
  @Parameters(["bizType"])
  getBcBusinessIntroduceQuery() {
    return this.services.initGet({
      reqUrl: "bcBusinessIntroduce/query",
      param: this.param,
    });
  }

  /**
   * 查询关联策略数据源
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getStrategyList() {
    return this.qcServices.initGet({
      reqUrl: "qcrule/v2/strategy/page?pageNum=1&pageSize=10000",
      param: this.param,
    });
  }

  /**
   * 查询指定流程样式配置表
   * @description:
   * @param {*}
   */
  @Parameters(["bizType", "flowNo"])
  getBizStyleQuery() {
    return this.services.initGet({
      reqUrl: "bcFlowStyleConfig/query",
      param: this.param,
    });
  }

  /**
   * 修改流程样式配置表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  bizStyleEdit() {
    return this.services.initPost({
      reqUrl: "bcFlowStyleConfig/edit",
      param: this.param,
    });
  }

  /**
   * 新增业务分组配置
   * @param {Object} param
   */
   @Parameters(["_data"])
   addBcBusinessLabel() {
     return this.services.initPost({
       reqUrl: "bcBusinessLabel/add",
       param: this.param,
     });
   }
   /**
    * 修改业务分组配置
    * @param {Object} param
    */
   @Parameters(["_data"])
   editBcBusinessLabel() {
     return this.services.initPost({
       reqUrl: "bcBusinessLabel/edit",
       param: this.param,
     });
   }
   /**
    * 删除业务分组配置
    * @param {Object} param
    */
   @Parameters(["_data"])
   deleteBcBusinessLabel() {
     return this.services.initPost({
       reqUrl: "bcBusinessLabel/delete",
       param: this.param,
     });
   }
   /**
    * 批量更新业务介绍卡片表
    * @param {Object} param
    */
   @Parameters(["businessIntroduceCardList"])
   bcBusinessIntroduceCardBatchUpdate() {
     return this.services.initPost({
       reqUrl: "bcBusinessIntroduceCard/batchUpdate",
       param: this.param,
     });
   }
   /**
    * 查询业务介绍卡片表列表
    * @param {Object} param
    */
   @Parameters(["bizType"])
   bcBusinessIntroduceCardList() {
     return this.services.initGet({
       reqUrl: "bcBusinessIntroduceCard/list",
       param: this.param,
     });
   }
}

export default new api();
