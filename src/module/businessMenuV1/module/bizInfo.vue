<template>
  <div>
    <a-modal
      title="业务介绍"
      :visible="showPop"
      :width="1000"
      @ok="confirmSubmit"
      @cancel="confirmClose"
      class="ant_modal_bigtable"
    >
      <a-form-model
        ref="form"
        :model="form"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <!-- 卡片列表 -->
        <transition-group name="card-list" tag="div">
          <div
            v-for="(card, index) in cardList"
            :key="'card-' + index"
            class="card-item"
            :class="{ 'card-item-deleting': deletingIndex === index }"
          >
            <div class="card-header">
              <span class="card-title">卡片 {{ index + 1 }}</span>
              <a-popconfirm
                v-if="cardList.length > 1"
                title="确定要删除这张卡片吗？"
                @confirm="removeCard(index)"
                placement="topRight"
                class="delete-btn"
              >
                <a-button
                  type="danger"
                  size="small"
                  :loading="deletingIndex === index"
                  icon="delete"
                  >删除</a-button
                >
              </a-popconfirm>
            </div>

            <a-form-model-item
              :label="'标题 ' + (index + 1)"
              required
              :validateStatus="card.titleError ? 'error' : ''"
              :help="card.titleError"
            >
              <a-input
                v-model="card.title"
                placeholder="例如申请条件/温馨提示"
                @change="validateCardTitle(index)"
                :maxlength="50"
              />
            </a-form-model-item>

            <a-form-model-item
              :label="'内容 ' + (index + 1)"
              required
              :validateStatus="card.contentError ? 'error' : ''"
              :help="card.contentError"
            >
              <tkEditor
                v-if="conEditorShow"
                v-model="card.content"
                ref="tinymce"
                :height="250"
                :disabled="false"
                :plugins="['link', 'code']"
                :init-params="{ link_title: false, target_list: false  }"
              />
            </a-form-model-item>
            <a-divider v-if="index < cardList.length - 1" />
          </div>
        </transition-group>

        <!-- 添加卡片按钮 -->
        <div class="add-card-btn">
          <a-button
            type="primary"
            icon="plus"
            @click="addCard"
            :disabled="cardList.length >= 5"
            :loading="isAdding"
            >添加卡片</a-button
          >
          <span v-if="cardList.length >= 5" class="card-limit-tip"
            >最多支持5张卡片</span
          >
        </div>

        <a-form-model-item label="设置页面底部链接">
          <a-radio-group v-model="form.enableBottomLink">
            <a-radio value="0">否</a-radio>
            <a-radio value="1">是</a-radio>
          </a-radio-group>
        </a-form-model-item>

        <div v-show="form.enableBottomLink === '1'">
          <a-form-model-item label="底部链接类型" required>
            <a-select
              v-model="form.bottomLinkType"
              placeholder="请选择链接类型"
            >
              <a-select-option value="1">业务办理链接</a-select-option>
              <a-select-option value="2">非业务办理链接</a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item label="底部链接文本" required>
            <a-input v-model="form.bottomLinkName" placeholder="请输入" />
          </a-form-model-item>

          <a-form-model-item label="跳转链接URL" required>
            <a-input v-model="form.bottomLinkUrl" placeholder="请输入" />
          </a-form-model-item>
        </div>

        <!-- 保留原有的编辑器，但默认隐藏 -->
        <!-- <div style="display: none">
          <tkEditor
            v-if="conEditorShow"
            ref="tinymce"
            v-model="form.conditions"
            :height="300"
            :disabled="false"
          />
          <tkEditor
            v-if="riskEditorShow"
            ref="tinymce"
            v-model="form.tips"
            :height="300"
            :disabled="false"
          />
        </div> -->
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import tkEditor from "@c/tinymceEditor";

export default {
  data() {
    return {
      conEditorShow: false,
      bizType: "",
      riskEditorShow: false,
      updataType: {
        type: "image",
      },
      // picList: [],
      deletingIndex: -1,
      isAdding: false,
      cardList: [
        {
          title: "",
          content: "",
          titleError: "",
          contentError: "",
        },
      ],
      form: {
        busIntCardList: [
          {
            title: "",
            content: "",
          },
        ],
        enableBottomLink: "", //启用底部链接：0 不启用；1启用；
        bottomLinkType: "", //底部链接类型：1 业务办理链接；2 非业务办理链接
        bottomLinkName: "", //底部链接名称
        bottomLinkUrl: "", // 底部链接地址
        conditions: "",
        tips: "",
      },
    };
  },
  inject: ["api"],
  components: { tkEditor },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    requestId: {
      type: String,
    },
    parameterData: {
      type: Object,
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.query();
      }
      this.$nextTick(() => {
        this.conEditorShow = n;
        this.riskEditorShow = n;
      });
    },
  },
  methods: {
    // 添加卡片
    async addCard() {
      if (this.isAdding || this.cardList.length >= 5) return;

      this.isAdding = true;
      console.log("添加卡片");

      try {
        this.cardList.push({
          title: "",
          content: "",
          titleError: "",
          contentError: "",
        });
        await this.$nextTick();
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        console.error("添加卡片失败:", error);
      } finally {
        this.isAdding = false;
      }
    },

    // 删除卡片
    async removeCard(index) {
      if (this.deletingIndex !== -1 || this.cardList.length <= 1) return;

      this.deletingIndex = index;
      try {
        // 先标记为删除状态，等待动画完成
        await new Promise((resolve) => setTimeout(resolve, 500));
        this.cardList.splice(index, 1);
        this.validateAllCards();
      } catch (error) {
        console.error("删除卡片失败:", error);
      } finally {
        this.deletingIndex = -1;
      }
    },

    // 验证卡片标题
    validateCardTitle(index) {
      const card = this.cardList[index];
      if (card.title.length > 50) {
        card.title = card.title.substring(0, 50);
      }
      card.titleError = !card.title.trim() ? "请输入标题" : "";
    },

    // 验证卡片内容
    validateCardContent(index) {
      const card = this.cardList[index];
      card.contentError = !card.content.trim() ? "请输入内容" : "";
    },

    // 验证所有卡片
    validateAllCards() {
      this.cardList.forEach((_, index) => {
        this.validateCardTitle(index);
        this.validateCardContent(index);
      });
    },

    async query() {
      this.bizType = this.parameterData.bizType;
      const { data: busIntQueryData = {} } =
        await this.api.getBcBusinessIntroduceQuery({
          bizType: this.bizType,
        });
      const { version = null } = busIntQueryData;
      const { data: busIntCardList = [] } =
        await this.api.bcBusinessIntroduceCardList({
          bizType: this.bizType,
        });
      this.form = {
        ...busIntQueryData,
        busIntCardList: busIntCardList.sort((a, b) => a.viewSort - b.viewSort),
      };

      // 初始化新增字段
      if (
        !Object.prototype.hasOwnProperty.call(this.form, "enableBottomLink")
      ) {
        this.form.enableBottomLink = "0";
      }
      if (version === null) {
        this.cardList = [];
        // 如果存在旧版的title和content，则转换为卡片格式
        if (Object.prototype.hasOwnProperty.call(this.form, "conditions")) {
          this.cardList.push({
            title: "申请条件",
            content: this.form.conditions || "",
            titleError: "",
            contentError: "",
          });
        }
        if (Object.prototype.hasOwnProperty.call(this.form, "tips")) {
          this.cardList.push({
            title: "温馨提示",
            content: this.form.tips || "",
            titleError: "",
            contentError: "",
          });
        }
        this.form.busIntCardList = this.cardList.map((card) => ({
          title: card.title,
          content: card.content,
        }));
      } else {
        this.cardList = this.form.busIntCardList.map((card) => ({
          ...card,
          titleError: "",
          contentError: "",
        }));
      }
      if (!Object.prototype.hasOwnProperty.call(this.form, "bottomLinkType")) {
        this.form.bottomLinkType = "0"; // 默认内部
      }
      if (!Object.prototype.hasOwnProperty.call(this.form, "bottomLinkName")) {
        this.form.bottomLinkName = "";
      }
      if (!Object.prototype.hasOwnProperty.call(this.form, "bottomLinkUrl")) {
        this.form.bottomLinkUrl = "";
      }
    },
    closePop() {
      this.cardList = [
        {
          title: "",
          content: "",
          titleError: "",
          contentError: "",
        },
      ];
      this.form = {
        busIntCardList: [
          {
            title: "",
            content: "",
          },
        ],
        enableBottomLink: "0",
        bottomLinkType: "",
        bottomLinkName: "",
        bottomLinkUrl: "",
        conditions: "",
        tips: "",
      };
      this.showPop = false;
    },
    updateList() {
      this.closePop();
      this.$emit("success");
    },
    confirmSubmit() {
      const _this = this;
      _this.$confirm({
        title: "温馨提示",
        content: `点击【确定】则该介绍页内容将立即生效，确认是否发布？?`,
        okType: "danger",
        onOk() {
          _this.submit();
        },
      });
    },
    confirmClose() {
      const _this = this;
      _this.$confirm({
        title: "温馨提示",
        content: `点击【取消】则编辑更新内容将不被保存，确认是否取消？?`,
        okType: "danger",
        onOk() {
          _this.closePop();
        },
      });
    },
    submit() {
      // 验证所有卡片
      this.validateAllCards();

      const {
        tips,
        conditions,
        enableBottomLink = null,
        bottomLinkType,
        bottomLinkName,
        bottomLinkUrl,
      } = this.form;

      // 检查是否有错误
      const hasError = this.cardList.some(
        (card) => card.titleError || card.contentError
      );

      if (hasError) {
        return this.$message.error("请完善所有卡片的必填信息");
      }

      // 验证底部链接
      if (enableBottomLink === null) {
        return this.$message.error("请选择是否设置页面底部链接");
      }
      if (enableBottomLink === "1") {
        if (!this.form.bottomLinkType) {
          return this.$message.error("请选择底部链接类型");
        }
        if (!this.form.bottomLinkName) {
          return this.$message.error("请输入底部链接文本");
        }
        if (!this.form.bottomLinkUrl) {
          return this.$message.error("请输入跳转链接URL");
        }
      }

      // 提交前将cardList同步到form.cards，移除错误信息
      this.form.busIntCardList = this.cardList.map((card, index) => ({
        bizType: this.bizType,
        title: card.title,
        content: card.content,
        viewSort: index + 1,
      }));

      let reqParams = {
        bizType: this.bizType,
        conditions,
        tips,
        // 新增字段
        version: "1.0.0",
        enableBottomLink,
      };
      if (enableBottomLink === "1") {
        reqParams = {
          ...reqParams,
          bottomLinkType,
          bottomLinkName,
          bottomLinkUrl,
        };
      }
      this.api.getBcBusinessIntroduceAdd(reqParams).then(() => {
        this.api
          .bcBusinessIntroduceCardBatchUpdate({
            businessIntroduceCardList: this.form.busIntCardList,
          })
          .then(({ code, msg }) => {
            if (code != 0) return this.$message.error(`修改失败：${msg}`);
            this.$message.success("修改成功！");
            this.updateList();
          });
      });
    },
  },
};
</script>

<style scoped>
.ant-layout {
  background-color: #fff;
}
.ant-layout-header {
  background-color: #fff;
}
.card-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
}
.card-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
.card-header {
  padding-bottom: 10px;
  margin-bottom: 15px;
  border-bottom: 1px dashed #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.delete-btn {
  position: absolute;
  right: 15px;
  top: 15px;
  z-index: 10;
}
.card-title {
  font-weight: bold;
  font-size: 16px;
}
.add-card-btn {
  margin: 20px 0;
  text-align: center;
}
.card-limit-tip {
  margin-left: 10px;
  color: #ff4d4f;
}
/* 卡片列表动画 */
.card-list-enter-active,
.card-list-leave-active {
  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);
}
.card-list-enter,
.card-list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}
/* 删除动画 */
.card-item-deleting {
  opacity: 0.6;
  transform: translateX(50px);
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}
/* 添加底部链接区域样式 */
.footer-link-section {
  border-top: 1px dashed #f0f0f0;
  padding-top: 15px;
  margin-top: 10px;
}
</style>
