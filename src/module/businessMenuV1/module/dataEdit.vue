<template>
  <div>
    <a-modal
      title="修改"
      v-model="showPop"
      @ok="submit"
      @cancel="reset"
      class="ant_modal_bigtable"
    >
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-model-item label="所属标签" prop="labelCodes">
          <a-select
            mode="multiple"
            show-search
            optionFilterProp="children"
            v-model="form.labelCodes"
            allowClear
            :options="labelOptions"
            placeholder="请选择所属标签"
          >
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="所属流程">
          <a-select
            show-search
            optionFilterProp="children"
            v-model="form.flowNo"
            :allowClear="true"
            :options="bizOptions"
            placeholder="请选择业务类型别名"
          >
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="业务访问url">
          <a-input v-model="form.bizUrl"></a-input>
        </a-form-model-item>
        <a-form-model-item label="业务类型别名" prop="bizNickName">
          <a-input v-model="form.bizNickName"></a-input>
        </a-form-model-item>
        <a-form-model-item label="业务简介" prop="bizDes">
          <a-textarea type="" v-model="form.bizDes"></a-textarea>
        </a-form-model-item>
        <a-form-model-item label="应用" prop="appType">
          <a-checkbox-group v-model="form.appType" name="checkboxgroup">
            <a-checkbox
              v-for="item in appTypeList"
              :key="item.dictCode"
              :value="item.dictValue"
            >
              {{ item.dictLabel }}
            </a-checkbox>
          </a-checkbox-group>
        </a-form-model-item>
        <a-form-model-item label="PC Logo 图标上传" prop="bizLogoPcImg">
          <a-upload
            name="file"
            list-type="picture-card"
            :multiple="true"
            :data="updataType"
            :file-list="PCFileList"
            action="/bc-manage-server/file/upload"
            @preview="handlePreview($event, 'PC')"
            @change="handleChange($event, 'PC')"
          >
            <div v-if="PCFileList.length < 1">
              <a-icon type="plus" />
              <div class="ant-upload-text">
                PC图标上传
              </div>
            </div>
          </a-upload>
        </a-form-model-item>
        <a-form-model-item label="app Logo 图标上传" prop="bizLogoMobileImg">
          <a-upload
            name="file"
            list-type="picture-card"
            :multiple="false"
            :data="updataType"
            :file-list="APPFileList"
            action="/bc-manage-server/file/upload"
            @preview="handlePreview($event, 'APP')"
            @change="handleChange($event, 'APP')"
          >
            <div v-if="APPFileList.length < 1">
              <a-icon type="plus" />
              <div class="ant-upload-text">
                app图标上传
              </div>
            </div>
          </a-upload>
        </a-form-model-item>
        <a-form-model-item label="是否热门" prop="isHot">
          <a-radio-group v-model="form.isHot">
            <a-radio value="0" key="0">否</a-radio>
            <a-radio value="1" key="1">是</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="是否展示" prop="isShow">
          <a-radio-group v-model="form.isShow">
            <a-radio value="0" key="0">不展示</a-radio>
            <a-radio value="1" key="1">展示</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="状态" prop="status">
          <a-radio-group v-model="form.status">
            <a-radio value="0" key="0">无效</a-radio>
            <a-radio value="1" key="1">有效</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="展示顺序" prop="viewSort">
          <a-input v-model="form.viewSort"></a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
const defaultForm = {
  labelCodes: [],
  flowNo: "",
  bizUrl: "",
  appType: [],
  bizLogoMobileImg: "",
  bizLogoPcImg: "",
  bizType: "",
  bizDes: "",
  bizNickName: "",
  isHot: "0",
  isShow: "1",
  status: "1",
  viewSort: "",
  id: "",
};
export default {
  name: "data_Edit",
  inject: ["api"],
  data() {
    return {
      updataType: {
        type: "image",
      },
      PCFileList: [],
      APPFileList: [],
      form: Object.assign({}, defaultForm),

      rules: {
        labelCodes: [
          {
            required: true,
            message: "所属标签不能为空",
            trigger: "blur",
          },
        ],
        flowNo: [
          {
            required: true,
            message: "业务类型不能为空",
            trigger: "blur",
          },
        ],
        bizNickName: [
          {
            required: true,
            message: "业务类型别名不能为空",
            trigger: "blur",
          },
        ],
        bizDes: [
          {
            required: true,
            message: "业务简介不能为空",
          },
        ],
        appType: [
          {
            required: true,
            message: "请选择应用",
          },
        ],
        viewSort: [
          {
            required: true,
            message: "请选择输入展示顺序",
          },
        ],
      },

      isPopShowTransfer: false,
      includeKeys: [],
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    requestId: {
      type: String,
    },
    labelList: {
      type: Array,
    },
    bizOptions: {
      type: Array,
    },
    parameterData: {
      type: Object,
    },
    appTypeList: {
      type: Array,
    },
  },
  computed: {
    labelOptions() {
      return  this.labelList.map((item) => ({
        label: item.labelName,
        value: item.labelCode,
      })).slice(1);
    },
    isShelfedState() {
      return this.form.isShelfed === "1" ? true : false;
    },
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      this.reset();
      if (n) {
        this.query();
      }
    },
  },
  methods: {
    query() {
      return this.api
        .getBcClientPortal({
          bcClientPortalId: this.parameterData.id,
        })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          this.form = { labelCodes:[],...data}
          this.form.bcBusinessLabelList.forEach((item) => {
            this.form.labelCodes.push(item.labelCode)
          });
          this.form.appType = this.form.appType.split(",");
          this.PCFileList = this.form.bizLogoPcImg
            ? [
                {
                  uid: "1",
                  name: "image.png",
                  status: "done",
                  url:
                    window.$hvue.customConfig.fileUrl + this.form.bizLogoPcImg,
                },
              ]
            : [];
          this.APPFileList = this.form.bizLogoMobileImg
            ? [
                {
                  uid: "2",
                  name: "image.png",
                  status: "done",
                  url:
                    window.$hvue.customConfig.fileUrl +
                    this.form.bizLogoMobileImg,
                },
              ]
            : [];
            console.log(this.form,'22222')
        });
    },

    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      // this.confirmLoading = false;
    },
    updateList() {
      this.showPop = false;
      this.$emit("success");
    },
    async handlePreview(file, type) {
      console.log(file);
      console.log(type);
    },
    handleChange(info, type) {
      let fileList = [...info.fileList];
      fileList = fileList.slice(-2);
      let resImg = info.file.response && info.file.response.data;
      fileList = fileList.map((file) => {
        if (file.response) {
          file.url = window.$hvue.customConfig.fileUrl + file.response.data;
        }
        return file;
      });
      if (type === "PC") {
        this.PCFileList = fileList;
        this.form.bizLogoPcImg = resImg;
      } else if (type === "APP") {
        this.APPFileList = fileList;
        this.form.bizLogoMobileImg = resImg;
      }
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if(!this.form.flowNo&&!this.form.bizUrl){
          return this.$message.error('所属流程和业务访问Url不能同时为空')
        }
        let param = JSON.parse(JSON.stringify(this.form));
        let bizType = param.flowNo?this.bizOptions[
          this.bizOptions.findIndex((item) => item.value === param.flowNo)
        ].bizType:'';
        let labelCodes = param.labelCodes.toString();
        let appType = param.appType.toString();
        param = {
          ...param,
          labelCodes,
          appType,
          bizType,
        };
        console.log(param,'1111')
        this.api.editBcClientPortal(param).then(({ code, msg }) => {
          if (code != 0) return this.$message.error(`修改失败：${msg}`);
          this.$message.success("修改成功！");
          this.showPop = false;
          this.$emit("success");
          this.reset();
        });
      });
    },
  },
};
</script>

<style scoped>
.ant-layout {
  background-color: #fff;
}
.ant-layout-header {
  background-color: #fff;
}
</style>
