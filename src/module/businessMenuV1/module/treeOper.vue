<template>
  <div>
    <a-modal :title="`${typeTitle}业务分组`"
      :visible="showPop"
      :ok-text="typeTitle"
      cancel-text="取消"
      @ok="submit"
      @cancel="closePop"
      :maskClosable="false">
      <a-form-model ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 14 }">
        <a-form-model-item label="业务分组编码"
          prop="labelCode">
          <a-input placeholder="请输入业务分组编码"
            v-model="form.labelCode"></a-input>
        </a-form-model-item>
        <a-form-model-item label="业务分组名称"
          prop="labelName">
          <a-input placeholder="请输入业务分组名称"
            v-model="form.labelName"></a-input>
        </a-form-model-item>
        <a-form-model-item label="业务分组图标" prop="labelPic">
          <a-upload
            name="file"
            list-type="picture-card"
            :multiple="false"
            :data="updataType"
            :file-list="labelList"
            action="/bc-manage-server/file/upload"
            @change="handleChange($event)"
          >
            <div v-if="labelList.length < 1">
              <a-icon type="plus" />
              <div class="ant-upload-text">
                图标上传
              </div>
            </div>
          </a-upload>
        </a-form-model-item>
      <a-form-model-item label="业务分组顺序"
          prop="viewSort" v-show="operationType==='set'">
          <a-input placeholder="请输入业务分组顺序"
            v-model="form.viewSort"></a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'treeOper',
  inject: ['api'],
  data() {
    return {
      form: {},
      rules: {
        labelCode: [
          {
            required: true,
            message: '业务分组编码不能为空',
            trigger: 'blur',
          },
        ],
        labelName: [
          {
            required: true,
            message: '业务分组名称不能为空',
            trigger: 'blur',
          },
        ],
      },
      updataType: {
        type: "image",
      },
      labelList: [],
    };
  },

  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
    // 操作类型
    operationType: {
      type: String,
      default: 'add',
    },
  },

  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
    typeTitle() {
      return this.operationType == 'add' ? '添加' : '修改';
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.operationType != 'add' && this.parameterData.id) {
        this.modify();
      } else if (this.parameterData.id) {
        this.form.id = this.parameterData.id;
      } else if (this.operationType == 'add' && !this.parameterData.id) {
        this.form.id = null;
      } else {
        this.reset();
      }
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = Object.assign({}, this.parameterData);
      this.labelList = this.form.labelPic?[
        {
          uid: "1",
          name: "image.png",
          status: "done",
          url:
            window.$hvue.customConfig.fileUrl + this.form.labelPic,
        },
      ]:[]
    },
    submit() {
      //新增;
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if (this.operationType == 'set') {
          let param = {
            id: this.parameterData.id,
            labelCode: this.form.labelCode,
            labelName: this.form.labelName,
            labelPic: this.form.labelPic,
            isView:'1',
            viewSort: this.form.viewSort
          };
          this.api.editBcBusinessLabel(param).then((res) => {
            if (res.code === 0) {
              this.$message.success(res.msg);
              this.$emit('success', true);
              this.closePop();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          let param = {
            labelCode: this.form.labelCode,
            labelName: this.form.labelName,
            labelPic: this.form.labelPic,
            isView:'1'
          };
          this.api.addBcBusinessLabel(param).then((res) => {
            if (res.code === 0) {
              this.$message.success(res.msg);
              this.$emit('success', true);
              this.closePop();
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      });
    },
    handleChange(info) {
      let fileList = [...info.fileList];
      fileList = fileList.slice(-2);
      let resImg = info.file.response && info.file.response.data;
      fileList = fileList.map((file) => {
        if (file.response) {
          file.url = window.$hvue.customConfig.fileUrl + file.response.data;
        }
        return file;
      });
        this.labelList = fileList;
        this.form.labelPic = resImg;
    },
  },
};
</script>
<style lang='scss' scoped>
</style>