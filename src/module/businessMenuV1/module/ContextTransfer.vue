<template>
	<a-modal
		v-model="showPop"
		:width="700"
		:maskClosable="false"
		title="添加关联协议"
		@ok="submit"
		@cancel="reset"
	>
		<a-transfer
			:data-source="soucreData"
			:target-keys="targetKeys"
			show-search
			:show-select-all="true"
			:list-style="{
				width: '300px',
				height: '390px'
			}"
			:render="item => `${item.title}（${item.key}）`"
			@change="handleChange"
		>
			<span slot="notFoundContent">没数据</span>
		</a-transfer>
	</a-modal>
</template>

<script>
	export default {
		name: "ContextTransfer",
		data() {
			return {
				soucreData: [],
				targetKeys: []
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			data: {
				type: Array,
				default: () => {
					return [];
				}
			},
			includeKeys: {
				type: Array,
				default: () => {
					return [];
				}
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // 改变的时候通知父组件
				}
			}
		},
		watch: {
			isPopShow(n) {
				if (n) {
					this.update();
				} else {
					this.reset();
				}
			},
			includeKeys(val) {
                console.log(val)
				this.targetKeys = val;
			}
		},
		created() {
			this.update();
		},
		methods: {
			update() {
				this.soucreData = this.data.map(item => ({
					key: item.strategyNo,
					title: item.strategyName
				}));
			},
			reset() {
				this.showPop = false;
			},
			submit() {
				let arr = [];
				this.targetKeys.forEach(e => {
					let obj = this.soucreData.filter(o => o.key == e)[0];
					let index = this.includeKeys.findIndex(o => o == e);
					if (!(index > -1)) {
						arr.push(obj);
					}
				});
				if (arr.length > 1) {
                    this.$message.error('关联协议唯一');
				} else {
					this.$emit("update", arr);
					this.reset();
				}
			},
			handleChange(nextTargetKeys) {
				this.targetKeys = nextTargetKeys;
			}
		}
	};
</script>

<style lang="scss" scoped>
</style>