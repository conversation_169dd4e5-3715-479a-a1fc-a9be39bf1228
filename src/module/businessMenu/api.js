// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询业务标签
   * @description:
   * @param {*}
   */
  @Parameters(["searchValue"])
  getBcAgreeRegList() {
    return this.services.initGet({
      reqUrl: "bcBusinessLabel/list",
      param: this.param,
    });
  }

  /**
   * 获取业务类型名称list
   * @param {Object} param
   */
  @Parameters()
  getBcBizTypeList() {
    return this.bfServices.initGet({
      reqUrl: "/bf/flow/page",
      param: this.param,
    });
  }

  /**
   * 获取应用list
   * @param {Object} param
   */
  @Parameters()
  getBcAppList() {
    return this.services.initGet({
      reqUrl: "bcApps/list",
      param: this.param,
    });
  }

  /**
   * 新增业务
   * @param {Object} param
   * - appIds {String} 多个以“，”隔开
   */
  @Parameters([
    "isShelfed",
    "bizType",
    "bizName",
    "bizDes",
    "bizLogoMobileImg",
    "bizLogoPcImg",
    "flowNo",
    "appIds",
    "appNames",
    "labelCode",
    "bizNickName",
    "labelName",
    "strategyNo",
  ])
  addBcBusinessShelf() {
    return this.services.initPost({
      reqUrl: "bcBusinessShelf/add",
      param: this.param,
    });
  }

  /**
   * 批量上下架
   * @param {Object} param
   * @returns {Promise}
   */
  @Parameters(["bcBusinessShelfIds", "isShelfed"])
  batchChangeShelfState() {
    return this.services.initPost({
      reqUrl: "bcBusinessShelf/batchChangeShelfState",
      param: this.param,
    });
  }

  /**
   * 修改业务
   * @param {Object} param
   * - appIds {String} 多个以“，”隔开
   */
  @Parameters([
    "isShelfed",
    "bizType",
    "bizName",
    "bizDes",
    "bizLogoMobileImg",
    "bizLogoPcImg",
    "appIds",
    "flowNo",
    "appNames",
    "labelCode",
    "bizNickName",
    "labelName",
    "viewSort",
    "strategyNo",
    "id",
  ])
  EditBcBusinessShelf() {
    return this.services.initPost({
      reqUrl: "bcBusinessShelf/edit",
      param: this.param,
    });
  }

  /**
   * 删除业务
   * @param {Object} param
   * - bcBusinessShelfIds {String} 参数主键ID
   */
  @Parameters(["bcBusinessShelfIds"])
  deleteBcBusinessShelf() {
    return this.services.initPost({
      reqUrl: "bcBusinessShelf/deletes",
      param: this.param,
    });
  }

  /**
   * 业务介绍
   * @param {Object} param
   */
  @Parameters(["bizType", "introduceContent"])
  getBcBusinessIntroduceAdd() {
    return this.services.initPost({
      reqUrl: "bcBusinessIntroduce/add",
      param: this.param,
    });
  }

  /**
   * 查询业务介绍
   * @param {Object} param
   */
  @Parameters(["bizType"])
  getBcBusinessIntroduceQuery() {
    return this.services.initGet({
      reqUrl: "bcBusinessIntroduce/query",
      param: this.param,
    });
  }

  /**
   * 查询关联策略数据源
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  getStrategyList() {
    return this.qcServices.initGet({
      reqUrl: "qcrule/v2/strategy/page?pageNum=1&pageSize=10000",
      param: this.param,
    });
  }

  /**
   * 查询指定流程样式配置表
   * @description:
   * @param {*}
   */
  @Parameters(["bizType", "flowNo"])
  getBizStyleQuery() {
    return this.services.initGet({
      reqUrl: "bcFlowStyleConfig/query",
      param: this.param,
    });
  }

  /**
   * 修改流程样式配置表
   * @description:
   * @param {*}
   */
  @Parameters(["_data"])
  bizStyleEdit() {
    return this.services.initPost({
      reqUrl: "bcFlowStyleConfig/edit",
      param: this.param,
    });
  }
}

export default new api();
