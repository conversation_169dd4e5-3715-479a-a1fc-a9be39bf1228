<template>
	<div>
		<a-modal
			title="业务介绍"
			:visible="showPop"
            :width="1000"
			@ok="submit"
			@cancel="closePop"
			class="ant_modal_bigtable"
		>
			<tkEditor
				v-if="editorShow"
				ref="tinymce"
				v-model="content"
				:height="300"
				:disabled="false"
			/>
		</a-modal>
	</div>
</template>

<script>
	import tkEditor from "@c/tinymceEditor";
	export default {
		data() {
			return {
				editorShow: false,
				bizType: "",
				content: ""
			};
        },
        inject: ["api"],
		components: { tkEditor },
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			requestId: {
				type: String
			},
			parameterData: {
				type: Object
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		watch: {
			isPopShow(n) {
				if (n) {
					this.query();
				}
				this.$nextTick(() => (this.editorShow = n));
			}
		},
		methods: {
			query() {
				this.bizType = this.parameterData.bizType;
				this.api
					.getBcBusinessIntroduceQuery({
						bizType: this.bizType
					})
					.then(data => {
                        console.log(data)
						this.content = data.data?data.data.introduceContent:'';
					});
			},
			closePop() {
				this.showPop = false;
			},
			updateList() {
				this.showPop = false;
				this.$emit("success");
			},
			submit() {
				this.api
					.getBcBusinessIntroduceAdd({
						bizType: this.bizType,
						introduceContent: this.content
					})
					.then(({ code, msg }) => {
						if (code != 0)
							return this.$message.error(`修改失败：${msg}`);
						this.$message.success("修改成功！");
						this.updateList();
					});
			}
		}
	};
</script>

<style scoped>
	.ant-layout {
		background-color: #fff;
	}
	.ant-layout-header {
		background-color: #fff;
    }
</style>
