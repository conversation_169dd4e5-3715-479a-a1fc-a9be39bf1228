<template>
	<div>
		<a-modal
			title="修改样式"
			:visible="showPop"
			@ok="submit"
			@cancel="closePop"
			:width="770"
		>
			<template slot="footer">
				<a-button key="back" @click="closePop">
					取消
				</a-button>
				<a-button type="primary" key="submit" @click="submit"
					>修改</a-button
				>
			</template>
			<a-layout>
				<a-layout-content
					class="pop_content"
					style="min-height: calc(270px - 120px);"
				>
					<!-- <a-row v-for="(item, index) in styleform" :key="index">
						<a-col :span="6" class="label_style">
							<label for=""
								>{{ item.title
								}}<a class="mark_style">*</a></label
							>
						</a-col>
						<a-col :span="18">
							<a-input
								v-model="info[item.key]"
								placeholder="色值 如#FFFFFF"
							/>
						</a-col>
					</a-row> -->
					<a-row>
            <a-col :span="3"
              class="label_style">
              <label for="">样式脚本</label>
            </a-col>
            <a-col :span="18">
              <AceEditor style="height: 400px"
                ref="AceEditor"
                :showMode="false"
                v-model="info.styleConfigStr" />
            </a-col>
          </a-row>
				</a-layout-content>
			</a-layout>
		</a-modal>
	</div>
</template>
<script>
	// import { styleform } from "../styleModule";
	import AceEditor from '@c/aceEditor';

	export default {
		name: "changeStyle",
		data() {
			return {
				// styleform,
				info: {
					styleConfigStr:''
				}
			};
		},
		inject: ["api"],
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			parameterData: {
				type: Object
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					console.log(val);
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		watch: {
			isPopShow(n) {
				if (n) {
					this.query();
				}
			}
		},
		components: { AceEditor },
		methods: {
			//获取详情
			query() {
				let { bizType, flowNo } = this.parameterData;
				this.api.getBizStyleQuery({ bizType, flowNo }).then(res => {
					this.$nextTick(() => {
						this.$refs.AceEditor.resize(true);
						if (res.data.styleConfigStr) {
							this.$refs.AceEditor.setValue(res.data.styleConfigStr);
						}
					});
					if (res.code === 0 && res.data) {
						// console.info("res", res.data);
                        this.info = res.data || {};
                        // this.styleform.forEach((item) => {
                        //     this.info[item.key] = res.data[item.key]
                        // })
					}
				});
			},
			//关闭
			closePop() {
				this.showPop = false;
				this.reset();
			},
			updateList() {
				this.showPop = false;
				this.$emit("success");
			},
			//提交
			submit() {
				let { bizType, flowNo } = this.parameterData;
				if (this.info.styleConfigStr == "") {
					this.$message.error("样式脚本不能为空");
					return false;
				}
				this.api
					.bizStyleEdit({
						bizType,
						flowNo,
						...this.info
					})
					.then(({ code, msg }) => {
						if (code != 0)
							return this.$message.error(`修改失败：${msg}`);
						this.$message.success("修改成功！");
						this.updateList();
					});
			},
			//重置
			reset() {
				this.info = {
					styleConfigStr: ""
				};
				this.$nextTick(() => {
					this.$refs.AceEditor &&
					this.$refs.AceEditor.setValue(this.info.styleConfigStr);
				});
			},
			handleChange() {}
		}
	};
</script>
<style scoped>
</style>