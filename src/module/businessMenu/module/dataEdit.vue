<template>
	<div>
		<a-modal
			title="修改"
			:visible="showPop"
			@ok="submit"
			@cancel="closePop"
			class="ant_modal_bigtable"
		>
			<a-form-model
				ref="form"
				:model="form"
                :rules="rules"
				:label-col="{ span: 8 }"
				:wrapper-col="{ span: 14 }"
			>
				<a-form-model-item label="所属标签" prop="labelCode">
					<a-select
						show-search
						optionFilterProp="children"
						v-model="form.labelCode"
						:allowClear="true"
						:options="labelOptions"
						placeholder="请选择所属标签"
					>
					</a-select>
				</a-form-model-item>
				<a-form-model-item label="所属流程" prop="flowNo">
					<a-select
						show-search
						optionFilterProp="children"
						v-model="form.flowNo"
						:allowClear="true"
						:options="bizOptions"
						placeholder="请选择业务类型别名"
					>
					</a-select>
				</a-form-model-item>
				<a-form-model-item label="业务类型别名" prop="bizNickName">
					<a-input v-model="form.bizNickName"></a-input>
				</a-form-model-item>
                <a-form-model-item label="业务简介" prop="bizDes">
					<a-textarea type="" v-model="form.bizDes"></a-textarea>
				</a-form-model-item>
				<a-form-model-item label="应用" prop="appIds">
					<a-checkbox-group
						v-model="form.appIds"
						name="checkboxgroup"
						:options="AppOptions"
					/>
				</a-form-model-item>
				<a-form-model-item label="PC Logo 图标上传" prop="bizLogoPcImg">
					<a-upload
						name="file"
						list-type="picture-card"
						:multiple="true"
						:data="updataType"
						:file-list="PCFileList"
						action="/bc-manage-server/file/upload"
						@preview="handlePreview($event, 'PC')"
						@change="handleChange($event, 'PC')"
					>
						<div v-if="PCFileList.length < 1">
							<a-icon type="plus" />
							<div class="ant-upload-text">
								PC图标上传
							</div>
						</div>
					</a-upload>
				</a-form-model-item>
				<a-form-model-item
					label="app Logo 图标上传"
					prop="bizLogoMobileImg"
				>
					<a-upload
						name="file"
						list-type="picture-card"
						:multiple="false"
						:data="updataType"
						:file-list="APPFileList"
						action="/bc-manage-server/file/upload"
						@preview="handlePreview($event, 'APP')"
						@change="handleChange($event, 'APP')"
					>
						<div v-if="APPFileList.length < 1">
							<a-icon type="plus" />
							<div class="ant-upload-text">
								app图标上传
							</div>
						</div>
					</a-upload>
				</a-form-model-item>
				<a-form-model-item label="上架/下架" prop="isShelfed">
					<a-switch
						:checked="isShelfedState"
						checked-children="上架"
						un-checked-children="下架"
						@change="isShelfedOnChange"
					>
						上架/下架
					</a-switch>
				</a-form-model-item>
                <!-- <a-form-model-item label="策略编号选择录入" prop="strategyNo">
					点击<a-button
						type="link"
						size="small"
						@click.stop="addStrategy"
					>
						“选择策略编号录入” </a-button
					>，已选择：<p v-if="strategyNo">{{strategyName}}（{{strategyNo}}）;</p>
				</a-form-model-item> -->
				<!-- <a-form-model-item label="序号" prop="incomeType">
					<a-input></a-input>
				</a-form-model-item>
				<a-form-model-item label="连接地址" prop="incomeType">
					<a-input></a-input>
				</a-form-model-item> -->
			</a-form-model>
		</a-modal>

        <!-- <ContextTransfer
			:isPopShow.sync="isPopShowTransfer"
			:includeKeys="includeKeys"
			:data="transferData"
			@update="update"
		/> -->
	</div>
</template>

<script>
    // import ContextTransfer from "./ContextTransfer";
    
	export default {
		name: "data_Edit",
		inject: ["api"],
		data() {
			return {
				updataType: {
					type: "image"
				},
				PCFileList: [],
                APPFileList: [],
                strategyName:'',
                strategyNo:'',
				form: {
					labelCode: "",
                    appIds: [],
                    flowNo:'',
					appNames: "",
					bizLogoMobileImg: "",
					bizLogoPcImg: "",
					bizType: "",
					labelName: "",
					bizNickName: "",
					isShelfed: "0",
					viewSort: "",
					id: ""
                },

                rules: {
					labelCode: [
						{
							required: true,
							message: "所属标签不能为空",
							trigger: "blur"
						}
					],
					flowNo: [
						{
							required: true,
							message: "业务类型不能为空",
							trigger: "blur"
						}
					],
					bizNickName: [
						{
							required: true,
							message: "业务类型别名不能为空",
							trigger: "blur"
						}
					],
					bizDes: [
						{
							required: true,
							message: "业务简介不能为空"
						}
					],
					appIds: [
						{
							required: true,
							message: "请选择应用"
						}
					]
				},
                
                isPopShowTransfer: false,
				includeKeys: []
			};
		},
		// components: { ContextTransfer },
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			requestId: {
				type: String
			},
			labelList: {
				type: Array
			},
			bizOptions: {
				type: Array
			},
			transferData: {
				type: Array
			},
			AppOptions: {
				type: Array
			},
			parameterData: {
				type: Object
			}
		},
		computed: {
			labelOptions() {
				return this.labelList.map(item => ({
					label: item.labelName,
					value: item.labelCode
				}));
			},
			isShelfedState() {
				return this.form.isShelfed === "1" ? true : false;
			},
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		watch: {
			isPopShow() {
				this.updataData();
			}
		},
		methods: {
			addStrategy() {
				this.isPopShowTransfer = true;
			},

			update(data) {
				this.strategyName = data[0].title;
				this.strategyNo = data[0].key;
			},

			updataData() {
				this.form = {
					...this.parameterData
				};
				this.form.appIds = this.form.appIds.split(",");
				this.PCFileList = this.parameterData.bizLogoPcImg
					? [
							{
								uid: "1",
								name: "image.png",
								status: "done",
								url:
									window.$hvue.customConfig.fileUrl +
									this.parameterData.bizLogoPcImg
							}]
					: [];
				this.APPFileList = this.parameterData.bizLogoMobileImg
					? [
							{
								uid: "2",
								name: "image.png",
								status: "done",
								url:
									window.$hvue.customConfig.fileUrl +
									this.parameterData.bizLogoMobileImg
							}]
                    : [];
				this.strategyName = this.parameterData.strategyName;
                this.strategyNo = this.parameterData.strategyNo;
                this.includeKeys = [this.parameterData.strategyNo]
			},

			closePop() {
				this.showPop = false;
			},
			updateList() {
				this.showPop = false;
				this.$emit("success");
			},
			isShelfedOnChange(e) {
				e ? (this.form.isShelfed = "1") : (this.form.isShelfed = "0");
			},
			async handlePreview(file, type) {
				console.log(file);
				console.log(type);
			},
			handleChange(info, type) {
                // const isJpgOrPng =
				// 	info.file.type === "image/jpeg" ||
				// 	info.file.type === "image/png";
				// if (!isJpgOrPng) {
				// 	this.$message.error("不支持的文件格式!");
				// 	return false;
				// }
				// const isLtSize = info.file.size / 1024 / 1024 < 2;
				// if (!isLtSize) {
				// 	this.$message.error("图片大小超过 2MB!");
				// 	return false;
				// }
				let fileList = [...info.fileList];
				fileList = fileList.slice(-2);
				let resImg = info.file.response && info.file.response.data;
				fileList = fileList.map(file => {
					if (file.response) {
						file.url =
							window.$hvue.customConfig.fileUrl + file.response.data;
					}
					return file;
				});
				if (type === "PC") {
					this.PCFileList = fileList;
					this.form.bizLogoPcImg = resImg;
				} else if (type === "APP") {
					this.APPFileList = fileList;
					this.form.bizLogoMobileImg = resImg;
				}
			},
			submit() {
				this.$refs.form.validate(valid => {
				if(!valid) return
				let labelName = this.labelList[
					this.labelList.findIndex(
						item => item.labelCode === this.form.labelCode
					)
				].labelName;
				let bizName = this.bizOptions[
					this.bizOptions.findIndex(
						item => item.value === this.form.flowNo
					)
                ].bizName;
                let bizType = this.bizOptions[
					this.bizOptions.findIndex(
						item => item.value === this.form.flowNo
					)
				].bizType;
				let appNames = [];
				this.AppOptions.forEach(e => {
					this.form.appIds.forEach(item => {
						if (item === e.value) {
							appNames.push(e.label);
						}
					});
				});
				this.form = {
					...this.form,
					appIds: this.form.appIds.join(","),
					appNames: appNames.join(","),
					labelName: labelName,
					bizType,
					bizName: bizName,
					strategyNo: this.strategyNo
				};
				this.api.EditBcBusinessShelf(this.form).then(({ code, msg }) => {
					if (code != 0) return this.$message.error(`修改失败：${msg}`);
					this.$message.success("修改成功！");
					this.updateList();
				});
				})
			}
		}
	};
</script>

<style scoped>
	.ant-layout {
		background-color: #fff;
	}
	.ant-layout-header {
		background-color: #fff;
	}
</style>
