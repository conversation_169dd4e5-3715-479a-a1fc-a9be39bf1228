<template>
  <div class="menu-right-limits">
    <!-- 添加修改 -->
    <dataAdd :isPopShow.sync="isAddPopShow"
      :requestId="registerId"
      :labelList="labelList"
      :bizOptions="bizOptions"
      :AppOptions="AppOptions"
      :transferData="transferData"
      @success="query" />
    <dataEdit :isPopShow.sync="isEditPopShow"
      :requestId="registerId"
      :labelList="labelList"
      :bizOptions="bizOptions"
      :AppOptions="AppOptions"
      :parameterData="selectData"
      :transferData="transferData"
      @success="query" />
    <bizInfo :isPopShow.sync="isBizInfoShow"
      :parameterData="selectData"
      :requestId="registerId"
      @success="query" />
    <changeStyle :isPopShow.sync="isStyleShow"
      :parameterData="selectData"
      @success="query" />
    <div class="searchForm">
      <a-form-model layout="inline"
        :model="tableForm">
        <tkSelectForm @query="query">
          <a-form-model-item label="业务类型别名">
            <a-input v-model="tableForm.bizNickName"
              :allowClear="true"
              placeholder="请输入业务类型别名"
              @keyup.enter.native="query">
            </a-input>
          </a-form-model-item>
          <a-form-model-item label="业务编号">
            <a-input v-model="tableForm.bizType"
              :allowClear="true"
              placeholder="请输入业务编号"
              @keyup.enter.native="query">
            </a-input>
          </a-form-model-item>
          <a-form-model-item label="上架状态">
            <a-select v-model="tableForm.isShelfed"
              :allowClear="true"
              placeholder="请选择上架状态"
              @keyup.enter.native="query">
              <a-select-option value="1">
                上架
              </a-select-option>
              <a-select-option value="0">
                下架
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="流程编号">
            <a-input v-model="tableForm.flowNo"
              :allowClear="true"
              placeholder="请输入流程编号"
              @keyup.enter.native="query">
            </a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <tk-table ref="table"
      :tableData.sync="comlun"
      :intercept-response="intercept_response"
      getMethods="bc-manage-server/bcBusinessShelf/page"
      :isSelected="true"
      :isPaging="true"
      :tableFrom="tableForm"
      :selectedRowKeys.sync="selectedRowKeys"
      tableId="id"
      :isTableLoading="false"
      id="tk-table"
      :tableWidth="1500"
      >
      <div class="table-buttun-area"
        slot="tableHeader">
        <a-button type="primary"
          icon="plus"
          @click="add">
          新增
        </a-button>
        <a-button icon="delete"
          type="danger"
          :disabled="selectedRowKeys.length <= 0"
          @click="remove">
          删除
        </a-button>
        <a-button icon="download"
          type="primary"
          @click="downloadExcel">
          导入
        </a-button>
        <a-button icon="upload"
          type="primary"
          @click="exportExcel">
          导出
        </a-button>
        <a-select v-if="!selectedRowKeys.length <= 0"
          placeholder="请选择是否上架"
          style="width: 160px"
          @change="handleChangeIsShelfed">
          <a-select-option value="1">
            是
          </a-select-option>
          <a-select-option value="0">
            否
          </a-select-option>
        </a-select>
      </div>
      <template slot="operation"
        slot-scope="data">
        <a-button type="link"
          size="small"
          class="btn"
          @click.stop="modify(data)">
          修改
        </a-button>
        <a-button type="link"
          size="small"
          class="btn"
          @click.stop="openBizInfo(data)">
          业务介绍
        </a-button>
        <a-button type="link"
          size="small"
          class="btn"
          @click.stop="changeStyle(data)">
          样式管理
        </a-button>
      </template>
    </tk-table>

    <upload :visible.sync="uploadShow"
      :downloadeUrl="downloadeUrl"
      :uploadUrl="uploadUrl"
      downloadeTitle="应用菜单模板文件"
      @success="query" />
  </div>
</template>

<script>
import dataAdd from './dataAdd';
import dataEdit from './dataEdit';
import bizInfo from './bizInfo';
import changeStyle from './changeStyle';
import upload from '@c/upload/upload';
export default {
  data() {
    return {
      downloadeUrl: '',
      uploadUrl: 'bcBusinessShelf/import',
      // 表格展示字段
      comlun: [
        // 循环
        {
          field: 'bizType',
          label: '业务编号',
          // width: 100,
          isSorter: true,
        },
        {
          field: 'bizNickName',
          label: '业务名',
          // width: 200,
          isSorter: true,
        },
        {
          field: 'labelName',
          label: '所属标签',
          // width: 120,
          isSorter: false,
        },
        {
          field: 'appNames',
          label: '已上架应用',
          isSorter: false,
        },
        {
          field: 'isShelfed',
          label: '上架状态',
          filter: (item) => (item === '1' ? '是' : '否'),
        },
        {
          field: 'flowNo',
          label: '流程编号',
          isSorter: false
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          fixed: 'right',
        },
      ],
      AppOptions: [],
      bizOptions: [],
      // 查询接口所需字段
      tableForm: {},
      operationType: 'add',
      // 是否显示弹窗
      isAddPopShow: false,
      isEditPopShow: false,
      isBizInfoShow: false,
      isStyleShow: false,
      //查看弹窗
      isLookPopShow: false,
      selectData: {}, // 所要修改的数据
      selectedRowKeys: [], // 当前用户选中参数
      registerId: undefined,
      uploadShow: false,
      transferData: [],
    };
  },
  inject: ['api'],
  components: { dataAdd, dataEdit, bizInfo, changeStyle, upload },
  props: {
    // 数据权限分组编号
    regId: {
      type: String,
    },
    labelList: {
      type: Array,
    },
  },
  mounted() {
    // 初始给与值
    this.$refs.table.getTableData();
    this.selectedRowKeys = [];
    // this.api.getStrategyList().then(data => {
    // 	this.transferData = data.data.list;
    // });
  },
  watch: {
    // 用户传入菜单编号
    regId: {
      handler(val) {
        if (val) {
          // 具有菜单编号时查询对应的权限列表
          this.tableForm['labelCode'] = val;
        } else {
          delete this.tableForm.labelCode;
        }
        this.$refs.table.getTableData();
        // 初始话选中
        this.selectedRowKeys = [];
      },
      deep: true,
    },
  },
  created() {
    // 获取业务类型list
    this.api.getBcBizTypeList().then((data) => {
      this.bizOptions = data.data.list.map((item) => ({
        label: `${item.flowName}（${item.flowNo}）`,
        value: item.flowNo,
        bizType: item.bizType,
        bizName: item.bizName,
      }));
      console.log(this.bizOptions);
    });
    this.api.getBcAppList().then((data) => {
      this.AppOptions = data.data.map((item) => ({
        label: item.appName,
        value: item.appId,
      }));
    });
  },
  methods: {
    // 导入downloadExcel
    downloadExcel() {
      this.uploadShow = true;
    },

    // 导出exportExcel
    exportExcel() {
      window.location.href = '/bc-manage-server/bcBusinessShelf/export';
    },
    // 打开业务介绍弹窗
    openBizInfo(data) {
      this.isBizInfoShow = true;
      this.selectData = JSON.parse(JSON.stringify(data));
    },
    // 删除对象中的空属性
    clean(obj) {
      for (var propName in obj) {
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName];
        }
      }
    },
    // 修改上架
    handleChangeIsShelfed(e) {
      this.api
        .batchChangeShelfState({
          isShelfed: e,
          bcBusinessShelfIds: this.selectedRowKeys.join(','),
        })
        .then(() => {
          this.$message.success('修改成功');
          this.$refs.table.getTableData();
          this.selectedRowKeys = [];
        });
    },
    add() {
      //点击新增按钮
      this.isAddPopShow = true;
      this.registerId = this.regId;
    },
    // 点击修改按钮
    modify(data) {
      this.isEditPopShow = true;
      this.selectData = JSON.parse(JSON.stringify(data));
    },
    // 点击修改样式按钮
    changeStyle(data) {
      this.isStyleShow = true;
      this.selectData = JSON.parse(JSON.stringify(data));
    },
    // 查看
    look(data) {
      this.isLookPopShow = true;
      this.agreeCode = data.agreeCode;
    },
    // 操作成功
    query() {
      this.clean(this.tableForm);
      this.selectedRowKeys = [];
      this.$refs.table.getTableData();
    },
    // reset() {
    //     this.tableForm = {}
    //     this.tableForm["labelCode"] = this.regId
    //     this.selectedRowKeys = [];
    // 	// this.$refs.table.getTableData();
    // },
    // 删除对应的权限
    remove() {
      this.$confirm({
        title: '删除业务',
        content: () => <p>确定删除当前业务？</p>,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.api
            .deleteBcBusinessShelf({
              bcBusinessShelfIds: this.selectedRowKeys.join(','),
            })
            .then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`删除业务失败：${msg}`);
              this.$message.success('删除业务成功！');
              this.$refs.table.getTableData();
              this.selectedRowKeys = [];
            });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep #tk-table .btn {
  margin: 0 !important;
}
</style>
