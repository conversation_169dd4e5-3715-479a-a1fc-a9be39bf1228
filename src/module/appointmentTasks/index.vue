<template>
  <a-card title="预约挽留列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm
          @query="dealYb('search')"
          @reset="handleReset"
          layoutType="flex"
          :defaultShowRow="2"
        >
          <a-form-model-item label="客户号">
            <a-input
              v-model="tableForm.clientId"
              placeholder="请输入客户号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户姓名">
            <a-input
              v-model="tableForm.clientName"
              placeholder="请输入客户姓名"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="受理单号">
            <a-input
              v-model="tableForm.serialNo"
              placeholder="请输入受理单号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="业务类型"> 预约销户 </a-form-model-item>
          <a-form-model-item label="任务状态">
            <a-select
              v-model="tableForm.taskStatus"
              placeholder="请选择任务状态"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in stateList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="执行组织">
            <a-select
              v-model="tableForm.executeGroup"
              placeholder="请选择执行组织"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(val, key) in executeGroupMap"
                :value="key"
                :key="key"
              >
                {{ val }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="执行人">
            <a-input
              v-model="tableForm.executeUser"
              placeholder="请输入执行人"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="销户申请时间">
            <a-range-picker v-model="tableForm.createTime" @change="onChange" />
          </a-form-model-item>
          <!--          <a-form-model-item label="销户申请时间">
                      <a-date-picker
                          v-model="tableForm.createTime"
                          valueFormat="YYYY-MM-DD"
                          placeholder="请选择销户申请时间"
                          style="width:100%"
                      />
                    </a-form-model-item>-->
          <a-form-model-item label="EBOSS审批单号">
            <a-input
              v-model="tableForm.ebossFdId"
              placeholder="请输入EBOSS审批单号"
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="page_main">
      <a-divider />
      <a-row class="btn_row">
        <a-col>
          <!--          <a-button type="primary" icon="plus" @click="dealYb('add')"
                    >新增
                    </a-button
                    >
                    <a-button
                        type="primary"
                        icon="edit"
                        @click="dealYb('edit')"
                        :disabled="!(selectedRowKeys.length == 1)"
                    >编辑
                    </a-button
                    >
                    <a-button
                        v-if="constShowDelelte"
                        type="primary"
                        icon="delete"
                        @click="dealYb('remove')"
                        :disabled="selectedRowKeys.length == 0"
                    >删除
                    </a-button
                    >-->
          <a-button type="primary" icon="redo" @click="dealYb('redo')"
            >刷新
          </a-button>
        </a-col>
      </a-row>
      <div class="access-table">
        <tk-table
          ref="table"
          :tableData.sync="columns"
          getMethods="bc-manage-server/taskInfo/list"
          :isSelected="true"
          :isPaging="true"
          :tableFrom="tableForm"
          :selectedRowKeys.sync="selectedRowKeys"
          tableId="id"
          :tableFromFilter="tableFormFilter"
        >
          <template slot="ebossFdId" slot-scope="data">
            {{ data.ebossFdId }}
            <a-tag color="#f5222d" v-show="getEbossFdId(data) !== ''">
              {{ getEbossFdId(data) }}
            </a-tag>
          </template>
          <template slot="operation" slot-scope="data">
            <a class="action-btn" @click="showDetail(data)"> 查看 </a>
          </template>
        </tk-table>
      </div>
    </div>
  </a-card>
</template>

<script>
import api from "./api";
import dayjs from "dayjs";

const defaultForm = {
  bizType: undefined,
  name: "",
  state: undefined,
};

export default {
  name: "appointmentTasks",
  data() {
    return {
      tableForm: {
        serialNo: "", //受理单号
        bizName: "销户预约",
        clientId: "", //客户ID
        clientName: "", // 客户姓名
        taskStatus: "", // 任务状态(0未执行1执行中2成功3失败4过期)
        executeUser: "", //执行人
        executeGroup: "", //执行组织id
        executeGroupName: "", //执行组织名称
        createTime: "", //销户申请时间
        createTimeBeginTime: "",
        createTimeEndTime: "",
        ebossFdId: "", //EBOSS审批单号
      },
      executeGroupMap: null, //执行组织列表
      stateList: [
        { value: "0", name: "未执行" },
        { value: "1", name: "执行中" },
        { value: "2", name: "成功" },
        { value: "3", name: "失败" },
        { value: "4", name: "过期" },
      ], //状态列表
      constShowDelelte: true,
      //table start
      //表格数据
      data: [],
      //表头数据
      columns: [
        {
          label: "业务流水号",
          // dataIndex: "name",
          field: "serialNo",
          isSorter: false,
        },
        {
          label: "业务名称",
          field: "bizName",
          isSorter: false,
          filter: (item) => (item ? item : "销户挽留"),
        },
        {
          label: "客户号",
          field: "clientId",
          isSorter: false,
        },
        {
          label: "客户姓名",
          // dataIndex: "state",
          field: "clientName",
          isSorter: false,
        },
        {
          label: "执行组织",
          field: "executeGroup",
          isSorter: false,
          filter: (item) => this.getExecuteGroupList(item),
        },
        {
          label: "执行人",
          field: "executeUser",
        },
        {
          label: "销户申请时间",
          field: "createTime",
        },
        {
          label: "任务状态",
          field: "taskStatus",
          filter: (item) => this.getTaskStatusList(item),
        },
        {
          label: "EBOSS审批单号",
          field: "ebossFdId",
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
          width: 220,
        },
      ],
      selectedRowKeys: [], // Check here to configure the default column

      //新增弹窗
      isPopShow: false, //弹窗是否显示
      requestId: "", //id
      typeList: window.$dict.dictTypeList("bc.common.smsTemplate"),
      businessList: [],
    };
  },
  components: {},
  created() {
    this.queryExecuteGroup();
  },
  methods: {
    getMap() {
      return {
        stateList: [
          { value: "1", name: "有效" },
          { value: "0", name: "无效" },
        ], //有效值列表
        taskTypeList: [
          { name: "预审任务", value: "1" },
          { name: "终审任务", value: "2" },
          { name: "办理任务", value: "3" },
        ],
        taskStatusList: [
          { value: "0", name: "未执行" },
          { value: "1", name: "执行中" },
          { value: "2", name: "成功" },
          { value: "3", name: "失败" },
          { value: "4", name: "过期" },
        ],
        thirdFlagList: [
          { name: "内部", value: "0" },
          { name: "外部", value: "1" },
        ],
      };
    },
    queryBusinessList() {
      api.queryBcClientPortalList({ status: "1" }).then((res) => {
        this.businessList = res.data;
      });
    },
    //新增刷新
    updateList(param) {
      if (param) {
        this.$refs.table.getTableData();
      }
    },
    //处理按钮
    dealYb(val) {
      if (val == "add") {
        //添加
        this.requestId = "";
        this.isPopShow = true;
      } else if (val == "edit") {
        //编辑
        this.requestId = this.selectedRowKeys[0] + ""; //获取id
        this.isPopShow = true;
      } else if (val == "remove") {
        //删除
        if (this.selectedRowKeys.length > 0) {
          this.remove();
        }
      } else if (val == "search") {
        //搜索
        this.$refs.table.getTableData();
      } else if (val == "redo") {
        this.$refs.table.getTableData();
      }
    },
    //删除
    remove() {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deleteApi();
        },
      });
    },
    //请求删除接口
    deleteApi() {
      let bcMsgBizConfigId;
      let funcName;
      let reqRaram;
      if (this.selectedRowKeys.length > 1) {
        bcMsgBizConfigId = this.selectedRowKeys.join(",");
        funcName = "deleteMsgs";
        reqRaram = {
          bcMsgBizConfigIds: bcMsgBizConfigId,
        };
      } else {
        bcMsgBizConfigId = this.selectedRowKeys[0];
        funcName = "deleteMsg";
        reqRaram = {
          bcMsgBizConfigId,
        };
      }
      api[funcName](reqRaram)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.$refs.table.getTableData();
            this.selectedRowKeys = [];
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    handleReset() {
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    getTaskStatusList(val) {
      const taskStatusList = this.getMap().taskStatusList;
      const filterData = taskStatusList.filter(({ value }) => value === val)[0];
      return filterData ? filterData.name : "";
    },
    queryExecuteGroup() {
      api
        .queryAllTeams()
        .then((res) => {
          if (res.code === 0) {
            this.executeGroupMap = res.data;
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    getExecuteGroupList(val) {
      let executeGroupMap = this.executeGroupMap;
      return executeGroupMap[val] || "";
    },
    showDetail(data) {
      let href = `/bc-manage-view/taskDetail?taskId=${data.taskId}`;
      window.open(href, "_blank");
    },
    onChange(date, dateString) {
      this.tableForm.createTimeBeginTime = dateString[0];
      this.tableForm.createTimeEndTime = dateString[1];
    },
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableForm.createTimeBeginTime)) >
        Date.parse(this.DateFormat(this.tableForm.createTimeEndTime))
      ) {
        param["createTimeBeginTime"] = "";
        param["createTimeEndTime"] = "";
      } else if (
        Date.parse(this.DateFormat(this.tableForm.createTimeBeginTime)) ===
        Date.parse(this.DateFormat(this.tableForm.createTimeEndTime))
      ) {
        param["createTimeBeginTime"] += " 00:00:00";
        param["createTimeEndTime"] += " 23:59:59";
      }
      delete param.createTime;
      return param;
    },
    getEbossFdId({ createTime }) {
      const createDate = dayjs(createTime);
      const nowDate = dayjs(new Date());
      const exigence = nowDate.diff(createDate, "hour");
      if (exigence >= 24) {
        return "急";
      } else {
        return "";
      }
    },
  },
};
</script>

<style>
.alert-test {
  text-align: center;
  padding: 5px;
}
</style>
