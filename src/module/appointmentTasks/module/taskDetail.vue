<template>
  <div>
    <a-layout>
      <a-layout-content
        style="background: #f9f9f9; padding: 10px 25px 90px 25px"
      >
        <div>
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">任务信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="任务名称">
              {{ detailInfo.bizName }}
            </a-descriptions-item>
            <a-descriptions-item label="任务类型">
              {{ detailInfo.bizType }}
            </a-descriptions-item>
            <a-descriptions-item label="任务状态">
              {{ detailInfo.taskStatus }}
            </a-descriptions-item>
            <a-descriptions-item label="销户申请时间">
              {{ detailInfo.createTime }}
            </a-descriptions-item>
            <a-descriptions-item label="任务有效期">
              {{ detailInfo.taskDeadline }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ detailInfo.updateTime }}
            </a-descriptions-item>
            <a-descriptions-item label="执行人">
              {{ detailInfo.executeUser }}
            </a-descriptions-item>
            <a-descriptions-item label="执行组织">
              {{ detailInfo.executeGroupName }}
            </a-descriptions-item>
            <a-descriptions-item label="任务描述">
              {{ detailInfo.taskMsg }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-if="taskParam.client_name">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">销户任务信息</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="客户姓名">
                  {{ taskParam.client_name }}
                </a-descriptions-item>
                <a-descriptions-item label="客户号">
                  {{ taskParam.client_id }}
                </a-descriptions-item>
                <a-descriptions-item label="资产等级">
                  {{ taskParam.asset_rank_name }}
                </a-descriptions-item>
                <a-descriptions-item label="利润贡献">
                  {{ taskParam.m_value_name }}
                </a-descriptions-item>
                <a-descriptions-item label="客户所属团队">
                  {{ taskParam.service_relation.service_name }}
                </a-descriptions-item>
                <a-descriptions-item label="服务人员">
                  {{ taskParam.service_relation.service_emp_name }}
                </a-descriptions-item>
                <a-descriptions-item label="外部市值">
                  {{ taskParam.mkt_value }}
                </a-descriptions-item>
                <a-descriptions-item label="开户时间">
                  {{ taskParam.open_date }}
                </a-descriptions-item>
                <a-descriptions-item label="客群标签">
                  {{ taskParam.net_exp_client_group }}
                </a-descriptions-item>
                <a-descriptions-item label="销户原因">
                  {{ JSON.parse(taskParam.cancel_questionnaire).cancel_reason }}
                </a-descriptions-item>
                <a-descriptions-item label="销户内容">
                  {{
                    JSON.parse(taskParam.cancel_questionnaire).cancel_content
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="销户申请时间">
                  {{ detailInfo.createTime }}
                </a-descriptions-item>
                <a-descriptions-item label="任务有效期">
                  {{ detailInfo.taskDeadline }}
                </a-descriptions-item>
                <a-descriptions-item label="任务状态">
                  {{ detailInfo.taskStatus }}
                </a-descriptions-item>
                <a-descriptions-item label="任务执行人">
                  {{ detailInfo.executeUser }}
                </a-descriptions-item>
                <a-descriptions-item label="执行团队">
                  {{ detailInfo.executeGroupName }}
                </a-descriptions-item>
                <a-descriptions-item label="客户归属">
                  {{ taskParam.customer_attribution }}
                </a-descriptions-item>
                <a-descriptions-item label="开户营业部">
                  {{ taskParam.open_branch_no }}
                </a-descriptions-item>
                <a-descriptions-item label="近一年资产峰值">
                  {{ taskParam.max_asset_y }}
                </a-descriptions-item>
                <a-descriptions-item label="当前资产">
                  {{ taskParam.asset_net }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">销户信息</a-col>
                </a-row>
              </div>
              <table
                width="100%"
                class="zh_info_table"
                cellpadding="0"
                cellspacing="0"
              >
                <tr>
                  <th><span>账户类型</span></th>
                  <th><span>账户号码</span></th>
                  <th><span>操作类型</span></th>
                </tr>
                <tr
                  v-for="(item, index) in taskParam.cancel_account_list"
                  :key="index"
                >
                  <td>
                    {{ item.account_type_name }}
                  </td>
                  <td>
                    <span>{{ item.account_code }}</span>
                  </td>
                  <td>
                    <span>{{ getActionType(item.action_type) }}</span>
                  </td>
                </tr>
              </table>
            </div>
          </a-row>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">销户挽留结果</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="客户销户原因">
                  {{ getCancelReason(taskParam.cancel_reason) }}
                </a-descriptions-item>
                <a-descriptions-item label="挽留结果">
                  {{ getCancelConclusion(taskParam.cancel_conclusion) }}
                </a-descriptions-item>
                <a-descriptions-item label="备注小结">
                  {{ taskParam.remark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>
<script>
import api from "../api";

export default {
  name: "taskDetail",
  data() {
    return {
      detailInfo: {
        bizName: "销户挽留",
        bizType: "销户挽留",
        taskStatus: "",
        createTime: "",
        taskDeadline: "",
        updateTime: "",
        executeUser: "",
        executeGroup: "",
        executeGroupName: "",
        taskMsg: "",
        clientName: "",
        serviceName: "",
        openBranchNo: "",
        maxAssetY: "",
        assetNet: "",
      },
      taskParam: {},
      cancelOperationTypeMap: [],
      reasonAccCancelMap: [],
      cancelConclusionMap: {
        0: "挽留失败",
        1: "挽留成功",
        2: "客户取消",
        3: "联系失败，不允许销户（衍生品）",
        4: "联系失败，不允许销户（有深圳持仓）",
        5: "联系失败，允许销户",
        6: "客户临柜销户",
      },
      taskStatusList: [
        { value: "0", name: "未执行" },
        { value: "1", name: "执行中" },
        { value: "2", name: "成功" },
        { value: "3", name: "失败" },
        { value: "4", name: "过期" },
      ], //状态列表
    };
  },
  watch: {},
  computed: {},
  created() {
    this.queryExecuteGroup();
  },
  methods: {
    getTaskDetail() {
      api
        .queryTask({
          taskInfoId: this.$route.query.taskId,
        })
        .then((res) => {
          if (res.code === 0) {
            const { data } = res;
            for (const key in this.detailInfo) {
              for (const k in data) {
                if (key === k) {
                  if (key === "executeGroupName") {
                    this.detailInfo[key] = this.getExecuteGroupList(
                      data.executeGroup
                    );
                    continue;
                  } else if (key === "taskStatus") {
                    this.detailInfo[key] = this.getTaskStatusList(
                      data.taskStatus
                    );
                    continue;
                  }
                  this.detailInfo[key] = data[k];
                }
              }
              this.taskParam = JSON.parse(data.taskParam);
              if (
                this.taskParam?.cancel_account_list?.constructor?.name ===
                "String"
              ) {
                this.taskParam.cancel_account_list = JSON.parse(
                  this.taskParam.cancel_account_list
                );
              }
            }
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    queryExecuteGroup() {
      this.$dict
        .dictContent("wa.common.cancelOperationType")
        .then((data) => {
          this.cancelOperationTypeMap = data;
          return this.$dict.dictContent("bc.common.reasonAccCancel");
        })
        .then((data) => {
          this.reasonAccCancelMap = data;
          return api.queryAllTeams({});
        })
        .then((res) => {
          if (res.code === 0) {
            this.executeGroupMap = res.data;
            this.getTaskDetail();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    getExecuteGroupList(val) {
      let executeGroupMap = this.executeGroupMap;
      return executeGroupMap[val];
    },
    getTaskStatusList(val) {
      const taskStatusList = this.taskStatusList;
      const filterData = taskStatusList.filter(({ value }) => value === val)[0];
      return filterData ? filterData.name : "";
    },
    getActionType(val) {
      if (val === "") return;
      const cancelOperationTypeMap = this.cancelOperationTypeMap;
      const filterList = cancelOperationTypeMap
        .filter(({ dictValue }) => val.includes(dictValue))
        .map(({ dictLabel }) => dictLabel);
      return filterList?.length > 0 ? filterList.join(", ") : "";
    },
    getCancelReason(list = []) {
      if (list?.constructor.name === "String") {
        list = JSON.parse(list);
      }
      // return list.map(({ option_content }) => option_content).join('，')
      if (list.length === 0) return;
      return list
        .map(({ choice, text }) => {
          let filterData = this.reasonAccCancelMap.filter(
            ({ dictValue }) => dictValue === choice
          )[0];
          if (choice === "17") {
            filterData.dictLabel = text;
          }
          return filterData ? filterData.dictLabel : "";
        })
        .join("，");
    },
    getCancelConclusion(key = "") {
      if (key === "") return;
      return this.cancelConclusionMap[key];
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}

::v-deep .ant-layout-content {
  background-color: #fff !important;
}

.pop-title {
  border-left: 5px solid #1890ff;
  padding-left: 10px;
}

.pop_header {
  background-color: #ffffff;
  color: "#282828";
}

.flow_col {
  word-break: break-all;
  min-height: 45px;
  margin: 20px 0px;
  padding-right: 25px;
}

.flow_col span {
  float: left;
  margin-right: 20px;
}

.flow_row .flow_col:not(:last-child):after {
  content: "";
  min-width: 1px;
  min-height: 45px;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}

.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}

.table_info_wrap {
  padding: 0 16px 20px;
}

.zh_info_table {
  width: 100%;
  border: 1px solid #e6e9f0;
}

.zh_info_table th {
  border: 0 none;
  border-bottom: 1px solid #e6e9f0;
  background: #fafafd;
  text-align: left;
  padding: 8px;
  font-weight: normal;
  font-size: 12px;
  line-height: 1.4;
  color: #1f2e4d;
}

.zh_info_table th:first-child {
  text-align: center;
}

.zh_info_table td {
  border: 0 none;
  border-bottom: 1px solid #e6e9f0;
  padding: 8px;
  text-align: left;
  font-weight: normal;
  font-size: 12px;
  line-height: 1.4;
  color: #1f2e4d;
}

.zh_info_table td:first-child {
  text-align: center;
}
</style>
