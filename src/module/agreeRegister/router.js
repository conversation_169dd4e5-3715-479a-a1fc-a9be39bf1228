/*
 * @Author: 梅锋
 * @Date: 2021-07-25 17:49:23
 * @LastEditTime: 2021-07-25 17:55:56
 * @LastEditors: Please set LastEditors
 * @Description: 协议登记管理路由
 * @FilePath: \bc-manage-view\src\module\agreeRegister\router.js
 */
// 注册对应的访问路由
export default [
  {
    path: '/agreeRegister',
    component: (resolve) => require(['./index'], resolve),
    name: 'agreeRegister',
    meta: { title: '协议登记管理' }
  }
];
