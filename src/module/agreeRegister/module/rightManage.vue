<template>
  <div class="page_main">
    <a-form
      class="ant-advanced-search-form"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-col :span="10">
        <a-form-item>
          <a-input
            class="base_tree_input_search"
            v-model="agreeCode"
            placeholder="请输入协议档案编号"
            style="margin-right: 8px;"
          />
        </a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item>
          <a-input
            class="base_tree_input_search"
            v-model="title"
            placeholder="请输入协议标题"
            style="margin-right: 8px;"
          />
        </a-form-item>
      </a-col>
      <a-col :span="4" :style="{ textAlign: 'right', marginTop: '4px' }">
        <a-button type="primary" @click="search()">
          查询
        </a-button>
      </a-col>
    </a-form>
    <a-divider />
    <tk-table
      ref="table"
      :tableData.sync="comlun"
      :intercept-response="intercept_response"
      getMethods="bc-manage-server/bcAgree/page"
      :isSelected="false"
      :isPaging="true"
      :tableFrom="tableForm"
      tableId="agreeId"
      :isTableLoading="false"
    >
      <template slot="operation" slot-scope="data">
        <a-button type="link" @click.stop="add(data)"> 增加 </a-button>
      </template>
    </tk-table>
  </div>
</template>

<script>
export default {
  name: 'right_manage',
  inject: ['api'],
  data() {
    return {
      //table start
      //表格数据
      data: [],
      //表头数据
      comlun: [
        // 循环
        {
          field: 'agreeId',
          label: '协议ID',
          width: 120,
          isSorter: false
        },
        {
          field: 'agreeCode',
          label: '协议档案编号',
          width: 120,
          isSorter: false
        },
        {
          field: 'title',
          label: '协议标题',
          width: 120,
          isSorter: false
        },
        {
          field: 'version',
          label: '版本号',
          width: 120,
          isSorter: false
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          width: 80,
          fixed: 'right'
        }
      ],
      agreeCode: '',
      title: '',
      tableForm: {
        groupId: undefined,
        publishStatus: '1'
      }
    };
  },
  props: {
    groupId: {
      type: Number
    },
    leftInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    regId: {
      type: Number
    }
  },
  watch: {
    groupId: {
      handler(val) {
        // 具有菜单编号时查询对应的权限列表
        console.log(val, 'val');
        this.tableForm['groupId'] = val;
        this.$refs.table && this.$refs.table.getTableData();
        // 初始话选中
      },
      deep: true,
      immediate: true
    }
  },
  created() {},
  components: {},
  methods: {
    search() {
      this.selectedRowKeys = [];
      this.$refs.table.getTableData();
    },
    add(data) {
      this.confirmLoading = true;
      this.api
        .addAgreeRegRel({ regId: this.regId, agreeId: data.agreeId })
        .then(({ msg, code }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`协议关联成功！：${msg}`);
          this.$message.success('协议关联成功!');
          // 通知操作成功
          this.$emit('success');
        });
    }
  }
};
</script>

<style>
.alert-test {
  text-align: center;
  padding: 5px;
}
</style>
