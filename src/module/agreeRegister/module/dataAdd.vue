<template>
  <div>
    <a-modal
      title="增加关联关系"
      :visible="showPop"
      @cancel="closePop"
      width="1000px"
      class="ant_modal_bigtable"
    >
      <template slot="footer">
        <a-button key="back" @click="closePop">
          关闭
        </a-button>
      </template>
      <a-layout>
        <a-layout-content
          class="pop_content"
          style="min-height: calc(710px - 120px);"
        >
          <a-row style="height: 100%;">
            <a-col :span="6" class="base_tree_nav_box" style="height: 100%;">
              <a-layout>
                <a-page-header title="协议分组列表" style="background: #fff;" />
                <a-layout-header>
                  <a-input-search
                    v-model="searchValue"
                    placeholder="请输入协议分组名称"
                    @change="search"
                    style="width: 150px"
                    allowClear
                  />
                </a-layout-header>
                <div class="tree_scroll">
                  <tk-tree
                    class="ant-tree-switcher-no"
                    :treeData="cacheTreeData"
                    :replaceFields="replaceFields"
                    :isIcon="false"
                    :selectedKeys.sync="selectedKeys"
                    @select="onSelect"
                  ></tk-tree>
                </div>
              </a-layout>
            </a-col>
            <a-col :span="18">
              <rightManage
                :groupId="groupId"
                :regId="regId"
                @success="updateList"
              ></rightManage>
            </a-col>
          </a-row>
        </a-layout-content>
      </a-layout>
    </a-modal>
  </div>
</template>

<script>
import rightManage from './rightManage.vue';
export default {
  name: 'data_add',
  inject: ['api'],
  data() {
    return {
      searchValue: '', //左侧菜单搜索值
      groupId: undefined, // 用户选中项菜单
      cacheTreeData: [], //缓存查询结果集
      treeData: [], // 树状列表填充数据
      // 树状列表调整对应的key
      replaceFields: {
        children: 'children',
        title: 'groupName',
        key: 'groupId'
      },
      selectedKeys: [],
      regId: undefined
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false
    },
    requestId: {
      type: Number
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        console.log(val);
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      }
    }
  },
  components: { rightManage },
  watch: {
    isPopShow(n) {
      if (n) {
        this.getTreeData();
      } else {
        this.selectedKeys = []; //清空选中左侧的groupId
      }
    },
    treeData(val) {
      this.cacheTreeData = val.filter((v) =>
        this.searchValue ? v.groupName.includes(this.searchValue) : true
      );
    }
  },
  methods: {
    search() {
      this.cacheTreeData = this.treeData.filter((v) =>
        this.searchValue ? v.groupName.includes(this.searchValue) : true
      );
    },
    onSelect({ groupId }) {
      console.log(groupId, '111');
      // 赋值
      this.groupId = groupId;
      this.regId = this.requestId;
    },
    // 查询当前菜单栏树状列表
    getTreeData() {
      this.api.getBcAgreeGroupList().then(({ code, data }) => {
        if (code != 0) return;
        this.treeData = data.map((item) => this.formatConversion(item));
        this.treeData = data;
      });
    },
    formatConversion(item) {
      item['scopedSlots'] = { icon: 'custom' };
      item['itemIcon'] = 'file';
      return item;
    },
    closePop() {
      this.showPop = false;
    },
    updateList() {
      this.showPop = false;
      this.$emit('success');
    }
  }
};
</script>

<style scoped>
.ant-layout {
  background-color: #fff;
}
.ant-layout-header {
  background-color: #fff;
}
</style>
