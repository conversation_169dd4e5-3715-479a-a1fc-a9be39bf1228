<template>
	<div class="menu-right-limits">
		<!-- 添加修改 -->
		<dataAdd
			:isPopShow.sync="isAddPopShow"
			:requestId="registerId"
			@success="query"
		/>
		<tk-table
			ref="table"
			:tableData.sync="comlun"
			:intercept-response="intercept_response"
			getMethods="bc-manage-server/bcAgreeRegRel/page"
			:isSelected="true"
			:isPaging="true"
			:tableFrom="tableForm"
			:selectedRowKeys.sync="selectedRowKeys"
			tableId="agreeId"
			:isTableLoading="false"
		>
			<div class="table-buttun-area" slot="tableHeader">
				<a-button
					:disabled="!regId"
					type="primary"
					icon="plus"
					@click="add"
				>
					新增
				</a-button>
			</div>
			<template slot="operation" slot-scope="data">
				<a-button type="link" size="small" @click.stop="addStrategy(data)">
					添加策略
				</a-button>
				<a-button type="link" size="small" @click.stop="remove(data)">
					删除
				</a-button>
			</template>
		</tk-table>

		<ContextTransfer
			:isPopShow.sync="isPopShowTransfer"
			:includeKeys="includeKeys"
			:data="transferData"
			@update="update"
		/>
	</div>
</template>

<script>
	import dataAdd from "./dataAdd";
	import ContextTransfer from "./ContextTransfer";
	export default {
		data() {
			return {
				// 表格展示字段
				comlun: [
					// 循环
					{
						field: "agreeId",
						label: "协议ID",
						// width: 80,
						isSorter: false
					},
					{
						field: "title",
						label: "协议标题",
						// width: 200,
						isSorter: false
					},
					{
						field: "strategyName",
						label: "关联策略",
						// width: 200,
						isSorter: false
					},
					{
						field: "strategyNo",
						label: "关联策略编号",
						// width: 200,
						isSorter: false
					},
					{
						field: "createBy",
						label: "创建人",
						// width: 120,
						isSorter: false
					},
					{
						field: "createDate",
                        label: "创建时间",
                        // width: 120,
						isSorter: true
					},
					{
						field: "operation",
						label: "操作",
						align: "center",
						fixed: "right"
					}
				],
				// 查询接口所需字段
				tableForm: {
					agreeCode: "", // 协议档案编号
					title: "" // 协议标题
				},
				operationType: "add",
				// 是否显示弹窗
				isAddPopShow: false,
				isEditPopShow: false,
				//查看弹窗
				isLookPopShow: false,
				selectData: {}, // 所要修改的权限
				selectedRowKeys: [], // 当前用户选中参数
				registerId: undefined,
				isPopShowTransfer: false,
				targetData: {},
				transferData: [],
				includeKeys: []
			};
		},
		inject: ["api"],
		components: { dataAdd, ContextTransfer },
		props: {
			// 数据权限分组编号
			regId: {
				type: Number
			}
		},
		created() {
			// 初始给与值
			this.tableForm["regId"] = this.regId;
		},
		watch: {
			// 用户传入菜单编号
			regId: {
				handler(val) {
					// 具有菜单编号时查询对应的权限列表
					this.tableForm["regId"] = val;
					this.$refs.table.getTableData();
					// 初始话选中
					this.selectedRowKeys = [];
				},
				deep: true
			}
		},
		mounted() {
			this.api.getStrategyList().then(data => {
				this.transferData = data.data.list;
			});
		},
		methods: {
			add() {
				//点击新增按钮
				this.isAddPopShow = true;
				this.registerId = this.regId;
			},
			// 点击修改按钮
			modify(data) {
				this.isEditPopShow = true;
				this.selectData = JSON.parse(JSON.stringify(data));
			},
			// 查看
			look(data) {
				this.isLookPopShow = true;
				this.agreeCode = data.agreeCode;
			},
			// 添加策略
			addStrategy(data) {
				this.targetData = data;
                this.includeKeys = [data.strategyNo];
				this.isPopShowTransfer = true;
			},
			// 添加策略操作成功
			update(arr) {
				this.api
					.setBcAgreeRegRel({
						relId: this.targetData.relId,
						regId: this.targetData.regId,
						agreeId: this.targetData.agreeId,
						updateBy: this.targetData.updateBy,
						strategyNo: arr[0].key
					})
					.then(({ code, msg }) => {
						if (code != 0)
							return this.$message.error(
								`添加关联策略失败：${msg}`
							);
						this.$message.success("添加关联策略成功！");
						this.$refs.table.getTableData();
						this.targetData = [];
					});
			},
			// 操作成功
			query() {
				this.selectedRowKeys = [];
				this.$refs.table.getTableData();
			},
			// 删除对应的权限
			remove(data) {
				this.$confirm({
					title: "删除关联关系",
					content: () => <p>确定删除当前登记协议的关联关系？</p>,
					okText: "确定",
					cancelText: "取消",
					onOk: () => {
						this.api
							.deleteAgreeRegRel({ bcAgreeRegRelId: data.relId })
							.then(({ code, msg }) => {
								if (code != 0)
									return this.$message.error(
										`删除登记协议关联关系失败：${msg}`
									);
								this.$message.success("删除登记协议关联关系成功！");
								this.$refs.table.getTableData();
								this.selectedRowKeys = [];
							});
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped></style>
