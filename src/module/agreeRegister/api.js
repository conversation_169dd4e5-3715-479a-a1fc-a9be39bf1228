// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
	 * 查询协议登记列表
	 * @description:
	 * @param {*}
	 */
	@Parameters(['searchValue'])
	getBcAgreeRegList() {
		return this.services.initGet({
			reqUrl: 'bcAgreeReg/list',
			param: this.param,
		});
	}

	/**
	 * 查询关联策略数据源
	 * @description:
	 * @param {*}
	 */
	@Parameters(['_data'])
	getStrategyList() {
		return this.qcServices.initGet({
			reqUrl: 'qcrule/v2/strategy/page?pageNum=1&pageSize=10000',
            param: this.param,
		});
	}

	/**
	 * 新增登记协议
	 * @param {Object} param
	 * - regName {String} 登记中文名
	 * - remark {String} 备注
	 */
	@Parameters(['regName', 'remark'])
	addAgreeRegister() {
		return this.services.initPost({
			reqUrl: 'bcAgreeReg/add',
			param: this.param,
		});
	}

	/**
	 * 修改登记协议
	 * @param {Object} param
	 * - regId {String}   关联信息id
	 * - regName {String} 登记中文名
	 * - remark {String} 备注
	 */
	@Parameters(['regId', 'regName', 'remark'])
	editAgreeRegister() {
		return this.services.initPost({
			reqUrl: 'bcAgreeReg/edit',
			param: this.param,
		});
	}

	/**
	 * 删除指定协议登记关联关系
	 * @param {Object} param
	 * - bcAgreeRegRelId {String} 协议关联id
	 *
	 */
	@Parameters(['bcAgreeRegRelId'])
	deleteAgreeRegRel() {
		return this.services.initPost({
			reqUrl: 'bcAgreeRegRel/delete',
			param: this.param,
		});
	}
	/**
	 * 删除多个协议登记关联关系
	 * @param {Object} param
	 * - bcAgreeRegRelIds {String} 参数主键ID列表，多个以“，”隔开
	 *
	 */
	@Parameters(['bcAgreeRegRelIds'])
	deletesAgreeRegRel() {
		return this.services.initPost({
			reqUrl: 'bcAgreeRegRel/deletes',
			param: this.param,
		});
	}

	/**
	 * 查询协议分组列表
	 * @description:
	 * @param {*}
	 */
	@Parameters(['searchValue'])
	getBcAgreeGroupList() {
		return this.services.initGet({
			reqUrl: 'bcAgreeGroup/list',
			param: this.param,
		});
	}

	/**
	 * 新增协议登记关联关系
	 * @description:
	 * @param {*}
	 * - regId {Integer} 登记ID
	 * - agreeId {Integer} 协议ID
	 */
	@Parameters(['regId', 'agreeId'])
	addAgreeRegRel() {
		return this.services.initPost({
			reqUrl: 'bcAgreeRegRel/add',
			param: this.param,
		});
	}

	/**
	 * 修改协议登记关联关系
	 * @description:
	 * @param {*}
	 * - relId {Integer} 协议关联关系ID
	 * - regId {Integer} 登记ID
	 * - agreeId {Integer} 协议ID
     * - strategyNo {String} 关联协议编号
	 * - updateBy {String} 修改用户的帐号
	 * @return {*}
	 */
	@Parameters(['relId', 'regId', 'agreeId', 'updateBy','strategyNo'])
	setBcAgreeRegRel() {
		return this.services.initPost({
			reqUrl: 'bcAgreeRegRel/edit',
			param: this.param,
		});
	}
}

export default new api();
