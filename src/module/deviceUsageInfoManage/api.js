// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询营业部列表
   * @description:
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBranchInfo() {
    return this.services.initGet({
      reqUrl: "bcBranchInfo/list",
      param: this.param,
    });
  }

  /**
   * 查询用户列表
   * @description:
   * @param {Object} param
   */
  @Parameters(["_data"])
  getUserList() {
    return this.workServices.initGet({
      reqUrl: "user/list",
      param: this.param,
    });
  }

  /**
   * 查询设备信息列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getPubDeviceInfoList() {
    return this.services.initGet({
      reqUrl: "pubDeviceInfo/list",
      param: this.param
    });
  }

  /**
   * 新增设备使用信息
   * @param {*}
   */
  @Parameters(["_data"])
  addPubDeviceUsageInfo() {
    return this.services.initPost({
      reqUrl: "pubDeviceUsageInfo/add",
      param: this.param,
    });
  }

  /**
   * 修改设备使用信息
   * @param {*}
   */
  @Parameters(["_data"])
  editPubDeviceUsageInfo() {
    return this.services.initPost({
      reqUrl: "pubDeviceUsageInfo/edit",
      param: this.param,
    });
  }

  /**
   * 删除多个设备使用信息
   * @param {Object} param
   * - pubDeviceUsageInfoIds {String} 参数主键ID列表，多个以“，”隔开
   *
   */
  @Parameters(["pubDeviceUsageInfoIds"])
  deletesPubDeviceUsageInfo() {
    return this.services.initPost({
      reqUrl: "pubDeviceUsageInfo/deletes",
      param: this.param,
    });
  }
}

export default new api();
