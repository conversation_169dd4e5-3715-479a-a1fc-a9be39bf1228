<template>
  <a-card title="设备使用信息" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query(1)" @reset="reset">
          <a-form-model-item label="客户经理姓名">
            <a-input
              v-model="tableForm.brokerName"
              placeholder="请输入客户经理姓名"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户经理工号">
            <a-input
              v-model="tableForm.brokerWorkNo"
              placeholder="请输入客户经理工号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="营业部">
            <a-select
              v-model="tableForm.branchNo"
              placeholder="请选择营业部，支持多选"
              allowClear
              show-search
              option-filter-prop="children"
              mode="multiple"
            >
              <a-select-option
                v-for="item in branchList"
                :key="item.branchId"
                :value="item.branchNo"
              >
                {{ item.branchName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="设备号">
            <a-input
              v-model="tableForm.deviceId"
              placeholder="请输入设备号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="使用起止日期">
            <a-range-picker v-model="tableForm.rangeDate" @change="onChange" />
          </a-form-model-item>
          <a-form-model-item label="备注">
            <a-input
              v-model="tableForm.remark"
              placeholder="请输入备注"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="最后一次登录时间">
            <a-date-picker
              v-model="tableForm.updateDate"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择最后一次登录时间"
            />
          </a-form-model-item>
          <a-form-model-item label="状态">
            <a-select v-model="tableForm.state" placeholder="请选择状态">
              <a-select-option
                v-for="(item, index) in ['无效', '有效']"
                :key="index"
                :value="index + ''"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/pubDeviceUsageInfo/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="reqTableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add">
            新增
          </a-button>
          <a-button
            icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove"
          >
            删除
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a @click.stop="modify(data)">修改</a>
        </template>
      </tk-table>
    </div>
    <addOrEdit
      :isPopShow.sync="isPopShow"
      @success="query"
      :parameterData="selectData"
    ></addOrEdit>
  </a-card>
</template>

<script>
import api from "./api";
import addOrEdit from "./module/addOrEdit";

// 默认表单属性
const defaultForm = {
  brokerName: "",
  brokerWorkNo: "",
  branchNo: undefined,
  deviceId: "",
  useBeginDate: "",
  useEndDate: "",
  remark: "",
  updateDate: "",
  state: undefined,
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "employeeNo",
          label: "登录账户",
          isSorter: false,
        },
        {
          field: "brokerName",
          label: "客户经理姓名",
          isSorter: false,
        },
        {
          field: "brokerWorkNo",
          label: "客户经理工号",
          isSorter: false,
        },
        {
          field: "branchNo",
          label: "营业部",
          isSorter: false,
          filter: (item) => this.exchangeBranchNo(item),
        },
        {
          field: "deviceId",
          label: "设备号",
          isSorter: false,
        },
        {
          field: "useBeginDate",
          label: "使用有效起始日期",
          isSorter: false,
        },
        {
          field: "useEndDate",
          label: "使用有效结束日期",
          isSorter: false,
        },
        {
          field: "remark",
          label: "备注",
          isSorter: false,
        },
        {
          field: "updateDate",
          label: "最后一次登录时间",
          isSorter: false,
        },
        {
          field: "state",
          label: "状态",
          isSorter: false,
          filter: (item) => (item === '1' ? "有效" : "无效"),
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      tableForm: {
        brokerName: "",
        brokerWorkNo: "",
        branchNo: undefined,
        deviceId: "",
        useBeginDate: "",
        useEndDate: "",
        remark: "",
        updateDate: "",
        state: undefined,
      },
      reqTableForm: {},
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isPopShow: false, //弹窗是否显示
      selectData: {},
      branchList: [],
      deviceInfoList: []
    };
  },
  provide: { api: api },
  components: {
    addOrEdit,
  },
  created() {
    api.getBranchInfo().then((res) => {
      this.branchList = res.data;
    });
    api.getPubDeviceInfoList().then((res) => {
      this.deviceInfoList = res.data;
    });
  },
  methods: {
    exchangeBranchNo(v) {
      let obj = this.branchList.find((item) => item.branchNo === v);
      return obj ? obj.branchName : v;
    },
    add() {
      this.isPopShow = true;
      this.selectData = {
        branchList: this.branchList,
        deviceInfoList: this.deviceInfoList
      };
    },
    remove() {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deletePre();
        },
      });
    },
    deletePre() {
      let pubDeviceUsageInfoIds = this.selectedRowKeys.join(",");
      api
        .deletesPubDeviceUsageInfo({ pubDeviceUsageInfoIds })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.query();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    modify(data) {
      this.isPopShow = true;
      this.selectData = {
        ...data,
        branchList: this.branchList,
        deviceInfoList: this.deviceInfoList
      };
    },
    onChange(date, dateString) {
      this.tableForm.useBeginDate = dateString[0];
      this.tableForm.useEndDate = dateString[1];
    },
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableForm.useBeginDate)) >
        Date.parse(this.DateFormat(this.tableForm.useEndDate))
      ) {
        param["useBeginDate"] = "";
        param["useEndDate"] = "";
      }
      delete param.rangeDate;
      return param;
    },
    query(type) {
      if (type) {
        this.reqTableForm = JSON.parse(JSON.stringify(this.tableForm));
        this.reqTableForm.branchNo =
          this.reqTableForm.branchNo && this.reqTableForm.branchNo.join(",");
      }
      this.$nextTick(() => {
        this.$refs.table.getTableData();
        this.selectedRowKeys = [];
      });
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
  },
};
</script>

<style lang="scss" scoped></style>
