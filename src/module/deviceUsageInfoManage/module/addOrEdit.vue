<template>
  <a-modal
    :title="`${parameterData.id ? '修改' : '新增'}设备使用信息`"
    class="data-ant-module"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
      :rules="rules"
    >
      <a-form-model-item label="客户经理">
        <a-select
          v-model="form.employeeNo"
          placeholder="请选择客户经理"
          :loading="dataLoading"
          show-search
          option-filter-prop="children"
          :allowClear="true"
          @change="getUserBrokerInfo"
          @search="handleSearch"
          @popupScroll="handlePopupScroll"
          @select="handleSelect"
        >
          <a-select-option
            v-for="item in renderedOptions"
            :value="item.userName"
            :key="item.userId"
          >
            {{ item.realName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="设备号" prop="deviceId">
        <a-select
          v-model="form.deviceId"
          placeholder="请选择设备号"
          show-search
          option-filter-prop="children"
          :allowClear="true"
        >
          <a-select-option
            v-for="item in parameterData.deviceInfoList"
            :value="item.deviceId"
            :key="item.id"
          >
            {{ item.deviceName }}-{{ item.deviceModel }}({{ item.deviceId }})
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="使用起始日期">
        <a-date-picker
          v-model="form.useBeginDate"
          valueFormat="YYYY-MM-DD"
          placeholder="请选择使用起始日期"
          @openChange="openChange"
          style="width:100%"
        />
      </a-form-model-item>
      <a-form-model-item label="使用结束日期">
        <a-date-picker
          v-model="form.useEndDate"
          valueFormat="YYYY-MM-DD"
          placeholder="请选择使用截止日期"
          @openChange="openChange"
          style="width:100%"
        />
      </a-form-model-item>
      <a-form-model-item label="使用备注">
        <a-input v-model="form.remark" placeholder="请输入使用备注"> </a-input>
      </a-form-model-item>
      <a-form-model-item label="状态" prop="state">
        <a-radio-group v-model="form.state">
          <a-radio
            v-for="(item, index) in ['无效', '有效']"
            :key="index"
            :value="index+''"
          >
            {{ item }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  employeeNo: undefined,
  brokerName: '',
  brokerWorkNo: '',
  deviceId: undefined,
  remark: "",
  state: "1",
};
const LOAD_NUM = 30; // 加载条数--可自定义
import { debounce } from "@/utils/parameter.js";

// 注册当前请求对应的上下文请求组
export default {
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        deviceId: [
          { required: true, message: "请选择设备号", trigger: "blur" },
        ],
      },
      confirmLoading: false,
      oriDataList: [], // 原数据列表 -- 从接口获取
      dataLoading: false, // 原数据列表的加载状态 -- 接口的响应状态
      searchVal: "", // 搜索的内容
      filterDataList: [], // 过滤的数据列表 -- 从dataList中过滤出含搜索内容的数据
      renderedOptions: [], // 已渲染的下拉列表
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  mounted() {
    this.getDataList();
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    // 获取数据源，并取数据源的前n条作为下拉列表的可选项
    getDataList() {
      this.dataLoading = true;
      this.api.getUserList().then((res) => {
        this.dataLoading = false;
        this.oriDataList = res.data; // 该接口返回的数据存放在res.data
        this.renderedOptions = this.oriDataList.slice(0, LOAD_NUM);
      });
    },
    // 文本框值变化时触发 -- 从数据源中过滤出含搜索内容的数据，并取过滤结果的前n条作为下拉列表的可选项
    handleSearch(val) {
      this.searchVal = val;
      let filterList = [];
      if (val) {
        filterList = this.oriDataList.filter(
          (item) => item.realName.indexOf(val) > -1
        );
      } else {
        filterList = this.oriDataList;
      }
      this.filterDataList = filterList;
      this.renderedOptions =
        filterList.length < LOAD_NUM
          ? filterList
          : filterList.slice(0, LOAD_NUM);
    },
    // 滚动时触发（防止抖动）
    handlePopupScroll: debounce(function() {
      if (this.searchVal === "") {
        this.loadMoreData(this.oriDataList);
      } else {
        this.loadMoreData(this.filterDataList);
      }
    }, 1000),
    // 加载更多数据到select框
    loadMoreData(dataList) {
      const renderedLen = this.renderedOptions.length; // 已渲染的下拉列表长度
      const totalLen = dataList.length; // 当前数据源的长度
      let addList = [];
      if (renderedLen < totalLen) {
        if (renderedLen + LOAD_NUM <= totalLen) {
          addList = dataList.slice(renderedLen, renderedLen + LOAD_NUM);
        } else {
          addList = dataList.slice(
            renderedLen,
            renderedLen + (totalLen % LOAD_NUM)
          );
        }
        this.renderedOptions = this.renderedOptions.concat(addList);
      }
    },
    // 被选中时调用，参数为选中项的 value (或 key) 值
    handleSelect(val) {
      if (this.searchVal) {
        const selectedArr = this.oriDataList.filter(
          (item) => item.userName === val
        ); // 从数据源中过滤出下拉框选中的值，并返回一个数组
        const restList = this.oriDataList.filter(
          (item) => item.userName !== val
        ); // 从数据源中过滤出其他的值，返回一个数组
        const newList = selectedArr.concat(restList).slice(0, LOAD_NUM); // 将选中的元素放到下拉列表的第一位
        this.renderedOptions = newList; // 更新已渲染的下拉列表
        this.oriDataList = selectedArr.concat(restList); // 更新数据源
        this.searchVal = ""; // 因为触发handleSelect函数时，会自动清空用户输入的内容。因此，searchVal需要重置。
      }
    },
    getUserBrokerInfo(v) {
      let obj = this.oriDataList.find((item) => item.userName === v);
      if (obj) {
        this.form.brokerName = obj.realName;
        this.form.brokerWorkNo = obj.workNum;
      }
    },
    openChange(status) {
      if (status == false) {
        // 切出事件，用于组件缓冲数据
        setTimeout(() => {
          if (this.form.useBeginDate && this.form.useEndDate) {
            if (
              Date.parse(this.DateFormat(this.form.useBeginDate)) >
              Date.parse(this.DateFormat(this.form.useEndDate))
            )
              return this.$message.error("结束日期不应早于开始日期");
          }
        });
      }
    },
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    query() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = this.parameterData;
      let obj = this.oriDataList.find(item=>item.userName===this.form.employeeNo)
      if(obj){
        this.renderedOptions = [obj,...this.renderedOptions]
      }
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        delete param.branchList
        delete param.createDate
        delete param.deviceInfoList
        delete param.rangeDate
        delete param.updateDate
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `设备使用信息${
                this.parameterData.id ? "修改" : "新增"
              }失败：${msg}`
            );
          this.$message.success(
            `设备使用信息${this.parameterData.id ? "修改" : "新增"}成功！`
          );
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        this.parameterData.id
          ? this.api.editPubDeviceUsageInfo(param).then(callback)
          : this.api.addPubDeviceUsageInfo(param).then(callback);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
