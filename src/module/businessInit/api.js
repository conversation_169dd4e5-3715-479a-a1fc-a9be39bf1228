// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
    /**
	 * 获取业务类型名称list
	 * @param {Object} param
	 */
    @Parameters()
    getBcBizTypeList() {
        return this.services.initGet({
            reqUrl:'bcBusiness/list',
            param: this.param
        });
    }

    /**
	 * 获取流程list
	 * @param {Object} param
	 */
    @Parameters(['bizType'])
    getBcFlowNoList() {
        return this.services.initGet({
            reqUrl:'bcBusinessFlow/bfFlowList',
            param: this.param
        });
    }

    /**
	 * 获取应用list
	 * @param {Object} param
	 */
    @Parameters()
    getBcAppList() {
        return this.services.initGet({
            reqUrl:'bcApps/list',
            param: this.param
        });
    }

	/**
	 * 新增业务初始化配置
	 * @param {Object} param
	 */
	@Parameters([
		'appId',
		'appName',
		'bizType',
		'bizName',
		'flowNo',
		'initRuleId',
		'isForceNewFlow',
		'isForceNewForm',
	])
	addBcBusinessFlow() {
		return this.services.initPost({
			reqUrl: 'bcBusinessFlow/add',
			param: this.param,
		});
	}

	/**
	 * 修改业务适当性
	 * @param {Object} param
	 * @returns {Promise}
	 */
	@Parameters([
		'id',
		'appId',
		'appName',
		'bizType',
		'bizName',
		'flowNo',
		'initRuleId',
		'isForceNewFlow',
		'isForceNewForm',
	])
	editBcBusinessFlow() {
		return this.services.initPost({
			reqUrl: 'bcBusinessFlow/edit',
			param: this.param,
		});
	}

	/**
	 * 删除单个业务适当性
	 * @param {Object} param
	 * - bcBusinessEgliId {String} 参数主键ID
	 */
	@Parameters(['ids'])
	deleteBcBusinessFlow() {
		return this.services.initPost({
			reqUrl: 'bcBusinessFlow/deletes',
			param: this.param,
		});
	}

	/**
	 * 查询业务适当性翻页列表
	 * @param {Object} param
	 * - beginTime {String} 开始时间
	 * - endTime {String} 结束时间
	 * - pageSize {integer} 每页数量
	 * - pageNumber {integer} 当前页码
	 * - orderBy {String} 排序字段
	 * - id {String} 唯一标识(UUID)
	 */
	@Parameters([
        'appId',
        'bizType',
        'isDesc',
        'flowNo',
		'beginTime',
		'endTime',
		'pageSize',
		'pageNum',
		'orderBy',
		'id',
	])
	queryBcBusinessFlow() {
		return this.services.initGet({
			reqUrl: 'bcBusinessFlow/page',
			param: this.param,
		});
	}
}

export default new api();
