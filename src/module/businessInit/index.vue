<template>
	<a-card title="业务初始化配置" :bordered="false">
		<div class="searchForm">
			<a-form-model layout="inline" :model="tableForm">
				<tkSelectForm @query="query" @reset="reset">
					<a-form-model-item label="业务类型名称">
						<a-select
							show-search
							optionFilterProp="children"
							v-model="tableForm.bizType"
							:allowClear="true"
							:options="dictMap['bizList']"
                            @keyup.enter.native="query"
							placeholder="请选择业务类型名称"
						>
						</a-select>
					</a-form-model-item>
					<a-form-model-item label="流程名称">
						<a-input
							placeholder="请输入流程名称"
							v-model="tableForm.flowName"
                            @keyup.enter.native="query"
						></a-input>
					</a-form-model-item>
					<a-form-model-item label="应用名称">
						<a-select
							show-search
							v-model="tableForm.appId"
							:allowClear="true"
							:options="dictMap['appList']"
                            @keyup.enter.native="query"
							placeholder="请选择应用名称"
						>
						</a-select>
					</a-form-model-item>
				</tkSelectForm>
			</a-form-model>
		</div>
		<div class="access-table">
			<tk-table
				ref="table"
				:intercept-response="intercept_response"
				:tableData.sync="columns"
				:tableFromFilter="tableFormFilter"
				getMethods="bc-manage-server/bcBusinessFlow/page"
				:isSelected="true"
				:isPaging="true"
				:tableFrom="tableForm"
				:selectedRowKeys.sync="selectedRowKeys"
				tableId="id"
			>
				<div class="table-button-area" slot="tableHeader">
					<a-button icon="plus" type="primary" @click="add">
						新增
					</a-button>
					<a-button
						icon="delete"
						type="danger"
						:disabled="selectedRowKeys.length <= 0"
						@click="remove"
					>
						删除
					</a-button>
				</div>
				<template slot="operation" slot-scope="data">
					<a-button type="link" @click.stop="modify(data)">
						修改
					</a-button>
				</template>
			</tk-table>
		</div>

		<add
			:isPopShow.sync="isAddPopShow"
			@success="query"
			:dictMap="dictMap"
		/>
		<edit
			:isPopShow.sync="isEditPopShow"
			@success="query"
			:parameterData="selectData"
			:dictMap="dictMap"
		/>
	</a-card>
</template>

<script>
	// 引入添加和编辑弹窗
	import add from "./module/add";
	import edit from "./module/edit";

	import api from "./api";
	export default {
		data() {
			return {
				columns: [
					// 循环
					{
						field: "id",
						label: "序号",
						isSorter: true,
						width: 80
					},
					{
						field: "bizType",
						label: "业务类型编号",
						isSorter: true,
						width: 140
					},
					{
						field: "bizName",
						label: "业务类型名称",
						isSorter: true,
						width: 220
					},
					{
						field: "flowNo",
						label: "流程编号",
						isSorter: true,
						width: 120
					},
					{
						field: "flowName",
						label: "流程名称",
						isSorter: true,
						width: 220
					},
					{
						field: "appName",
						label: "应用名称",
						isSorter: true,
						width: 180
					},
					{
						field: "isForceNewForm",
						label: "是否新建表单",
						isSorter: false,
						width: 140,
						filter: item => (item === "1" ? "是" : "否")
					},
					{
						field: "isForceNewFlow",
						label: "是否新建流程",
						isSorter: false,
						width: 140,
						filter: item => (item === "1" ? "是" : "否")
					},
					{
						field: "operation",
						label: "操作",
						align: "center",
						width: 140,
						fixed: "right"
					}
				],
				tableForm: {
					// id: '', // 编号
					bizType: undefined,
					flowName: undefined,
					appId: undefined
				},
				dictMap: {
					bizList: [],
					flowList: [],
					appList: []
				},
				selectData: {},
				selectedRowKeys: [], // Check here to configure the default column
				isAddPopShow: false, //添加弹窗是否显示
				isEditPopShow: false //修改弹窗是否显示
			};
		},
		provide: { api: api },
		components: { add, edit },
		created() {
			this.getDictMap();
		},
		watch: {
			"tableForm.bizType": {
				handler: e => {
					// 获取流程名称list
					api.getBcFlowNoList({ bizType: e }).then(data => {
						this.dictMap["flowList"] = data.data.map(item => ({
							label: item.flowName,
							value: item.flowNo
						}));
					});
				},
				deep: true
			}
		},
		methods: {
			getDictMap() {
				// 获取业务类型list
				api.getBcBizTypeList().then(data => {
					this.dictMap["bizList"] = data.data.map(item => ({
						label: item.bizName,
						value: item.bizType
					}));
				});
				// 获取应用名称list
				api.getBcAppList().then(data => {
					this.dictMap["appList"] = data.data.map(item => ({
						label: item.appName,
						value: item.appId
					}));
				});
			},

			// 搜索框参数
			tableFormFilter(param) {
				return param;
			},

			query() {
				this.$refs.table.getTableData();
				this.selectedRowKeys = [];
			},

			reset() {
				// 没有选中对应的菜单时进行初始化重置
				// this.tableForm = JSON.parse(JSON.stringify(defaultForm));
			},

			// 点击新增按钮
			add() {
				this.isAddPopShow = true;
				this.selectData = {};
			},

			modify(data) {
				this.isEditPopShow = true;
				this.selectData = data;
			},

			remove() {
				if (this.selectedRowKeys.length > 0) {
					this.$confirm({
						title: "业务初始化",
						content: () => <p>确定删除当前业务初始化数据?</p>,
						okText: "确定",
						cancelText: "取消",
						onOk: () => {
							api.deleteBcBusinessFlow({
								ids: this.selectedRowKeys.join(",")
							}).then(({ code, msg }) => {
								if (code != 0)
									return this.$message.error(
										`删除业务初始化失败：${msg}`
									);
								this.$message.success("删除业务定义成功！");
								this.$refs.table.getTableData();
								this.selectedRowKeys = [];
							});
						}
					});
				}
			}
		}
	};
</script>

<style lang="sass" scoped>

</style>