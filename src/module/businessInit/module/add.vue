<template>
	<a-modal
		:title="`新建业务初始化配置`"
		:width="600"
		v-model="showPop"
		ok-text="确认"
		cancel-text="取消"
		@ok="submit"
		@cancel="reset"
		:maskClosable="false"
	>
		<a-form-model
			ref="form"
			:model="form"
			:label-col="{ span: 8 }"
			:wrapper-col="{ span: 14 }"
		>
			<a-form-model-item label="业务流程" prop="bizType">
				<a-select
					show-search
					optionFilterProp="children"
					v-model="form.bizType"
					:allowClear="true"
					:options="dictMap['bizList']"
					placeholder="请选择业务类型名称（编号）"
					@change="onChange"
				>
				</a-select>
				<a-select
					show-search
					optionFilterProp="children"
					v-model="form.flowNo"
					:allowClear="true"
					:options="dictMap['flowList']"
					placeholder="请选择流程名称（编号）"
				>
				</a-select>
			</a-form-model-item>
			<a-form-model-item label="应用名称" prop="appId">
				<a-checkbox-group
					v-model="form.appId"
					:options="dictMap['appList']"
				/>
			</a-form-model-item>
			<a-form-model-item label="是否新建表单" prop="isForceNewForm">
				<a-radio-group
					v-model="form.isForceNewForm"
					:options="newFormList"
				/>
			</a-form-model-item>
			<a-form-model-item label="是否新建流程" prop="isForceNewFlow">
				<a-radio-group
					v-model="form.isForceNewFlow"
					:options="newNodeList"
				/>
			</a-form-model-item>
		</a-form-model>
	</a-modal>
</template>

<script>
	const AppList = [];
	const newFormList = [
		{ label: "是", value: "1" },
		{ label: "否", value: "0" }
	];
	const newNodeList = [
		{ label: "是", value: "1" },
		{ label: "否", value: "0" }
	];
	// 默认表单属性
	const defaultForm = {
		bizType: undefined, // 业务类型编号
		bizName: "", // 业务类型名
		flowNo: undefined, //流程编号
		appId: [],
		appName: "",
		initRuleId: "",
		isForceNewFlow: "0",
		isForceNewForm: "0"
	};

	export default {
		name: "businessInit_add",
		inject: ["api"],
		data() {
			return {
				AppList,
				newFormList,
				newNodeList,
				form: Object.assign({}, defaultForm), //表单数据,
				// rules: {
				// 	bizType: [
				// 		{
				// 			required: true,
				// 			message: "业务类型不能为空",
				// 			trigger: "blur"
				// 		}
				// 	],
				// 	bizRiskLevel: [
				// 		{
				// 			required: true,
				// 			message: "业务风险等级不能为空",
				// 			trigger: "blur"
				// 		}
				// 	]
				// },
				// 异步加载
				confirmLoading: false,
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			// 是否展示添加弹窗
			visible: {
				type: Boolean,
				default: false
			},
			// 修改时传入参数
			parameterData: {
				type: Object,
				default: () => {}
			},
			dictMap: {
				type: Object,
				default: () => {}
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			},
			typeTitle() {
				return "添加";
			}
		},
		watch: {
			isPopShow(n) {
				if (n) {
					this.query();
				} else {
					this.reset();
				}
				this.$nextTick(() => {});
			}
		},
		methods: {
			// 重置对应的表单
			reset() {
				// 重置表单验证属性
				this.$refs.form && this.$refs.form.resetFields();
				this.form = Object.assign({}, defaultForm);
				this.confirmLoading = false;
			},
			onChange(e) {
				this.api.getBcFlowNoList({ bizType: e }).then(data => {
					this.dictMap["flowList"] = data.data.map(item => ({
						label: item.flowName,
						value: item.flowNo
                    }));
                    this.form.flowNo = this.dictMap["flowList"][0] ? this.dictMap["flowList"][0].value : undefined
                });
			},
			query() {
			},
			// 提交数据权限分组创建
			submit() {
				this.$refs.form.validate(valid => {
					if (!valid) return;
					this.confirmLoading = true;
                    let appId = this.form.appId;
					let appName = [];
					this.dictMap["appList"].forEach(e => {
						this.form.appId.forEach(item => {
							if (item === e.value) {
								appName.push(e.label);
							}
						});
					});
					let bizName = this.dictMap["bizList"][
						this.dictMap["bizList"].findIndex(
							item => item.value === this.form.bizType
						)
					].label;
					let param = JSON.parse(
						JSON.stringify({
							flowNo: this.form.flowNo,
							appId:appId.join(','),
							appName:appName.join(','),
							bizType: this.form.bizType,
							bizName,
							isForceNewFlow: this.form.isForceNewFlow,
							isForceNewForm: this.form.isForceNewForm
						})
                    );
					let callback = ({ code, msg }) => {
						this.confirmLoading = false;
						if (code != 0)
							return this.$message.error(
								`新增初始化配置失败：${msg}`
							);
						this.$message.success(`新增初始化配置成功！`);
						// 关闭弹窗
						this.showPop = false;
						// 通知操作成功
						this.$emit("success");
						// 重置表单
						this.reset();
					};
					this.api.addBcBusinessFlow(param).then(callback);
				});
			}
		}
	};
</script>

<style lang="scss" scoped></style>
