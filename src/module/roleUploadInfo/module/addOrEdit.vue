<template>
  <a-modal
    :title="`${parameterData.id ? '修改' : '新增'}法人上传证件类型`"
    class="data-ant-module"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      :rules="rules"
    >
      <a-form-model-item label="档案名称" prop="archivesId">
        <a-select
          v-model="form.archivesId"
          placeholder="请选择档案名称"
          show-search
          option-filter-prop="children"
          :allowClear="true"
        >
          <a-select-option
            v-for="item in parameterData.archivesList"
            :value="item.id"
            :key="item.id"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="业务类型" prop="bizType">
        <a-select
          v-model="form.bizType"
          placeholder="请选择业务类型"
          show-search
          option-filter-prop="children"
          :allowClear="true"
        >
          <a-select-option
            v-for="item in parameterData.businessList"
            :value="item.bizType"
            :key="item.id"
          >
            {{ item.bizName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="证件名称" prop="certificateName">
        <a-input v-model="form.certificateName" placeholder="请输入证件名称">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="是否合伙企业" prop="isEnterprisePartner">
        <a-select
          v-model="form.isEnterprisePartner"
          placeholder="请选择是否合伙企业"
        >
          <a-select-option
            v-for="(item, index) in ['否', '是']"
            :key="index"
            :value="index"
          >
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item
        label="上传类型是否根据法人类型判断"
        prop="legalUploadType"
      >
        <a-select
          v-model="form.legalUploadType"
          placeholder="请选择上传类型是否根据法人类型判断"
        >
          <a-select-option
            v-for="(item, index) in ['否', '是']"
            :key="index"
            :value="index"
          >
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="办理角色" prop="roleType">
        <a-select v-model="form.roleType" placeholder="请选择办理角色">
          <a-select-option
            v-for="(item, index) in ['经办人', '执行事务合伙人（或委派代表）']"
            :key="index"
            :value="index + 1"
          >
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="状态" prop="status">
        <a-radio-group v-model="form.status">
          <a-radio
            v-for="(item, index) in ['无效', '有效']"
            :key="index"
            :value="index"
          >
            {{ item }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="档案信息示例图">
        <a-upload
          name="file"
          list-type="picture-card"
          :multiple="true"
          :data="updataType"
          :file-list="fileList"
          action="/bc-manage-server/file/upload"
          @preview="handlePreview"
          @change="handleChange"
        >
          <div v-if="fileList.length < 1">
            <a-icon type="plus" />
            <div class="ant-upload-text">
              图片上传
            </div>
          </div>
        </a-upload>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  archivesId: undefined,
  bizType: undefined,
  certificateName: "",
  isEnterprisePartner: undefined,
  legalUploadType: undefined,
  roleType: undefined,
  status: 1,
  exampleDiagram: "",
};

// 注册当前请求对应的上下文请求组
export default {
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        archivesId: [
          { required: true, message: "请选择档案名称", trigger: "blur" },
        ],
        bizType: [
          { required: true, message: "请选择业务类型", trigger: "blur" },
        ],
        certificateName: [
          { required: true, message: "证件名称不能为空", trigger: "blur" },
        ],
        isEnterprisePartner: [
          { required: true, message: "请选择是否合伙企业", trigger: "blur" },
        ],
        legalUploadType: [
          {
            required: true,
            message: "请选择上传类型是否根据法人类型判断",
            trigger: "blur",
          },
        ],
        roleType: [
          { required: true, message: "请选择办理角色", trigger: "blur" },
        ],
      },
      confirmLoading: false,
      updataType: {
        type: "image",
      },
      fileList: [],
      previewVisible: false,
      previewImage: "",
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    handlePreview(info) {
      if (info.url) {
        this.previewImage = info.url;
        this.previewVisible = true;
      }
      if (info.response && info.response.data) {
        this.previewImage =
          window.$hvue.customConfig.fileUrl + info.response.data;
        this.previewVisible = true;
      }
    },
    // 选择图片修改
    handleChange(info) {
      let fileList = [...info.fileList];
      fileList = fileList.slice(-2);
      let resImg = info.file.response && info.file.response.data;
      fileList = fileList.map((file) => {
        if (file.response) {
          file.url = window.$hvue.customConfig.fileUrl + file.response.data;
        }
        return file;
      });
      this.fileList = fileList;
      this.form.exampleDiagram = resImg;
    },
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
      this.fileList = [];
    },
    // 用户点击修改进入
    query() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.api
        .getRoleUploadInfo({
          bcRoleUploadInfoId: this.parameterData.id,
        })
        .then((res) => {
          if (res.code != 0) return;
          this.form = res.data;
          this.fileList = this.form.exampleDiagram
            ? [
                {
                  uid: "1",
                  name: "image.png",
                  status: "done",
                  url:
                    window.$hvue.customConfig.fileUrl +
                    this.form.exampleDiagram,
                },
              ]
            : [];
        });
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        if (this.fileList.length === 0) {
          param.exampleDiagram = "";
        }
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `法人上传证件类型${
                this.parameterData.id ? "修改" : "新增"
              }失败：${msg}`
            );
          this.$message.success(
            `法人上传证件类型${this.parameterData.id ? "修改" : "新增"}成功！`
          );
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        this.parameterData.id
          ? this.api.editRoleUploadInfo(param).then(callback)
          : this.api.addRoleUploadInfo(param).then(callback);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
