<template>
  <a-card title="法人上传证件类型列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="档案名称">
            <a-select
              v-model="tableForm.archivesId"
              placeholder="请选择档案名称"
              allowClear
              show-search
              option-filter-prop="children"
            >
              <a-select-option
                v-for="item in archivesList"
                :key="item.id"
                :value="item.archivesId"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="业务类型">
            <a-select
              v-model="tableForm.bizType"
              placeholder="请选择业务类型"
              allowClear
              show-search
              option-filter-prop="children"
            >
              <a-select-option
                v-for="item in businessList"
                :key="item.id"
                :value="item.bizType"
              >
                {{ item.bizName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="证件名称">
            <a-input
              v-model="tableForm.certificateName"
              placeholder="请输入证件名称"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="是否合伙企业">
            <a-select
              v-model="tableForm.isEnterprisePartner"
              placeholder="请选择是否合伙企业"
            >
              <a-select-option
                v-for="(item, index) in ['否', '是']"
                :key="index"
                :value="index"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="上传类型是否根据法人类型判断">
            <a-select
              v-model="tableForm.legalUploadType"
              placeholder="请选择上传类型是否根据法人类型判断"
            >
              <a-select-option
                v-for="(item, index) in ['否', '是']"
                :key="index"
                :value="index"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="办理角色">
            <a-select v-model="tableForm.roleType" placeholder="请选择办理角色">
              <a-select-option
                v-for="(item, index) in [
                  '经办人',
                  '执行事务合伙人（或委派代表）',
                ]"
                :key="index"
                :value="index + 1"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="状态">
            <a-select v-model="tableForm.status" placeholder="请选择状态">
              <a-select-option
                v-for="(item, index) in ['无效', '有效']"
                :key="index"
                :value="index"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcRoleUploadInfo/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add">
            新增
          </a-button>
          <a-button
            icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove"
          >
            删除
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a @click.stop="modify(data)">修改</a>
        </template>
      </tk-table>
    </div>
    <addOrEdit
      :isPopShow.sync="isPopShow"
      @success="query"
      :parameterData="selectData"
    ></addOrEdit>
  </a-card>
</template>

<script>
import api from "./api";
import addOrEdit from "./module/addOrEdit";

// 默认表单属性
const defaultForm = {
  archivesId: undefined,
  bizType: undefined,
  certificateName: "",
  isEnterprisePartner: undefined,
  legalUploadType: undefined,
  roleType: undefined,
  status: undefined
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "archivesId",
          label: "档案名称",
          isSorter: false,
          filter: (item) => this.filterValue("archivesList", item),
        },
        {
          field: "bizType",
          label: "业务类型",
          isSorter: false,
          filter: (item) => this.filterValue("businessList", item),
        },
        {
          field: "certificateName",
          label: "证件名称",
          isSorter: false,
        },
        {
          field: "isEnterprisePartner",
          label: "是否合伙企业",
          isSorter: false,
          filter: (item) => (item === 1 ? "是" : "否"),
        },
        {
          field: "legalUploadType",
          label: "上传类型是否根据法人类型判断",
          isSorter: false,
          filter: (item) => (item === 1 ? "是" : "否"),
        },
        {
          field: "roleType",
          label: "办理角色",
          isSorter: false,
          filter: (item) =>
            item === 1 ? "经办人" : "执行事务合伙人（或委派代表）",
        },
        {
          field: "status",
          label: "状态",
          isSorter: false,
          filter: (item) => (item === 1 ? "有效" : "无效"),
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      tableForm: {
        archivesId: undefined,
        bizType: undefined,
        certificateName: "",
        isEnterprisePartner: undefined,
        legalUploadType: undefined,
        roleType: undefined,
        status: undefined,
      },
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isPopShow: false, //弹窗是否显示
      selectData: {},
      businessList: [],
      archivesList: [],
    };
  },
  provide: { api: api },
  components: {
    addOrEdit,
  },
  created() {
    api.getBcBusinessList().then((res) => {
      this.businessList = res.data;
    });
    api.getBcArchivesList().then((res) => {
      this.archivesList = res.data;
    });
  },
  methods: {
    filterValue(type, value) {
      if (type === "archivesList") {
        let obj = this[type].find((item) => item.id === value);
        return obj ? obj.name : value;
      } else {
        let obj = this[type].find((item) => item.bizType === value);
        return obj ? obj.bizName : value;
      }
    },
    add() {
      this.isPopShow = true;
      this.selectData = {
        businessList: this.businessList,
        archivesList: this.archivesList,
      };
    },
    remove() {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deletePre();
        },
      });
    },
    deletePre() {
      let bcRoleUploadInfoIds = this.selectedRowKeys.join(",");
      api
        .deletesRoleUploadInfo({ bcRoleUploadInfoIds })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.query();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    modify(data) {
      this.isPopShow = true;
      this.selectData = {
        ...data,
        businessList: this.businessList,
        archivesList: this.archivesList,
      };
    },
    tableFormFilter(param) {
      return param;
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
  },
};
</script>

<style lang="scss" scoped></style>
