// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询业务定义列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBcBusinessList() {
    return this.services.initGet({
      reqUrl: "bcBusiness/list",
      param: this.param,
    });
  }

  /**
   * 查询档案信息列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getBcArchivesList() {
    return this.services.initGet({
      reqUrl: "bcArchives/list",
      param: this.param,
    });
  }

  /**
   * 新增法人上传证件类型
   * @param {*}
   */
  @Parameters(["_data"])
  addRoleUploadInfo() {
    return this.services.initPost({
      reqUrl: "bcRoleUploadInfo/add",
      param: this.param,
    });
  }

  /**
   * 修改法人上传证件类型
   * @param {*}
   */
  @Parameters(["_data"])
  editRoleUploadInfo() {
    return this.services.initPost({
      reqUrl: "bcRoleUploadInfo/edit",
      param: this.param,
    });
  }

  /**
   * 删除多个法人上传证件类型
   * @param {Object} param
   * - bcRoleUploadInfoIds {String} 参数主键ID列表，多个以“，”隔开
   *
   */
  @Parameters(["bcRoleUploadInfoIds"])
  deletesRoleUploadInfo() {
    return this.services.initPost({
      reqUrl: "bcRoleUploadInfo/deletes",
      param: this.param,
    });
  }

  /**
   * 查询指定法人上传证件类型
   * @description:
   * @param {Object} param
   * - bcRoleUploadInfoId {String} 参数主键ID
   */
  @Parameters(["bcRoleUploadInfoId"])
  getRoleUploadInfo() {
    return this.services.initGet({
      reqUrl: "bcRoleUploadInfo/query",
      param: this.param,
    });
  }
}

export default new api();
