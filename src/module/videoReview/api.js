// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  @Parameters([
    "busiType",
    "videoTaskId",
    "identityType",
    "identity",
    "state",
    "busiSn",
    "createTime",
    "pageSize",
    "pageNum",
  ])
  videoPage() {
    return this.services.initGet({
      reqUrl: "gjV2/audit/video/page",
      param: this.param,
    });
  }

  @Parameters(["videoTaskId"])
  videoShowDetiail() {
    return this.services.initGet({
      reqUrl: "gjV2/audit/video/showDetail",
      param: this.param,
    });
  }

  /**
   * 查询文件信息
   * @param {Object} param
   */
  @Parameters(["fileId"])
  queryImgfileBytes() {
    return this.services.initGet({
      reqUrl: "gj/download",
      param: this.param,
    });
  }
  F;
}

export default new api();
