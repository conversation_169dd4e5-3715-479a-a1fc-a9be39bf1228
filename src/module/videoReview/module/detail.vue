<template>
  <div>
    <a-layout>
      <a-layout-content
        style="background: #f9f9f9; padding: 10px 25px 90px 25px"
      >
        <div>
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title"
                >业务类型：{{ getBusiTypeList(userInfo.busiType) }}</a-col
              >
            </a-row>
          </div>
        </div>
        <div
          v-if="userInfo?.doubleRecordInfo"
          :style="{ marginTop: 15 + 'px' }"
        >
          <a-descriptions bordered>
            <a-descriptions-item label="双录名称">
              {{ userInfo?.doubleRecordInfo?.doubleRecordName }}
            </a-descriptions-item>
          </a-descriptions>
          <div class="list_header top8">
            <a-row>
              <a-col :span="6" class="pop-title">双录产品列表</a-col>
            </a-row>
          </div>
          <a-table
            :data-source="userInfo?.doubleRecordInfo?.doubleRecordList"
            :columns="columns"
          />
        </div>

        <div v-if="showVideoWitness">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">双向视频</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card :bordered="false" class="self">
                    <video
                      width="600"
                      height="400"
                      controls="controls"
                      :src="videoWitnessSrc"
                    ></video>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div v-if="showSingleVideo">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">单向视频</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card :bordered="false" class="self">
                    <video
                      width="600"
                      height="400"
                      controls="controls"
                      :src="singleVideoSrc"
                    ></video>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div v-if="userInfo?.clientInfo?.show === '1'">
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">客户信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="客户编号">
              {{ userInfo?.clientInfo?.clientId }}
            </a-descriptions-item>
            <a-descriptions-item label="客户姓名">
              {{ userInfo?.clientInfo?.clientName }}
            </a-descriptions-item>
            <a-descriptions-item label="身份证号">
              {{ userInfo?.clientInfo?.idNo }}
            </a-descriptions-item>
            <a-descriptions-item label="性别">
              {{ userInfo?.clientInfo?.gender === "0" ? "男" : "女" }}
            </a-descriptions-item>
            <a-descriptions-item label="生日">
              {{ userInfo?.clientInfo?.idNo?.slice(6, 14) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件开始日期">
              {{ userInfo?.clientInfo?.begindate }}
            </a-descriptions-item>
            <a-descriptions-item label="证件结束日期">
              {{ userInfo?.clientInfo?.enddate }}
            </a-descriptions-item>
            <a-descriptions-item label="证件地址">
              {{ userInfo?.clientInfo?.idAddress }}
            </a-descriptions-item>
            <a-descriptions-item label="证件签发机关">
              {{ userInfo?.clientInfo?.issuedDepart }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-if="userInfo?.incidenceRelationInfo?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">关联关系确认账户</a-col>
                </a-row>
              </div>
              <a-card>
                <p
                  v-for="(
                    { csdcHolderKind, stockAccount, needUploadNoPosition }, i
                  ) in incidenceRelationList"
                  :key="i"
                >
                  {{ csdcHolderKind }} {{ stockAccount }}
                  {{ needUploadNoPosition }}
                </p>
              </a-card>
            </div>
          </a-row>
        </div>
        <div v-if="clientImgArr.length > 0">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">客户业务档案</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card class="self">
                    <viewer class="idcardImg">
                      <div
                        v-for="({ label, imgUrl }, i) in clientImgArr"
                        :key="i"
                        class="idcardImg_context"
                      >
                        <p>{{ label }}</p>
                        <img style="cursor: pointer" :src="imgUrl" />
                      </div>
                    </viewer>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div v-if="userInfo?.clientCounterfile?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">客户柜台档案</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card class="self">
                    <viewer class="idcardImg">
                      <div
                        v-for="(a, i) in userInfo?.clientCounterfile
                          ?.customerImageDownloadVOS"
                        :key="i"
                        class="idcardImg_context"
                      >
                        <p>{{ clientCounterFileMap[a.imageNo] }}</p>
                        <img
                          style="cursor: pointer"
                          :src="`data:image/jpeg;base64,${a.imageData}`"
                        />
                      </div>
                    </viewer>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">视频信息</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="流程ID">
                  {{ userInfo?.videoInfo?.videoTaskId }}
                </a-descriptions-item>
                <a-descriptions-item label="标识类型">
                  {{ getIdentityTypeList(userInfo?.videoInfo?.identityType) }}
                </a-descriptions-item>
                <a-descriptions-item label="标识">
                  {{ userInfo?.videoInfo?.identity }}
                </a-descriptions-item>
                <a-descriptions-item label="状态">
                  {{ getStatusList(userInfo?.videoInfo?.state) }}
                </a-descriptions-item>
                <a-descriptions-item label="客户姓名">
                  {{ userInfo?.clientInfo?.clientName }}
                </a-descriptions-item>
                <a-descriptions-item label="业务流水号">
                  {{ userInfo?.videoInfo?.busiSn }}
                </a-descriptions-item>
                <a-descriptions-item label="创建时间">
                  {{ formatDate(userInfo?.videoInfo?.createTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="更新时间">
                  {{ formatDate(userInfo?.videoInfo?.updateTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="操作员">
                  {{ userInfo?.videoInfo?.operator }}
                </a-descriptions-item>
                <a-descriptions-item label="操作备注">
                  {{ userInfo?.videoInfo?.videoRemark }}
                </a-descriptions-item>
                <a-descriptions-item label="备注">
                  {{ userInfo?.videoInfo?.remark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">审核信息</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="初审状态">
                  {{ getAuditStatusList(userInfo?.auditInfo?.state) }}
                </a-descriptions-item>
                <a-descriptions-item label="初审结论">
                  {{ userInfo?.auditInfo?.videoRemark }}
                </a-descriptions-item>
                <a-descriptions-item label="初审备注">
                  {{ userInfo?.auditInfo?.remark }}
                </a-descriptions-item>
                <a-descriptions-item label="初审人">
                  {{ userInfo?.auditInfo?.operator }}
                </a-descriptions-item>
                <a-descriptions-item label="初审时间">
                  {{ formatDate(userInfo?.auditInfo?.auditTime) }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
        <div v-if="userInfo?.reservationInfo" :style="{ marginTop: 15 + 'px' }">
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">预约单信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="业务类型">
              {{ userInfo?.reservationInfo?.businessType }}
            </a-descriptions-item>
            <a-descriptions-item label="创建人">
              {{ userInfo?.reservationInfo?.opUserId }}
            </a-descriptions-item>
            <a-descriptions-item label="预约时间">
              {{ formatDate(userInfo?.reservationInfo?.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="有效期至">
              {{ formatDate(userInfo?.reservationInfo?.expireTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="预约单编号">
              {{ userInfo?.reservationInfo?.serialNo }}
            </a-descriptions-item>
            <a-descriptions-item label="办理业务">
              {{ userInfo?.reservationInfo?.subBusinessType }}
            </a-descriptions-item>
            <a-descriptions-item label="备注">
              {{ userInfo?.reservationInfo?.remark }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>
<script>
import api from "../api";
import moment from "moment";
export default {
  name: "videoReviewDetail",
  data() {
    return {
      userInfo: {},
      singleVideoSrc: "",
      videoWitnessSrc: "",
      showSingleVideo: false,
      showVideoWitness: false,
      clientImgArr: [],
      processTypeList: [
        { value: "", name: "全部" },
        { value: "30002", name: "补开股东户双向视频" },
        { value: "30112", name: "两融开户线上预约视频" },
        { value: "30111", name: "两融开户视频（APP）" },
        { value: "30059", name: "非现特殊视频" },
        { value: "30075", name: "开立深市衍生品账户" },
        { value: "30070", name: "产品购买双录" },
        { value: "30072", name: "两融开户视频" },
        { value: "30104", name: "关联关系确认双向视频" },
      ], //流程类型列表
      identityTypeList: [
        { value: "1", name: "资金账号" },
        { value: "10", name: "信用资金账号" },
        { value: "11", name: "国金账户" },
        { value: "12", name: "客户号" },
        { value: "13", name: "期权账号" },
        { value: "100", name: "通行证pid" },
        { value: "200", name: "员工域帐号" },
        { value: "2200", name: "设备号" },
      ], //标识类型列表
      statusList: [
        { value: "0", name: "初始化" },
        { value: "1", name: "审核通过" },
        { value: "2", name: "审核驳回" },
        { value: "3", name: "视频中断" },
        { value: "9", name: "取消办理" },
      ],
      auditStatusList: [
        { value: "0", name: "初始化" },
        { value: "1", name: "审核通过" },
        { value: "2", name: "审核驳回" },
        { value: "3", name: "视频中断" },
        { value: "4", name: "取消办理" },
      ],
      clientBusFileMap: {
        1: "身份证照片正面",
        2: "身份证照片反面",
        3: "客户头像",
        4: "视频见证录像",
        5: "辅证图片",
        7: "单向视频",
        8: "客户公安头像",
        9: "净资产证明档案",
        10: "总资产证明档案",
        11: "近三年年均收入证明档案",
        12: "投资经验证明档案",
        13: "无持仓证明",
      },
      clientCounterFileMap: {
        80: "客户头像",
        82: "公安头像",
        "6A": "身份证正面",
        "6B": "身份证反面",
      },
      columns: [
        {
          title: "产品编号",
          dataIndex: "productNo",
          key: "productNo",
        },
        {
          title: "产品代码",
          dataIndex: "productCode",
          key: "productCode",
        },
        {
          title: "产品名称",
          dataIndex: "productName",
          key: "productName",
        },
      ],
    };
  },
  provide: [api],
  computed: {
    incidenceRelationList() {
      let list = [];
      if (this.userInfo?.incidenceRelationInfo?.incidenceRelationList) {
        this.userInfo.incidenceRelationInfo.incidenceRelationList.forEach(
          ({ ...it }) => {
            list.push({
              ...it,
            });
          }
        );
      }
      return list;
    },
  },
  created() {
    this.queryClientDetail();
  },
  methods: {
    // 查询客户资料详情
    queryClientDetail() {
      api
        .videoShowDetiail({
          videoTaskId: this.$route.query.videoTaskId,
        })
        .then((res) => {
          if (res.code == 0) {
            if (res.data?.clientBusinessfile?.length > 0) {
              res.data.clientBusinessfile.forEach((item) => {
                const i = item.archiveType;
                this.queryfileBytes(item.fileId, (imgFileBytes) => {
                  this.clientImgArr.push({
                    i,
                    label: this.clientBusFileMap[i],
                    imgUrl: "data:image/jpeg;base64," + imgFileBytes,
                  });
                  this.clientImgArr.sort((a, b) => a.i - b.i);
                });
              });
            }
            if (res.data.singleVideoId) {
              this.showSingleVideo = true;
              this.queryfileBytes(
                res.data.singleVideoId,
                (singleVideoFileBytes) => {
                  this.singleVideoSrc =
                    "data:video/mp4;base64," + singleVideoFileBytes;
                }
              );
            }
            if (res.data.videoWitnessId) {
              this.showVideoWitness = true;
              this.queryfileBytes(
                res.data.videoWitnessId,
                (videoWintnessFileBytes) => {
                  this.videoWitnessSrc =
                    "data:video/mp4;base64," + videoWintnessFileBytes;
                }
              );
            }
            this.userInfo = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
    },
    queryfileBytes(fileId = "", callback) {
      let fileBytes = "";
      if (fileId === "" || fileId === "null") {
        return callback("");
      }
      api
        .queryImgfileBytes({
          fileId,
        })
        .then((res) => {
          if (res.code == 0) {
            fileBytes = res.data.fileBytes;
          } else {
            fileBytes = "";
          }
          callback && callback(fileBytes);
        });
    },
    formatDate(v) {
      if (!v) return "";
      return moment(new Date(v)).format("YYYY/MM/DD HH:mm:ss");
    },
    getBusiTypeList(val) {
      const filterData = this.processTypeList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getIdentityTypeList(val) {
      const filterData = this.identityTypeList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getStatusList(val) {
      const filterData = this.statusList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getAuditStatusList(val) {
      const filterData = this.auditStatusList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: "#282828";
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}

.idcardImg img {
  width: 335px;
  height: 200px;
  margin: 12px;
}
.idcardImg_context {
  width: 350px;
  display: inline-block;
}
</style>
