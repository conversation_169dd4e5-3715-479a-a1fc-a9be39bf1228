<template>
  <a-card title="视频初审列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="流程类型">
            <a-select
              v-model="tableForm.busiType"
              placeholder="请选择流程类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in processTypeList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="流程流水号">
            <a-input
              v-model="tableForm.videoTaskId"
              placeholder="请输入流程流水号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="标识类型">
            <a-select
              v-model="tableForm.identityType"
              placeholder="请选择标识类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in identityTypeList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="标识">
            <a-input
              v-model="tableForm.identity"
              placeholder="请输入标识"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="状态">
            <a-select
              v-model="tableForm.state"
              placeholder="请选择状态"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in statusList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
           <a-form-model-item label="业务流水号">
            <a-input
              v-model="tableForm.busiSn"
              placeholder="请输入业务流水号"
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/gjV2/audit/video/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="preBizId"
      >
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="look(data)">查看</a-button>
        </template>
      </tk-table>
    </div>
  </a-card>
</template>

<script>
import api from "./api";
import moment from "moment";

// 默认表单api属性
const defaultForm = {};

export default {
  name: "videoReview",
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      processTypeList: [
        { value: "", name: "全部" },
        { value: "30002", name: "补开股东户双向视频" },
        { value: "30112", name: "两融开户线上预约视频" },
        { value: "30111", name: "两融开户视频（APP）" },
        { value: "30059", name: "非现特殊视频" },
        { value: "30075", name: "开立深市衍生品账户" },
        { value: "30070", name: "产品购买双录" },
        { value: "30072", name: "两融开户视频" },
        { value: "30104", name: "关联关系确认双向视频" }
      ], //流程类型列表
      identityTypeList: [
        { value: "", name: "全部" },
        { value: "1", name: "资金账号" },
       /*  { value: "10", name: "信用资金账号" },
        { value: "11", name: "国金账户" }, */
        { value: "12", name: "客户号" },
       /*  { value: "13", name: "期权账号" },
        { value: "100", name: "通行证pid" },
        { value: "200", name: "员工域帐号" },
        { value: "2200", name: "设备号" }, */
      ], //标识类型列表
      statusList: [
        { value: "", name: "全部" },
        { value: "0", name: "初始化" },
        { value: "1", name: "审核通过" },
        { value: "2", name: "审核驳回" },
        { value: "3", name: "视频中断" },
        { value: "9", name: "取消办理" },
      ], //状态列表
      // 表头数据
      columns: [
        {
          field: "busiType",
          label: "流程类型",
          isSorter: false,
          filter: (item) => this.getBusiTypeList(item),
        },
        {
          field: "videoTaskId",
          label: "流程流水号",
          isSorter: false,
        },
        {
          field: "identityType",
          label: "标识类型",
          isSorter: false,
          filter: (item) => this.getIdentityTypeList(item),
        },
        {
          field: "identity",
          label: "标识",
          isSorter: false,
        },
        {
          field: "state",
          label: "状态",
          isSorter: false,
          filter: (item) => this.getStatusList(item),
        },
        {
          field: "busiSn",
          label: "业务流水号",
          isSorter: false,
        },
        {
          field: "remark",
          label: "备注",
          isSorter: false,
        },
        {
          field: "createTime",
          label: "创建时间",
          isSorter: false,
          filter: (v) => this.formatDate(v),
        },
        {
          field: "updateTime",
          label: "更新时间",
          isSorter: false,
          filter: (v) => this.formatDate(v),
        },
        {
          field: "isLocked",
          label: "是否锁定",
          isSorter: false,
          filter: (v) => (v === "1" ? "已被其他用户锁定" : "未被其他用户锁定"),
        },
        {
          field: "lockTime",
          label: "锁定时间",
          isSorter: false,
          filter: (v) => this.formatDate(v),
        },
        {
          field: "lockedUser",
          label: "锁定人",
          isSorter: false,
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          width: 300,
          fixed: "right",
        },
      ],
      tableForm: {
        busiType: "", //流程类型
        videoTaskId: "", //流程流水号
        identityType: "", //标识类型
        identity: "", //标识
        state: "", //状态
        busiSn: "", //业务流水号
      },
      stateList: [
        { value: "0", name: "待处理" },
        { value: "1", name: "审核中" },
        { value: "2", name: "审核驳回" },
        { value: "3", name: "办理完成" },
        { value: "4", name: "已过期" },
        { value: "5", name: "已作废" },
      ], //状态列表
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isAddPopShow: false, //添加弹窗是否显示
      isEditPopShow: false, //修改弹窗是否显示
      //查看弹窗
      isLookPopShow: false,
      selectData: {},
      operationType: "add",
      preBizNameMap: {},
      branchList: [],
    };
  },
  provide() {
    return {
      api: api,
    };
  },
  methods: {
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    // 点击新增按钮
    add() {
      this.isAddPopShow = true;
      this.selectData = {};
    },
    // 点击修改按钮
    modify(data) {
      this.isEditPopShow = true;
      this.selectData = data;
    },
    // 查看
    look(data) {
      let href = `/bc-manage-view/videoReviewDetail?videoTaskId=${data.videoTaskId}`;
      window.open(href, "_blank");
    },
    openChange(status) {
      if (status == false) {
        // 切出事件，用于组件缓冲数据
        setTimeout(() => {
          if (this.tableForm.beginTime && this.tableForm.endTime) {
            if (
              Date.parse(this.DateFormat(this.tableForm.beginTime)) >
              Date.parse(this.DateFormat(this.tableForm.endTime))
            )
              return this.$message.error("结束日期不应早于开始日期");
          }
        });
      }
    },
    // 搜索框参数
    tableFormFilter(param) {
      return param;
    },
    // 删除
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: "预约业务单表",
          content: () => <p>确定删除当前预约业务单表数据?</p>,
          okText: "确定",
          cancelText: "取消",
          onOk: () => {
            api
              .deletesBcPreBiz({ bcPreBizIds: this.selectedRowKeys.join(",") })
              .then(({ code, msg }) => {
                if (code != 0)
                  return this.$message.error(`删除预约业务单表失败：${msg}`);
                this.$message.success("删除预约业务单表成功！");
                this.$refs.table.getTableData();
                this.selectedRowKeys = [];
              });
          },
        });
      }
    },
    getBusiTypeList(val) {
      const filterData = this.processTypeList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getIdentityTypeList(val) {
      const filterData = this.identityTypeList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getStatusList(val) {
      const filterData = this.statusList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    formatDate(v = "") {
     if(!v) return ''
      return moment(new Date(v)).format("YYYY/MM/DD HH:mm:ss");
    },
  },
};
</script>

<style lang="scss" scoped></style>
