// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询业务展示内容列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getContentList() {
    return this.services.initGet({
      reqUrl: "bcContent/list",
      param: this.param,
    });
  }

  /**
   * 分页查询业务展示内容列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getContentPage() {
    return this.services.initGet({
      reqUrl: "bcContent/page",
      param: this.param,
    });
  }
  /**
   * 新增业务展示内容
   * @param {Object} param
   */
  @Parameters(["_data"])
  addContent() {
    return this.services.initPost({
      reqUrl: "bcContent/add",
      param: this.param,
    });
  }
  /**
   * 修改业务展示内容
   * @param {Object} param
   */
  @Parameters(["_data"])
  editContent() {
    return this.services.initPost({
      reqUrl: "bcContent/edit",
      param: this.param,
    });
  }
  /**
   * 删除业务展示内容
   * @param {Object} param
   */
  @Parameters(["_data"])
  deleteContent() {
    return this.services.initPost({
      reqUrl: "bcContent/deletes",
      param: this.param,
    });
  }
}

export default new api();
