<!--  -->
<template>
  <a-card title="业务流程介绍"
    :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline"
        :model="tableFrom">
        <tkSelectForm @query="query"
          @reset="reset">
          <a-form-model-item label="展示标题">
            <a-input placeholder="请输入标题"
              v-model="tableFrom.title"
              :allowClear="true"></a-input>
          </a-form-model-item>
          <a-form-model-item label="内容标识">
            <a-input placeholder="请输入内容标识"
              v-model="tableFrom.contentFlag"
              :allowClear="true"></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table ref="table"
        :intercept-response="intercept_response"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/bcContent/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableFrom"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id">
        <div class="table-button-area"
          slot="tableHeader">
          <a-button icon="plus"
            type="primary"
            @click="add">
            新增
          </a-button>
          <a-button icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove">
            删除
          </a-button>
        </div>
        <template slot="operation"
          slot-scope="data">
          <span>
            <a @click.stop="modify(data)">编辑</a>
          </span>
        </template>
      </tk-table>
    </div>
    <addOrEdit :isPopShow.sync="isPopShow"
      @success="query"
      :parameterData="selectData"></addOrEdit>
  </a-card>
</template>

<script>
import api from './api';
import addOrEdit from './module/addOrEdit';
export default {
  data() {
    return {
      columns: [
        // 循环
        {
          field: 'title',
          label: '展示标题',
          align: 'center',
          isSorter: false,
          width: 100,
        },
        {
          field: 'contentFlag',
          label: '内容标识',
          align: 'center',
          isSorter: false,
          width: 150,
        },
       {
          field: 'state',
          label: '是否生效',
          align: 'center',
          width: 150,
          filter: (item) => (item === 1 ? '是' : '否'),
        },
        {
          field: 'createTime',
          label: '创建时间',
          align: 'center',
          isSorter: true,
          width: 150,
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          width: 150,
          fixed: 'right',
        },
      ],
      tableFrom: {
        contentFlag: '',
        title: '',
      },
      selectData: {},
      selectedRowKeys: [], // Check here to configure the default column

      //新增弹窗
      isPopShow: false, //弹窗是否显示
    };
  },

  provide: { api: api },

  components: { addOrEdit },

  methods: {
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    reset() {
      this.tableFrom.title = '';
      this.tableFrom.contentFlag = '';
      this.$refs.table.getTableData();
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    add() {
      this.isPopShow = true;
      this.selectData = {};
    },
    remove() {
      let _this = this;
      _this.$confirm({
        title: '温馨提示',
        content: `是否确认删除?`,
        okType: 'danger',
        onOk() {
          _this.deletePre();
        },
      });
    },

    deletePre() {
      let _this = this;
      let contentIds = this.selectedRowKeys.join(',');
      api
        .deleteContent({ contentIds })
        .then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.query();
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          _this.$message.error(e.message);
        });
    },

    modify(data) {
      this.isPopShow = true;
      this.selectData = data;
    },

    // 搜索框参数
    tableFormFilter(param) {
      return param;
    },
  },
};
</script>
<style lang='scss' scoped>
</style>