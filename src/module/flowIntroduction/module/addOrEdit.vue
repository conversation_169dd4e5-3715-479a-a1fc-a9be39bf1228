<template>
  <a-modal :title="title"
    class="data-ant-module"
    :width="1000"
    :bodyStyle="{ 'max-height': '450px', 'overflow-y': 'auto' }"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false">
    <a-form-model ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules">
      <a-form-model-item label="标题"
        prop="title">
        <a-input v-model="form.title"
          placeholder="请输入标题"
          allowClear></a-input>
      </a-form-model-item>
      <a-form-model-item label="内容标识"
        prop="contentFlag">
        <a-input v-model="form.contentFlag"
          placeholder="请输入内容标识"
          allowClear></a-input>
      </a-form-model-item>
      <a-form-model-item label="是否生效">
        <a-radio-group v-model="form.state">
          <a-radio :value="0"
            :key="0">不生效</a-radio>
          <a-radio :value="1"
            :key="1">生效</a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="内容"
        prop="content">
        <WEditor5 v-if="showPop"
          ref="editor"
          :content="form.content"
          @getContent="getContent"></WEditor5>
      </a-form-model-item>
    </a-form-model>

  </a-modal>
</template>
<script>
import WEditor5 from '@c/wEditor5/index.vue';
// 默认表单属性
const defaultForm = {
  title: '', // 标题
  content: '', // 内容
  contentFlag: '', // 内容标识
  state: 1, // 状态
};

// 注册当前请求对应的上下文请求组
export default {
  inject: ['api'],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
        contentFlag: [
          { required: true, message: '内容标识不能为空', trigger: 'blur' },
        ],
      },
      confirmLoading: false,
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    WEditor5,
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
    title() {
      return this.parameterData.title ? '修改' : '添加';
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.title) {
        this.modify();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      if (this.$refs.editor) {
        this.$refs.editor.isClear();
      }
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },

    getContent(val) {
      this.form.content = val;
    },

    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = this.parameterData;
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        this.$refs.editor.getEditorContent(); //获取富文本内容
        // this.form.content = encodeURIComponent(this.form.content); //转码操作
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(`业务流程介绍${this.title}失败：${msg}`);
          this.$message.success(`业务流程介绍${this.title}成功！`);
          // 通知操作成功
          this.$emit('success');
          // 关闭弹窗 重置表单
          this.closePop();
        };
        let _this = this;
        if (_this.parameterData.title) {
          _this.api.editContent(param).then(callback);
        } else {
          _this.api.addContent(param).then(callback);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
