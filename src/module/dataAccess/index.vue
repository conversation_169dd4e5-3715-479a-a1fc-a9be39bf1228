<!--
 * @Author: your name
 * @Date: 2021-03-29 23:11:26
 * @LastEditTime: 2021-07-16 09:59:36
 * @LastEditors: liu quan
 * @Description: In User Settings Edit
 * @FilePath: \bus-child-view\src\module\dataAccess\index.vue
-->
<template>
  <a-layout class="menu-content">
    <a-layout-sider class="menu-tree-sider">
      <a-card :bordered="false">
        <template slot="title">
          <h4>数据权限分组列表</h4>
          <a-layout-header style="padding:0px 0px; background: #FFFFFF;">
            <a-form-item prop="searchValue">
              <a-input-search v-model="searchValue" placeholder="请输入分组名称" @change="search" style="width: 150px" allowClear />
            </a-form-item>
            <a-form-item >
              <a-button type="link" icon="plus" @click="treeOperationShow = true,operationType='add'" style="margin-left:10px">新增</a-button>
            </a-form-item>
          </a-layout-header>
        </template>
        <div class="menu-left-tree">
          <div class="menu-list">
            <tkTightTree @select="select" @rightClick="rightClick">
              <tk-tree class="ant-tree-switcher-no" :treeData="cacheTreeData" :replaceFields="replaceFields" :isIcon="true" :selectedKeys.sync="selectedKeys"></tk-tree>
              <a-menu-item key="1" @click="treeOperationShow = true,operationType='add'"> 添加数据权限分组 </a-menu-item>
              <a-menu-item key="2" @click="treeOperationShow = true,operationType='set'" v-show="Object.keys(rightMenu).length > 0"> 修改数据权限分组 </a-menu-item>
              <a-menu-item key="3" @click="riskMenu('remove')" v-show="Object.keys(rightMenu).length > 0"> 删除当前数据权限分组 </a-menu-item>
              <a-menu-item key="4" @click="getTreeData"> 刷新数据权限分组 </a-menu-item>
            </tkTightTree>
          </div>   
          <treeOper :isPopShow.sync="treeOperationShow" @success="getTreeData" :parameterData="rightMenu" :operationType="operationType" />
        </div>
      </a-card>
    </a-layout-sider>
    <a-layout-content class="menu-right-content">
      <a-card title="数据权限列表" :bordered="false">
        <dataTable :roleDataGroupId="roleDataGroupId" />
      </a-card>
    </a-layout-content>
  </a-layout>
</template>

<script>
import treeOper from './module/treeOper';
import dataTable from './module/table'; // 引入右侧表格
import api from './api';

export default {
  name: 'dataAccess',
  components: { dataTable, treeOper },
  provide: { "api": api },
  data() {
    return {
      searchValue: '', //左侧菜单搜索值
      roleDataGroupId: undefined, // 用户选中项菜单
      cacheTreeData: [], //缓存查询结果集
      treeData: [], // 树状列表接口查询数据
      rightMenu: {}, // 右击对象
      // 树状列表调整对应的key
      replaceFields: {
        children: 'children', title: 'roleDataGroupName', key: 'roleDataGroupId'
      },
      selectedKeys: [],
      treeOperationShow: false, // 数据权限分组添加弹窗是否显示
      operationType: "add"
    };
  },
  watch: {
    treeData(val) {
      this.cacheTreeData = val.filter(v=> this.searchValue ? v.roleDataGroupName.includes(this.searchValue) : true)
    }
  },
  created() {
    this.getTreeData();
  },
  methods: {
    search(){
      this.cacheTreeData = this.treeData.filter(v=> this.searchValue ? v.roleDataGroupName.includes(this.searchValue) : true)
    },
    select({ roleDataGroupId }) {
      // 赋值
      this.roleDataGroupId = roleDataGroupId;
    },
    rightClick(data) {
      this.rightMenu = data;
    },
    // 查询当前菜单栏树状列表
    getTreeData() {
      api.getRoleDataGroupList().then(({ code, data }) => {
        if (code != 0) return;
        this.treeData = data.map(item => this.formatConversion(item));
        this.treeData = data;
      })
    },
    formatConversion(item) {
      item["scopedSlots"] = { icon: 'custom' };
      item["itemIcon"] = "file";
      return item;
    },
    // 点击右击菜单
    riskMenu(type) {
      if (type == "remove") {
        let { roleDataGroupName, roleDataGroupId } = this.rightMenu;
        this.$confirm({
          title: '删除菜单',
          content: () => <p>确定删除指定的“{roleDataGroupName}”数据权限分组？</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            api.removeRoleDataGroup({ roleDataGroupId }).then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`删除数据权限分组失败：${msg}`);
              this.$message.success('删除数据权限分组成功！');
              this.getTreeData();
            })
          },
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>