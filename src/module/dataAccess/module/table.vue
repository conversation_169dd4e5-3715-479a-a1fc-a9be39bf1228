<template>
  <div class="menu-right-limits">
    <div class="searchFrom">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query">
          <a-form-model-item label="关键字">
            <a-input v-model="tableForm.searchValue" placeholder="请输入关键字" allowClear />
          </a-form-model-item>
          <a-form-model-item label="生效状态">
            <a-select v-model="tableForm.status">
              <a-select-option value=""> 全选 </a-select-option>
              <a-select-option value="0"> 生效 </a-select-option>
              <a-select-option value="1"> 失效 </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <!-- 添加修改 -->
    <dataForm :isPopShow.sync="isPopShow" :operationType="operationType" :roleDataGroupId="roleDataGroupId" :parameterData="selectData" @success="query" />
    <tk-table ref="table" :tableData.sync="comlun" :intercept-response="intercept_response" getMethods="work-manage-server/roleDataType/page" :isSelected="true" :isPaging="true" :tableFrom="tableForm" :selectedRowKeys.sync="selectedRowKeys" tableId="roleDataTypeId" :isTableLoading="false">
      <div class="table-buttun-area" slot="tableHeader">
        <a-button :disabled="!roleDataGroupId" type="primary" icon="plus" @click="add"> 新增 </a-button>
        <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove"> 删除 </a-button>
      </div>
      <template slot="operation" slot-scope="data">
        <a-button type="link" @click="modify(data)"> 修改 </a-button>
      </template>
    </tk-table>
  </div>
</template>

<script>
import dataForm from './form';

export default {
  data() {
    return {
      // 表格展示字段
      comlun: [
        { field: "roleDataName", label: "类型名称", fixed: "left", isEllipsis: true, isSorter: true },
        { field: "roleDataType", label: "类型标识", isSorter: true },
        { field: "status", label: "是否生效", filter: item => item == '0' ? '生效' : '失效', isSorter: true },
        { field: "createBy", label: "创建者", isSorter: true },
        { field: "createTime", label: "创建时间", filter: item => !item ? '--' : this.DateFormat(item).format("yyyy-MM-dd"), isSorter: true },
        { field: "operation", label: "操作", align: "center" },
      ],
      // 查询接口所需字段
      tableForm: {
        roleDataGroupId: "",
        status: ""
      },
      operationType: 'add',
      // 是否显示弹窗
      isPopShow: false,
      selectData: {}, // 所要修改的权限
      selectedRowKeys: [], // 当前用户选中参数
    };
  },
  inject: ["api"],
  components: { dataForm },
  props: {
    // 数据权限分组编号
    roleDataGroupId: {
      type: String,
      default: undefined
    },
  },
  created() {
    // 初始给与值
    this.tableForm["roleDataGroupId"] = this.roleDataGroupId;
  },
  watch: {
    // 用户传入菜单编号
    roleDataGroupId: {
      handler(val) {
        // 具有菜单编号时查询对应的权限列表
        this.tableForm["roleDataGroupId"] = val;
        this.$refs.table.getTableData()
        // 初始话选中
        this.selectedRowKeys = [];
      },
      deep: true
    },
  },
  methods: {
    add(){ //点击新增按钮
      this.isPopShow = true;
      this.operationType='add';
      this.selectData={}
    },
    // 点击修改按钮
    modify(data) {
      this.isPopShow = true;
      this.operationType = "set";
      this.selectData = JSON.parse(JSON.stringify(data));
    },
    // 操作成功
    query() {
      this.selectedRowKeys = [];
      this.$refs.table.getTableData()
    },
    // 删除对应的权限
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: '删除数据权限分类',
          content: () => <p>确定删除当前勾选的数据权限分类？</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            this.api.removeRoleDataType({ roleDataTypeIds: this.selectedRowKeys.join(",") }).then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`删除数据权限分类失败：${msg}`);
              this.$message.success('删除数据权限分类成功！');
              this.$refs.table.getTableData();
              this.selectedRowKeys = [];
            })
          },
        });
      }
    }
  },
}
</script>

<style lang="scss" scoped>
</style>