<!--
 * @Author: liu quan
 * @Date: 2021-07-08 21:48:16
 * @LastEditors: liu quan
 * @LastEditTime: 2021-07-14 16:33:21
 * @FilePath: \bus-child-view\src\module\dataAccess\module\treeOper.vue
-->
<template>
  <a-modal :title="`${typeTitle}数据权限分组`" v-model="showPop" ok-text="确认" cancel-text="取消" @ok="submit" @cancel="reset"  :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :rules="rules">
      <a-form-model-item label="数据权限分组标记" prop="roleDataGroupKey">
        <a-input v-model="form.roleDataGroupKey" placeholder="请输入数据权限分组标记" />
      </a-form-model-item>
      <a-form-model-item label="数据权限分组名称" prop="roleDataGroupName">
        <a-input v-model="form.roleDataGroupName" placeholder="请输入数据权限分组名称" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>

export default {
  name: 'dataAccess_treeOper',
  inject: ["api"],
  data() {
    return {
      form: {}, // 权限添加表单
      // 表单权限验证
      rules: {
        roleDataGroupKey: [{ required: true, message: '数据权限分组标记不能为空', trigger: 'blur' }],
        roleDataGroupName: [{ required: true, message: '数据权限分组名称不能为空', trigger: 'blur' }],
      },
      // 异步加载
      confirmLoading: false
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: ()=>{}
    },
    // 操作类型
    operationType:  {
      type: String,
      default: "add"
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      }
    },
    typeTitle (){
      return (this.operationType == "add") ? "添加" : "修改";
    }
  },
  watch: {
    isPopShow(n) {
      if (n && this.operationType != 'add' && this.parameterData.roleDataGroupKey) {
        this.modify();
      }else{
        this.reset();
      }
    }
  },
  methods: {
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset()
      this.form = Object.assign({}, this.parameterData);
    },
    // 提交数据权限分组创建
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`数据权限分组${this.typeTitle}失败：${msg}`);
          this.$message.success(`数据权限${this.typeTitle}成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
        }
        this.operationType == "add" ? this.api.addRoleDataGroup(param).then(callback) : this.api.editRoleDataGroup(param).then(callback);
      })
    },
  },
};
</script>
