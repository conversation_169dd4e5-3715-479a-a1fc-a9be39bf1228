<template>
  <a-modal :title="`${typeTitle}数据权限分类`" class="data-ant-module" v-model="showPop" ok-text="确认" cancel-text="取消" @ok="submit" @cancel="reset"  :width="750" :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <a-form-model-item label="类型标识" prop="roleDataType">
        <a-input v-model="form.roleDataType" placeholder="请输入类型标识" />
      </a-form-model-item>
      <a-form-model-item label="类型名称" prop="roleDataName">
        <a-input v-model="form.roleDataName" placeholder="请输入类型名称" />
      </a-form-model-item>
      <a-form-model-item label="是否生效" prop="status">
        <a-switch v-model="form.status" />
      </a-form-model-item>
      <a-form-model-item label="富文本" prop="datasource">
        <tkEditor v-if="editorShow" ref="tinymce" v-model="form.datasource" :height="300" :disabled="false" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import tkEditor from '@c/tinymceEditor'

export default {
  name: 'dataAccess_form',
  inject: ["api"],
  components: { tkEditor },
  data() {
    return {
      // 权限添加表单 - 默认
      form: {
        roleDataType: undefined,
        roleDataName: undefined,
        datasource: '',
        status: true
      },
      // 表单权限验证
      rules: {
        roleDataType: [{ required: true, message: '类型标识不能为空', trigger: 'blur' }],
        roleDataName: [{ required: true, message: '类型名称不能为空', trigger: 'blur' }],
      },
      // 异步加载
      confirmLoading: false,
      editorShow: false
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    roleDataGroupId: String, // 数据权限类型组编号
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: ()=>{}
    },
    // 当前所要做的操作
    operationType: {
      type: String,
      default: "add"
    },
    // 对应的操作表单
    data: {
      type: Object,
      default: () => ({})
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      }
    },
    typeTitle (){
      return (this.operationType == "add") ? "添加" : "修改";
    }
  },
  watch: {
    isPopShow(n) {
      if (n && this.operationType != 'add' && this.parameterData.roleDataType) {
        this.modify();
      }else{
        this.reset();
      }
      this.$nextTick(()=> this.editorShow = n)
      
    }
  },
  methods: {
    // 重置对应的表单
    reset() {
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {
        roleDataType: undefined,
        roleDataName: undefined,
        datasource: '',
        status: true
      }
      this.confirmLoading = false
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset()
      this.form = Object.assign({}, this.parameterData)
      this.form.status = this.form.status == 0 ? true : false;
    },
    // 提交权限创建
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        // 新增权限和新增菜单公用同一接口但是入参不同
        this.operationType == "add" ? param["roleDataGroupId"] = this.roleDataGroupId : '';
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`权限${this.typeTitle}失败：${msg}`);
          this.$message.success(`权限${this.typeTitle}成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
        }
        this.operationType == "add" ? this.api.addRoleDataType(param).then(callback) : this.api.editRoleDataType(param).then(callback);
      })
    },
  },
};
</script>
<style lang="scss" scoped>
</style>