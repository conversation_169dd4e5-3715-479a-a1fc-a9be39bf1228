/*
 * @Author: liu quan
 * @Date: 2021-07-08 21:48:16
 * @LastEditors: liu quan
 * @LastEditTime: 2021-07-15 19:25:25
 * @FilePath: \bus-child-view\src\module\dataAccess\api.js
 */
// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

  /**
   * 查询数据权限列表
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["searchValue"])
  getRoleDataGroupList() {
    return this.services.initGet({ reqUrl: "roleDataGroup/list", param: this.param });
  }

  /**
   * 删除数据权限分组
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleDataGroupId"])
  removeRoleDataGroup() {
    return this.services.initPost({ reqUrl: "roleDataGroup/delete", param: this.param });
  }

  /**
   * 新增数据权限分组
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleDataGroupKey", "roleDataGroupName"])
  addRoleDataGroup() {
    return this.services.initPost({ reqUrl: "roleDataGroup/add", param: this.param });
  }

  /**
   * 修改数据权限分组
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleDataGroupKey", "roleDataGroupName", "roleDataGroupId"])
  editRoleDataGroup() {
    return this.services.initPost({ reqUrl: "roleDataGroup/edit", param: this.param });
  }

  /**
   * 删除数据权限分类
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleDataTypeIds"])
  removeRoleDataType() {
    return this.services.initPost({ reqUrl: "roleDataType/deletes", param: this.param });
  }

  /**
   * 添加数据权限分类
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["datasource", "datasourceFieldLabel", "datasourceFieldValue", "datasourceMode", "roleDataGroupId", "roleDataMode", "roleDataName", "roleDataType", "status"])
  addRoleDataType() {
    return this.services.initPost({ reqUrl: "roleDataType/add", param: this.param });
  }

  /**
   * 修改数据权限分类
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["datasource", "datasourceFieldLabel", "datasourceFieldValue", "datasourceMode", "roleDataGroupId", "roleDataMode", "roleDataName", "roleDataType", "status", "roleDataTypeId"])
  editRoleDataType() {
    return this.services.initPost({ reqUrl: "roleDataType/edit", param: this.param });
  }

}

export default new api();