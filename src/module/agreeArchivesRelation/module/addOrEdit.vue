<template>
  <a-modal
    :title="`${parameterData.id ? '修改' : '新增'}协议档案关联`"
    class="data-ant-module"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="closePop"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
      :rules="rules"
    >
      <a-form-model-item label="协议名称" prop="agreementName">
        <a-input v-model="form.agreementName" placeholder="请输入协议名称">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="档案名称" prop="archivesId">
        <a-select
          v-model="form.archivesId"
          placeholder="请选择档案名称"
          show-search
          option-filter-prop="children"
          :allowClear="true"
        >
          <a-select-option
            v-for="item in parameterData.archivesList"
            :value="item.id"
            :key="item.id"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="业务类型" prop="bizType">
        <a-select
          v-model="form.bizType"
          placeholder="请选择业务类型"
          show-search
          option-filter-prop="children"
          :allowClear="true"
        >
          <a-select-option
            v-for="item in parameterData.businessList"
            :value="item.bizType"
            :key="item.id"
          >
            {{ item.bizName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="协议描述">
        <a-input v-model="form.description" placeholder="请输入协议描述">
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="备注">
        <a-input v-model="form.remark" placeholder="请输入备注"> </a-input>
      </a-form-model-item>
      <a-form-model-item label="状态" prop="status">
        <a-radio-group v-model="form.status">
          <a-radio
            v-for="(item, index) in ['无效', '有效']"
            :key="index"
            :value="index"
          >
            {{ item }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  agreementName: "",
  archivesId: undefined,
  bizType: undefined,
  description: "",
  remark: "",
  status: 1,
};

// 注册当前请求对应的上下文请求组
export default {
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        agreementName: [
          { required: true, message: "协议名称不能为空", trigger: "blur" },
        ],
        archivesId: [
          { required: true, message: "请选择档案名称", trigger: "blur" },
        ],
        bizType: [
          { required: true, message: "请选择业务类型", trigger: "blur" },
        ],
      },
      confirmLoading: false,
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    query() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = this.parameterData;
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(
              `协议档案关联${
                this.parameterData.id ? "修改" : "新增"
              }失败：${msg}`
            );
          this.$message.success(
            `协议档案关联${this.parameterData.id ? "修改" : "新增"}成功！`
          );
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        this.parameterData.id
          ? this.api.editAgreementArchivesRelation(param).then(callback)
          : this.api.addAgreementArchivesRelation(param).then(callback);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
