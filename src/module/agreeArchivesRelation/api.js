// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
   * 查询业务定义列表
   * @param {Object} param
   */
     @Parameters(["_data"])
     getBcBusinessList() {
       return this.services.initGet({
         reqUrl: "bcBusiness/list",
         param: this.param
       });
     }
	
	/**
   * 查询档案信息列表
   * @param {Object} param
   */
     @Parameters(["_data"])
     getBcArchivesList() {
       return this.services.initGet({
         reqUrl: "bcArchives/list",
         param: this.param
       });
     }
	
	/**
	 * 新增协议档案关联
	 * @param {*}
	 */
	@Parameters(['_data'])
	addAgreementArchivesRelation() {
		return this.services.initPost({
			reqUrl: 'bcAgreementArchivesRelation/add',
			param: this.param
		});
	}

	/**
	 * 修改协议档案关联
	 * @param {*}
	 */
   @Parameters(['id','agreementName','archivesId','bizType','description','remark','status'])
   editAgreementArchivesRelation() {
     return this.services.initPost({
       reqUrl: 'bcAgreementArchivesRelation/edit',
       param: this.param
     });
   }

   

	/**
	 * 删除多个协议档案关联
	 * @param {Object} param
	 * - bcAgreementArchivesRelationIds {String} 参数主键ID列表，多个以“，”隔开
	 *
	 */
	@Parameters(['bcAgreementArchivesRelationIds'])
	deletesAgreementArchivesRelation() {
		return this.services.initPost({
			reqUrl: 'bcAgreementArchivesRelation/deletes',
			param: this.param
		});
	}

}

export default new api();
