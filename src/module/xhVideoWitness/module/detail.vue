<template>
  <div>
    <a-layout>
      <a-layout-content
        style="background: #f9f9f9; padding: 10px 25px 90px 25px"
      >
        <div>
          <div class="list_header">
            <a-row>
              <a-col :span="6" class="pop-title">流程明细查看</a-col>
            </a-row>
          </div>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">双向视频</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card :bordered="false" class="self">
                    <video
                      width="600"
                      height="400"
                      controls="controls"
                      :src="videoWitnessSrc"
                    ></video>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">客户上传档案</a-col>
                </a-row>
              </div>
              <a-row>
                <a-col>
                  <a-card class="self">
                    <viewer class="idcardImg">
                      <img
                        v-for="(a, i) in clientFile"
                        :key="i"
                        style="cursor: pointer"
                        :src="a.imgUrl"
                      />
                    </viewer>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-row>
        </div>
        <div>
          <div class="list_header" v-show="userInfo?.clientInfo?.show === '1'">
            <a-row>
              <a-col :span="6" class="pop-title">公安信息</a-col>
            </a-row>
          </div>
          <a-descriptions bordered>
            <a-descriptions-item label="客户姓名">
              {{ userInfo?.clientInfo?.clientName }}
            </a-descriptions-item>
            <a-descriptions-item label="身份证号">
              {{ userInfo?.clientInfo?.idNo }}
            </a-descriptions-item>
            <a-descriptions-item label="性别">
              {{ userInfo?.clientInfo?.gender === "0" ? "男" : "女" }}
            </a-descriptions-item>
            <a-descriptions-item label="生日">
              {{ userInfo?.clientInfo?.idNo?.slice(6, 14) }}
            </a-descriptions-item>
            <a-descriptions-item label="证件开始日期">
              {{ userInfo?.clientInfo?.begindate }}
            </a-descriptions-item>
            <a-descriptions-item label="证件结束日期">
              {{ userInfo?.clientInfo?.enddate }}
            </a-descriptions-item>
            <a-descriptions-item label="证件地址">
              {{ userInfo?.clientInfo?.idAddress }}
            </a-descriptions-item>
            <a-descriptions-item label="证件签发机关">
              {{ userInfo?.clientInfo?.issuedDepart }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-show="userInfo?.ancellationAccountInfo?.show === '1'">
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">销户信息</a-col>
                </a-row>
              </div>
              <a-table
                :dataSource="cancelAccountList"
                :columns="xhInfoColumns"
                :pagination="false"
                bordered
              ></a-table>
              <a-descriptions bordered>
                <a-descriptions-item label="挽留备注">
                  {{ userInfo?.ancellationAccountInfo?.wlRemark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
        <div>
          <a-row :style="{ marginTop: 15 + 'px' }">
            <div class="content-border">
              <div class="list_header">
                <a-row>
                  <a-col :span="6" class="pop-title">流程信息</a-col>
                </a-row>
              </div>
              <a-descriptions bordered>
                <a-descriptions-item label="流程ID">
                  {{ userInfo?.auditInfo?.processId }}
                </a-descriptions-item>
                <a-descriptions-item label="标识类型">
                  {{ getIdentityTypeList(userInfo?.auditInfo?.identityType) }}
                </a-descriptions-item>
                <a-descriptions-item label="标识">
                  {{ userInfo?.auditInfo?.identity }}
                </a-descriptions-item>
                <a-descriptions-item label="状态">
                  {{ getAuditStatusList(userInfo?.auditInfo?.state) }}
                </a-descriptions-item>
                <a-descriptions-item label="上一状态">
                  {{ getAuditStatusList(userInfo?.auditInfo?.lastState) }}
                </a-descriptions-item>
                <a-descriptions-item label="客户姓名">
                  {{ userInfo?.clientInfo?.clientName }}
                </a-descriptions-item>
                <a-descriptions-item label="业务流水号">
                  {{ userInfo?.auditInfo?.busiSn }}
                </a-descriptions-item>
                <a-descriptions-item label="创建时间">
                  {{ formatDate(userInfo?.auditInfo?.createTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="更新时间">
                  {{ formatDate(userInfo?.auditInfo?.updateTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="操作员">
                  {{ userInfo?.auditInfo?.operator }}
                </a-descriptions-item>
                <a-descriptions-item label="操作备注">
                  {{ userInfo?.auditInfo?.videoRemark }}
                </a-descriptions-item>
                <a-descriptions-item label="备注">
                  {{ userInfo?.auditInfo?.remark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>
<script>
import api from "../api";
import moment from "moment";
export default {
  name: "xhVideoWitnessDetail",
  data() {
    return {
      userInfo: {},
      videoWitnessSrc: "",
      clientFile: [],
      processTypeList: [
        { value: "30002", name: "补开股东户" },
        { value: "30059", name: "内部转托管" },
        { value: "30070", name: "产品购买双录" },
        { value: "30072", name: "PC两融开户" },
        { value: "30075", name: "股票期权账户" },
        { value: "30104", name: "关联关系绑定" },
        { value: "30109", name: "账户权限视图" },
        { value: "30111", name: "两融全线上开户" },
        { value: "30112", name: "两融预约开户" },
      ], //流程类型列表
      identityTypeList: [
        { value: "1", name: "资金账号" },
        { value: "10", name: "信用资金账号" },
        { value: "11", name: "国金账户" },
        { value: "12", name: "客户号" },
        { value: "13", name: "期权账号" },
        { value: "100", name: "通行证pid" },
        { value: "200", name: "员工域帐号" },
        { value: "2200", name: "设备号" },
      ], //标识类型列表
      statusList: [
        { value: "0", name: "初始化" },
        { value: "1", name: "审核通过" },
        { value: "2", name: "审核驳回" },
        { value: "3", name: "视频中断" },
        { value: "9", name: "取消办理" },
      ],
      auditStatusList: [
        { value: "", name: "全部" },
        { value: "0", name: "初始化" },
        { value: "1", name: "初审通过" },
        { value: "2", name: "初审不通过" },
        { value: "3", name: "复审通过" },
        { value: "4", name: "复审不通过" },
      ],
      xhInfoColumns: [
        {
          title: "账户类型",
          dataIndex: "accountType",
          key: "accountType",
        },
        {
          title: "账号",
          dataIndex: "accountCode",
          key: "accountCode",
        },
        {
          title: "操作",
          dataIndex: "action",
          key: "action",
        },
        /* {
          title: "销户状态",
          dataIndex: "checkflag",
          key: "checkflag",
        }, */
      ],
    };
  },
  provide: [api],
  computed: {
    cancelAccountList() {
      let list = [];
      if (this.userInfo?.ancellationAccountInfo?.cancellationAccountInfoList) {
        this.userInfo.ancellationAccountInfo.cancellationAccountInfoList.forEach(
          ({ ...it }) => {
            list.push(it);
          }
        );
      }
      return list;
    },
  },
  created() {
    this.queryClientDetail();
  },
  methods: {
    // 查询客户资料详情
    queryClientDetail() {
      api
        .videoShowDetiail({
          processId: this.$route.query.processId,
        })
        .then((res) => {
          if (res.code == 0) {
            if (res.data.videoWitnessId) {
              this.queryfileBytes(
                res.data.videoWitnessId,
                (videoWintnessFileBytes) => {
                  this.videoWitnessSrc =
                    "data:video/mp4;base64," + videoWintnessFileBytes;
                }
              );
            } else {
              this.videoWitnessSrc = "data:video/mp4;base64,";
            }
            if (res.data.clientBusinessfile) {
              res.data.clientBusinessfile.forEach((item) => {
                this.queryfileBytes(item.fileId, (imgFileBytes) => {
                  item.imgUrl = "data:image/jpeg;base64," + imgFileBytes;
                  this.clientFile.push(item);
                });
              });
            }
            this.userInfo = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
    },
    queryfileBytes(fileId = "", callback) {
      let fileBytes = "";
      if (fileId === "" || fileId === "null") {
        return callback("");
      }
      api
        .queryImgfileBytes({
          fileId,
        })
        .then((res) => {
          if (res.code == 0) {
            fileBytes = res.data.fileBytes;
          } else {
            fileBytes = "";
          }
          callback && callback(fileBytes);
        });
    },
    getBusiTypeList(val) {
      const filterData = this.processTypeList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getIdentityTypeList(val) {
      const filterData = this.identityTypeList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getStatusList(val) {
      const filterData = this.statusList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    getAuditStatusList(val) {
      const filterData = this.auditStatusList.filter(
        ({ value }) => value === val
      )[0];
      return filterData ? filterData.name : "";
    },
    formatDate(v = "") {
      if (!v) return "";
      return moment(new Date(v)).format("YYYY/MM/DD HH:mm:ss");
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: "#282828";
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}

.idcardImg img {
  width: 335px;
  height: 200px;
  margin: 12px;
}
</style>
