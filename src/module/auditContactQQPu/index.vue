<template>
    <a-card title="预约QQ视频客户列表" class="management" :bordered="false">
        <div class="searchFrom">
            <a-form-model layout="inline" :model="tableFrom">
                <tkSelectForm @query="success" @reset="reset">
                    <a-form-model-item label="客户姓名">
                        <a-input v-model="tableFrom.clientName" placeholder="请输入客户姓名" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="流水号">
                        <a-input v-model="tableFrom.clientId" placeholder="请输入流水号" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="QQ号码">
                        <a-input v-model="tableFrom.qq" placeholder="请输入QQ号码" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="领取状态">
                        <a-select v-model="tableFrom.islock" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.islock']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="见证状态">
                        <a-select v-model="tableFrom.auditorStatus" placeholder="全部" show-search
                            option-filter-prop="children" allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.auditorStatus']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="预约时间段">
                        <a-select v-model="tableFrom.dealTimeVal" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.dealTimeVal']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="手机号码">
                        <a-input v-model="tableFrom.mobileTel" placeholder="请输入手机号码" allowClear />
                    </a-form-model-item>
                    <!-- <a-form-model-item label="视频业务类型">
                        <a-select v-model="tableFrom.bussType" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.bussType']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item> -->

                    <a-form-model-item label="见证坐席">
                        <a-input v-model="tableFrom.agentName" placeholder="请输入见证坐席" allowClear />
                    </a-form-model-item>
                </tkSelectForm>
            </a-form-model>
        </div>
        <div class="access-table">
            <tk-table ref="table" :tableData.sync="comlun"
                getMethods="bc-manage-server/gjKhHis/margin/migration/bespeak/query"
                :intercept-response="intercept_response" :isPaging="true" :tableFromFilter="tableFormFilter"
                :tableFrom="tableFrom" tableId="clientId">
                <template slot="operation" slot-scope="data">
                    <a class="action-btn" @click="showDetail(data)"> 查看详情 </a>
                </template>
            </tk-table>

        </div>
    </a-card>
</template>

<script>

export default {
    name: 'auditRecords',
    data() {
        return {
            comlun: [
                { field: 'clientId', label: '流水号', width: 100 },
                {
                    field: 'clientName', label: ' 客户姓名', width: 100,
                },
                {
                    field: 'idNo', label: '证件号码', width: 150,
                },
                {
                    field: 'enWitnessType', label: '视频业务类型', width: 100,
                    filter: item => this.getDictText('bc.common.bussType', item),
                },

                {
                    field: 'witnessFlag', label: '见证状态', width: 100,
                    filter: item => this.getDictText('bc.common.auditorStatus', item),
                },

                {
                    field: 'agentName', label: ' 见证人', width: 100,
                },
                {
                    field: 'dealTimeVal', label: '预约时间段', width: 150,
                    // filter: item => this.getDictText('bc.common.dealTimeVal', item),
                },
                {
                    field: 'preengageDate', label: ' 预约提交时间', width: 150,
                },
                {
                    field: 'islock', label: '领取状态', width: 100,
                    filter: item => this.getDictText('bc.common.islock', item),
                },
                { field: 'operation', label: '操作', align: 'center', fixed: 'right', width: 100 }

            ],
            dictMap: {
                'bc.common.islock': [
                    { value: 0, label: '未领取' },
                    { value: 1, label: '已领取' },
                ],
                'bc.common.auditorStatus': [
                    { value: 0, label: '不通过' },
                    { value: 1, label: '通过' },
                    { value: 4, label: '待见证' },
                ],
                'bc.common.bussType': [
                    { value: '0', label: '新开证券账户' },
                    { value: '1', label: '补开证券账户' },
                    { value: '2', label: '融资融券开户' },
                    { value: '3', label: '创业板新开' },
                ],
                'bc.common.dealTimeVal': [
                    { value: '09:00-12:00', label: '09:00-12:00' },
                    { value: '12:00-14:00', label: '12:00-14:00' },
                    { value: '14:00-17:00', label: '14:00-17:00' },
                    { value: '17:00-19:00', label: '17:00-19:00' },
                    { value: '19:00-22:00', label: '19:00-22:00' },
                ],
            },
            tableFrom: {
                bussType: '0',
            },
        }
    },
    methods: {
        getDictText(key, value) {
            let results = this.dictMap[key] || [];
            results = results.filter((item) => {
                return item.value == value;
            });
            return (results && results.length && results[0].label) || '';
        },
        tableFormFilter(param) {

            return param;
        },
        success() {
            this.$refs.table.getTableData();
        },
        // 重置
        reset() {
            this.tableFrom = {};
        },
        // 查看详情
        showDetail(data) {
            console.log('data', data)
            // const params = new URLSearchParams(window.location.search);
            const datas = JSON.stringify(data);
            let href = `/bc-manage-view/auditContactHistory/DetailInfo?processId=${data.clientId}&details=${datas}`
            window.open(href, '_blank')
        },
    }
}
</script>

