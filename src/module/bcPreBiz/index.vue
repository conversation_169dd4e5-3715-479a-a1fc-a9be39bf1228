<!--
 * Generate by tech-manage-server-v1.0.0#work-code-generator-v1.0.2
 * @Author: weizp
 * @Date: 2023-10-20 14:21:02
 * @LastEditTime: 2023-10-20 14:21:02
 * @LastEditors: weizp
 * @Description: 预约业务单表首页
 * @FilePath: \bcPreBiz\src\module\bcPreBiz\index.vue
-->
<template>
  <a-card title="预约业务单表列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm
          @query="query"
          @reset="reset"
          layoutType="flex"
          :defaultShowRow="2"
        >
          <a-form-model-item label="资金账号">
            <a-input
              v-model="tableForm.fundAccount"
              placeholder="请输入资金账号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户姓名">
            <a-input
              v-model="tableForm.clientName"
              placeholder="请输入客户姓名"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="业务类型">
            <a-select
              v-model="tableForm.preBizType"
              placeholder="请选择业务类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in preBizNameMap"
                :value="v.dictValue"
                :key="i"
              >
                {{ v.dictLabel }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="营业部">
            <a-input
              v-model="tableForm.branchNo"
              placeholder="请输入营业部"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="见证人">
            <a-input
              v-model="tableForm.witnesstor"
              placeholder="请输入见证人"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="审核人">
            <a-input
              v-model="tableForm.reviewer"
              placeholder="请输入审核人"
            ></a-input>
          </a-form-model-item>
          <!--          <a-form-model-item label="渠道">
            <a-input
                v-model="tableForm.opStaion"
                placeholder="请输入渠道"
            ></a-input>
          </a-form-model-item>-->
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/bcPreBiz/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="preBizId"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add"> 新增</a-button>
          <!--          <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove"> 删除</a-button>-->
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="look(data)"> 查看</a-button>
          <a-button
            type="link"
            v-show="['0'].includes(data.status) && data.preBizType !== '9'"
            @click.stop="deleteItem(data)"
          >
            作废
          </a-button>
        </template>
      </tk-table>
      <add
        :isPopShow.sync="isAddPopShow"
        @success="query"
        :parameterData="selectData"
      />
      <edit
        :isPopShow.sync="isEditPopShow"
        @success="query"
        :parameterData="selectData"
      />
      <LookComponent
        :isPopShow.sync="isLookPopShow"
        :parameterData="selectData"
      />
    </div>
  </a-card>
</template>

<script>
// 引入添加和编辑弹窗
import add from "./module/add";
import edit from "./module/edit";
// 引入查看弹窗
import LookComponent from "./module/detail";
import api from "./api";

// 默认表单api属性
const defaultForm = {};

export default {
  name: "bcPreBiz",
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        /*{
          field: "preBizId",
          label: "预约单ID",
          isSorter: false,
        },*/
        {
          field: "fundAccount",
          label: "资金账号",
          isSorter: false,
        },
        {
          field: "clientName",
          label: "客户姓名",
          isSorter: false,
        },
        /*{
          field: "bizType",
          label: "受理业务类型",
          isSorter: false,
        },*/
        {
          field: "preBizType",
          label: "预约业务类型",
          isSorter: false,
          filter: (val) => this.getPreBizNameMap(val),
        },
        /*{
          field: 'preBizName',
          label: '预约业务类型',
          isSorter: false,
        },*/
        {
          field: "status",
          label: "预约状态",
          isSorter: false,
          filter: (val) => this.getStatusList(val),
        },
        /* {
           field: "flowInsId",
           label: "受理单ID",
           isSorter: false,
         },*/
        {
          field: "createTime",
          label: "创建时间",
          isSorter: false,
          filter: (item) =>
            !item ? "--" : this.DateFormat(item).format("yyyy-MM-dd hh:mm:ss"),
        },
        {
          field: "operator",
          label: "创建人员",
          isSorter: false,
        },
        {
          field: "expireTime",
          label: "过期时间",
          isSorter: false,
          filter: (item) =>
            !item ? "--" : this.DateFormat(item).format("yyyy-MM-dd hh:mm:ss"),
        },
        {
          field: "witnessTime",
          label: "见证时间",
          isSorter: false,
          filter: (item) =>
            !item ? "--" : this.DateFormat(item).format("yyyy-MM-dd hh:mm:ss"),
        },
        {
          field: "witnesstor",
          label: "见证人",
          isSorter: false,
        },
        {
          field: "reviewTime",
          label: "最近审核时间",
          isSorter: false,
          filter: (item) =>
            !item ? "--" : this.DateFormat(item).format("yyyy-MM-dd hh:mm:ss"),
        },
        {
          field: "reviewer",
          label: "最新审核人",
          isSorter: false,
        },
        /*{
          field: 'opStaion',
          label: '渠道终端',
          isSorter: false,
        },*/
        {
          field: "branchNo",
          label: "营业部",
          isSorter: false,
          filter: (val) => this.getBranchName(val),
        },
        /*{
          field: "updateTime",
          label: "修改时间",
          isSorter: false,
          filter: item => !item ? '--' : this.DateFormat(item).format("yyyy-MM-dd"),
        },*/
        {
          field: "operation",
          label: "操作",
          align: "center",
          width: 300,
          fixed: "right",
        },
      ],
      tableForm: {
        fundAccount: "", //资金账号
        clientName: "", //客户姓名
        preBizName: "", //预约业务类型
        status: "", //预约状态 0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期 5已作废
        createTime: "", //创建预约单时间
        operator: "", //办理人
        expireTime: "", //过期时间
        witnessTime: "", //见证时间
        witnesstor: "", //见证人
        reviewTime: "", //最近审核时间
        reviewer: "", //最新审核人
        opStaion: "", //渠道终端
        branchNo: "", //营业部
      },
      stateList: [
        { value: "0", name: "待处理" },
        { value: "1", name: "审核中" },
        { value: "2", name: "审核驳回" },
        { value: "3", name: "办理完成" },
        { value: "4", name: "已过期" },
        { value: "5", name: "已作废" },
      ], //状态列表
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isAddPopShow: false, //添加弹窗是否显示
      isEditPopShow: false, //修改弹窗是否显示
      //查看弹窗
      isLookPopShow: false,
      selectData: {},
      operationType: "add",
      preBizNameMap: {},
      branchList: [],
    };
  },
  provide() {
    return {
      api: api,
      getPreBizNameMap: this.returnPreBizNameMap,
      getBranchList: this.returnBranchList,
    };
  },
  created() {
    this.getMap();
  },
  components: { add, edit, LookComponent },
  methods: {
    getMap() {
      this.$dict
        .dictContent("bc.common.preBizType")
        .then((data) => {
          // this.preBizNameMap = data.filter(({ dictValue }) => dictValue !== '9')
          this.preBizNameMap = data;
          return this.$dict.dictContent("wa.common.branchList");
        })
        .then((data) => {
          // this.preBizNameMap = data.filter(({ dictValue }) => dictValue !== '9')
          this.branchList = data;
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    getStatusList(val) {
      const stateList = this.stateList;
      const filterData = stateList.filter(({ value }) => value === val)[0];
      return filterData ? filterData.name : "";
    },
    getPreBizNameMap(val) {
      const preBizNameMap = this.preBizNameMap;
      const filterData = preBizNameMap.filter(
        ({ dictValue }) => dictValue === val
      )[0];
      return filterData ? filterData.dictLabel : "";
    },
    getBranchName(val) {
      const branchList = this.branchList;
      const filterData = branchList.filter(
        ({ dictValue }) => dictValue === val
      )[0];
      return filterData ? filterData.dictLabel : "";
    },
    returnPreBizNameMap() {
      return this.preBizNameMap;
    },
    returnBranchList() {
      return this.branchList;
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    // 点击新增按钮
    add() {
      this.isAddPopShow = true;
      this.selectData = {};
    },
    // 点击修改按钮
    modify(data) {
      this.isEditPopShow = true;
      this.selectData = data;
    },
    // 查看
    look(data) {
      this.isLookPopShow = true;
      this.selectData = data;
    },
    openChange(status) {
      if (status == false) {
        // 切出事件，用于组件缓冲数据
        setTimeout(() => {
          if (this.tableForm.beginTime && this.tableForm.endTime) {
            if (
              Date.parse(this.DateFormat(this.tableForm.beginTime)) >
              Date.parse(this.DateFormat(this.tableForm.endTime))
            )
              return this.$message.error("结束日期不应早于开始日期");
          }
        });
      }
    },
    // 搜索框参数
    tableFormFilter(param) {
      return param;
    },
    deleteItem({ preBizId }) {
      this.$confirm({
        title: "预约业务单表",
        content: () => <p>确定作废当前预约业务单表数据?</p>,
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          api.editBcPreBiz({ status: "5", preBizId }).then(({ code, msg }) => {
            if (code != 0)
              return this.$message.error(`作废预约业务单表失败：${msg}`);
            this.$message.success("作废预约业务单表成功！");
            this.$refs.table.getTableData();
            this.selectedRowKeys = [];
          });
        },
      });
    },
    // 删除
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: "预约业务单表",
          content: () => <p>确定删除当前预约业务单表数据?</p>,
          okText: "确定",
          cancelText: "取消",
          onOk: () => {
            api
              .deletesBcPreBiz({ bcPreBizIds: this.selectedRowKeys.join(",") })
              .then(({ code, msg }) => {
                if (code != 0)
                  return this.$message.error(`删除预约业务单表失败：${msg}`);
                this.$message.success("删除预约业务单表成功！");
                this.$refs.table.getTableData();
                this.selectedRowKeys = [];
              });
          },
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
