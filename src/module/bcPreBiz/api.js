// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

    /**
    * 新增预约业务单表
    * @param {Object} param
    * - preBizId {Integer} 预约单ID
    * - fundAccount {String} 资金账号
    * - clientName {String} 客户姓名
    * - bizType {String} 受理业务类型
    * - preBizType {String} 预约业务类型
    * - preBizName {String} 预约业务类型名称
    * - status {String} 预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期
    * - flowInsId {String} 受理单ID
    * - expireTime {Date} 过期时间
    * - operator {String} 办理人
    * - witnesstor {String} 见证人
    * - witnessTime {Date} 见证时间
    * - reviewer {String} 最新审核人
    * - reviewTime {Date} 最近审核时间
    * - opStaion {String} 渠道终端
    * - branchNo {String} 营业部
    * - createTime {Date} 创建时间
    * - updateTime {Date} 修改时间
    */
    @Parameters(["preBizId", "fundAccount", "clientName", "bizType", "preBizType", "preBizName", "status", "flowInsId", "expireTime", "operator", "witnesstor", "witnessTime", "reviewer", "reviewTime", "opStaion", "branchNo", "createTime", "updateTime", "remark"])
    addBcPreBiz() {
      return this.services.initPost({ reqUrl:"bcPreBiz/add", param: this.param});
    }

    /**
    * 修改预约业务单表
    * @param {Object} param
    * - preBizId {Integer} 预约单ID
    * - fundAccount {String} 资金账号
    * - clientName {String} 客户姓名
    * - bizType {String} 受理业务类型
    * - preBizType {String} 预约业务类型
    * - preBizName {String} 预约业务类型名称
    * - status {String} 预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期
    * - flowInsId {String} 受理单ID
    * - expireTime {Date} 过期时间
    * - operator {String} 办理人
    * - witnesstor {String} 见证人
    * - witnessTime {Date} 见证时间
    * - reviewer {String} 最新审核人
    * - reviewTime {Date} 最近审核时间
    * - opStaion {String} 渠道终端
    * - branchNo {String} 营业部
    * - createTime {Date} 创建时间
    * - updateTime {Date} 修改时间
    * @returns {Promise}
    */
    @Parameters(["preBizId", "fundAccount", "clientName", "bizType", "preBizType", "preBizName", "status", "flowInsId", "expireTime", "operator", "witnesstor", "witnessTime", "reviewer", "reviewTime", "opStaion", "branchNo", "createTime", "updateTime"])
    editBcPreBiz() {
     return this.services.initPost({ reqUrl:"bcPreBiz/edit", param: this.param});
    }

    /**
    * 删除单个预约业务单表
    * @param {Object} param
    * - bcPreBizId {String} 参数主键ID
    */
    @Parameters(["bcPreBizId"])
    deleteBcPreBiz() {
      return this.services.initPost({ reqUrl:"bcPreBiz/delete", param: this.param});
    }

    /**
    * 删除多个预约业务单表
    * @param {Object} param
    * - bcPreBizIds {String} 参数主键ID列表，多个以“，”隔开
    *
    */
    @Parameters(["bcPreBizIds"])
    deletesBcPreBiz() {
      return this.services.initPost({ reqUrl:"bcPreBiz/deletes", param: this.param});
    }

    /**
    * 查询预约业务单表翻页列表
    * @param {Object} param
    * - beginTime {String} 开始时间
    * - endTime {String} 结束时间
    * - pageSize {integer} 每页数量
    * - pageNumber {integer} 当前页码
    * - orderBy {String} 排序字段
    */
    @Parameters(["beginTime", "endTime", "pageSize", "pageNum", "orderBy", ])
    queryBcPreBizPage() {
      return this.services.initGet({ reqUrl: "bcPreBiz/page", param: this.param });
    }

    /**
    * 查询指定预约业务单表详情
    * @param {Object} param
    * - bcPreBizId {String} 参数主键ID
    */
    @Parameters(["bcPreBizId"])
    queryBcPreBizDetail() {
      return this.services.initGet({ reqUrl: "bcPreBiz/query", param: this.param});
    }

  /**
   * 根据资金账号查询客户姓名
   * @param {Object} param
   * - bcPreBizId {String} 参数主键ID
   */
  @Parameters(["fundAccount"])
  clientInfoQry() {
    return this.services.initGet({ reqUrl: "bcPreBiz/clientInfoQry", param: this.param});
  }

}

export default new api();
