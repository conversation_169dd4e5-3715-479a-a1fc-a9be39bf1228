<!--
 * Generate by tech-manage-server-v1.0.0#work-code-generator-v1.0.2
 * @Author: weizp
 * @Date: 2023-10-20 14:21:02
 * @LastEditTime: 2023-10-20 14:21:02
 * @LastEditors: weizp
 * @Description: 预约业务单表首页
 * @FilePath: \bcPreBiz\src\module\bcPreBiz\index.vue
-->
<template>
  <a-modal
    :title="`查看预约业务单表`"
    :width="500"
    v-model="showPop"
    :maskClosable="false"
  >
    <a-descriptions bordered :column="1">
      <a-descriptions-item label="预约单ID" prop="preBizId">
        {{ form.preBizId }}
      </a-descriptions-item>
      <a-descriptions-item label="资金账号" prop="fundAccount">
        {{ form.fundAccount }}
      </a-descriptions-item>
      <a-descriptions-item label="客户姓名" prop="clientName">
        {{ form.clientName }}
      </a-descriptions-item>
      <a-descriptions-item label="受理业务类型" prop="bizType">
        {{ form.bizType }}
      </a-descriptions-item>
      <a-descriptions-item label="预约业务类型" prop="preBizType">
        {{ form.preBizType }}
      </a-descriptions-item>
      <a-descriptions-item label="预约业务类型名称" prop="preBizName">
        {{ form.preBizName }}
      </a-descriptions-item>
      <a-descriptions-item label="预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期 5 已作废" prop="status">
        {{ form.status }}
      </a-descriptions-item>
      <a-descriptions-item label="受理单ID" prop="flowInsId">
        {{ form.flowInsId }}
      </a-descriptions-item>
      <a-descriptions-item label="过期时间" prop="expireTime">
        {{ form.expireTime }}
      </a-descriptions-item>
      <a-descriptions-item label="办理人" prop="operator">
        {{ form.operator }}
      </a-descriptions-item>
      <a-descriptions-item label="见证人" prop="witnesstor">
        {{ form.witnesstor }}
      </a-descriptions-item>
      <a-descriptions-item label="见证时间" prop="witnessTime">
        {{ form.witnessTime }}
      </a-descriptions-item>
      <a-descriptions-item label="最新审核人" prop="reviewer">
        {{ form.reviewer }}
      </a-descriptions-item>
      <a-descriptions-item label="最近审核时间" prop="reviewTime">
        {{ form.reviewTime }}
      </a-descriptions-item>
<!--      <a-descriptions-item label="渠道终端" prop="opStaion">
        {{ form.opStaion }}
      </a-descriptions-item>-->
      <a-descriptions-item label="营业部" prop="branchNo">
        {{ getBranchName(form.branchNo) }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间" prop="createTime">
        {{ form.createTime }}
      </a-descriptions-item>
      <a-descriptions-item label="修改时间" prop="updateTime">
        {{ form.updateTime }}
      </a-descriptions-item>
      <a-descriptions-item label="备注" prop="remark">
        {{ form.remark }}
      </a-descriptions-item>
    </a-descriptions>
    <template slot="footer">
      <a-button type="primary" @click="ok">确定</a-button>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: "BcPreBiz_detail",
  inject: ["api",'getPreBizNameMap',"getBranchList"],
  data() {
    return {
      form: {}, //表单数据,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.preBizId) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    getBranchName(val) {
      const branchList = this.getBranchList()
      const filterData = branchList.filter(({ dictValue }) => dictValue === val)[0]
      return filterData ? filterData.dictLabel : ''
    },
    ok() {
      this.showPop = false;
      this.reset()
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    query() {
      this.reset();
      return this.api
        .queryBcPreBizDetail({ bcPreBizId: this.parameterData.preBizId })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          this.form = data;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
  .ant-descriptions-bordered.ant-descriptions-item-content,
  .ant-descriptions-bordered .ant-descriptions-item-label{
    min-width: 110px;
  }
  .ant-descriptions-item-content{
    word-break: break-all;
  }
</style>
