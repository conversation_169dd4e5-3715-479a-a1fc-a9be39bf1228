<!--
 * Generate by tech-manage-server-v1.0.0#work-code-generator-v1.0.2
 * @Author: weizp
 * @Date: 2023-10-20 14:21:02
 * @LastEditTime: 2023-10-20 14:21:02
 * @LastEditors: weizp
 * @Description: 预约业务单表首页
 * @FilePath: \bcPreBiz\src\module\bcPreBiz\index.vue
-->
<template>
  <a-modal
    :title="`${typeTitle}预约业务单表`"
    :width="500"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="预约单ID" prop="preBizId">
        <a-input v-model="form.preBizId" placeholder="请输入预约单ID" disabled ></a-input>
      </a-form-model-item>
      <a-form-model-item label="资金账号" prop="fundAccount">
        <a-input v-model="form.fundAccount" placeholder="请输入资金账号" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="客户姓名" prop="clientName">
        <a-input v-model="form.clientName" placeholder="请输入客户姓名" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="受理业务类型" prop="bizType">
        <a-input v-model="form.bizType" placeholder="请输入受理业务类型" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="预约业务类型" prop="preBizType">
        <a-input v-model="form.preBizType" placeholder="请输入预约业务类型" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="预约业务类型名称" prop="preBizName">
        <a-input v-model="form.preBizName" placeholder="请输入预约业务类型名称" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期" prop="status">
        <a-input v-model="form.status" placeholder="请输入预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="受理单ID" prop="flowInsId">
        <a-input v-model="form.flowInsId" placeholder="请输入受理单ID" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="过期时间" prop="expireTime">
        <a-date-picker v-model="form.expireTime" show-time placeholder="选择过期时间" @change="onExpireTimeChange" @ok="onExpireTimeOk" />
      </a-form-model-item>
      <a-form-model-item label="办理人" prop="operator">
        <a-input v-model="form.operator" placeholder="请输入办理人" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="见证人" prop="witnesstor">
        <a-input v-model="form.witnesstor" placeholder="请输入见证人" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="见证时间" prop="witnessTime">
        <a-date-picker v-model="form.witnessTime" show-time placeholder="选择见证时间" @change="onWitnessTimeChange" @ok="onWitnessTimeOk" />
      </a-form-model-item>
      <a-form-model-item label="最新审核人" prop="reviewer">
        <a-input v-model="form.reviewer" placeholder="请输入最新审核人" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="最近审核时间" prop="reviewTime">
        <a-date-picker v-model="form.reviewTime" show-time placeholder="选择最近审核时间" @change="onReviewTimeChange" @ok="onReviewTimeOk" />
      </a-form-model-item>
      <a-form-model-item label="渠道终端" prop="opStaion">
        <a-input v-model="form.opStaion" placeholder="请输入渠道终端" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="营业部" prop="branchNo">
        <a-input v-model="form.branchNo" placeholder="请输入营业部" ></a-input>
      </a-form-model-item>
      <a-form-model-item label="创建时间" prop="createTime">
        <a-date-picker v-model="form.createTime" show-time placeholder="选择创建时间" @change="onCreateTimeChange" @ok="onCreateTimeOk" />
      </a-form-model-item>
      <a-form-model-item label="修改时间" prop="updateTime">
        <a-date-picker v-model="form.updateTime" show-time placeholder="选择修改时间" @change="onUpdateTimeChange" @ok="onUpdateTimeOk" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>

  // 默认表单属性
const defaultForm = {
    preBizId: "", // 预约单ID
    fundAccount: "", // 资金账号
    clientName: "", // 客户姓名
    bizType: "", // 受理业务类型
    preBizType: "", // 预约业务类型
    preBizName: "", // 预约业务类型名称
    status: "", // 预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期
    flowInsId: "", // 受理单ID
    expireTime: "", // 过期时间
    operator: "", // 办理人
    witnesstor: "", // 见证人
    witnessTime: "", // 见证时间
    reviewer: "", // 最新审核人
    reviewTime: "", // 最近审核时间
    opStaion: "", // 渠道终端
    branchNo: "", // 营业部
    createTime: "", // 创建时间
    updateTime: "", // 修改时间
};

  export default {
    name: "bcPreBiz_Edit",
    inject: ["api"],
    data() {
      return {
        form: Object.assign({}, defaultForm), //表单数据,
        rules: {
          preBizId: [
              { required: true, message: "预约单ID不能为空", trigger: "blur"}
          ],
          fundAccount: [
              { required: true, message: "资金账号不能为空", trigger: "blur"}
          ],
          clientName: [
              { required: true, message: "客户姓名不能为空", trigger: "blur"}
          ],
          bizType: [
              { required: true, message: "受理业务类型不能为空", trigger: "blur"}
          ],
          preBizType: [
              { required: true, message: "预约业务类型不能为空", trigger: "blur"}
          ],
          preBizName: [
              { required: true, message: "预约业务类型名称不能为空", trigger: "blur"}
          ],
          status: [
              { required: true, message: "预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期不能为空", trigger: "blur"}
          ],
          flowInsId: [
              { required: true, message: "受理单ID不能为空", trigger: "blur"}
          ],
          expireTime: [
              { required: true, message: "过期时间不能为空", trigger: "blur"}
          ],
          operator: [
              { required: true, message: "办理人不能为空", trigger: "blur"}
          ],
          witnesstor: [
              { required: true, message: "见证人不能为空", trigger: "blur"}
          ],
          witnessTime: [
              { required: true, message: "见证时间不能为空", trigger: "blur"}
          ],
          reviewer: [
              { required: true, message: "最新审核人不能为空", trigger: "blur"}
          ],
          reviewTime: [
              { required: true, message: "最近审核时间不能为空", trigger: "blur"}
          ],
          opStaion: [
              { required: true, message: "渠道终端不能为空", trigger: "blur"}
          ],
          branchNo: [
              { required: true, message: "营业部不能为空", trigger: "blur"}
          ],
          createTime: [
              { required: true, message: "创建时间不能为空", trigger: "blur"}
          ],
          updateTime: [
              { required: true, message: "修改时间不能为空", trigger: "blur"}
          ],
        },
        // 异步加载
        confirmLoading: false,
      };
    },
    props: {
      isPopShow: {
        type: Boolean,
        default: false,
      },
      // 是否展示添加弹窗
      visible: {
        type: Boolean,
        default: false,
      },
      // 修改时传入参数
      parameterData: {
        type: Object,
        default: ()=>{}
      },
    },
    computed: {
      showPop: {
        get() {
            return this.isPopShow;
        },
        set(val) {
            this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
        }
      },
      typeTitle (){
        return "修改";
      }
    },
    watch: {
      isPopShow(n) {
        if (n && this.parameterData.preBizId) {
          this.query();
        }else{
          this.reset();
        }
        this.$nextTick(()=> {
        });
      }
    },
    methods: {
      // 重置对应的表单
      reset() {
        // 重置表单验证属性
        this.$refs.form && this.$refs.form.resetFields();
        this.form = Object.assign({}, defaultForm);
        this.confirmLoading = false;
      },
      query() {
        this.reset();
        return this.api.queryBcPreBizDetail({ bcPreBizId: this.parameterData.preBizId }).then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          this.form = data;
        });
      },
      // 提交预约业务单表分组创建
      submit() {
        this.$refs.form.validate((valid) => {
          if (!valid) return;
          this.confirmLoading = true;
          let param = JSON.parse(JSON.stringify(this.form));
          let callback = ({ code, msg }) => {
            this.confirmLoading = false;
            if (code != 0) return this.$message.error(`预约业务单表${this.typeTitle}失败：${msg}`);
            this.$message.success(`预约业务单表${this.typeTitle}成功！`);
            // 关闭弹窗
            this.showPop = false;
            // 通知操作成功
            this.$emit("success");
            // 重置表单
            this.reset();
          };
          this.api.editBcPreBiz(param).then(callback);
        });
      },
      onExpireTimeChange(value, dateString) {
        console.log('选择的过期时间: ', value);
        console.log('格式化选择时间: ', dateString);
        this.form.expireTime = dateString;
      },
      onExpireTimeOk(value) {
        console.log('onExpireTimeOk: ', value);
      },
      onWitnessTimeChange(value, dateString) {
        console.log('选择的见证时间: ', value);
        console.log('格式化选择时间: ', dateString);
        this.form.witnessTime = dateString;
      },
      onWitnessTimeOk(value) {
        console.log('onWitnessTimeOk: ', value);
      },
      onReviewTimeChange(value, dateString) {
        console.log('选择的最近审核时间: ', value);
        console.log('格式化选择时间: ', dateString);
        this.form.reviewTime = dateString;
      },
      onReviewTimeOk(value) {
        console.log('onReviewTimeOk: ', value);
      },
      onCreateTimeChange(value, dateString) {
        console.log('选择的创建时间: ', value);
        console.log('格式化选择时间: ', dateString);
        this.form.createTime = dateString;
      },
      onCreateTimeOk(value) {
        console.log('onCreateTimeOk: ', value);
      },
      onUpdateTimeChange(value, dateString) {
        console.log('选择的修改时间: ', value);
        console.log('格式化选择时间: ', dateString);
        this.form.updateTime = dateString;
      },
      onUpdateTimeOk(value) {
        console.log('onUpdateTimeOk: ', value);
      },
    },
  };
</script>

<style lang="scss" scoped>
</style>