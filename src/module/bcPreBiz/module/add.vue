<template>
  <a-modal
      :title="`${typeTitle}预约业务单表`"
      :width="500"
      v-model="showPop"
      ok-text="确认"
      cancel-text="取消"
      @ok="submit"
      @cancel="reset"
      :maskClosable="false"
  >
    <a-form-model
        ref="form"
        :model="form"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 14 }"
        :rules="rules"
    >
      <a-form-model-item label="资金账号" prop="fundAccount">
        <a-input v-model="form.fundAccount" placeholder="请输入资金账号" @change="fundAccChange"></a-input>
      </a-form-model-item>
      <a-form-model-item label="客户姓名" prop="clientName">
        <!--       <a-input v-model="form.clientName" placeholder="请输入客户姓名" ></a-input>-->
        {{ form.clientName }}
      </a-form-model-item>
      <a-form-model-item label="预约业务类型" prop="preBizType">
        <!--       <a-input v-model="form.preBizType" placeholder="请输入预约业务类型" ></a-input>-->
        <a-select
            v-model="form.preBizType"
            placeholder="请选择预约业务类型"
            show-search
            option-filter-prop="children"
            allowClear
            @change="onPreBizType"
        >
          <a-select-option
              v-for="(v, i) in preBizNameMap"
              :value="v.dictValue"
              :key="i"
          >
            {{ v.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="受理业务类型" prop="bizType">
        {{ getBizName(form.bizType) }}
      </a-form-model-item>
      <a-form-model-item label="营业部编号" prop="branchNo">
        {{ getBranchName(form.branchNo) }}
      </a-form-model-item>
      <a-form-model-item label="备注" prop="remark">
        <a-input v-model="form.remark" placeholder="备注"></a-input>
      </a-form-model-item>
      <!--     <a-form-model-item label="预约业务类型名称" prop="preBizName">
             <a-input v-model="form.preBizName" placeholder="请输入预约业务类型名称" ></a-input>
           </a-form-model-item>
           <a-form-model-item label="预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期" prop="status">
             <a-input v-model="form.status" placeholder="请输入预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期" ></a-input>
           </a-form-model-item>
           <a-form-model-item label="受理单ID" prop="flowInsId">
             <a-input v-model="form.flowInsId" placeholder="请输入受理单ID" ></a-input>
           </a-form-model-item>
           <a-form-model-item label="过期时间" prop="expireTime">
             <a-date-picker show-time placeholder="选择过期时间" @change="onExpireTimeChange" @ok="onExpireTimeOk" />
           </a-form-model-item>
           <a-form-model-item label="办理人" prop="operator">
             <a-input v-model="form.operator" placeholder="请输入办理人" ></a-input>
           </a-form-model-item>
           <a-form-model-item label="见证人" prop="witnesstor">
             <a-input v-model="form.witnesstor" placeholder="请输入见证人" ></a-input>
           </a-form-model-item>
           <a-form-model-item label="见证时间" prop="witnessTime">
             <a-date-picker show-time placeholder="选择见证时间" @change="onWitnessTimeChange" @ok="onWitnessTimeOk" />
           </a-form-model-item>
           <a-form-model-item label="最新审核人" prop="reviewer">
             <a-input v-model="form.reviewer" placeholder="请输入最新审核人" ></a-input>
           </a-form-model-item>
           <a-form-model-item label="最近审核时间" prop="reviewTime">
             <a-date-picker show-time placeholder="选择最近审核时间" @change="onReviewTimeChange" @ok="onReviewTimeOk" />
           </a-form-model-item>
           <a-form-model-item label="渠道终端" prop="opStaion">
             <a-input v-model="form.opStaion" placeholder="请输入渠道终端" ></a-input>
           </a-form-model-item>
           <a-form-model-item label="营业部" prop="branchNo">
             <a-input v-model="form.branchNo" placeholder="请输入营业部" ></a-input>
           </a-form-model-item>
           <a-form-model-item label="创建时间" prop="createTime">
             <a-date-picker show-time placeholder="选择创建时间" @change="onCreateTimeChange" @ok="onCreateTimeOk" />
           </a-form-model-item>
           <a-form-model-item label="修改时间" prop="updateTime">
             <a-date-picker show-time placeholder="选择修改时间" @change="onUpdateTimeChange" @ok="onUpdateTimeOk" />
           </a-form-model-item>-->
    </a-form-model>
  </a-modal>
</template>

<script>

// 默认表单属性
const defaultForm = {
  fundAccount: '', // 资金账号
  clientName: '', // 客户姓名
  bizType: '', // 受理业务类型
  preBizType: '', // 预约业务类型
  preBizName: '', // 预约业务类型名称
  status: '0', // 预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期 5已作废
  flowInsId: '', // 受理单ID
  expireTime: '', // 过期时间
  operator: '', // 办理人
  witnesstor: '', // 见证人
  witnessTime: '', // 见证时间
  reviewer: '', // 最新审核人
  reviewTime: '', // 最近审核时间
  opStaion: '', // 渠道终端
  branchNo: '', // 营业部
  createTime: '', // 创建时间
  updateTime: '', // 修改时间
  remark: '' // 备注
}

export default {
  name: 'bcPreBiz_Add',
  inject: ['api', 'getPreBizNameMap', 'getBranchList'],
  data () {
    return {
      form: Object.assign({}, defaultForm), //表单数据,
      rules: {
        fundAccount: [
          { required: true, message: '资金账号不能为空', trigger: 'blur' }
        ],
        clientName: [
          { required: true, message: '客户姓名不能为空', trigger: 'blur', }
        ],
        /*bizType: [
            { required: true, message: "受理业务类型不能为空", trigger: "blur"}
        ],*/
        preBizType: [
          { required: true, message: '预约业务类型不能为空', trigger: 'blur' }
        ],
        remark: [
          { required: false, trigger: 'blur' }
        ],
        /*preBizName: [
            { required: true, message: "预约业务类型名称不能为空", trigger: "blur"}
        ],
        status: [
            { required: true, message: "预约状态：0 待处理；1 审核中；2 审核驳回；3 办理完成；4 已过期不能为空", trigger: "blur"}
        ],
        flowInsId: [
            { required: true, message: "受理单ID不能为空", trigger: "blur"}
        ],
        expireTime: [
            { required: true, message: "过期时间不能为空", trigger: "blur"}
        ],
        operator: [
            { required: true, message: "办理人不能为空", trigger: "blur"}
        ],
        witnesstor: [
            { required: true, message: "见证人不能为空", trigger: "blur"}
        ],
        witnessTime: [
            { required: true, message: "见证时间不能为空", trigger: "blur"}
        ],
        reviewer: [
            { required: true, message: "最新审核人不能为空", trigger: "blur"}
        ],
        reviewTime: [
            { required: true, message: "最近审核时间不能为空", trigger: "blur"}
        ],
        opStaion: [
            { required: true, message: "渠道终端不能为空", trigger: "blur"}
        ],
        branchNo: [
            { required: true, message: "营业部不能为空", trigger: "blur"}
        ],
        createTime: [
            { required: true, message: "创建时间不能为空", trigger: "blur"}
        ],
        updateTime: [
            { required: true, message: "修改时间不能为空", trigger: "blur"}
        ],*/
      },
      // 异步加载
      confirmLoading: false,
      preBizNameMap: {}
    }
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {}
    },
  },
  computed: {
    showPop: {
      get () {
        return this.isPopShow
      },
      set (val) {
        this.$emit('update:isPopShow', val) // visible 改变的时候通知父组件
      }
    },
    typeTitle () {
      return '添加'
    }
  },
  watch: {
    isPopShow (n) {
      if (n && this.parameterData.bcPreBizId) {
        this.query()
      } else {
        this.reset()
      }
      this.$nextTick(() => {
        this.preBizNameMap = this.getPreBizNameMap().filter(({ dictValue }) => dictValue !== '9')
      })
    },
  },
  methods: {
    getBranchName(val) {
      const branchList = this.getBranchList()
      const filterData = branchList.filter(({ dictValue }) => dictValue === val)[0]
      return filterData ? filterData.dictLabel : ''
    },
    fundAccChange () {
      if (this.form.fundAccount === '') return
      this.api.clientInfoQry({
        fundAccount: this.form.fundAccount
      }).then(({ data, code }) => {
        if (code === 0) {
          this.form.clientName = data.clientName
          this.form.branchNo = data.branchNo
        } else {
          this.form.clientName = ''
          this.form.branchNo = ''
        }

      })
    },
    // 重置对应的表单
    reset () {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields()
      this.form = Object.assign({}, defaultForm)
      this.confirmLoading = false
    },
    query () {
      this.reset()
      return this.api.queryBcPreBizDetail({ bcPreBizId: this.parameterData.bcPreBizId }).then((res) => {
        if (res.code != 0) return
        let data = res.data
        this.form = data
      })
    },
    // 提交预约业务单表创建
    submit () {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.confirmLoading = true
        this.form.operator = this.$store.getters.userInfo?.userName || ''
        let param = JSON.parse(JSON.stringify(this.form))
        let callback = ({ code, msg }) => {
          this.confirmLoading = false
          if (code != 0) return this.$message.error(`预约业务单表${this.typeTitle}失败：${msg}`)
          this.$message.success(`预约业务单表${this.typeTitle}成功！`)
          // 关闭弹窗
          this.showPop = false
          // 通知操作成功
          this.$emit('success')
          // 重置表单
          this.reset()
        }
        this.api.addBcPreBiz(param).then(callback)
      })
    },
    onPreBizType (value) {
      console.log('选择的预约类型: ', value)
      this.form.preBizType = value
      this.form.preBizName = this.preBizNameMap.filter(({ dictValue }) => dictValue === value)[0].dictLabel
      if (this.form.preBizType === '8') {
        this.form.bizType = '010276'
      } else if (this.form.preBizType === '9') {
        this.form.bizType = '010277'
      } else {
        this.form.bizType = '010262'
      }
    },
    onExpireTimeChange (value, dateString) {
      console.log('选择的过期时间: ', value)
      console.log('格式化选择时间: ', dateString)
      this.form.expireTime = dateString
    },
    onExpireTimeOk (value) {
      console.log('onExpireTimeOk: ', value)
    },
    onWitnessTimeChange (value, dateString) {
      console.log('选择的见证时间: ', value)
      console.log('格式化选择时间: ', dateString)
      this.form.witnessTime = dateString
    },
    onWitnessTimeOk (value) {
      console.log('onWitnessTimeOk: ', value)
    },
    onReviewTimeChange (value, dateString) {
      console.log('选择的最近审核时间: ', value)
      console.log('格式化选择时间: ', dateString)
      this.form.reviewTime = dateString
    },
    onReviewTimeOk (value) {
      console.log('onReviewTimeOk: ', value)
    },
    onCreateTimeChange (value, dateString) {
      console.log('选择的创建时间: ', value)
      console.log('格式化选择时间: ', dateString)
      this.form.createTime = dateString
    },
    onCreateTimeOk (value) {
      console.log('onCreateTimeOk: ', value)
    },
    onUpdateTimeChange (value, dateString) {
      console.log('选择的修改时间: ', value)
      console.log('格式化选择时间: ', dateString)
      this.form.updateTime = dateString
    },
    onUpdateTimeOk (value) {
      console.log('onUpdateTimeOk: ', value)
    },
    getBizName (bizType) {
      /*预约业务 其他业务 010262
      预约业务-补充档案 010276
      预约业务-两融开户 010277*/
      const dict = {
        '010262': '其他业务',
        '010276': '补充档案',
        '010277': '两融开户'
      }
      return dict[bizType]
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
