<template>
  <a-card title="客户经理信息列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :searchFrom.sync="tableForm">
        <tkSelectForm @query="query(1)" @reset="reset">
          <a-form-model-item label="员工名称">
            <a-input
              v-model="tableForm.brokerName"
              placeholder="请输入员工名称"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="员工编号">
            <a-input
              v-model="tableForm.brokerNo"
              placeholder="请输入员工编号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="执业编号">
            <a-input
              v-model="tableForm.workNo"
              placeholder="请输入执业编号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="营业部">
            <a-select
              v-model="tableForm.branchNos"
              placeholder="请选择营业部，支持多选"
              allowClear
              show-search
              option-filter-prop="children"
              mode="multiple"
            >
              <a-select-option
                v-for="item in branchList"
                :key="item.branchId"
                :value="item.branchNo"
              >
                {{ item.branchName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="手机号">
            <a-input
              v-model="tableForm.phone"
              placeholder="请输入手机号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="员工类型">
            <a-select
              v-model="tableForm.brokerTypes"
              placeholder="请选择员工类型，支持多选"
              allowClear
              show-search
              option-filter-prop="children"
              mode="multiple"
            >
              <a-select-option
                v-for="item in brokerTypeList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.key }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="状态">
            <a-select v-model="tableForm.status" placeholder="请选择状态">
              <a-select-option
                v-for="(item, index) in ['有效', '无效']"
                :key="index"
                :value="index + ''"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/userBroker/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="reqTableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="brokerId"
      >
        <div class="table-buttun-area" slot="tableHeader">
          <a-button icon="upload" type="primary" @click="exportExcel">
            批量导出
          </a-button>
        </div>
        <template slot="branchInfo" slot-scope="data">
          <span v-if="data.brokerType == '01'">五矿证券总部</span>
          <span v-else>{{ getBranchName(data) }}</span>
        </template>
        <template slot="avatar" slot-scope="data">
          <a :disabled="!data.avatar" @click.stop="show(data.avatar)"
            >查看大头照</a
          >
        </template>
      </tk-table>
    </div>
    <a-modal
      :visible="previewVisible"
      :footer="null"
      @cancel="previewVisible = false"
    >
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </a-card>
</template>

<script>
import api from "./api";

// 默认表单属性
const defaultForm = {
  brokerName: "",
  brokerNos: "",
  workNo: "",
  branchNo: undefined,
  phone: "",
  brokerTypes: undefined,
  status: undefined,
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "brokerName",
          label: "员工姓名",
          isSorter: false,
        },
        {
          field: "brokerNo",
          label: "员工编号",
          isSorter: false,
        },
        {
          field: "brokerType",
          label: "员工类型",
          isSorter: false,
          filter: (item) => this.filterValue(item),
        },
        {
          field: "phone",
          label: "手机号",
          isSorter: false,
        },
        {
          field: "branchInfo",
          label: "营业部",
          isSorter: false,
        },
        {
          field: "status",
          label: "状态",
          isSorter: false,
          filter: (item) => (item === "0" ? "有效" : "无效"),
        },
        {
          field: "workNo",
          label: "执业编号",
          isSorter: false,
        },
        {
          field: "avatar",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      tableForm: {
        brokerName: "",
        brokerNos: "",
        workNo: "",
        branchNo: undefined,
        phone: "",
        brokerTypes: undefined,
        status: undefined,
      },
      reqTableForm: {},
      selectedRowKeys: [],
      branchList: [],
      brokerTypeList: [
        {
          key: "经纪人",
          value: "00",
        },
        {
          key: "客户经理",
          value: "01",
        },
        {
          key: "理财经理",
          value: "02",
        },
        {
          key: "投顾经理",
          value: "03",
        },
      ],
      previewVisible: false,
      previewImage: "",
    };
  },
  provide: { api },
  created() {
    api.getBranchInfo().then((res) => {
      this.branchList = res.data;
    });
  },
  methods: {
    // 导出exportExcel
    exportExcel() {
      let url = "/bc-manage-server/userBroker/export";
      if (this.selectedRowKeys.length !== 0) {
        url = url + "?brokerIds=" + this.selectedRowKeys.join(",");
      } else {
        Object.keys(this.reqTableForm).forEach((item) => {
          if (!this.reqTableForm[item]) {
            delete this.reqTableForm[item];
          }
        });
        if (Object.keys(this.reqTableForm).length !== 0) {
          Object.keys(this.reqTableForm).forEach((item, index) => {
            if (index === 0) {
              url = url + "?" + item + "=" + Object.values(this.reqTableForm)[index];
            } else {
              url = url + "&" + item + "=" + Object.values(this.reqTableForm)[index];
            }
          });
        }
      }
      window.location.href = url;
    },
    getBranchName(data) {
      return (
        data.branchName ||
        this.branchList.find((item) => item.branchNo === data.branchNo)?.bran
      );
    },
    show(data) {
      this.previewImage = window.$hvue.customConfig.fileUrl + data;
      this.previewVisible = true;
    },
    filterValue(value) {
      let obj = this.brokerTypeList.find((item) => item.value === value);
      return obj ? obj.key : value;
    },
    query(type) {
      if (type) {
        this.reqTableForm = JSON.parse(JSON.stringify(this.tableForm));
        this.reqTableForm.branchNos =
          this.reqTableForm.branchNos && this.reqTableForm.branchNos.join(",");
        this.reqTableForm.brokerTypes =
          this.reqTableForm.brokerTypes &&
          this.reqTableForm.brokerTypes.join(",");
      }
      this.$nextTick(() => {
        this.$refs.table.getTableData();
        this.selectedRowKeys = [];
      });
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
  },
};
</script>

<style lang="scss" scoped></style>
