<template>
    <a-card title="超限额查询" class="management" :bordered="false">
        <div class="searchFrom">
            <a-form-model layout="inline" :model="tableFrom">
                <tkSelectForm @query="success" @reset="reset">
                    <a-form-model-item label="申请编号">
                        <a-input v-model="tableFrom.applyNo" placeholder="请输入申请编号" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="客户号">
                        <a-input v-model="tableFrom.clientId" placeholder="请输入客户号" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="申请状态">
                        <a-select v-model="tableFrom.appStatus" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.appStatus']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>

                    <a-form-model-item label="审核状态">
                        <a-select v-model="tableFrom.approval_type" placeholder="全部" show-search
                            option-filter-prop="children" allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.approvalType']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>

                </tkSelectForm>
            </a-form-model>
        </div>
        <div class="access-table">
            <tk-table ref="table" :tableData.sync="comlun" getMethods="bc-manage-server/gjKhHis//margin/overQuotaQuery"
                :intercept-response="intercept_response" :isPaging="true" :tableFromFilter="tableFormFilter"
                :tableFrom="tableFrom" tableId="id">
            </tk-table>

        </div>
    </a-card>
</template>

<script>

export default {
    name: 'auditRecords',
    data() {
        return {
            comlun: [
                { field: 'apply_no', label: '申请编号', width: 210 },
                { field: 'client_id', label: ' 客户号', width: 100 },
                { field: 'client_name', label: ' 客户姓名', width: 100 },
                {
                    field: 'app_time', label: '申请时间', width: 150,
                },
                { field: 'brance_no', label: '分支机构 ', width: 100 },
                {
                    field: 'credit_type', label: '征信类型', width: 100,
                    filter: item => this.getDictText('bc.common.creditType', item)
                },
                {
                    field: 'app_status', label: '申请状态', width: 100,
                    filter: item => this.getDictText('bc.common.appStatus', item)
                },
                { field: 'credit_coefficient', label: '额度系数', width: 100 },
                { field: 'credit_quota', label: '授信额度', width: 150 },
                { field: 'asset_amount', label: '资产总值', width: 150 },
                { field: 'approval_type', label: '审核状态', width: 100, filter: item => this.getDictText('bc.common.approvalType', item) },
            ],
            dictMap: {
                'bc.common.creditType': [
                    { value: '0', label: '首次征信' },
                    { value: '1', label: '再次征信' },
                ],
                'bc.common.appStatus': [
                    { value: '0', label: '已申请' },
                    { value: '1', label: '已拒绝' },
                ],
                'bc.common.approvalType': [
                    { value: '0', label: '未处理' },
                    { value: '1', label: '通过' },
                    { value: '2', label: '驳回' },
                ],

            },
            tableFrom: {},
        }
    },
    methods: {
        getDictText(key, value) {
            let results = this.dictMap[key] || [];
            results = results.filter((item) => {
                return item.value == value;
            });
            return (results && results.length && results[0].label) || '';
        },
        tableFormFilter(param) {
            return param;
        },
        success() {
            this.$refs.table.getTableData();
        },
        // 重置
        reset() {
            this.tableFrom = {};
        },
    }
}
</script>

