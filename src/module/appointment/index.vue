<template>
  <a-card title="预约单管理" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="handleReset">
          <a-form-model-item label="业务类型">
            <a-select
              v-model="tableForm.businessType"
              placeholder="请选择业务类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in businessList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="预约状态">
            <a-select
              v-model="tableForm.preStatus"
              placeholder="请选择预约状态"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in preStatusList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <!-- <a-form-model-item label="用户标识">
            <a-select
              v-model="tableForm.userType"
              placeholder="请选择用户标识"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in userTypeList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item> -->
          <a-form-model-item label="用户账号">
            <a-input
              v-model="tableForm.userId"
              placeholder="请输入用户账号"
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="page_main">
      <a-divider />
      <a-row class="btn_row">
        <a-col>
          <a-button type="primary" icon="redo" @click="query">刷新 </a-button>
        </a-col>
      </a-row>
      <div class="access-table">
        <tk-table
          ref="table"
          :tableData.sync="columns"
          getMethods="bc-manage-server/gjV2/audit/workOrder/page"
          :isSelected="true"
          :isPaging="true"
          :tableFrom="tableForm"
          :selectedRowKeys.sync="selectedRowKeys"
          tableId="id"
        >
          <template slot="operation" slot-scope="data">
            <a class="action-btn" @click="showDetail(data)"> 查看 </a>
          </template>
        </tk-table>
      </div>
    </div>

    <addOrEdit
      :isPopShow.sync="isPopShow"
      :selectedRowValue="selectedRowValue"
    />
  </a-card>
</template>

<script>
import addOrEdit from './module/addOrEdit'
import api from './api'
import moment from 'moment'

const defaultForm = {
  bizType: undefined,
  name: '',
  state: undefined,
}

export default {
  name: 'appointment',
  data() {
    return {
      tableForm: {
        businessType: '30059', //业务类型
        preStatus: '', //预约状态
        userType: '', //用户标识
        userId: '', //用户账号
      },
      businessList: [
        { value: '30059', name: '非现特殊视频' },
        { value: '30060', name: '补档案资料' },
      ], //业务类型列表
      preStatusList: [
        { value: '0', name: '正常' },
        { value: '1', name: '已完成' },
        { value: '2', name: '过期' },
        { value: '3', name: '强制结束' },
        { value: '4', name: '取消办理' },
        { value: '9', name: '删除' },
      ], //业务类型列表
      // userTypeList: [{ value: '0', name: '客户号' }], //用户标识列表
      data: [],
      //表头数据
      columns: [
        {
          label: '工单ID',
          field: 'id',
          isSorter: false,
        },
        {
          label: '客户编号',
          field: 'userId',
          isSorter: false,
        },
        {
          label: '客户姓名',
          field: 'clientName',
          isSorter: false,
        },
        {
          label: '业务类型',
          field: 'businessType',
          isSorter: false,
        },
        {
          label: '预约状态',
          field: 'preStatus',
          isSorter: false,
          filter: (item) => this.getTaskStatusList(item),
        },
        {
          label: '创建时间',
          field: 'createTime',
          isSorter: false,
          filter: (item) => moment(item).format('YYYY--MM--DD HH:MM:SS'),
        },
        {
          label: '更新时间',
          field: 'updateTime',
          isSorter: false,
          filter: (item) => moment(item).format('YYYY--MM--DD HH:MM:SS'),
        },
        {
          label: '操作员',
          field: 'opUserId',
          isSorter: false,
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: 220,
        },
      ],
      selectedRowKeys: [], // Check here to configure the default column
      isPopShow: false, //弹窗是否显示
      selectedRowValue: {},
    }
  },
  components: { addOrEdit },
  methods: {
    //处理按钮
    query() {
      this.$refs.table.getTableData()
    },
    handleReset() {
      this.tableForm = JSON.parse(JSON.stringify(defaultForm))
    },
    getTaskStatusList(val) {
      const filterData = this.preStatusList.filter(
        ({ value }) => value === val
      )[0]
      return filterData ? filterData.name : ''
    },
    formatDateTime(date) {
      return moment(date).format('YYYY-MM-DD HH:MM:SS')
    },
    showDetail(data) {
      api.queryItem({ id: data.id }).then((res) => {
        let { code, data } = res
        if (code == 0) {
          this.selectedRowValue = data
          this.selectedRowValue.createTime = this.formatDateTime(
            data.createTime
          )
          this.selectedRowValue.expireTime = this.formatDateTime(
            data.expireTime
          )
          this.selectedRowValue.statusName = this.getTaskStatusList(
            data.preStatus
          )
          this.isPopShow = true
        }
      })
    },
  },
}
</script>

<style>
.alert-test {
  text-align: center;
  padding: 5px;
}
</style>
