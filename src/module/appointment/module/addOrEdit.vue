<template>
  <div v-show="showPop" class="test">
    <a-modal
      title="查看工单"
      :visible="showPop"
      :destroyOnClose="true"
      class="ant_modal_bigtable"
      @cancel="closePop"
    >
      <template slot="footer">
        <a-button key="back" @click="closePop"> 关闭 </a-button>
      </template>
      <div class="first_line">
        <div class="item">
          <h3>客户编号</h3>
          <input
            type="text"
            readonly
            :value="selectedRowValue.clientId"
            class="form_control"
          />
        </div>
        <div class="item">
          <h3>姓名</h3>
          <input
            type="text"
            readonly
            :value="selectedRowValue.clientName"
            class="form_control"
          />
        </div>
        <div class="item">
          <h3>预约单编号</h3>
          <input
            type="text"
            readonly
            :value="selectedRowValue.serialNo"
            class="form_control"
          />
        </div>
      </div>
      <div class="second_line">
        <div class="item">
          <h3>当前状态</h3>
          <input
            type="text"
            readonly
            :value="selectedRowValue.statusName"
            class="form_control"
          />
        </div>
        <div class="item">
          <h3>创建人</h3>
          <input
            type="text"
            readonly
            :value="selectedRowValue.opUserId"
            class="form_control"
          />
        </div>
        <div class="item">
          <h3>预约时间</h3>
          <input
            type="text"
            readonly
            :value="selectedRowValue.createTime"
            class="form_control"
          />
        </div>
        <div class="item">
          <h3>到期时间</h3>
          <input
            type="text"
            readonly
            :value="selectedRowValue.expireTime"
            class="form_control"
          />
        </div>
      </div>
      <div class="third_line">
        <div>
          <h3>预约单编号</h3>
          <a-radio-group
            name="needPassword"
            :value="selectedRowValue.businessType === '30059' ? '1' : '0'"
            disabled
          >
            <a-radio value="1" key="30059">非现特殊视频</a-radio>
            <a-radio value="0" key="30060">补档案资料</a-radio>
          </a-radio-group>
        </div>
      </div>
      <div class="fourth_line">
        <h3>办理业务</h3>
        <input
          type="text"
          readonly
          :value="selectedRowValue.subBusinessType"
          class="form_control"
        />
      </div>
      <div class="fifth_line">
        <h3>备注</h3>
        <div class="form_control remark">{{ selectedRowValue.remark }}</div>
      </div>
    </a-modal>
  </div>
</template>
<script>
export default {
  name: '',
  data() {
    return {}
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    selectedRowValue: {
      type: Object,
    },
  },
  mounted() {},
  computed: {
    showPop: {
      get() {
        return this.isPopShow
      },
      set(val) {
        this.$emit('update:isPopShow', val) // visible 改变的时候通知父组件
      },
    },
  },
  methods: {
    //关闭
    closePop() {
      this.showPop = false
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .ant-modal {
  width: 1500px !important;
}
::v-deep .idKind .ant-checkbox-wrapper {
  display: block;
}

::v-deep .idKind .ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0;
}
::v-deep .ant-radio-group {
  display: flex;
}
::v-deep .ant-radio-wrapper {
  display: flex;
  align-items: center;
  span {
    color: black;
  }
}
::v-deep .ant-modal-body {
  max-height: none !important;
  padding: 50px 30px;
}
.first_line {
  display: flex;
  justify-content: space-between;
}
.form_control {
  background-color: #eee;
  opacity: 1;
  border-color: #d2d6de;
  display: block;
  width: 100%;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s,
    -webkit-box-shadow ease-in-out 0.15s;
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.second_line,
.third_line {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
.remark {
  min-height: 150px;
}
.fourth_line,
.fifth_line {
  margin-top: 20px;
}
</style>
