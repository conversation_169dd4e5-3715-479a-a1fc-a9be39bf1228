<template>
  <a-card title="三方存管变更失败" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="资金账号">
            <a-input
              v-model="tableForm.fundAccount"
              placeholder="请输入资金账号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户姓名">
            <a-input
              v-model="tableForm.clientName"
              placeholder="请输入客户姓名"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="联系电话">
            <a-input
              v-model="tableForm.mobilePhone"
              placeholder="请输入联系电话"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="存管银行指定状态">
            <a-select
              v-model="tableForm.bankRegStatus"
              placeholder="请选择存管银行指定状态"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in bankRegStatusMap"
                :value="v.dictValue"
                :key="i"
              >
                {{ v.dictLabel }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="/bc-manage-server/depository/audit/pageQuery"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="preBizId"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button type="primary" icon="redo" @click="query">刷新</a-button>
          <a-button type="primary" icon="download" @click="downloadExcel">
            下载excel
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="look(data)">查看</a-button>
          <a-button type="link" @click.stop="add(data)" v-if="data.bankRegStatus == 0"
            >预指定</a-button
          >
        </template>
      </tk-table>
      <add
        :isPopShow.sync="isAddPopShow"
        @success="query"
        :parameterData="selectData"
        :bankList="bankList"
      />
      <LookComponent
        :isPopShow.sync="isLookPopShow"
        :parameterData="selectData"
      />
    </div>
  </a-card>
</template>

<script>
// 引入添加和编辑弹窗
import add from './module/add'
// 引入查看弹窗
import LookComponent from './module/detail'
import api from './api'
import { formatMobile } from '@/utils/parameter.js'
import { request } from 'bus-common-component/lib/extension'

//下载文件配置
const fields = [
  'clientId|客户号',
  'fundAccount|资金账号',
  'clientName|客户姓名',
  'mobilePhone|联系电话',
  'bankName|存管银行名称',
  'bankNo|存管银行编号',
  'bankRegStatus|存管银行指定状态',
  'bankRegDesc|存管银行指定状态描述',
  'operateTime|操作时间',
  'operator|操作人',
]

// 默认表单属性
const defaultForm = {}

export default {
  name: 'depositoryFail',
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: 'clientId',
          label: '客户号',
          isSorter: false,
        },
        {
          field: 'fundAccount',
          label: '资金账号',
          isSorter: false,
        },
        {
          field: 'clientName',
          label: '客户姓名',
          isSorter: false,
        },
        {
          field: 'mobilePhone',
          label: '联系电话',
          isSorter: false,
          filter: (item) => (!item ? '--' : formatMobile(item)),
        },
        {
          field: 'bankName',
          label: '存管银行名称',
          isSorter: false,
        },
        {
          field: 'bankRegDesc',
          label: '存管银行指定状态',
          isSorter: false,
        },
        {
          field: 'operateTime',
          label: '操作时间',
          isSorter: false,
          filter: (item) =>
            !item ? '--' : this.DateFormat(item).format('yyyy-MM-dd hh:mm:ss'),
        },
        {
          field: 'operator',
          label: '操作人',
          isSorter: false,
        },
        {
          field: 'operation',
          label: '操作',
          align: 'center',
          width: 300,
          fixed: 'right',
        },
      ],
      tableForm: {
        clientId: '', //客户号
        fundAccount: '', //资金账号
        clientName: '', //客户姓名
        mobilePhone: '', //联系电话
        bankName: '', //存管银行名称
        bankNo: '', //存管银行编号
        bankRegStatus: '', //存管银行指定状态
        bankRegDesc: '', //存管银行指定状态描述
        operateTime: '', //操作时间
        operator: '', //操作人
      },
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isAddPopShow: false, //添加弹窗是否显示
      //查看弹窗
      isLookPopShow: false,
      selectData: {},
      bankRegStatusMap: {},
      bankList: [],
    }
  },
  provide() {
    return {
      api: api,
      getPreBizNameMap: this.returnPreBizNameMap,
    }
  },
  created() {
    this.getMap()
    this.getBankList()
  },
  components: { add, LookComponent },
  methods: {
    getMap() {
      this.$dict
        .dictContent('bc.common.bankRegStatus')
        .then((data) => {
          console.log('普通存管银行指定状态', data)
          this.bankRegStatusMap = data
        })
        .catch((e) => {
          this.$message.error(e.message)
        })
    },
    getBankList() {
      api.selectBcBankBybankNo().then((data) => {
        console.log('银行列表', data)
        if (data.code == 0) {
          this.bankList = data.data
        }
      })
    },
    getPreBizNameMap(val) {
      const bankRegStatusMap = this.bankRegStatusMap
      const filterData = bankRegStatusMap.filter(
        ({ dictValue }) => dictValue === val
      )[0]
      return filterData ? filterData.dictLabel : ''
    },
    returnPreBizNameMap() {
      return this.bankRegStatusMap
    },
    // 查询数据
    query() {
      console.log('查询了')
      this.$refs.table.getTableData()
      this.selectedRowKeys = []
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      console.log('重置了')
      this.tableForm = JSON.parse(JSON.stringify(defaultForm))
    },
    // 点击新增按钮
    add(data) {
      console.log('新增信息data', data)
      this.isAddPopShow = true
      this.selectData = data
    },
    // 查看
    look(data) {
      console.log('查看信息data', data)
      this.isLookPopShow = true
      this.selectData = data
    },
    // 搜索框参数
    tableFormFilter(param) {
      return param
    },
    setFields(fields) {
      return fields.map((item) => {
        const [key, name, dict, dictOptions] = item.split('|')
        return {
          fieldAlign: 'center',
          fieldDict: dict || '',
          fieldDictDatas: dictOptions || '[]',
          fieldFormat: 'string',
          fieldKey: key,
          fieldName: name,
        }
      })
    },
    // 下载excel
    downloadExcel() {
      console.log('下载文件')
      const url = '/user'
      const fileName = '用户列表'
      const data = {
        fields: this.setFields(fields),
        fileName,
        interfacePath:
          'http://work-manage-server/work-manage-server/user/exportList',
        limit: 0,
        offset: 0,
        params: {},
      }
      this.$confirm({
        title: '导出用户管理',
        content: '确定导出当前数据吗？',
        onOk: () => {
          return new Promise((resolve, reject) => {
            new request()
              .download(
                '/work-manage-server/export/excel' + url,
                data,
                `${fileName}.xlsx`
              )
              .then((data) => {
                if (data && data.code !== 0 && data.msg) {
                  this.$message.error(`导出数据失败：${data.msg}`)
                  reject()
                  return
                }
                resolve()
              })
              .catch(reject)
          })
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped></style>
