<template>
  <a-modal
    :title="`三方存管变更失败`"
    :width="500"
    v-model="showPop"
    :maskClosable="false"
  >
    <a-descriptions bordered :column="1">
      <a-descriptions-item label="资金账号" prop="fundAccount">
        {{ form.fundAccount }}
      </a-descriptions-item>
      <a-descriptions-item label="联系电话" prop="clientName">
        {{ form.mobilePhone }}
      </a-descriptions-item>
      <a-descriptions-item label="存管银行" prop="clientName">
        {{ form.bankName }}
      </a-descriptions-item>
      <a-descriptions-item label="备注" prop="clientName">
        {{ form.remark || '--' }}
      </a-descriptions-item>
      <a-descriptions-item
        label="操作人"
        prop="clientName"
        v-if="form.operator"
      >
        {{ form.operator }}
      </a-descriptions-item>
    </a-descriptions>
    <template slot="footer">
      <a-button type="primary" @click="close">关闭</a-button>
    </template>
  </a-modal>
</template>
<script>
import { formatMobile } from '@/utils/parameter.js'
export default {
  name: 'depositoryFail_detail',
  data() {
    return {
      form: {}, //表单数据,
    }
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow
      },
      set(val) {
        this.$emit('update:isPopShow', val) // 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.query()
      }
    },
  },
  methods: {
    close() {
      this.showPop = false
    },
    query() {
      this.form = this.parameterData
      this.form.mobilePhone = formatMobile(this.parameterData.mobilePhone)
    },
  },
}
</script>

<style lang="scss" scoped>
.ant-descriptions-bordered.ant-descriptions-item-content,
.ant-descriptions-bordered .ant-descriptions-item-label {
  min-width: 110px;
}
.ant-descriptions-item-content {
  word-break: break-all;
}
</style>
