<template>
  <a-modal
    :title="`三方存管变更失败`"
    :width="500"
    v-model="showPop"
    ok-text="保存"
    cancel-text="关闭"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="资金账号" prop="fundAccount">
        {{ form.fundAccount }}
      </a-form-model-item>
      <a-form-model-item label="联系电话" prop="mobilePhone">
        {{ form.mobilePhone }}
      </a-form-model-item>
      <a-form-model-item label="存管银行" prop="bankName">
        <a-select
          v-model="form.bankName"
          placeholder="请选择"
          show-search
          option-filter-prop="children"
          allowClear
          @change="onChangeBank"
        >
          <a-select-option
            v-for="(v, i) in bankList"
            :value="v.bankName"
            :key="i"
          >
            {{ v.bankName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="备注" prop="remark">
        <a-input v-model="form.remark" placeholder="备注"></a-input>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
// 默认表单属性
const defaultForm = {
  id: '',
  clientId: '', //客户号
  fundAccount: '', // 资金账号
  clientName: '', // 客户姓名
  mobilePhone: '', //联系电话
  bankNo: '', // 银行编号
  bankName: '', //银行名称
  bankRegStatus: '', //普通三方存管银行预指定状态
  bankRegDesc: '', //普通三方存管银行预指定状态描述
  createTime: '', //创建时间
  operateTime: '', //操作时间
  operator: '', //操作人
  remark: '', //备注
}

export default {
  name: 'bcPreBiz_Add',
  inject: ['api'],
  data() {
    return {
      form: Object.assign({}, defaultForm), //表单数据,
      rules: {
        bankName: [
          { required: true, message: '存管银行不能为空', trigger: 'blur' },
        ],
        remark: [{ required: false, trigger: 'blur' }],
      },
      // 异步加载
      confirmLoading: false,
    }
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
    bankList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow
      },
      set(val) {
        this.$emit('update:isPopShow', val) // visible 改变的时候通知父组件
      },
    },
    typeTitle() {
      return '添加'
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.clientId) {
        this.query()
      } else {
        this.reset()
      }
    },
  },
  methods: {
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields()
      this.form = Object.assign({}, defaultForm)
      this.confirmLoading = false
    },
    query() {
      this.reset()
      this.form = this.parameterData
    },
    // 提交
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.confirmLoading = true
        this.form.operator = this.$store.getters.userInfo?.userName || ''
        let param = JSON.parse(JSON.stringify(this.form))
        this.api.update(param).then((res) => {
          this.confirmLoading = false
          console.log('更新结果', res)
          if (res.code != 0) {
            return this.$message.error(`${res.msg}`)
          }
          this.$message.success(`三方存管绑定成功！`)
          this.showPop = false
          // 通知操作成功
          this.$emit('success')
          // 重置表单
          this.reset()
        })
      })
    },
    onChangeBank(value) {
      console.log('选择的存管银行: ', value)
      this.form.bankName = value.bankName
      this.form.bankNo = value.bankNo
    },
  },
}
</script>

<style lang="scss" scoped></style>
