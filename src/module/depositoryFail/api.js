// 加载对应的装饰器
import { Parameters } from '@u/decorator'

class api {
  /**
   * 三方存管预指定任务更新
   * - clientId {Integer} 客户号
   * - fundAccount {String} 资金账号
   * - clientName {String} 客户姓名
   * - mobilePhone {String} 联系电话
   * - bankName {String} 存管银行名称
   * - bankNo {String} 存管银行编号
   * - bankRegStatus {String} 存管银行指定状态
   * - bankRegDesc {String} 存管银行指定状态描述
   * - operateTime {Date} 操作时间
   * - operator {String} 操作人
   * @param {Object} param
   */
  @Parameters([
    'clientId',
    'fundAccount',
    'clientName',
    'mobilePhone',
    'bankName',
    'bankNo',
    'bankRegStatus',
    'bankRegDesc',
    'operateTime',
    'operator',
  ])
  update() {
    return this.services.initGet({
      reqUrl: 'depository/audit/update',
      param: this.param,
    })
  }

  /**
   * 银行列表查询
   * @param {Object} param
   */
  @Parameters([])
  selectBcBankBybankNo() {
    return this.bffservices.initGet({
      reqUrl: 'bffbcBank/selectBcBankBybankNo',
      param: this.param,
    })
  }
}

export default new api()
