<template>
	<a-card title="协议字串替换" :bordered="false">
		<div class="access-table">
			<tk-table
				ref="table"
				:intercept-response="intercept_response"
				:tableData.sync="columns"
				:tableFromFilter="tableFormFilter"
				getMethods="bc-manage-server/bcAgreeContentsReplace/page"
				:isSelected="true"
				:isPaging="true"
				:selectedRowKeys.sync="selectedRowKeys"
				tableId="id"
			>
				<div class="table-button-area" slot="tableHeader">
					<a-button icon="plus" type="primary" @click="add">
						新增
					</a-button>
					<a-button
						icon="delete"
						type="danger"
						:disabled="selectedRowKeys.length <= 0"
						@click="remove"
					>
						删除
					</a-button>
					<a-button
						icon="download"
						type="primary"
						@click="downloadExcel"
					>
						导入
					</a-button>
					<a-button icon="upload" type="primary" @click="exportExcel">
						导出
					</a-button>
				</div>
				<template slot="operation" slot-scope="data">
					<a-button type="link" @click.stop="modify(data)">
						修改
					</a-button>
				</template>
			</tk-table>
		</div>

		<add :isPopShow.sync="isAddPopShow" @success="query" />
		<edit
			:isPopShow.sync="isEditPopShow"
			@success="query"
			:parameterData="selectData"
		/>
		<upload :visible.sync="uploadShow" @success="query"/>
	</a-card>
</template>

<script>
	// 引入添加和编辑弹窗
	import add from "./module/add";
	import edit from "./module/edit";
	import upload from "./module/upload";
	import { request } from "bus-common-component/lib/extension";

	import api from "./api";
	export default {
		data() {
			return {
				columns: [
					// 循环
					{
						field: "agreeId",
						label: "协议id",
						isSorter: true,
						width: 80
					},
					{
						field: "agreeTitle",
						label: "协议标题",
						width: 140
					},
					{
						field: "contextKey",
						label: "上下文键",
						width: 140
					},
					{
						field: "remark",
						label: "备注",
						width: 220
					},
					{
						field: "thirdKey",
						label: "三方系统key",
						width: 140
					},
					{
						field: "operation",
						label: "操作",
						align: "center",
						width: 140,
						fixed: "right"
					}
				],
				selectData: {},
				selectedRowKeys: [], // Check here to configure the default column
				isAddPopShow: false, //添加弹窗是否显示
				isEditPopShow: false, //修改弹窗是否显示
				uploadShow: false,
				fields: [
					{
						fieldAlign: "center",
						fieldDict: "",
						fieldFormat: "",
						fieldKey: "agreeId",
						fieldName: "协议id"
					},
					{
						fieldAlign: "center",
						fieldDict: "",
						fieldFormat: "",
						fieldKey: "agreeTitle",
						fieldName: "协议标题"
					},
					{
						fieldAlign: "center",
						fieldDict: "",
						fieldFormat: "",
						fieldKey: "contextKey",
						fieldName: "上下文键"
					},
					{
						fieldAlign: "center",
						fieldDict: "",
						fieldFormat: "",
						fieldKey: "remark",
						fieldName: "备注"
					},
					{
						fieldAlign: "center",
						fieldDict: "",
						fieldFormat: "",
						fieldKey: "thirdKey",
						fieldName: "三方系统key"
					}
				]
			};
		},
		provide: { api: api },
		components: { add, edit, upload },
		created() {
		},
		watch: {},
		methods: {
			// 导入downloadExcel
			downloadExcel() {
				this.uploadShow = true;
			},

			// 导出exportExcel
			exportExcel() {
				const service = new request({});
				let params = {
					fields: this.fields,
					fileName: "协议字串替换表",
					interfacePath:
						"http://bc-manage-server/bc-manage-server/bcAgreeContentsReplace/page",
					limit: 100,
					offset: 0,
					params: {}
				};
				service.download(
					"/work-manage-server/export/excel",
					{ ...params },
					"协议字串替换.xlsx"
				);
			},

			// 搜索框参数
			tableFormFilter(param) {
				return param;
			},

			query() {
				this.$refs.table.getTableData();
				this.selectedRowKeys = [];
			},

			reset() {
				// 没有选中对应的菜单时进行初始化重置
				// this.tableForm = JSON.parse(JSON.stringify(defaultForm));
			},

			// 点击新增按钮
			add() {
				this.isAddPopShow = true;
				this.selectData = {};
			},

			modify(data) {
				this.isEditPopShow = true;
				this.selectData = data;
			},

			remove() {
				if (this.selectedRowKeys.length > 0) {
					this.$confirm({
						title: "业务初始化",
						content: () => <p>确定删除当前数据?</p>,
						okText: "确定",
						cancelText: "取消",
						onOk: () => {
							api.deleteBcAgreeContentsReplace({
								bcAgreeContentsReplaceIds: this.selectedRowKeys.join(
									","
								)
							}).then(({ code, msg }) => {
								if (code != 0)
									return this.$message.error(`删除失败：${msg}`);
								this.$message.success("删除成功！");
								this.$refs.table.getTableData();
								this.selectedRowKeys = [];
							});
						}
					});
				}
			}
		}
	};
</script>

<style lang="sass" scoped>

</style>