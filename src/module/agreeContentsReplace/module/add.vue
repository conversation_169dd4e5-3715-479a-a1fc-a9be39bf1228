<template>
	<a-modal
		:title="`新建业务初始化配置`"
		:width="600"
		v-model="showPop"
		ok-text="确认"
		cancel-text="取消"
		@ok="submit"
		@cancel="reset"
		:maskClosable="false"
	>
		<a-form-model
			ref="form"
			:model="form"
			:label-col="{ span: 8 }"
			:wrapper-col="{ span: 14 }"
		>
			<a-form-model-item label="协议id" prop="agreeTitle">
				<a-input v-model="form.agreeId"></a-input>
			</a-form-model-item>
            <a-form-model-item label="协议标题" prop="agreeTitle">
				<a-input v-model="form.agreeTitle"></a-input>
			</a-form-model-item>
            <a-form-model-item label="上下文键" prop="agreeTitle">
				<a-input v-model="form.contextKey"></a-input>
			</a-form-model-item>
            <a-form-model-item label="备注" prop="agreeTitle">
				<a-input v-model="form.remark"></a-input>
			</a-form-model-item>
            <a-form-model-item label="三方系统key" prop="agreeTitle">
				<a-input v-model="form.thirdKey"></a-input>
			</a-form-model-item>
		</a-form-model>
	</a-modal>
</template>

<script>
	// 默认表单属性
	const defaultForm = {
        agreeId:'',
        agreeTitle:'',
        contextKey:'',
        remark:'',
        thirdKey:'',
	};

	export default {
		name: "businessInit_add",
		inject: ["api"],
		data() {
			return {
				form: Object.assign({}, defaultForm), //表单数据,
				// rules: {
				// 	bizType: [
				// 		{
				// 			required: true,
				// 			message: "业务类型不能为空",
				// 			trigger: "blur"
				// 		}
				// 	],
				// 	bizRiskLevel: [
				// 		{
				// 			required: true,
				// 			message: "业务风险等级不能为空",
				// 			trigger: "blur"
				// 		}
				// 	]
				// },
				// 异步加载
				confirmLoading: false,
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			// 是否展示添加弹窗
			visible: {
				type: Boolean,
				default: false
			},
			// 修改时传入参数
			parameterData: {
				type: Object,
				default: () => {}
			},
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			},
			typeTitle() {
				return "添加";
			}
		},
		watch: {
			isPopShow(n) {
				if (n) {
					this.query();
				} else {
					this.reset();
				}
				this.$nextTick(() => {});
			}
		},
		methods: {
			// 重置对应的表单
			reset() {
				// 重置表单验证属性
				this.$refs.form && this.$refs.form.resetFields();
				this.form = Object.assign({}, defaultForm);
				this.confirmLoading = false;
			},
			query() {
			},
			// 提交数据权限分组创建
			submit() {
				this.$refs.form.validate(valid => {
					if (!valid) return;
					let callback = ({ code, msg }) => {
						this.confirmLoading = false;
						if (code != 0)
							return this.$message.error(
								`新增失败：${msg}`
							);
						this.$message.success(`新增成功！`);
						// 关闭弹窗
						this.showPop = false;
						// 通知操作成功
						this.$emit("success");
						// 重置表单
						this.reset();
					};
					this.api.addBcAgreeContentsReplace(this.form).then(callback);
				});
			}
		}
	};
</script>

<style lang="scss" scoped></style>
