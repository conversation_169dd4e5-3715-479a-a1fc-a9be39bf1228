// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

	/**
	 * 新增业务初始化配置
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	addBcAgreeContentsReplace() {
		return this.services.initPost({
			reqUrl: 'bcAgreeContentsReplace/add',
			param: this.param,
		});
	}

	/**
	 * 修改业务适当性
	 * @param {Object} param
	 * @returns {Promise}
	 */
	@Parameters(["_data"])
	editBcAgreeContentsReplace() {
		return this.services.initPost({
			reqUrl: 'bcAgreeContentsReplace/edit',
			param: this.param,
		});
	}

	/**
	 * 删除单个业务适当性
	 * @param {Object} param
	 * - bcBusinessEgliId {String} 参数主键ID
	 */
	@Parameters(['bcAgreeContentsReplaceIds'])
	deleteBcAgreeContentsReplace() {
		return this.services.initPost({
			reqUrl: 'bcAgreeContentsReplace/deletes',
			param: this.param,
		});
	}
}

export default new api();
