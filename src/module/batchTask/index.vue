<template>
	<a-layout class="menu-content">
		<a-layout-sider class="menu-tree-sider">
			<a-card :bordered="false">
				<template slot="title">
					<h4>业务类型</h4>
					<a-layout-header
						style="padding:0px 0px; background: #FFFFFF;"
					>
						<a-form-item prop="searchValue">
							<a-input-search
								v-model="searchValue"
								placeholder="请输入开通业务名称"
								@change="search"
								style="width: 150px"
								allowClear
							/>
						</a-form-item>
					</a-layout-header>
				</template>
				<div class="menu-left-tree">
					<tkTightTree @select="select" @rightClick="rightClick">
						<tk-tree
							class="ant-tree-switcher-no"
							:treeData="cacheTreeData"
							:replaceFields="replaceFields"
							:isSelected="true"
							:checkedKeys.sync="selectedKeys"
						></tk-tree>
					</tkTightTree>
					<treeOper
						:isPopShow.sync="treeOperationShow"
						@success="getTreeData"
						:parameterData="rightMenu"
						:operationType="operationType"
					/>
				</div>
			</a-card>
		</a-layout-sider>
		<a-layout-content class="menu-right-content">
			<a-card title="任务列表" :bordered="false">
				<dataTable
					:regId="regId"
					:labelList="cacheTreeData"
					:bizList="bizList"
					:selectedKeys="selectedKeys"
					@success="getListOption"
				/>
			</a-card>
		</a-layout-content>
	</a-layout>
</template>

<script>
	import treeOper from "./module/treeOper"; // 引入分组右击组件
	import dataTable from "./module/table"; // 引入右侧表格
	import api from "./api";

	export default {
		components: { treeOper, dataTable },
		provide() {
			return { api };
		},
		data() {
			return {
				searchValue: "", //左侧菜单搜索值
				regId: "", // 用户选中项菜单
				cacheTreeData: [], //缓存查询结果集
				treeData: [], // 树状列表填充数据
				rightMenu: {}, // 右击对象
				// 树状列表调整对应的key
				replaceFields: {
					children: "children",
					title: "regName",
					key: "regId"
				},
				selectedKeys: [],
				treeOperationShow: false, // 协议分组添加弹窗是否显示
				operationType: "add",
				bizList: []
			};
		},
		watch: {
			treeData(val) {
				this.cacheTreeData = val.filter(v =>
					this.searchValue ? v.regName.includes(this.searchValue) : true
				);
			}
		},
		created() {
			// this.getTreeData();
			this.getListOption();
		},
		methods: {
			getListOption() {
				api.getBcBizTypeList({queryType:'1'}).then(data => {
					this.bizList = data.data.map(item => ({
						label: item.bizName,
						value: item.bizType
					}));
					this.treeData = data.data.map(item =>
						this.formatConversion(item)
					);
				});
			},

			search() {
				this.cacheTreeData = this.treeData.filter(v =>
					this.searchValue ? v.regName.includes(this.searchValue) : true
				);
			},
			select({ regId }) {
				// 赋值
				this.regId = regId;
			},
			rightClick(data) {
				this.rightMenu = data;
			},
			// 查询当前菜单栏树状列表
			getTreeData() {
				api.getBcBusinessShelfList({ isShelfed: "1" }).then(
					({ code, data }) => {
						if (code != 0) return;
						this.treeData = data.map(item =>
							this.formatConversion(item)
						);
						this.treeData = data;
					}
				);
			},
			formatConversion(item) {
				item["scopedSlots"] = { icon: "custom" };
				item["itemIcon"] = "file";
				item.regName = `${item.bizName}(${item.bizCount})`;
				item.regId = item.bizType;
				return item;
			}
		}
	};
</script>

<style lang="scss" scoped></style>
