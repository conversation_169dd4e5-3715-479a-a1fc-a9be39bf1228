<template>
	<div>
		<a-modal
			title="修改"
			:visible="showPop"
			:destroyOnClose="true"
			@ok="submit"
			@cancel="closePop"
			class="ant_modal_bigtable"
		>
			<a-form-model
				ref="form"
				:model="form"
				:label-col="{ span: 8 }"
				:wrapper-col="{ span: 14 }"
			>
				<a-form-model-item label="任务名称" prop="taskName">
					<a-input
						placeholder="请输入任务名称"
						v-model="form.taskName"
					></a-input>
				</a-form-model-item>
				<a-form-model-item label="业务类型" prop="bizType">
					<a-select
						allowClear
						show-search
						optionFilterProp="children"
						placeholder="请选择业务类型"
						v-model="form.bizType"
						:options="bizList"
					></a-select>
				</a-form-model-item>
				<a-form-model-item label="交易日执行" prop="isTradeDateExe">
					<a-switch
						v-model="form.isTradeDateExe"
						checked-children="开"
						un-checked-children="关"
					></a-switch>
				</a-form-model-item>
				<a-form-model-item label="允许执行时间段" prop="isTradeDateExe">
					<div class="flexItem">
						<a-time-picker
							placeholder="允许开始时间"
							:default-value="moment(form.enBeginTime, 'HH:mm')"
							format="HH:mm"
							@change="beginDateOnChange"
						/>
						<a-time-picker
							placeholder="允许结束时间"
							:default-value="moment(form.enEndTime, 'HH:mm')"
							format="HH:mm"
							@change="endDateOnChange"
						/>
					</div>
				</a-form-model-item>
				<a-form-model-item label="任务有效" prop="taskStatus">
					<a-switch
						v-model="form.taskStatus"
						checked-children="开"
						un-checked-children="关"
					></a-switch>
				</a-form-model-item>
				<a-form-model-item label="任务服务URI" prop="taskStatus">
					<a-input
						placeholder="请输入任务执行服务URI"
						v-model="form.callUri"
					></a-input>
				</a-form-model-item>
				<a-form-model-item label="是否轮询" prop="isLoop">
					<a-switch
						v-model="form.isLoop"
						checked-children="开"
						un-checked-children="关"
					></a-switch>
				</a-form-model-item>
				<a-form-model-item
					v-if="form.isLoop"
					label="轮询次数"
					prop="taskStatus"
				>
					<a-select
						placeholder="轮询次数"
						style="width: 180px"
						allowClear
						v-model="form.loopTimes"
						:options="loopOptions"
					></a-select>
				</a-form-model-item>
				<a-form-model-item
					v-if="form.isLoop"
					label="轮询间隔"
					prop="taskStatus"
				>
					<a-input
						placeholder="请输入轮询间隔"
						style="width: 180px"
						v-model.number="form.loopInterval"
						suffix="单位/秒"
						@input="validInput"
					></a-input>
				</a-form-model-item>
				<a-form-model-item label="是否并行" prop="isConcurrency">
					<a-switch
						v-model="form.isConcurrency"
						checked-children="开"
						un-checked-children="关"
					></a-switch>
				</a-form-model-item>
				<a-form-model-item label="失败驳回" prop="isFailreject">
					<a-switch
						v-model="form.isFailreject"
						checked-children="开"
						un-checked-children="关"
					></a-switch>
				</a-form-model-item>
			</a-form-model>
		</a-modal>
	</div>
</template>

<script>
	import moment from "moment";

	const loopOptions = [
		{ label: "1次", value: "1" },
		{ label: "3次", value: "3" },
		{ label: "10次", value: "10" },
		{ label: "无限制", value: "-1" }
	];
	export default {
		name: "data_add",
		inject: ["api"],
		data() {
			return {
				loopOptions,
				form: {
					taskName: "",
					bizType: undefined,
					isLoop: false,
					loopTimes: undefined,
					loopInterval: "",
					isConcurrency: false,
					callUri: "",
					taskStatus: "",
					isFailreject: false,
					enBeginTime: "09:00",
					enEndTime: "16:00",
					isTradeDateExe: true
				}
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			requestId: {
				type: String
			},
			bizList: {
				type: Array
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		watch: {
			isPopShow() {
                this.form['bizType'] = this.$attrs.bizType
            }
		},
		methods: {
			validInput(){
				this.form.loopInterval = this.form.loopInterval.replace(/[^\d]/g, '');
			},
			moment,
			closePop() {
				this.showPop = false;
			},
			updateList() {
                this.showPop = false;
                this.$refs.form.resetFields();
				this.$emit("success");
			},
			beginDateOnChange(date, dateString) {
				this.form.enBeginTime = dateString;
			},
			endDateOnChange(date, dateString) {
				this.form.enEndTime = dateString;
			},
			submit() {
				let params = {
					taskName: this.form.taskName,
					bizType: this.form.bizType,
					isLoop: this.form.isLoop ? "1" : "0",
					loopTimes: this.form.loopTimes,
					loopInterval: this.form.loopInterval,
					isConcurrency: this.form.isConcurrency ? "1" : "0",
					callUri: this.form.callUri,
					taskStatus: this.form.taskStatus ? "1" : "0",
					isFailreject: this.form.isFailreject ? "1" : "0",
					enBeginTime:
						this.form.enBeginTime.split(":")[0] +
						this.form.enBeginTime.split(":")[1],
					enEndTime:
						this.form.enEndTime.split(":")[0] +
						this.form.enEndTime.split(":")[1],
					isTradeDateExe: this.form.isTradeDateExe ? "1" : "0"
				};
				this.api.getBcBatchTaskAdd(params).then(() => {
					this.updateList();
				});
			}
		}
	};
</script>

<style scoped>
	.ant-layout {
		background-color: #fff;
	}
	.ant-layout-header {
		background-color: #fff;
	}
	.flexItem {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
</style>
