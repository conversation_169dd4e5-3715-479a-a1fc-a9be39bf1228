<template>
	<div>
		<a-modal
			:width="1100"
			v-model="showPop"
			:destroyOnClose="true"
			title="参数配置"
			ok-text="保存"
			cancel-text="取消"
			@ok="submit"
			:maskClosable="false"
		>
			<a-layout class="menu-content" style="background: #fff;">
				<a-tabs tab-position="top" type="line" v-model="tabIndex">
					<a-tab-pane key="1" tab="输入参数">
						<div class="ant-table-title">
							<a-button
								type="primary"
								icon="plus"
								@click="add('添加入参')"
								>新增</a-button
							>
							<a-button
								icon="delete"
								type="danger"
								:disabled="selectedRowKeys.length <= 0"
								@click.stop="remove"
								>删除</a-button
							>
						</div>
						<a-table
							ref="table1"
							:row-selection="rowSelection1"
							:columns="columns1"
							:data-source="data1"
							:pagination="false"
						>
							<a-switch
								slot="isNeed"
								slot-scope="item"
								v-model="item.isNeed"
							></a-switch>
							<span slot="inSource" slot-scope="item">{{
								item.inSource === "1"
									? "受理上下文"
									: "任务出参"
							}}</span>
						</a-table>
					</a-tab-pane>
					<a-tab-pane key="2" tab="输出参数">
						<div class="ant-table-title">
							<a-button
								type="primary"
								icon="plus"
								@click="add('添加出参')"
								>新增</a-button
							>
							<a-button
								icon="delete"
								type="danger"
								:disabled="selectedRowKeys.length <= 0"
								@click.stop="remove"
								>删除</a-button
							>
						</div>
						<a-table
							ref="table2"
							:row-selection="rowSelection2"
							:columns="columns2"
							:data-source="data2"
							:pagination="false"
						>
							<a-switch
								slot="isNeed"
								slot-scope="item"
								v-model="item.isNeed"
							></a-switch>
						</a-table>
					</a-tab-pane>
				</a-tabs>
			</a-layout>
		</a-modal>
		<ContextTransfer
			:isPopShow.sync="isPopShowTransfer"
			:includeKeys="includeKeys"
			:title="transferTitle"
			:data="transferData"
			@update="update"
		/>
	</div>
</template>

<script>
	import ContextTransfer from "./ContextTransfer";
	export default {
		name: "data_add",
		inject: ["api"],
		components: {
			ContextTransfer
		},
		data() {
			return {
				tabIndex: "1",
				transferData: [],
				includeKeys: [],
				selectedRowKeys: [],
				isPopShowTransfer: false,
				transferTitle: "",
				tableForm: {},
				columns1: [
					{
						key: "inKey",
						title: "参数key",
						dataIndex: "inKey"
					},
					{
						key: "inDes",
						title: "参数名",
						dataIndex: "inDes"
					},
					{
						key: "isNeed",
						title: "是否必填",
						scopedSlots: { customRender: "isNeed" }
					},
					{
						key: "inSource",
						title: "入参来源",
						scopedSlots: { customRender: "inSource" }
					},
					{
						key: "inSourceTaskName",
						title: "来源任务",
						dataIndex: "inSourceTaskName"
					}
				],
				columns2: [
					{
						key: "outKey",
						title: "参数key",
						dataIndex: "outKey"
					},
					{
						key: "outDes",
						title: "参数名",
						dataIndex: "outDes"
					},
					{
						key: "isNeed",
						title: "是否必填",
						scopedSlots: { customRender: "isNeed" }
					}
				],
				data1: [],
				data2: []
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			requestId: {},
			bizType: {},
			bizList: {
				type: Array
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			},
			rowSelection1() {
				return {
					onChange: (selectedRowKeys, selectedRows) => {
						this.selectedRowKeys = selectedRows.map(item => item.id);
					}
				};
			},
			rowSelection2() {
				return {
					onChange: (selectedRowKeys, selectedRows) => {
						this.selectedRowKeys = selectedRows.map(item => item.id);
					}
				};
			},
			rowSelection3() {
				return {
					onChange: (selectedRowKeys, selectedRows) => {
						this.selectedRowKeys = selectedRows.map(item => item.id);
					}
				};
			}
		},
		watch: {
			isPopShow() {
				this.updataData();
			},
			tabIndex() {
				this.getTableDate();
				this.getTransferDate();
				this.selectedRowKeys = [];
			}
		},
		methods: {
			updataData() {
				this.getTransferDate();
				this.getTableDate();
			},
			closePop() {
				this.showPop = false;
			},
			updateList() {
				this.showPop = false;
				this.$emit("success");
			},
			getTableDate() {
				if (this.tabIndex == "1") {
					this.api
						.getBcBatchTaskInList({ taskId: this.requestId })
						.then(data => {
							this.data1 = data.data;
							this.data1.forEach(item => {
								if (item.isNeed === "0") {
									item.isNeed = false;
								} else if (item.isNeed === "1") {
									item.isNeed = true;
								}
							});
							this.includeKeys = this.data1.map(item => item.inKey);
						});
				} else if (this.tabIndex == "2") {
					this.api
						.getBcBatchTaskOutList({ taskId: this.requestId })
						.then(data => {
							this.data2 = data.data;
							this.data2.forEach(item => {
								if (item.isNeed === "0") {
									item.isNeed = false;
								} else if (item.isNeed === "1") {
									item.isNeed = true;
								}
							});
							this.includeKeys = this.data2.map(item => item.outKey);
						});
				}
			},
			getTransferDate() {
				let params = { bizType: this.bizType };
				if (this.tabIndex === "1") {
					Object.assign(params, {
						taskSort: 0
					});
					this.api.getBcBatchTaskOutParamList(params).then(data => {
						this.transferData = data.data;
					});
				} else {
					this.api.getBcBatchTaskOutAllParams(params).then(data => {
						this.transferData = data.data;
					});
				}
			},
			update(data) {
				if (this.tabIndex == "1") {
					let params = data.map(item => ({
						taskId: this.requestId,
						inKey: item.key,
						inDes: item.title,
						inSource: item.source,
						inSourceTaskId: item.sourceTaskId,
						isNeed: "0"
					}));
					this.api.getBcBatchTaskInAdds(params).then(() => {
						this.getTableDate();
					});
				} else if (this.tabIndex == "2") {
					let params = data.map(item => ({
						taskId: this.requestId,
						outKey: item.key,
						outDes: item.title,
						isNeed: "0"
					}));
					this.api.getBcBatchTaskOutAdds(params).then(() => {
						this.getTableDate();
					});
				}
			},
			add(title) {
				this.transferTitle = title;
				this.isPopShowTransfer = true;
			},
			remove() {
				this.$confirm({
					title: "删除参数",
					content: () => <p>确定删除？</p>,
					okText: "确定",
					cancelText: "取消",
					onOk: () => {
						let callBack = ({ code, msg }) => {
							if (code != 0)
								return this.$message.error(`删除失败：${msg}`);
							this.$message.success("删除成功！");
							this.getTableDate();
							this.selectedRowKeys = [];
						};
						if (this.tabIndex == "1") {
							this.api
								.getBcBatchTaskInDeletes({
									ids: this.selectedRowKeys.join(",")
								})
								.then(callBack);
						} else if (this.tabIndex == "2") {
							this.api
								.getBcBatchTaskOutDeletes({
									ids: this.selectedRowKeys.join(",")
								})
								.then(callBack);
						}
					}
				});
			},
			submit() {
				if (this.tabIndex == "1") {
					let params = this.data1.map(item => ({
						taskId: this.requestId,
						id: item.id,
						inKey: item.inKey,
						inDes: item.inDes,
						inSource: item.inSource,
						inSourceTaskId: item.sourceTaskId,
						isNeed: item.isNeed ? "1" : "0"
					}));
					this.api.getBcBatchTaskInEdits(params).then(() => {
                        this.getTableDate();
                        this.showPop = false
					});
				} else if (this.tabIndex == "2") {
					let params = this.data2.map(item => ({
						taskId: this.requestId,
						id: item.id,
						outKey: item.outKey,
						outDes: item.outDes,
						outSort: item.outSort,
						isNeed: item.isNeed ? "1" : "0"
					}));
					this.api.getBcBatchTaskOutEdits(params).then(() => {
                        this.getTableDate();
                        this.showPop = false
					});
                }
			}
		}
	};
</script>

<style scoped>
	.ant-layout {
		background-color: #fff;
	}
	.ant-layout-header {
		background-color: #fff;
	}
	.loopItem {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
</style>
