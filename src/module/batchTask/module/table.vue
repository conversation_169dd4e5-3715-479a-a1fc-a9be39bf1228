<template>
	<div class="menu-right-limits">
		<!-- 添加修改 -->
		<dataAdd
			v-bind="$attrs"
			:isPopShow.sync="isAddPopShow"
			:requestId="registerId"
            :bizType="regId"
			:labelList="labelList"
			@success="query"
		/>
		<dataEdit
            v-bind="$attrs"
			:isPopShow.sync="isEditPopShow"
			:requestId="registerId"
			:labelList="labelList"
			:parameterData="selectData"
			@success="query"
		/>
		<task-list :isPopShow.sync="isTaskPopShow" :requestId="taskId" :bizType="regId"></task-list>
		<upload :visible.sync="uploadShow" uploadUrl="bcBatchTask/import" @success="query"/>
		<tk-table
			ref="table"
			:tableData.sync="comlun"
			:intercept-response="intercept_response"
			getMethods="bc-manage-server/bcBatchTask/list"
			:isSelected="true"
			:isPaging="true"
			:tableFrom="tableForm"
			:selectedRowKeys.sync="selectedRowKeys"
			tableId="id"
			:isTableLoading="false"
		>
			<div class="table-buttun-area" slot="tableHeader">
				<a-button
					:disabled="!regId"
					type="primary"
					icon="plus"
					@click="add"
				>
					新增
				</a-button>
				<a-button
					icon="delete"
					type="danger"
					:disabled="selectedRowKeys.length <= 0"
					@click="remove"
				>
					删除
				</a-button>
				<a-button icon="upload" :disabled="!selectedKeys.length" type="primary" @click="exportExcel">
					批量导出
				</a-button>
				<a-button icon="download" type="primary" @click="uploadShow = true">
					导入
				</a-button>	
			</div>
			<template slot="operation" slot-scope="data">
				<a-button type="link" @click.stop="modify(data)">
					修改
				</a-button>
                <a-button
					type="link"
					@click="openConfig(data)"
				>
					配置
				</a-button>
			</template>
		</tk-table>
	</div>
</template>

<script>
	import dataAdd from "./dataAdd";
	import dataEdit from "./dataEdit";
	import taskList from "./taskList";
	// 引入导入弹窗
	import upload from '@c/upload/upload';
	export default {
		data() {
			return {
				// 表格展示字段
				comlun: [
					// 循环
					{
						field: "id",
						label: "任务ID",
						// width: 100,
						isSorter: true
					},
					{
						field: "taskName",
						label: "任务名",
						// width: 200,
						isSorter: true
					},
					{
						field: "isTradeDateExe",
						label: "交易日执行",
                        isSorter: false,
                        filter: item => (item === "1" ? "是" : "否")
					},
					{
						field: "taskStatus",
						label: "是否有效",
						isSorter: false,
						filter: item => (item === "1" ? "是" : "否")
					},
					{
						field: "taskSort",
						label: "任务顺序",
						// width: 200,
						isSorter: true
					},
					{
                        field: "updateTime",
                        width: 200,
						label: "更新时间"
					},
					{
						field: "operation",
						label: "操作",
						align: "center",
						fixed: "right"
					}
				],
				// 查询接口所需字段
				tableForm: {},
				operationType: "add",
				// 是否显示弹窗
				isAddPopShow: false,
                isEditPopShow: false,
                isTaskPopShow: false,
				//查看弹窗
				isLookPopShow: false,
				selectData: {}, // 所要修改的数据
				selectedRowKeys: [], // 当前用户选中参数
                registerId: undefined,
                taskId:undefined,
				uploadShow: false,
			};
		},
		inject: ["api"],
		components: { dataAdd, dataEdit, taskList, upload },
		props: {
			// 数据权限分组编号
			regId: {
				type: String
			},
			labelList: {
				type: Array
			},
			selectedKeys: {
				type: Array
			},
		},
		created() {
			// 初始给与值
			this.tableForm["bizType"] =  this.regId;
		},
		watch: {
			regId: {
				handler(val) {
					this.tableForm["bizType"] = val;
					this.$refs.table.getTableData();
					this.selectedRowKeys = [];
				},
				deep: true
			}
		},
		methods: {
			exportExcel() {
				this.$confirm({
					title: "导出跑批任务",
					content: () => <p>确定导出您所选中跑批任务吗？</p>,
					okText: "确定",
					cancelText: "取消",
					onOk: () => {
						window.location.href="/bc-manage-server/bcBatchTask/export?bizTypes="+ this.selectedKeys.join(',')
					}
				});
			},
			add() {
				//点击新增按钮
				this.isAddPopShow = true;
			},
			// 点击修改按钮
			modify(data) {
				this.isEditPopShow = true;
				this.selectData = JSON.parse(JSON.stringify(data));
			},
			// 查看
			look(data) {
				this.isLookPopShow = true;
				this.agreeCode = data.agreeCode;
            },
            // 配置
            openConfig(data) {
                this.taskId = data.id
                this.isTaskPopShow = true
            },
			// 操作成功
			query() {
				this.selectedRowKeys = [];
				this.$refs.table.getTableData();
				this.$emit('success')
			},
			// 删除对应的权限
			remove() {
				this.$confirm({
					title: "删除任务",
					content: () => <p>确定删除当前任务？</p>,
					okText: "确定",
					cancelText: "取消",
					onOk: () => {
						this.api
							.getBcBatchTaskDeletes({
								ids: this.selectedRowKeys.join(",")
							})
							.then(({ code, msg }) => {
								if (code != 0)
									return this.$message.error(
										`删除任务失败：${msg}`
									);
								this.$message.success("删除任务成功！");
								this.$refs.table.getTableData();
								this.selectedRowKeys = [];
								this.$emit('success')
							});
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped></style>
