// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
	 * 获取业务类型名称list
	 * @param {Object} param
	 */
	@Parameters(['queryType'])
	getBcBizTypeList() {
		return this.services.initGet({
			reqUrl: 'bcBusiness/list',
			param: this.param,
		});
	}

	/**
	 * 查询已上架的业务
	 * @description:
	 * @param {*}
	 */
	@Parameters(['isShelfed'])
	getBcBusinessShelfList() {
		return this.services.initGet({
			reqUrl: 'bcBusinessShelf/list',
			param: this.param,
		});
	}

	/**
	 * 新增任务
	 * @description:
	 * @param {*}
	 */
	@Parameters([
		'taskName',
		'bizType',
		'isLoop',
		'loopTimes',
		'loopInterval',
		'isConcurrency',
		'callUri',
		'taskStatus',
		'isFailreject',
		'enBeginTime',
		'enEndTime',
		'isTradeDateExe',
	])
	getBcBatchTaskAdd() {
		return this.services.initPost({
			reqUrl: 'bcBatchTask/add',
			param: this.param,
		});
	}

	/**
	 * 修改任务
	 * @description:
	 * @param {*}
	 */
	@Parameters([
		'id',
		'taskName',
		'bizType',
		'isLoop',
		'loopTimes',
		'loopInterval',
		'isConcurrency',
		'callUri',
		'taskStatus',
		'isFailreject',
		'enBeginTime',
		'enEndTime',
		'isTradeDateExe',
		'taskSort'
	])
	getBcBatchTaskEdit() {
		return this.services.initPost({
			reqUrl: 'bcBatchTask/edit',
			param: this.param,
		});
	}

	/**
	 * 批量删除任务
	 * @description:
	 * @param {*}
	 */
	@Parameters(['ids'])
	getBcBatchTaskDeletes() {
		return this.services.initPost({
			reqUrl: 'bcBatchTask/deletes',
			param: this.param,
		});
	}

	/**
	 * 查询任务列表
	 * @description:
	 * @param {*}
	 */
	@Parameters(['bizType'])
	getBcBatchTaskList() {
		return this.services.initGet({
			reqUrl: 'bcBatchTask/list',
			param: this.param,
		});
	}

	/**
	 * 当前业务所有上下文及taskSort前置任务出参(默认上下文中所有)
	 * @description:
	 * @param {*}
	 */
	@Parameters(['taskSort', 'bizType'])
	getBcBatchTaskOutParamList() {
		return this.services.initGet({
			reqUrl: 'bcBatchTaskOut/paramList',
			param: this.param,
		});
	}

	/**
	 * 全量元数据
	 * @description:
	 * @param {*}
	 */
	@Parameters([])
	getBcBatchTaskOutAllParams() {
		return this.services.initGet({
			reqUrl: 'bcBatchTaskOut/allParams',
			param: this.param,
		});
	}

	/**
	 * 批量添加任务入参
	 * @description:
	 * @param {*}
	 */
	@Parameters(['_data'])
	getBcBatchTaskInAdds() {
		return this.services.initPost({
			reqUrl: 'bcBatchTaskIn/adds',
			param: this.param,
		});
	}

	/**
	 * 批量修改任务入参
	 * @description:
	 * @param {*}
	 */
	@Parameters(['_data'])
	getBcBatchTaskInEdits() {
		return this.services.initPost({
			reqUrl: 'bcBatchTaskIn/edits',
			param: this.param,
		});
	}

	/**
	 * 批量删除任务入参
	 * @description:
	 * @param {*}
	 */
	@Parameters(['ids'])
	getBcBatchTaskInDeletes() {
		return this.services.initPost({
			reqUrl: 'bcBatchTaskIn/deletes',
			param: this.param,
		});
	}

	/**
	 * 任务入参列表
	 * @description:
	 * @param {*}
	 */
	@Parameters(['taskId'])
	getBcBatchTaskInList() {
		return this.services.initGet({
			reqUrl: 'bcBatchTaskIn/list',
			param: this.param,
		});
	}

	/**
	 * 批量添加任务出参
	 * @param {Object} param
	 * - _data 不走装饰器参数拦截
	 */
	@Parameters(['_data'])
	getBcBatchTaskOutAdds() {
		return this.services.initPost({
			reqUrl: 'bcBatchTaskOut/adds',
			param: this.param,
		});
	}

	/**
	 * 批量修改任务出参
	 * @description:
	 * @param {*}
	 */
	@Parameters(['_data'])
	getBcBatchTaskOutEdits() {
		return this.services.initPost({
			reqUrl: 'bcBatchTaskOut/edits',
			param: this.param,
		});
	}

	/**
	 * 批量删除任务出参
	 * @description:
	 * @param {*}
	 */
	@Parameters(['ids'])
	getBcBatchTaskOutDeletes() {
		return this.services.initPost({
			reqUrl: 'bcBatchTaskOut/deletes',
			param: this.param,
		});
	}

	/**
	 * 任务出参列表
	 * @description:
	 * @param {*}
	 */
	@Parameters(['taskId'])
	getBcBatchTaskOutList() {
		return this.services.initGet({
			reqUrl: 'bcBatchTaskOut/list',
			param: this.param,
		});
	}

	/**
	 * 批量添加结果定义
	 * @description:
	 * @param {*}
	 */
	@Parameters(['_data'])
	getBcBatchResultAdds() {
		return this.services.initPost({
			reqUrl: 'bcBatchResult/adds',
			param: this.param,
		});
	}

	/**
	 * 批量修改结果定义
	 * @description:
	 * @param {*}
	 */
	@Parameters(['_data'])
	getBcBatchResultEdits() {
		return this.services.initPost({
			reqUrl: 'bcBatchResult/edits',
			param: this.param,
		});
	}

	/**
	 * 批量删除结果定义
	 * @description:
	 * @param {*}
	 */
	@Parameters(['ids'])
	getBcBatchResultDeletes() {
		return this.services.initPost({
			reqUrl: 'bcBatchResult/deletes',
			param: this.param,
		});
	}

	/**
	 * 任务结果定义列表
	 * @description:
	 * @param {*}
	 */
	@Parameters(['bizType'])
	getBcBatchResultList() {
		return this.services.initGet({
			reqUrl: 'bcBatchResult/list',
			param: this.param,
		});
	}
}

export default new api();
