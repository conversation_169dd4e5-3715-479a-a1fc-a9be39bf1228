<template>
	<div>
		<a-modal
			:width="1100"
			title="跑批详情"
			:visible="showPop"
			:destroyOnClose="true"
			:footer="null"
			@cancel="closePop"
			class="ant_modal_bigtable"
		>
			<a-layout class="menu-content" style="background: #fff;">
				<a-table
					:columns="columns"
					:data-source="data"
					:pagination="false"
				>
					<template slot="taskStatus" slot-scope="text, record">
						{{
							taskStatue.filter(it => it.value === record.taskStatus)[0]
								.label
						}}
					</template>
					<template slot="operation" slot-scope="text, record">
						<a-button type="link" @click.stop="reDo(record)" v-permissionCheck="'validBcBatchTaskIns'">
							重新执行
						</a-button>
						<a-button type="link" @click.stop="pass(record)" v-permissionCheck="'validTaskInsJump'">
							跳过
						</a-button>
						<a-button type="link" @click.stop="detail(record)">
							查看任务配置
						</a-button>
						<a-button type="link" @click.stop="show(record,'入参')">
							任务入参
						</a-button>
						<a-button type="link" @click.stop="show(record,'出参')">
							任务出参
						</a-button>
					</template>
				</a-table>
			</a-layout>
			<taskDetail :isPopShow.sync="detailShowPop" :taskId="taskId" />
			<taskInOrOut v-if="paramShowPop" :isPopShow.sync="paramShowPop" :type="type" :taskId="taskId" :formId="formId"/>
			<editData :isPopShow.sync="showEditPop" :taskInfo="taskInfo" :taskParam="taskParam" @success="getDetail"></editData>
		</a-modal>
	</div>
</template>
<script>
	import taskDetail from "./taskDetail";
	import taskInOrOut from "./taskInOrOut";
	import editData from './editData'
	const taskStatue = [
		{ value: "0", label: "待处理" },
		{ value: "1", label: "处理中" },
		{ value: "2", label: "驳回" },
		{ value: "3", label: "成功" },
		{ value: "4", label: "失败" },
		{ value: "5", label: "跳过" },
		{ value: "6", label: "暂停" }
	];
	export default {
		components: { taskDetail,taskInOrOut,editData },
		inject: ["api"],
		data() {
			return {
				taskStatue,
				taskId: "",
				detailShowPop: false,
				data: [],
				columns: [
					{
						key: "taskName",
						title: "任务名",
						dataIndex: "taskName"
					},
					{
						key: "taskStatus",
						title: "任务状态",
						dataIndex: "taskStatus",
						scopedSlots: { customRender: "taskStatus" }
					},
					{
						key: "errorMsg",
						title: "错误信息",
						dataIndex: "errorMsg"
					},
					{
						key: "createTime",
						title: "首次执行时间",
						dataIndex: "createTime"
					},
					{
						key: "updateTime",
						title: "更新时间",
						dataIndex: "updateTime"
					},
					{
						title: "操作",
						scopedSlots: { customRender: "operation" }
					}
				],
				paramShowPop: false,
				type: '',
				formId:'',
				taskParam:[],
				taskInfo:{},
				showEditPop: false
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			requestId: {
				type: String,
				default: ""
			},
			bizType: {
				type: String,
				default: ""
			}
		},
		mounted() {},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		methods: {
			show(data,type){
				this.taskId = data.taskId
				this.type = type
				this.paramShowPop = true
			},
			updateList() {
				this.showPop = false;
				this.$emit("success");
			},
			//关闭
			closePop() {
				this.showPop = false;
			},
			//提交
			submit() {},

			reDo(data) {
				this.api
					.reDoTaskIns({ bizType:this.bizType,taskId:data.taskId, formId: this.formId })
					.then(({ code, msg }) => {
						if (code != 0)
							return this.$message.error(`操作失败：${msg}`);
						this.$message.success("操作成功！");
						this.getDetail();
					});
			},

			pass(data) {
				this.api.bcBatchTaskOutList({taskId:data.taskId}).then(outRes=>{
					if(outRes.code!==0) return this.$message.error(`操作失败：${outRes.msg}`);
					if(outRes.data.length!==0){
						this.taskParam = outRes.data.map(item=>{
                                            return {
                                                title: item.outDes,
                                                key: item.outKey,
                                                value: '',
												isNeed: item.isNeed
                                            }
                                        })
                        this.taskInfo = data
                        this.showEditPop = true
					}else{
						this.api
						.taskInsJump({ bizType:this.bizType,taskId:data.taskId,taskInsId: data.id, formId: this.formId,taskInsOutList:[]})
						.then(({ code, msg }) => {
							if (code != 0)
								return this.$message.error(`操作失败：${msg}`);
							this.$message.success("操作成功！");
							this.getDetail();
						});
					}
				})

				
			},

			getDetail() {
				let _this = this;
				this.api
					.bcBatchTaskInsList({ formId: this.requestId, bizType: this.bizType })
					.then(data => {
						_this.data = data.data;
					});
				this.formId = this.requestId
			},

			detail(data) {
                this.taskId = data.taskId
				this.detailShowPop = true;
			}
		},
		watch: {
			isPopShow(n) {
				if(n){
					this.getDetail();
				}
			}
		}
	};
</script>
<style lang="scss" scoped>
.ant_modal_bigtable ::v-deep .ant-btn {
	margin: 0 10px;
	padding: 0;
}
</style>