<template>
  <div>
    <a-modal
      title="修改跑批任务出参"
      :visible="showPop"
      @ok="getValue"
      @cancel="closePop"
    >
      <template slot="footer">
        <a-button key="back" @click="closePop">
          取消
        </a-button>
        <a-button type="primary" key="submit" @click="getValue">
          修改
        </a-button>
      </template>
      <a-layout>
        <a-layout-content
          class="pop_content"
          style="min-height: calc(200px - 120px);"
        >
          <a-row v-for="(item, index) in info" :key="index">
            <a-col :span="6" class="label_style">
              <label for=""
                >{{ item.title }}{{ item.isNeed === "1" ? "*" : "" }}</label
              >
            </a-col>
            <a-col :span="18">
              <a-input
                v-model="item.value"
                :placeholder="'请输入' + item.title"
                type="text"
              />
            </a-col>
          </a-row>
        </a-layout-content>
      </a-layout>
    </a-modal>
  </div>
</template>
<script>
export default {
  name: "editData",
  data() {
    return {
      info: [],
    };
  },
  inject: ["api"],
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    taskInfo: {
      type: Object,
      default: () => {},
    },
    taskParam: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        console.log(val);
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.info = this.taskParam;
      }
    },
  },
  methods: {
    //关闭
    closePop() {
    this.info = []
      this.showPop = false;
    },
    //提交
    getValue() {
      let flag = this.info.some((item) => item.isNeed === "1" && !item.value);
      if (flag) {
        this.$message.error("请输入必填入参");
        return false;
      }
      let taskList = this.info.filter((item) => item.value);
      let taskInsOutList = taskList.map((item) => {
        return {
          outKey: item.key,
          outValue: item.value,
        };
      });
      this.api
        .taskInsJump({
          bizType: this.taskInfo.bizType,
          taskId: this.taskInfo.taskId,
          taskInsId: this.taskInfo.id,
          formId: this.taskInfo.formId,
          taskInsOutList,
        })
        .then(({ code, msg }) => {
          if (code != 0) return this.$message.error(`操作失败：${msg}`);
          this.closePop()
          this.$message.success("操作成功！");
          this.$emit("success");
        });
    },
  },
};
</script>
<style scoped>
</style>
