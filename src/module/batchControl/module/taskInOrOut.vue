<template>
  <a-modal
    :title="'查看'+type"
    :width="1000"
    v-model="showPop"
    :footer="null"
    :maskClosable="false"
  >
    <div class="access-tabl">
      <tk-table
        ref="table"
        :intercept-response="intercept_response"
        :tableData.sync="columns"
        :getMethods="type==='入参'?'bc-manage-server/bcBatchTaskIn/queryTaskInValueList':'bc-manage-server/bcBatchTaskOut/queryTaskOutValueList'"
        :isSelected="false"
        :isPaging="true"
        :tableFrom="tableForm"
        tableId="id"
        :isTableLoading="true"
        style="min-height:300px;"
      >
      </tk-table>
    </div>
  </a-modal>
</template>
<script>
export default {
  name: 'taskInOrOut',
  inject: ['api'],
  data() {
    return {
      form: {}, //表单数据,
      columns: [],
      tableForm: {
        formId: '',
        taskId: ''
      }
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false
    },
    // 传入参数
    type: {
      type: String
    },
    taskId: {
      type: Number
    },
    formId: {
      type: String
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // 改变的时候通知父组件
      }
    }
  },
  created(){
    this.columns = this.type==='入参'?[
      {
        field: 'inKey',
        label: '入参key',
        isSorter: false
      },
      {
        field: 'inDes',
        label: '入参描述',
        isSorter: false
      },
      {
        field: 'inValue',
        label: '入参值',
        isSorter: false
      }
    ]:[
      {
        field: 'outKey',
        label: '出参key',
        isSorter: false
      },
      {
        field: 'outDes',
        label: '出参描述',
        isSorter: false
      },
      {
        field: 'outValue',
        label: '出参值',
        isSorter: false
      }
    ]
    this.tableForm.formId = this.formId
    this.tableForm.taskId = this.taskId
    this.$refs.table && this.$refs.table.getTableData();
  },
  methods: {
  }
};
</script>

<style lang="scss" scoped></style>
