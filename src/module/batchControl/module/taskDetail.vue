<template>
	<div>
		<a-modal
			title="任务详情"
			:visible="showPop"
			:destroyOnClose="true"
			@ok="submit"
			@cancel="closePop"
			class="ant_modal_bigtable"
		>
			<a-form-model
				ref="form"
				:model="form"
				:label-col="{ span: 8 }"
				:wrapper-col="{ span: 14 }"
			>
				<a-form-model-item label="任务名称" prop="taskName">
					<a-input
						placeholder="请输入任务名称"
						v-model="form.taskName"
						disabled
					></a-input>
				</a-form-model-item>
				<a-form-model-item label="业务类型" prop="bizType">
					<a-input
						placeholder="请输入任务名称"
						v-model="form.bizName"
						disabled
					></a-input>
				</a-form-model-item>
				<a-form-model-item label="交易日执行" prop="isTradeDateExe">
					<a-switch
						v-model="form.isTradeDateExe"
						checked-children="开"
						un-checked-children="关"
						disabled
					></a-switch>
				</a-form-model-item>
				<a-form-model-item label="允许执行时间段" prop="isTradeDateExe">
					<div class="flexItem">
						<a-input
							placeholder="请输入任务名称"
							v-model="form.enBeginTime"
							disabled
						></a-input>
						<a-input
							placeholder="请输入任务名称"
							v-model="form.enEndTime"
							disabled
						></a-input>
					</div>
				</a-form-model-item>
				<a-form-model-item label="任务有效" prop="taskStatus">
					<a-switch
						v-model="form.taskStatus"
						checked-children="开"
						un-checked-children="关"
						disabled
					></a-switch>
				</a-form-model-item>
				<a-form-model-item label="任务服务URI" prop="taskStatus">
					<a-input
						placeholder="请输入任务执行服务URI"
						v-model="form.callUri"
						disabled
					></a-input>
				</a-form-model-item>
				<a-form-model-item label="是否轮询" prop="isLoop">
					<a-switch
						v-model="form.isLoop"
						checked-children="开"
						un-checked-children="关"
						disabled
					></a-switch>
				</a-form-model-item>
				<a-form-model-item
					v-if="form.isLoop"
					label="轮询次数"
					prop="taskStatus"
				>
					<a-select
						placeholder="轮询次数"
						style="width: 180px"
						allowClear
						v-model="form.loopTimes"
						:options="loopOptions"
						disabled
					></a-select>
				</a-form-model-item>
				<a-form-model-item
					v-if="form.isLoop"
					label="轮询间隔"
					prop="taskStatus"
				>
					<a-input
						type="number"
						placeholder="请输入轮询间隔"
						style="width: 180px"
						v-model="form.loopInterval"
						suffix="单位/秒"
						disabled
					></a-input>
				</a-form-model-item>
				<a-form-model-item label="是否并行" prop="isConcurrency">
					<a-switch
						v-model="form.isConcurrency"
						checked-children="开"
						un-checked-children="关"
						disabled
					></a-switch>
				</a-form-model-item>
				<a-form-model-item label="失败驳回" prop="isFailreject">
					<a-switch
						v-model="form.isFailreject"
						checked-children="开"
						un-checked-children="关"
						disabled
					></a-switch>
				</a-form-model-item>
			</a-form-model>
		</a-modal>
	</div>
</template>
<script>
	const loopOptions = [
		{ label: "1次", value: "1" },
		{ label: "3次", value: "3" },
		{ label: "10次", value: "10" },
		{ label: "无限制", value: "-1" }
	];
	export default {
		inject: ["api"],
		data() {
			return {
				loopOptions,
				form: {
					taskName: "",
					bizName: undefined,
					isLoop: false,
					loopTimes: undefined,
					loopInterval: "",
					isConcurrency: false,
					callUri: "",
					taskStatus: "",
					isFailreject: false,
					enBeginTime: "",
					enEndTime: "",
					isTradeDateExe: true
				}
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			taskId: {
				default: ""
			}
		},
		mounted() {},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		methods: {
			updateList() {
				this.showPop = false;
				this.$emit("success");
			},
			//关闭
			closePop() {
				this.showPop = false;
			},
			//提交
			submit() {},

			getDetail() {
				this.api
					.bcBatchTaskQuery({ bcBatchTaskId: this.taskId })
					.then(data => {
						let res = data.data;
						this.form.taskName = res.taskName;
						this.form.bizName = res.bizName;
						this.form.isLoop = res.isLoop === "0" ? false : true;
						this.form.loopTimes = res.loopTimes;
						this.form.loopInterval = res.loopInterval;
						this.form.isConcurrency =
							res.isConcurrency === "0" ? false : true;
						this.form.callUri = res.callUri;
						this.form.taskStatus =
							res.taskStatus === "0" ? false : true;
						this.form.isFailreject =
							res.isFailreject === "0" ? false : true;
						this.form.isTradeDateExe =
							res.isTradeDateExe === "0" ? false : true;
						this.form.enBeginTime = res.enBeginTime ? res.enBeginTime.substring(0, 2) + ":" + res.enBeginTime.substring(2, 4) : "";
						this.form.enEndTime = res.enEndTime ? res.enEndTime.substring(0, 2) + ":" + res.enEndTime.substring(2, 4) : "";
					});
			}
		},
		watch: {
			isPopShow() {
				this.getDetail();
			}
		}
	};
</script>
<style scoped>
</style>