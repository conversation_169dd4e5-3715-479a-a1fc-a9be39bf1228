<template>
  <div>
    <a-modal
      title="查看跑批注册入参"
      :visible="showPop"
      :width="770"
      @cancel="closePop"
    >
      <template slot="footer">
        <a-button key="back" @click="closePop">
          关闭
        </a-button>
      </template>
      <tkJsonFormat
        id="json_format"
        :value.sync="regData"
        ref="jsonFormat"
        type="show"
      />
    </a-modal>
  </div>
</template>
<script>
export default {
  name: "regData",
  data() {
    return {
      regData: {},
    };
  },
  inject: ["api"],
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    requestId: {
      type: String,
      default: "",
    },
    bizType: {
      type: String,
      default: "",
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        console.log(val);
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.query();
      }
    },
  },
  methods: {
    //获取详情
    query() {
      this.api
        .bcBatchRegQuery({
          bizType: this.bizType,
          formId: this.requestId,
        })
        .then((res) => {
          console.log(res, "111");
          if (res.code != 0) return this.$message.error(`操作失败：${res.msg}`);
          this.$nextTick(() => {
            if (res.data.regData) {
              this.regData = JSON.parse(res.data.regData);
            }
          });
        });
    },
    //关闭
    closePop() {
      this.showPop = false;
      this.regData = {};
    },
  },
};
</script>
<style scoped></style>
