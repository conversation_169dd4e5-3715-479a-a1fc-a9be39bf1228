<template>
  <a-card title="跑批监控" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm
          @query="query"
          @reset="reset"
          layoutType="flex"
          :defaultShowRow="2"
        >
          <a-form-model-item label="客户号">
            <a-input
              placeholder="请输入客户号"
              v-model="tableForm.clientId"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="受理单编号">
            <a-input
              placeholder="请输入受理单编号"
              v-model="tableForm.formId"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="业务类型名">
            <a-input
              placeholder="请输入业务类型名"
              v-model="tableForm.bizName"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户姓名">
            <a-input
              placeholder="请输入客户姓名"
              v-model="tableForm.clientName"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="跑批状态">
            <a-select
              :options="projStatus"
              placeholder="请选择跑批状态"
              v-model="tableForm.projStatus"
            />
          </a-form-model-item>
          <a-form-model-item label="失败原因">
            <a-input
              v-model="tableForm.errorInfo"
              placeholder="请输入失败原因"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item label="失败原因过滤词">
            <a-input
              v-model="tableForm.errorInfoFilter"
              placeholder="请输入失败原因过滤词"
              allowClear
            />
          </a-form-model-item>
          <!-- <a-form-model-item label="跑批开始时间">
						<a-date-picker
							v-model="tableForm.beginTime"
							valueFormat="YYYY-MM-DD"
							placeholder="开始时间"
							@openChange="openChange"
						/>
					</a-form-model-item>
					<a-form-model-item label="跑批结束时间">
						<a-date-picker
							v-model="tableForm.endTime"
							valueFormat="YYYY-MM-DD"
							placeholder="结束时间"
							@openChange="openChange"
						/>
					</a-form-model-item> -->
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :intercept-response="intercept_response"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/bcBatchProjIns/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
      >
        <div class="table-button-area" slot="tableHeader"></div>
        <template slot="operation" slot-scope="data">
          <a-button
            type="link"
            @click.stop="stop(data)"
            v-permissionCheck="'validProjInsStop'"
          >
            终止
          </a-button>
          <a-button type="link" @click.stop="detail(data)"> 查看任务 </a-button>
          <a-button type="link" @click.stop="inject(data)">
            跑批注册入参
          </a-button>
        </template>
      </tk-table>
    </div>
    <batchDetail
      :isPopShow.sync="detailShowPop"
      :requestId="requestId"
      :bizType="bizType"
    />
    <regData
      :isPopShow.sync="regDataPop"
      :requestId="requestId"
      :bizType="bizType"
    />
  </a-card>
</template>

<script>
import batchDetail from "./module/batchDetail";
import regData from "./module/regData";
import moment from "moment";
import api from "./api";

const projStatus = [
  { value: "0", label: "待办" },
  { value: "1", label: "办理中" },
  { value: "2", label: "驳回" },
  { value: "3", label: "成功" },
  { value: "4", label: "失败" },
  { value: "5", label: "终止" },
  { value: "6", label: "中断" },
];

const defaultTableForm = {
  clientId: "",
  formId: "",
  bizName: "",
  clientName: "",
  projStatus: undefined,
};

export default {
  components: { batchDetail, regData },
  provide() {
    return { api };
  },
  data() {
    return {
      projStatus,
      requestId: "",
      tableForm: JSON.parse(JSON.stringify(defaultTableForm)),
      columns: [
        {
          field: "formId",
          label: "受理单编号",
          isSorter: true,
          width: 140,
        },
        {
          field: "bizName",
          label: "业务类型",
        },
        {
          field: "clientId",
          label: "客户标识",
        },
        {
          field: "clientName",
          label: "客户姓名",
        },
        {
          field: "projStatus",
          label: "跑批状态",
          filter: (item) => {
            return this.projStatus.filter((it) => it.value === item)[0].label;
          },
        },
        {
          field: "errorInfo",
          label: "失败原因",
          isEllipsis: true,
          width: 160,
        },
        {
          field: "updateTime",
          label: "更新时间",
        },
        /* {
						field: "errorInfo",
						label: "异常说明"
					}, */
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      selectedRowKeys: [],
      detailShowPop: false,
      bizType: "",
      regDataPop: false,
    };
  },
  methods: {
    moment,
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },

    // 搜索框参数
    tableFormFilter(param) {
      return param;
    },

    reset() {
      this.tableForm = JSON.parse(JSON.stringify(defaultTableForm));
      this.$refs.table.getTableData();
    },

    detail(data) {
      this.requestId = data.formId;
      this.bizType = data.bizType;
      this.detailShowPop = true;
    },

    stop(data) {
      this.$confirm({
        title: "终止任务",
        content: () => <p>确定终止任务?</p>,
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          api
            .projInsStop({
              bizType: data.bizType,
              formId: data.formId,
            })
            .then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`终止失败：${msg}`);
              this.$message.success("终止成功！");
              this.$refs.table.getTableData();
              this.selectedRowKeys = [];
            });
        },
      });
    },

    inject(data) {
      this.requestId = data.formId;
      this.bizType = data.bizType;
      this.regDataPop = true;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .access-table .ant-btn {
  padding: 0;
}
</style>
