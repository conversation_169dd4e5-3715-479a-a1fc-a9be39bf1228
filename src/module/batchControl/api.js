// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api { 

    /** 
    * 修改办理业务定义（用于终止）
    * @param {Object} param
    */
    @Parameters(['id','projStatus'])
    bcBatchProjInsEdit() {
      return this.services.initPost({ reqUrl:"bcBatchProjIns/edit", param: this.param});
    }                   

    /** 
    * 跑批详情
    * @param {Object} param
    */
   @Parameters(['_data'])
   bcBatchTaskInsList() {
     return this.services.initGet({ reqUrl:"bcBatchTaskIns/list", param: this.param});
   }    

   /** 
    * 跑批详情编辑
    * @param {Object} param
    */
   @Parameters(['_data'])
   bcBatchTaskInsEdit() {
     return this.services.initPost({ reqUrl:"bcBatchTaskIns/edit", param: this.param});
   }    
   
   /** 
    * 查看配置
    * @param {Object} param
    */
   @Parameters(['_data'])
   bcBatchTaskQuery() {
     return this.services.initGet({ reqUrl:"bcBatchTask/query", param: this.param});
   } 

   /** 
    * 查看跑批注册入参
    * @param {Object} param
    */
   @Parameters(['_data'])
   bcBatchRegQuery() {
     return this.services.initGet({ reqUrl:"bcBatchReg/query", param: this.param});
   } 

   /** 
    * 查询任务出参定义列表
    * @param {Object} param
    */
    @Parameters(['_data'])
    bcBatchTaskOutList() {
      return this.services.initGet({ reqUrl:"bcBatchTaskOut/list", param: this.param});
    }    
 
    /** 
     * 跑批实例终止
     * @param {Object} param
     */
    @Parameters(['_data'])
    projInsStop() {
      return this.beServices.initPost({ reqUrl:"batch/projInsStop", param: this.param});
    }

    /** 
     * 跑批任务跳过（带修改参数）
     * @param {Object} param
     */
    @Parameters(['_data'])
    taskInsJump() {
      return this.beServices.initPost({ reqUrl:"batch/taskInsJump", param: this.param});
    }

    /** 
     * 重新执行
     * @param {Object} param
     */
     @Parameters(['_data'])
     reDoTaskIns() {
       return this.beServices.initPost({ reqUrl:"batch/taskInsReExecute", param: this.param});
     }
}

export default new api();