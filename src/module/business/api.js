// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

    /**
    * 新增业务定义
    * @param {Object} param
    * - id {String} 唯一标识(UUID)
    * - bizType {String} 业务类型编号（前两位：投资者类型，第3位：系统编号，后3位：业务编号）
    * - bizName {String} 业务类型名
    * - sysNo {String} 系统编号
    * - investorType {String} 客户类型编号（01:个人、02:机构）
    * - remark {String} 备注信息
    */
    @Parameters(["id", "bizType", "bizName", "sysNo", "investorType", "remark"])
    addBcBusiness() {
      return this.services.initPost({ reqUrl:"bcBusiness/add", param: this.param});
    }

    /**
    * 修改业务定义
    * @param {Object} param
    * - id {String} 唯一标识(UUID)
    * - bizType {String} 业务类型编号（前两位：投资者类型，第3位：系统编号，后3位：业务编号）
    * - bizName {String} 业务类型名
    * - sysNo {String} 系统编号
    * - investorType {String} 客户类型编号（01:个人、02:机构）
    * - remark {String} 备注信息
    * @returns {Promise}
    */
    @Parameters(["id", "bizType", "bizName", "sysNo", "investorType", "remark"])
    editBcBusiness() {
     return this.services.initPost({ reqUrl:"bcBusiness/edit", param: this.param});
    }

    /**
    * 删除单个业务定义
    * @param {Object} param
    * - bcBusinessId {String} 参数主键ID
    */
    @Parameters(["bcBusinessIds"])
    deleteBcBusiness() {
      return this.services.initPost({ reqUrl:"bcBusiness/delete", param: this.param});
    }

    /**
    * 删除多个业务定义
    * @param {Object} param
    * - bcBusinessIds {String} 参数主键ID列表，多个以“，”隔开
    *
    */
    @Parameters(["bcBusinessIds"])
    deletesBcBusiness() {
      return this.services.initPost({ reqUrl:"bcBusiness/deletes", param: this.param});
    }

    /**
    * 查询业务定义翻页列表
    * @param {Object} param
    * - beginTime {String} 开始时间
    * - endTime {String} 结束时间
    * - pageSize {integer} 每页数量
    * - pageNumber {integer} 当前页码
    * - orderBy {String} 排序字段
    * - id {String} 唯一标识(UUID)
    * - bizType {String} 业务类型编号（前两位：投资者类型，第3位：系统编号，后3位：业务编号）
    * - bizName {String} 业务类型名
    * - sysNo {String} 系统编号
    * - investorType {String} 客户类型编号（01:个人、02:机构）
    */
    @Parameters(["beginTime", "endTime", "pageSize", "pageNum", "orderBy", "id", "bizType", "bizName", "sysNo", "investorType"])
    queryBcBusinessPage() {
      return this.services.initGet({ reqUrl: "bcBusiness/page", param: this.param });
    }

    /**
    * 查询指定业务定义详情
    * @param {Object} param
    * - bcBusinessId {String} 参数主键ID
    */
    @Parameters(["bcBusinessId"])
    queryBcBusinessDetail() {
      return this.services.initGet({ reqUrl: "bcBusiness/query", param: this.param});
    }

    /**
    * 根据业务类型导出业务流程配置
    * @param {Object} param
    * - bizTypes {String} 业务类型，多个逗号隔开
    *
    */
    @Parameters(["bizTypes"])
    exportConfig() {
      return this.bfServices.initPost({ reqUrl:"bfBusinessFlowPublish/exportConfig", param: this.param});
    }

}

export default new api();