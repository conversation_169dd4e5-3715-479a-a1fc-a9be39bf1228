<template>
  <a-modal
    :title="`查看业务定义`"
    :width="500"
    v-model="showPop"
    :maskClosable="false"
  >
    <a-descriptions bordered :column="1">
      <a-descriptions-item label="业务类型编号" prop="bizType">
        {{ form.bizType }}
      </a-descriptions-item>
      <a-descriptions-item label="业务类型名" prop="bizName">
        {{ form.bizName }}
      </a-descriptions-item>
      <a-descriptions-item label="系统编号" prop="sysNo">
        {{ form.sysNo }}
      </a-descriptions-item>
      <a-descriptions-item label="客户类型" prop="investorType">
        {{ form.investorType }}
      </a-descriptions-item>
      <a-descriptions-item label="备注信息" prop="remark">
        {{ form.remark }}
      </a-descriptions-item>
      <a-descriptions-item label="创建人" prop="createBy">
        {{ form.createBy }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间" prop="createDate">
        {{ form.createDate }}
      </a-descriptions-item>
      <a-descriptions-item label="修改人" prop="updateBy">
        {{ form.updateBy }}
      </a-descriptions-item>
      <a-descriptions-item label="修改时间" prop="updateDate">
        {{ form.updateDate }}
      </a-descriptions-item>
    </a-descriptions>
    <template slot="footer">
      <a-button type="primary" @click="ok">确定</a-button>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: 'business_detail',
  inject: ['api'],
  data() {
    return {
      form: {} //表单数据,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false
    },
    // 传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dictMap: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // 改变的时候通知父组件
      }
    }
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    }
  },
  methods: {
    ok() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    query() {
      this.reset();
      return this.api
        .queryBcBusinessDetail({ bcBusinessId: this.parameterData.id })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          this.dictMap['bc.common.investorType'].forEach((item) => {
            if (item.value === data.investorType) {
              data.investorType = item.label;
            }
          });
          this.form = data;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.ant-descriptions-bordered.ant-descriptions-item-content,
.ant-descriptions-bordered .ant-descriptions-item-label {
  min-width: 110px;
}
.ant-descriptions-item-content {
  word-break: break-all;
}
</style>
