<template>
  <a-modal :title="`${typeTitle}业务定义`" :width="500" v-model="showPop" ok-text="确认" cancel-text="取消" @ok="submit"
    @cancel="reset" :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :rules="rules">
      <a-form-model-item label="业务类型编号" prop="bizType">
        <a-input v-model="form.bizType" placeholder="请输入业务类型编号"></a-input>
      </a-form-model-item>
      <a-form-model-item label="业务类型名" prop="bizName">
        <a-input v-model="form.bizName" placeholder="请输入业务类型名"></a-input>
      </a-form-model-item>
      <a-form-model-item label="系统编号" prop="sysNo" v-show="false">
        <a-input v-model="form.sysNo" placeholder="请输入系统编号"></a-input>
      </a-form-model-item>
      <!-- <a-form-model-item label="客户类型编号" prop="investorType">
        <a-select
          v-model="form.investorType"
          :allowClear="true"
          :options="dictArray"
          placeholder="请选择客户类型"
        >
        </a-select>
      </a-form-model-item> -->
      <a-form-model-item label="备注信息" prop="remark">
        <a-input v-model="form.remark" placeholder="请输入备注信息"></a-input>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
// 默认表单属性
const defaultForm = {
  bizType: '', // 业务类型编号（前两位：投资者类型，第3位：系统编号，后3位：业务编号）
  bizName: '', // 业务类型名
  sysNo: '1', // 系统编号
  investorType: undefined, // 客户类型编号（01:个人、02:机构）
  remark: '' // 备注信息
};

export default {
  name: 'business_add',
  inject: ['api'],
  data() {
    return {
      form: Object.assign({}, defaultForm), //表单数据,
      rules: {
        bizType: [
          { required: true, message: '业务类型编号不能为空', trigger: 'blur' }
        ],
        bizName: [
          { required: true, message: '业务类型名不能为空', trigger: 'blur' }
        ],
        sysNo: [
          { required: true, message: '系统编号不能为空', trigger: 'blur' }
        ],
        // investorType: [
        //   { required: true, message: '客户类型不能为空', trigger: 'blur' }
        // ],
        remark: [
          { required: true, message: '备注信息不能为空', trigger: 'blur' }
        ]
      },
      // 异步加载
      confirmLoading: false,
      dictArray: []
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false
    },
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => { }
    },
    dictMap: {
      type: Object,
      default: () => { }
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      }
    },
    typeTitle() {
      return '添加';
    }
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.bcBusinessId) {
        this.query();
      } else {
        this.reset();
      }
      this.$nextTick(() => { });
    }
  },
  methods: {
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
      // this.dictArray = this.dictMap['bc.common.investorType'];
    },
    query() {
      this.reset();
      return this.api
        .queryBcBusinessDetail({
          bcBusinessId: this.parameterData.bcBusinessId
        })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          this.form = data;
        });
    },
    // 提交数据权限分组创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        // 重新赋值客户类型编号
        param.bizType = param.bizType.replace(/\s/g, '')
        param.investorType = param.bizType.substring(0, 2)
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(`业务定义${this.typeTitle}失败：${msg}`);
          this.$message.success(`业务定义${this.typeTitle}成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit('success');
          // 重置表单
          this.reset();
        };
        this.api.addBcBusiness(param).then(callback);
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
