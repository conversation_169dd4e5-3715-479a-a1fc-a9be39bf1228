<template>
	<a-card title="业务定义列表" :bordered="false">
		<div class="searchForm">
			<a-form-model layout="inline" :model="tableForm">
				<tkSelectForm @query="query" @reset="reset">
					<a-form-model-item label="编号">
						<a-input v-model="tableForm.id" placeholder="请输入编号"></a-input>
					</a-form-model-item>
					<a-form-model-item label="业务类型编号">
						<a-input v-model="tableForm.bizType" placeholder="请输入业务类型编号"></a-input>
					</a-form-model-item>
					<a-form-model-item label="业务类型名">
						<a-input v-model="tableForm.bizName" placeholder="请输入业务类型名"></a-input>
					</a-form-model-item>
					<a-form-model-item label="系统编号">
						<a-input v-model="tableForm.sysNo" placeholder="请输入系统编号"></a-input>
					</a-form-model-item>
					<a-form-model-item label="客户类型">
						<a-select show-search v-model="tableForm.investorType" :allowClear="true" :options="dictMap['investor_type']"
							placeholder="请选择客户类型">
						</a-select>
					</a-form-model-item>
				</tkSelectForm>
			</a-form-model>
		</div>
		<div class="access-table">
			<tk-table ref="table" :intercept-response="intercept_response" :tableData.sync="columns"
				:tableFromFilter="tableFormFilter" getMethods="bc-manage-server/bcBusiness/page" :isSelected="true"
				:isPaging="true" :tableFrom="tableForm" :selectedRowKeys.sync="selectedRowKeys" tableId="id">
				<div class="table-button-area" slot="tableHeader">
					<a-button icon="plus" type="primary" @click="add">
						新增
					</a-button>
					<a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove">
						删除
					</a-button>
					<a-button icon="download" type="primary" @click="downloadExcel(0)">
						导入
					</a-button>
					<a-button icon="upload" type="primary" @click="exportExcel">
						导出
					</a-button>
					<a-button icon="download" type="primary" @click="downloadExcel(1)">
						导入配置
					</a-button>
					<a-button icon="upload" type="primary" @click="exportConfig(1)" :disabled="selectedRowKeys.length <= 0">
						批量导出配置
					</a-button>
				</div>
				<template slot="operation" slot-scope="data">
					<a-button style="margin: 0 !important;" type="link" @click.stop="look(data)">
						查看
					</a-button>
					<a-button style="margin: 0 !important;" type="link" @click.stop="modify(data)">
						修改
					</a-button>
					<a-button style="margin: 0 !important;" type="link" @click.stop="config(data)">
						配置
					</a-button>
					<a-button style="margin: 0 !important;" type="link" @click.stop="exportConfig(0, data)">
						导出配置
					</a-button>
				</template>
			</tk-table>
			<add :isPopShow.sync="isAddPopShow" @success="query" :parameterData="selectData" :dictMap="dictMap" />
			<edit :isPopShow.sync="isEditPopShow" @success="query" :parameterData="selectData" :dictMap="dictMap" />
			<LookComponent :isPopShow.sync="isLookPopShow" :parameterData="selectData" :dictMap="dictMap" />
		</div>
		<upload :visible.sync="uploadShow" :downloadeUrl="downloadeUrl" :uploadUrl="uploadUrl" :baseUrl="baseUrl" downloadeTitle="业务类型模板文件"
			@success="query" />
	</a-card>
</template>

<script>
// 引入添加和编辑弹窗
import add from "./module/add";
import edit from "./module/edit";
// 引入导入弹窗
import upload from '@c/upload/upload';
// 引入查看弹窗
import LookComponent from "./module/detail";
import api from "./api";
import { request } from 'bus-common-component/lib/extension';

// 默认表单属性
const defaultForm = {
	id: "", // 编号
	bizType: "", // 业务类型编号（前两位：投资者类型，第3位：系统编号，后3位：业务编号）
	bizName: "", // 业务类型名
	sysNo: "", // 系统编号
	investorType: undefined // 客户类型编号（01:个人、02:机构）
};

export default {
	name: "business",
	data() {
		return {
			downloadeUrl: '',
			uploadUrl: 'bcBusiness/import',
			baseUrl: '/bc-manage-server',
			math: Math.random(), // 随机标识-标记当前右击菜单唯一性
			// 表头数据
			columns: [
				// 循环
				{
					field: "id",
					label: "编号",
					isSorter: true,
					width: 100
				},
				{
					field: "bizType",
					label: "业务类型编号",
					isSorter: false,
					width: 120
				},
				{
					field: "bizName",
					label: "业务类型名",
					isSorter: false,
					width: 160
				},
				{
					field: "sysNo",
					label: "系统编号",
					isSorter: false,
					width: 130
				},
				{
					field: "investorType",
					label: "客户类型",
					isSorter: false,
					width: 130,
					filter: item => this.getDictText("bc.common.investorType", item)
				},
				{
					field: "remark",
					label: "备注信息",
					isSorter: false,
					width: 160
				},
				{
					field: "createBy",
					label: "创建人",
					isSorter: false,
					width: 130
				},
				{
					field: "createDate",
					label: "创建时间",
					isSorter: true,
					width: 150,
					isEllipsis: true
					// filter: (item) =>
					//   !item ? '--' : this.DateFormat(item).format('yyyy-MM-dd')
				},
				{
					field: "updateBy",
					label: "修改人",
					isSorter: false,
					width: 130
				},
				{
					field: "updateDate",
					label: "修改时间",
					isSorter: true,
					width: 150,
					isEllipsis: true
					// filter: (item) =>
					//   !item ? '--' : this.DateFormat(item).format('yyyy-MM-dd')
				},
				{
					field: "operation",
					label: "操作",
					align: "center",
					width: 300,
					fixed: "right"
				}
			],
			tableForm: {
				// id: '', // 编号
				bizType: "", // 业务类型编号（前两位：投资者类型，第3位：系统编号，后3位：业务编号）
				bizName: "", // 业务类型名
				sysNo: "", // 系统编号
				investorType: undefined // 客户类型编号（01:个人、02:机构）
			},
			selectedRowKeys: [], // Check here to configure the default column
			//新增弹窗
			isAddPopShow: false, //添加弹窗是否显示
			isEditPopShow: false, //修改弹窗是否显示
			//查看弹窗
			isLookPopShow: false,
			selectData: {},
			operationType: "add",
			uploadShow: false,
			dictMap: {
				'bc.common.investorType': []
			}
		};
	},
	provide: { api: api },
	created() {
		Promise.all([this.queryDictMap()]).finally();
	},
	components: { add, edit, LookComponent, upload },
	methods: {
		config(data) {
			window.open(`/bf-manage-view/serviceDesign?bizType=${data.bizType}&bizName=${data.bizName}`, '_blank')
		},
		exportConfig(type, data) {
			let bizTypes = type
				? this.$refs.table.data
					.filter((item) => this.selectedRowKeys.includes(item.id))
					.map((item) => item.bizType)
					.join(",")
				: data.bizType;
			//导出配置
			this.$confirm({
				title: "导出配置",
				content: () => <p>确定导出您所选中配置吗？</p>,
				okText: "确定",
				cancelText: "取消",
				onOk: () => {
				return new Promise((resolve, reject) => {
					new request({
					option: window.microEncryption?.openEncryption
						? window.microEncryption?.config
						: undefined,
					})
					.download(
						"/bf-manage-server/bfBusinessFlowPublish/exportConfig",
						{
						bizTypes,
						fileType: 'json'
						},
						"业务配置.json"
					)
					.then((data) => {
						if (data && data.code !== 0 && data.msg) {
						this.$message.error(`导出数据失败：${data.msg}`);
						reject();
						return;
						}
						resolve();
					})
					.catch(reject);
				});
				}
			});
		},
		query() {
			this.$refs.table.getTableData();
			this.selectedRowKeys = [];
		},
		// 重置
		reset() {
			// 没有选中对应的菜单时进行初始化重置
			this.tableForm = JSON.parse(JSON.stringify(defaultForm));
		},
		queryDictMap() {
			return new Promise(resolve => {
				Object.keys(this.dictMap).forEach(item => {
					this.$dict.dictContent(item).then(data => {
						this.dictMap[item] = (data || []).map(data => {
							return {
								label: data.dictLabel,
								value: data.dictValue
							};
						});
						resolve();
					});
				});
			});
		},
		getDictText(key, value) {
			let results = this.dictMap[key] || [];
			results = results.filter(item => {
				return item.value == value;
			});
			return (
				(results && results.length && results[0].label) || value || ""
			);
		},
		// 点击新增按钮
		add() {
			this.isAddPopShow = true;
			this.selectData = {};
		},
		// 点击修改按钮
		modify(data) {
			this.isEditPopShow = true;
			this.selectData = data;
		},
		// 查看
		look(data) {
			this.isLookPopShow = true;
			this.selectData = data;
		},
		// openChange(status) {
		//   if (status == false) {
		//     // 切出事件，用于组件缓冲数据
		//     setTimeout(() => {
		//       if (this.tableForm.beginTime && this.tableForm.endTime) {
		//         if (
		//           Date.parse(this.DateFormat(this.tableForm.beginTime)) >
		//           Date.parse(this.DateFormat(this.tableForm.endTime))
		//         )
		//           return this.$message.error('结束日期不应早于开始日期');
		//       }
		//     });
		//   }
		// },
		// 搜索框参数
		tableFormFilter(param) {
			return param;
		},
		// 删除
		remove() {
			if (this.selectedRowKeys.length > 0) {
				this.$confirm({
					title: "业务定义",
					content: () => <p>确定删除当前业务定义数据?</p>,
					okText: "确定",
					cancelText: "取消",
					onOk: () => {
						api.deletesBcBusiness({
							bcBusinessIds: this.selectedRowKeys.join(",")
						}).then(({ code, msg }) => {
							if (code != 0)
								return this.$message.error(
									`删除业务定义失败：${msg}`
								);
							this.$message.success("删除业务定义成功！");
							this.$refs.table.getTableData();
							this.selectedRowKeys = [];
						});
					}
				});
			}
		},
		// 导入downloadExcel
		downloadExcel(type) {
			this.baseUrl = !type ? '/bc-manage-server' : '/bf-manage-server'
			this.uploadUrl = !type ? 'bcBusiness/import' : 'bfBusinessFlowPublish/importConfig'
			this.uploadShow = true;
		},
		// 导出exportExcel
		exportExcel() {
			window.location.href = '/bc-manage-server/bcBusiness/export';
		},
	}
};
</script>

<style lang="scss" scoped></style>
