<template>
	<div>
		<a-modal
			title="客户融资融券信用详情"
			:visible="showPop"
			:width="1400"
			:footer="null"
			@cancel="closePop"
			class="ant_modal_bigtable"
		>
			<a-table
				:columns="columns1"
				:data-source="tableData"
				:pagination="false"
			>
			</a-table>
			<h3 style="margin-top: 30px">
				持有上市公司限售股份详情
			</h3>
			<a-table
				:columns="columns_1"
				:data-source="tableData1"
				:pagination="false"
			>
			</a-table>
			<h3 style="margin-top: 30px">
				任职上市公司高管详情
			</h3>
			<a-table
				:columns="columns_2"
				:data-source="tableData2"
				:pagination="false"
			>
			</a-table>
			<h3 style="margin-top: 30px">
				上市公司持股5%以上股东详情
			</h3>
			<a-table
				:columns="columns_3"
				:data-source="tableData3"
				:pagination="false"
			>
			</a-table>
			<h3 style="margin-top: 30px">关联账户详情</h3>
			<a-table
				:columns="columns_4"
				:data-source="tableData4"
				:pagination="false"
			>
			</a-table>
		</a-modal>
	</div>
</template>

<script>
export default {
	data() {
		return {
			tableData: [],
			columns1: [
				{
					title: '客户编号',
					dataIndex: 'clientId',
					key: 'clientId',
				},
				{
					title: '办理业务',
					dataIndex: 'bizName',
					key: 'bizName',
					width: 200,
				},
				{
					title: '限售股份持有情况',
					dataIndex: 'companyLimitsellHolderJson',
					key: 'companyLimitsellHolderJson',
					customRender: (item) =>
						JSON.parse(item).answer
							? '持有'
							: '未持有',
				},
				{
					title: '高管标识',
					dataIndex: 'marketCompanyExecvJson',
					key: 'marketCompanyExecvJson',
					customRender: (item) =>
						JSON.parse(item).answer
							? '是'
							: '不是',
				},
				{
					title: '是否持股5%以上',
					dataIndex: 'applyMajorStockContext',
					key: 'applyMajorStockContext',
					customRender: (item) =>
						JSON.parse(item).answer
							? '是'
							: '不是',
				},
				{
					title: '有无关联账户',
					dataIndex: 'applyCompanyContext',
					key: 'applyCompanyContext',
					customRender: (item) =>
						JSON.parse(item).answer
							? '有'
							: '无',
				},
				{
					title: '更新时间',
					dataIndex: 'updateTime',
					key: 'updateTime',
					width: 200,
				},
				{
					title: '站点信息',
					dataIndex: 'opStation',
					key: 'opStation',
					width: 400,
				},
			],
			columns_1: [
				{
					title: '证券代码',
					dataIndex: 'stockCode',
					key: 'stockCode',
				},
				{
					title: '证券名称',
					dataIndex: 'stockName',
					key: 'stockName',
				},
				{
					title: '持有数量',
					dataIndex: 'number',
					key: 'number',
				},
				{
					title: '时间',
					dataIndex: 'openDate',
					key: 'openDate',
				},
			],
			columns_2: [
				{
					title: '上市公司证券代码',
					dataIndex: 'stockCode',
					key: 'stockCode',
				},
				{
					title: '上市公司名称',
					dataIndex: 'companyName',
					key: 'companyName',
				},
				{
					title: '职位',
					dataIndex: 'duty',
					key: 'duty',
				},
				{
					title: '任职截止日期',
					dataIndex: 'employmentPeriod',
					key: 'employmentPeriod',
				},
			],
			columns_3: [
				{
					title: '证券代码',
					dataIndex: 'stockCode',
					key: 'stockCode',
				},
				{
					title: '持有数量',
					dataIndex: 'number',
					key: 'number',
				},
				{
					title: '股东账户',
					dataIndex: 'fundAccount',
					key: 'fundAccount',
				},
			],
			columns_4: [
				{
					title: '关联人姓名',
					dataIndex: 'affiliatedName',
					key: 'affiliatedName',
				},
				{
					title: '关联人证件号码',
					dataIndex: 'idNo',
					key: 'idNo',
				},
				{
					title: '关联人关系',
					dataIndex: 'affiliatedRelation',
					key: 'affiliatedRelation',
				},
				{
					title: '股东账户',
					dataIndex: 'fundAccount',
					key: 'fundAccount',
				},
			],
			tableData1: [],
			tableData2: [],
			tableData3: [],
			tableData4: [],
		};
	},
	props: {
		isPopShow: {
			type: Boolean,
			default: false,
		},
		detail: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		showPop: {
			get() {
				return this.isPopShow;
			},
			set(val) {
				this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
			},
		},
	},
	watch: {
		isPopShow(n) {
			if (n) {
				this.renderingView();
			}
			this.$nextTick(() => (this.editorShow = n));
		},
	},
	methods: {
		closePop() {
			this.showPop = false;
		},

		renderingView() {
			this.tableData = [this.detail];
			this.tableData1 = JSON.parse(
				this.detail.companyLimitsellHolderJson
			).answerDetail;
			this.tableData2 = JSON.parse(
				this.detail.marketCompanyExecvJson
			).answerDetail;
			this.tableData3 = JSON.parse(
				this.detail.applyMajorStockContext
			).answerDetail;
			this.tableData4 = JSON.parse(
				this.detail.applyCompanyContext
			).answerDetail;
		},
	},
};
</script>

<style scoped>
.ant-layout {
	background-color: #fff;
}
.ant-layout-header {
	background-color: #fff;
}
</style>
