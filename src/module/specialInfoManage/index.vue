<template>
  <a-card title="董监高管理" :bordered="false">
    <detail :isPopShow.sync="showDetail" :detail="dateDetail" />
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset(0)">
          <a-form-model-item label="客户号">
            <a-input
              placeholder="请输入客户号"
              v-model="tableForm.clientId"
              allowClear
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="业务类型">
            <a-select
              v-model="tableForm.bizType"
              placeholder="请选择业务类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="(v, i) in bizTypeList"
                :value="v.value"
                :key="i"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="受理起止时间">
            <a-range-picker
              v-model="acceptCompTimePicker"
              @change="onChange"
              :format="timeFormat"
              :show-time="timePickerOptions"
            />
          </a-form-model-item>
          <!-- <a-form-model-item label="起始时间">
            <a-date-picker
              v-model="tableForm.beginTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              placeholder="起始时间"
            />
          </a-form-model-item>
          <a-form-model-item label="截止时间">
            <a-date-picker
              v-model="tableForm.endTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              placeholder="截止时间"
            />
          </a-form-model-item> -->
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :intercept-response="intercept_response"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/credit/queryExtensionPage"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        :isSelected="true"
        tableId="id"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button
            icon="upload"
            :disabled="selectedRowKeys.length <= 0"
            type="primary"
            @click="exportExcel"
          >
            导出
          </a-button>
          <a-button icon="upload" type="primary" @click="exportExcelAll">
            全部导出
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="modify(data)"> 详情 </a-button>
        </template>
      </tk-table>
    </div>
  </a-card>
</template>

<script>
// 引入添加和编辑弹窗
import api from "./api";
import moment from "moment";
import detail from "./detail";

export default {
  components: { detail },
  data() {
    return {
      showDetail: false,
      acceptCompTimePicker: [
        moment().subtract(6, "months").set({ hour: 0, minute: 0, second: 0 }),
        moment().set({ hour: 23, minute: 59, second: 59 }),
      ],
      timeFormat: "YYYY/MM/DD HH:mm:ss",
      timePickerOptions: {
        format: "HH:mm:ss", // 设置时间格式为小时:分钟:秒
        defaultValue: [
          moment("00:00:00", "HH:mm:ss"),
          moment("23:59:59", "HH:mm:ss"),
        ], // 设置默认结束时间为23:59
      },
      selectedRowKeys: [],
      dateDetail: {},
      columns: [
        // 循环
        {
          field: "clientId",
          label: "客户编号",
          isSorter: true,
        },
        {
          field: "bizName",
          label: "办理业务",
          isSorter: false,
        },
        {
          field: "companyLimitsellHolderJson",
          label: "限售股份持有情况",
          filter: (item) => (JSON.parse(item).answer ? "持有" : "未持有"),
        },
        {
          field: "marketCompanyExecvJson",
          label: "高管标识",
          filter: (item) => (JSON.parse(item).answer ? "是" : "不是"),
        },
        {
          field: "applyMajorStockContext",
          label: "是否持股5%以上",
          filter: (item) => (JSON.parse(item).answer ? "是" : "不是"),
        },
        {
          field: "applyCompanyContext",
          label: "有无关联账户",
          filter: (item) => (JSON.parse(item).answer ? "有" : "无"),
        },
        {
          field: "updateTime",
          label: "更新时间",
        },
        {
          field: "acceptCompTime",
          label: "受理时间",
        },
        {
          field: "opStation",
          label: "站点信息",
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: true,
        },
      ],
      isBuffetList: this.$baseDict.base.isBuffetList, //方式列表
      tableForm: {
        clientId: "",
        bizType: undefined,
        beginTime: moment()
          .subtract(6, "months")
          .set({ hour: 0, minute: 0, second: 0 })
          .format("YYYY/MM/DD HH:mm:ss"),
        endTime: moment()
          .set({ hour: 23, minute: 59, second: 59 })
          .format("YYYY/MM/DD HH:mm:ss"),
      },
      bizTypeList: [
        { value: "010221", name: "开通信用北交所权限" },
        { value: "010174", name: "两融预约开户" },
        { value: "30072", name: "PC两融开户" },
      ],
    };
  },
  provide: { api: api },
  methods: {
    // 搜索框参数
    tableFormFilter(param) {
      return param;
    },

    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
      this.requestId = "";
    },

    onChange(date, dateString) {
      console.log("data onChange start");
      console.log(date);
      this.tableForm.beginTime = dateString[0];
      this.tableForm.endTime = dateString[1];
    },

    exportExcel() {
      window.location.href =
        "/bc-manage-server/credit/export?ids=" + this.selectedRowKeys.join(",");
    },

    exportExcelAll() {
      const {
        clientId = "",
        bizType = "",
        beginTime = "",
        endTime = "",
      } = this.tableForm;
      window.location.href = `/bc-manage-server/credit/exportAll?bizType=${bizType}&clientId=${clientId}&beginTime=${beginTime}&endTime=${endTime}`;
    },

    updateList() {},

    reset() {
      this.acceptCompTimePicker = [
        moment().subtract(6, "months").set({ hour: 0, minute: 0, second: 0 }),
        moment().set({ hour: 23, minute: 59, second: 59 }),
      ];
      this.tableForm = {
        clientId: "",
        bizType: "",
        beginTime: moment()
          .subtract(6, "months")
          .set({ hour: 0, minute: 0, second: 0 })
          .format("YYYY/MM/DD HH:mm:ss"),
        endTime: moment()
          .set({ hour: 23, minute: 59, second: 59 })
          .format("YYYY/MM/DD HH:mm:ss"),
      };
      this.$nextTick(() => {
        this.$refs.table.getTableData();
      });
    },

    modify(data) {
      console.log(data);
      this.dateDetail = data;
      this.showDetail = true;
      // api.acceptCreditFlow({ id: data.id }).then(
      // 	(res) => {
      // 		if (res.code === 0) {
      // 			this.$message.success('同步成功');
      // 			this.$nextTick(() => {
      // 				this.$refs.table.getTableData();
      // 			});
      // 		} else {
      // 			this.$message.error(res.msg);
      // 		}
      // 	}
      // );
    },

    queryPage(data) {
      api.queryPage({}).then((res) => {
        this.isPopShow = true;
        this.selectData = res.data;
        this.requestId = data.bankId;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .img-table {
  max-height: 23px;
}
</style>
