// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
	 * @param {Object} param
	 */
	@Parameters([])
	queryPage() {
		return this.services.initGet({
			reqUrl: '/credit/queryPage',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	acceptCreditFlow() {
		return this.services.initPost({
			reqUrl: '/credit/acceptCreditFlow',
			param: this.param,
		});
	}
}

export default new api();
