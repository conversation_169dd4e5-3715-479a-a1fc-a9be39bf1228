<template>
  <div class="base_sty_column_box"
    ref="content"
    @click="hideRightMenu">
    <div class="base_tree_nav_box"
      ref="addEditTree"
      v-show="displayTree">
      <a-layout style="height: 100%;background: #fff;">
        <a-page-header title="测评问卷列表"
          style="background: #fff;" />
        <a-layout-header>
          <a-button type="link"
            icon="plus"
            size="small"
            @click="dealBtn('add')">新增</a-button>
        </a-layout-header>
        <a-tree v-if="data.length>0"
          style="background: #fff;"
          :show-icon="showIcon"
          :show-line="showLine"
          :treeData="data"
          :expanded-keys="expandedKeys"
          :auto-expand-parent="autoExpandParent"
          @expand="onExpand"
          @select="onSelect"
          @rightClick="rightClick">
          <a-icon slot="file"
            type="copy" />
        </a-tree>
        <div class="base_right_click_menu"
          v-if="isPopShow"
          id="rightClickMenu"
          ref="rightMenu"
          :style="tmpStyle">
          <a-button v-if="showMenu=='0'"
            type="link"
            size="small"
            @click="dealBtn('edit')">修改测评问卷主题</a-button>
          <a-button v-if="showMenu=='0'"
            type="link"
            size="small"
            @click="downloadExcel">导入</a-button>
          <a-button v-if="showMenu=='0'"
            type="link"
            size="small"
            @click="exportExcel">导出</a-button>
          <a-button v-if="showMenu!='0'"
            type="link"
            size="small"
            @click="dealBtn('addVersion')">新增测评问卷版本</a-button>
          <a-button v-if="showMenu!='0'"
            type="link"
            size="small"
            @click="dealBtn('editVersion')">修改测评问卷版本</a-button>
          <a-button v-if="showMenu!='0'"
            type="link"
            size="small"
            @click="dealBtn('copy')">复制测评问卷版本</a-button>
          <a-button v-if="showMenu!='0'"
            type="link"
            size="small"
            @click="dealBtn('remove')">删除测评问卷版本</a-button>
        </div>
      </a-layout>
    </div>
    <div style="width: 1px;height: 100%;background: rgb(221 221 221);float: left;position: relative;"></div>
    <div class="base_sty_column_right"
      ref="addEditContent"
      v-show="displayTree1">
      <rightManage :id="leftInfo.subjectId"
        :leftInfo="leftInfo"
        @uploadSuccess="query"></rightManage>
    </div>
    <upload :visible.sync="uploadShow"
      :downloadeUrl="downloadeUrl"
      :uploadUrl="uploadUrl"
      downloadeTitle=""
      @success="query" />
    <addOrEdit v-show="addOrEditPop"
      :isPopShow.sync="addOrEditPop"
      :requestId="requestId"
      :dataRef="NodeTreeItem"
      @updateList="updateList"></addOrEdit>
    <addOrEditVersion v-show="addOrEditVersionPop"
      :isPopShow.sync="addOrEditVersionPop"
      :requestId="requestVersionId"
      :requestNo="requestVersionNo"
      :dataRef="NodeTreeItem"
      @updateList="updateList"></addOrEditVersion>
  </div>
</template>
<script>
// import { surveySubjectList,surveySubjectDelete,surveyVersionCopy} from "@/service/service.js"
import api from './api';
import rightManage from './components/rightManage.vue';
import upload from '@c/upload/upload';
export default {
  name: 'survey',
  data() {
    return {
      // constShowDelelte : $hvue.config.constShowDelelte,
      key: '', //搜索的key
      iskey: '', //确认搜索Key
      displayTree: true, //默认显示左侧菜单树
      displayTree1: true, //默认显示右侧模块
      data: [], //树形数据
      showLine: true, //是否展示连接线
      showIcon: true, //是否展示 TreeNode title 前的图标，没有默认样式，如设置为 true，需要自行定义图标相关样式
      // defaultSelectedkeys: [],  //默认选中节点
      expandedKeys: [], //默认展示一级
      autoExpandParent: true, //是否自动展开父节点
      NodeTreeItem: {
        id: '',
      }, // 右键菜单数据
      tmpStyle: '', //触发右键style
      addressRightType: null, //右键选中地址类型 R：区域 P：省份 C：城市 Z：县/区域
      currentRightKey: null, //右键选中Id
      isPopShow: false, //是否显示树形右击弹窗

      leftInfo: {}, //左侧选中信息
      currentLeftKey: null, //左键选中Id
      // addressLeftType: null,   //左键选中地址类型 R：区域 P：省份 C：城市 Z：县/区域

      requestId: '', //选中的左侧id
      addOrEditPop: false, //是否显示新增区域弹窗
      showMenu: '',
      requestVersionId: '', // 选中的子节点id
      requestVersionNo: '', // 选中的父节点id
      addOrEditVersionPop: false, // 是否展示新增/修改问卷版本弹窗
      uploadShow: false, // 导入弹框
      downloadeUrl: '',
      uploadUrl: 'survey/import',
    };
  },
  mounted() {
    this.queryAreaTree();
  },
  components: {
    addOrEdit: () =>
      import(/* webpackChunkName: "addOrEdit" */ './components/addOrEdit.vue'),
    addOrEditVersion: () =>
      import(
        /* webpackChunkName: "addOrEditVersion" */ './components/addOrEditVersion.vue'
      ),
    // rightManage: () => import(/* webpackChunkName: "rightManage" */ '@/views/survey/components/rightManage.vue'),
    rightManage,
    upload,
  },
  methods: {
    query() {
      this.queryAreaTree();
    },
    //获取树形数据
    queryAreaTree() {
      let _this = this;
      // _this.defaultSelectedkeys = []
      api
        .surveySubjectList(
          {},
          {
            encode: false,
          }
        )
        .then((res) => {
          if (res.code === 0) {
            res.data.forEach((v) => {
              // v.title = v.name
              // v.key = v.subjectId
              v.slots = {
                icon: 'file',
              };
              _this.dealTreeData(v);
            });
            _this.data = res.data;
            // _this.defaultSelectedkeys.push(_this.data[0].subjectId)
            // this.leftInfo = _this.data[0]
            console.log(res);
          } else {
            _this.data = [];
          }
        });
    },
    // 导入downloadExcel
    downloadExcel() {
      this.uploadShow = true;
    },

    // 导出exportExcel
    exportExcel() {
      window.location.href = `/bc-manage-server/survey/export?subjectNo=${this.currentRightKey}`;
    },
    //处理树形数据
    dealTreeData(v) {
      v.title = v.name;
      v.key = v.subjectId;

      if (v.children) {
        v.children.forEach((v1) => {
          this.dealTreeData(v1);
        });
      }
    },
    hideTree() {
      if (this.displayTree && this.displayTree1) {
        this.displayTree = !this.displayTree;
        if (!this.displayTree) {
          this.$refs.addEditContent.style.marginLeft = '0';
        } else {
          this.$refs.addEditContent.style.marginLeft = '200px';
        }
      } else {
        this.displayTree1 = !this.displayTree1;
        if (!this.displayTree1) {
          this.$refs.addEditTree.style.width =
            this.$refs.content.clientWidth - 10 + 'px';
        } else {
          this.$refs.addEditTree.style.width = '200px';
        }
      }
    },
    hideTree1() {
      if (this.displayTree && this.displayTree1) {
        this.displayTree1 = !this.displayTree1;
        if (!this.displayTree1) {
          this.$refs.addEditTree.style.width =
            this.$refs.content.clientWidth - 10 + 'px';
        } else {
          this.$refs.addEditTree.style.width = '200px';
        }
      } else {
        this.displayTree = !this.displayTree;
        if (!this.displayTree) {
          this.$refs.addEditContent.style.marginLeft = '0';
        } else {
          this.$refs.addEditContent.style.marginLeft = '200px';
        }
      }
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    //搜索区域
    redo() {
      let _this = this;
      _this.iskey = _this.key;
      _this.queryAreaTree();
    },
    //左侧点击
    onSelect(selectedKeys, info) {
      if (info.node.dataRef.parentId == '0') return;
      this.currentLeftKey = selectedKeys;
      this.leftInfo = info.node.dataRef;
      // this.addressLeftType = this.leftInfo.addressType
    },
    // 右击事件
    rightClick({ event, node }) {
      const x = event.x;
      const y = event.y;
      this.NodeTreeItem = {
        pageX: x,
        pageY: y,
        id: node._props.eventKey,
        title: node._props.title,
        parentOrgId: node._props.dataRef.parentOrgId || null,
        dataRef: node._props.dataRef || {},
        type: node._props.dataRef.parentId,
      };
      let scroll = this.$refs.addEditTree.scrollTop;
      this.tmpStyle = {
        position: 'absolute',
        maxHeight: 40,
        textAlign: 'left',
        left: '30px',
        top: `${y - 160 + scroll}px`,
        display: 'flex',
        flexDirection: 'column',
      };
      this.showMenu = node._props.dataRef.parentId;
      this.currentRightKey = node._props.eventKey;
      this.isPopShow = true;
      this.$forceUpdate();
    },
    // 隐藏右键弹框
    hideRightMenu() {
      this.isPopShow = false;
    },
    //刷新树形
    updateList(param) {
      if (param) {
        this.NodeTreeItem = { id: '' };
        this.queryAreaTree();
      }
    },
    //点击右键弹框类型处理
    dealBtn(type) {
      let _this = this;
      if (type == 'add') {
        _this.requestId = '';
        _this.addOrEditPop = true;
      } else if (type == 'edit') {
        _this.requestId = _this.NodeTreeItem.dataRef.subjectNo;
        _this.addOrEditPop = true;
      } else if (type == 'remove') {
        let _this = this;
        _this.$confirm({
          title: '温馨提示',
          content: `是否确认操作?`,
          okType: 'danger',
          onOk() {
            api
              .surveySubjectDelete(
                {
                  ids: [_this.NodeTreeItem.dataRef.subjectId],
                },
                {
                  encode: false,
                  post: 2,
                }
              )
              .then((res) => {
                if (res.code === 0) {
                  _this.$message.success(res.msg);
                  _this.queryAreaTree();
                } else {
                  _this.$message.error(res.msg);
                }
              });
          },
        });
      } else if (type == 'addVersion') {
        _this.requestVersionNo = '';
        _this.requestVersionId = _this.NodeTreeItem.dataRef.subjectId;
        _this.addOrEditVersionPop = true;
      } else if (type == 'editVersion') {
        _this.requestVersionNo = _this.NodeTreeItem.dataRef.subjectNo;
        _this.requestVersionId = _this.NodeTreeItem.dataRef.subjectId;
        _this.addOrEditVersionPop = true;
      } else if (type == 'copy') {
        let _this = this;
        let param = {
          subjectId: _this.NodeTreeItem.dataRef.subjectId,
        };
        let option = {
          encode: false,
          post: 2,
        };
        api.surveyVersionCopy(param, option).then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.queryAreaTree();
          } else {
            _this.$message.error(res.msg);
          }
        });
      } else if (type == 'fresh') {
        _this.queryAreaTree();
      }
    },
  },
  watch: {},
};
</script>
<style scoped>
.base_right_click_menu {
  width: 152px;
}
/* .base_tree_nav_box /deep/ .ant-tree li span.ant-tree-switcher{
    display: none;
} */
.base_tree_nav_box .ant-layout-header {
  background: #fff;
  z-index: 2;
}

.base_sty_column_box .base_tree_nav_box {
  width: 180px;
}
.base_sty_column_right {
  margin-left: 184px;
}
.ant-page-header {
  padding: 12px 0px !important;
}
.base_tree_nav_box .ant-layout-header {
  padding: 0px 10px 0px 0px;
}
.base_right_click_menu .ant-btn-sm {
  padding: 0;
}
</style>