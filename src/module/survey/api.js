// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveySubjectList() {
		return this.services.initGet({
			reqUrl: 'survey/subject/list',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveySubjectDelete() {
		return this.services.initPost({
			reqUrl: 'survey/subject/delete',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyVersionCopy() {
		return this.services.initPost({
			reqUrl: 'survey/subject/copy',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyQuestionList() {
		return this.services.initGet({
			reqUrl: 'survey/question/list',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyQuestionDelete() {
		return this.services.initPost({
			reqUrl: 'survey/question/delete',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyDefineList() {
		return this.services.initGet({
			reqUrl: 'survey/define/list',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyDefineDelete() {
		return this.services.initPost({
			reqUrl: 'survey/define/delete',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyAnswerList() {
		return this.services.initGet({
			reqUrl: 'survey/answer/list',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyAnswerDelete() {
		return this.services.initPost({
			reqUrl: 'survey/answer/delete',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveySubjectDetail() {
		return this.services.initGet({
			reqUrl: 'survey/subject/get',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	queryEnumNoList() {
		return this.services.initPost({
			reqUrl: 'dict/queryDict?dictType=' + this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyVersionAdd() {
		return this.services.initPost({
			reqUrl: 'survey/subject/addVersion',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyVersionEdit() {
		return this.services.initPost({
			reqUrl: 'survey/subject/editVersion',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyQuestionDetail() {
		return this.services.initGet({
			reqUrl: 'survey/question/get',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyQuestionAdd() {
		return this.services.initPost({
			reqUrl: 'survey/question/add',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyQuestionEdit() {
		return this.services.initPost({
			reqUrl: 'survey/question/edit',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyDefineDetail() {
		return this.services.initGet({
			reqUrl: 'survey/define/get',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyDefineAdd() {
		return this.services.initPost({
			reqUrl: 'survey/define/add',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyDefineEdit() {
		return this.services.initPost({
			reqUrl: 'survey/define/edit',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyAnswerDetail() {
		return this.services.initGet({
			reqUrl: 'survey/answer/get',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyAnswerAdd() {
		return this.services.initPost({
			reqUrl: 'survey/answer/add',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveyAnswerEdit() {
		return this.services.initPost({
			reqUrl: 'survey/answer/edit',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveySubjectAdd() {
		return this.services.initPost({
			reqUrl: 'survey/subject/add',
			param: this.param,
		});
	}

	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	surveySubjectEdit() {
		return this.services.initPost({
			reqUrl: 'survey/subject/edit',
			param: this.param,
		});
	}
	
	/**
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	contextQuery() {
		return this.services.initGet({
			reqUrl: 'flowIns/context/list',
			param: this.param,
		});
	}

}

export default new api();
