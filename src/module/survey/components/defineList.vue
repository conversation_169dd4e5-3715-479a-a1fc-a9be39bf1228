<template>
    <div>
        <a-modal
              title="查看问卷等级"
              :visible="showPop"
              @cancel="closePop"
              class="ant_modal_bigtable"
            >
          <template slot="footer">
            <a-button key="back" @click="closePop">
              关闭
            </a-button>
          </template>
             <a-layout>
                <a-layout-content class="pop_content" style="min-height: calc(468px - 60px);">
                    <a-row>
                        <a-col :span="24">
                            <a-button type="link" icon="plus" size="small" @click="dealYb('add')">新增</a-button>
                            <a-button type="link" icon="edit" size="small" @click="dealYb('edit')" :disabled="!(selectedRowKeys.length==1)">编辑</a-button>
                            <a-button type="link" icon="delete" size="small" @click="dealYb('remove')" :disabled="selectedRowKeys.length==0">删除</a-button>
                            <a-button type="link" icon="redo" size="small" @click="dealYb('redo')">刷新</a-button>
                        </a-col>
                    </a-row>
                    <a-table 
                        :columns="columns" 
                        :data-source="data" 
                        :loading="loading"
                        :pagination="pagination"
                        :customRow="customRow"
                        :row-selection="{ fixed: true, selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                        @change="handleChange"
                        row-key='key'>
                        <template slot="state" slot-scope="state">
                            {{ state|dealType(stateList)}}
                        </template>
                        <template slot="isTrue" slot-scope="isTrue">
                            {{ isTrue|dealType(isTrueList) }}
                        </template>
                    </a-table>
                    <!-- <a-pagination 
                        style="margin-top: 10px;text-align: right;"
                        :total="total"
                        :current="current"
                        :pageSize="pageSize"
                        :pageSizeOptions="pageSizeOptions"
                        show-size-changer 
                        show-quick-jumper 
                        :show-total="total => `共 ${total} 条`"
                        @change="change"
                        @showSizeChange="showSizeChange">   
                    </a-pagination>  -->
                </a-layout-content>
            </a-layout>
        </a-modal>
        <addOrEditDefine :isPopShow.sync="isShowAddFalse" @updateList="updateList" :requestId="id" :leftInfo="leftInfo"></addOrEditDefine>
    </div>
</template>
<script>
// import { surveyDefineList,surveyDefineDelete } from "@/service/service.js";
import api from '../api'
export default {
    name: 'defineList',
	
    data() {
        return {
            stateList: this.$baseDict.base.stateList,  //状态列表
            isTrueList: this.$baseDict.base.isTrueList,  //答案列表
            //table start
            //表格数据
            data: [],
            //表头数据
            columns: [{
                title: '内容',
                dataIndex: 'define',
                key: 'define',
                width: 120,
                sorter: true,
				ellipsis: true,
                // scopedSlots: { customRender: 'transType' }
            },
            {
                title: '值',
                dataIndex: 'defineValue',
                key: 'defineValue',
                width: 120,
                sorter: true,
				ellipsis: true,
                scopedSlots: { customRender: 'defineValue' }
            },
            {
                title: '分数上限',
                dataIndex: 'upLimit',
                key: 'upLimit',
                width: 120,
                sorter: true,
				ellipsis: true,
                
            },
            {
                title: '分数下限',
                dataIndex: 'downLimit',
                key: 'downLimit',
                width: 120,
                sorter: true,
				ellipsis: true,
            },
            {
                title: '状态',
                dataIndex: 'state',
                key: 'state',
                width: 120,
                sorter: true,
				ellipsis: true,
                scopedSlots: { customRender: 'state' }
            },
            {
                title: '备注',
                dataIndex: 'remark',
                key: 'remark',
                width: 120,
                sorter: true,
				ellipsis: true,
            },
            {
                title: '创建时间',
                dataIndex: 'createDate',
                key: 'createDate',
                width: 180,
                sorter: true,
				ellipsis: true,
            },
            {
                title: '创建人',
                dataIndex: 'createBy',
                key: 'createBy',
                width: 120,
                sorter: true,
				ellipsis: true,
            },
            {
                title: '最后修改时间',
                dataIndex: 'modifiedDate',
                key: 'modifiedDate',
                width: 180,
                sorter: true,
				ellipsis: true,
            },
            {
                title: '最后修改人',
                dataIndex: 'modifiedBy',
                key: 'modifiedBy',
                width: 130,
                sorter: true,
				ellipsis: true,
            }], 
            
            selectedRowKeys: [], // Check here to configure the default column
            pagination: false,   //是否显示table分页
            loading: false,    //是否显示loading
            orderByColumn: '',  //排序name
            isAsc: '',       //排序方向
            //table end

            //pagination start
            total: 0,     //table总页数
            current: 1,    //当前页数
            pageSize: 10,  //每页条数
            pageSizeOptions: ['10', '20', '50', '100'],   //指定每页可以显示多少条
            //pagination end

            //新增
            isShowAddFalse: false, //新增弹窗
            id: '',  //选择id

            createBy: "9090",
            modifiedBy: "9090",
        }
    },
    props: {
        isPopShow: {
            type: Boolean,
            default: false
        },
        requestId: {
            type: String,
            default: ""
        },
        leftInfo: {
            type: Object,
            default: ()=>{
                return {}
            }
        }
    },
    computed: {
        showPop: {
            get() {
                return this.isPopShow;
            },
            set(val) {
                console.log(val);
                this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
            },
        },
    },
    components: {
        addOrEditDefine: () => import(/* webpackChunkName: "addOrEditDefine" */ './addOrEditDefine.vue'),
    },
    methods: {
        //关闭
        closePop() {
            this.showPop = false
        },
        //新增刷新
        updateList(param) {
            if(param){
                this.queryTable()
            }
        },
        //请求table
        queryTable() {
            let _this = this
            this.loading = true
            api.surveyDefineList({
                subId: _this.requestId,
                orderByColumn: _this.orderByColumn,
                isAsc: _this.isAsc,
            },{
                encode: false
            },{
                type: 1
            }).then(res => {
                _this.loading = false
                _this.selectedRowKeys = []
                if(res.code == 0) {
                    console.info("data",_this.data)
                    _this.data = res.data
                }else{
                    _this.$message.error(res.msg);
                    _this.data = []
                }
            }).catch((e) => {
                _this.data = []
                _this.loading = false
                _this.$message.error(e.message)
            })
        },
        //排序
        handleChange(pagination, filters, sorter) {
            this.orderByColumn = sorter.columnKey
            this.isAsc = sorter.order ? sorter.order.replace("end","") : ''
            this.queryTable()
        },
        //单选全选
        onSelectChange(selectedRowKeys) {
            console.log('selectedRowKeys changed: ', selectedRowKeys);
            this.selectedRowKeys = selectedRowKeys;
        },
        //点击一行选中效果
        customRow (record, index) {
            let _this = this
            return {
                on: {
                    click: () => {
                        if(!_this.selectedRowKeys.includes(index)){
                            _this.selectedRowKeys=[index]
                        }
                    }
                }
            }
        },
        //页码改变的回调，参数是改变后的页码及每页条数
        change(page, pageSize) {
            this.current = page
            this.pageSize = pageSize
            this.queryTable()
        },
        //总数变化时回调
        showSizeChange(current, size) {
            this.current = current
            this.pageSize = size
            this.queryTable()
        },
        //处理按钮
        dealYb(val) {
            let _this = this
            if(val == 'add') { //添加
                _this.id = ""
                _this.isShowAddFalse = true
            }else if(val == 'edit') { //编辑
                _this.id = _this.data[_this.selectedRowKeys[0]].defineId  //获取id
                _this.isShowAddFalse = true
            }else if(val == 'remove') { //删除
                if(_this.selectedRowKeys.length>0) {
                    this.remove()
                }
            }else if(val == 'redo') {
                _this.queryTable()
            }
        },
         //删除
        remove() {
            let _this = this
            _this.$confirm({
                title: '温馨提示',
                content: `是否确认删除?`,
                okType: 'danger',
                onOk () {
                    _this.deleteApi()
                }
            })
        },
        //请求删除接口
        deleteApi() {
            let _this = this,
               str = []
            _this.selectedRowKeys.forEach((v) => {
                str.push(_this.data[v].defineId)
            })
            api.surveyDefineDelete({ids: str},{
                encode: false,
                post: 2
            }).then(res => {
                if(res.code === 0) {
                    _this.$message.success(res.msg);
                    _this.queryTable()
                }else{
                    _this.$message.error(res.msg);
                }
            }).catch((e) => {
                _this.$message.error(e.message)
            })
        },
    },
    watch: {
        isPopShow(n) {
            let _this = this
            if(n) {
                if(_this.requestId != ''){
                    _this.queryTable()
                } 
            }
        }
    },
    filters: {
        dealType (val,list) {
            if(!val) return '' 
            let arr = []
            arr = list.filter((v)=>{
                return v.key == val
            })
            return arr[0] ? arr[0].value: ''
        },
    },
}
</script>
<style scoped>
.jc_pop_fixed{
    margin-top: -234px;
    margin-left: -500px;
    width: 1000px;
    height: 468px;
}
.jc_pop_fixed /deep/ .ant-table-thead > tr > th:nth-child(2),
.jc_pop_fixed /deep/ .ant-table-thead > tr > td:nth-child(2){
    width: 120px;
}   
.jc_pop_fixed /deep/ .ant-table-thead > tr > th,.jc_pop_fixed /deep/ .ant-table-tbody > tr > td{
    padding: 16px 0;
}
</style>