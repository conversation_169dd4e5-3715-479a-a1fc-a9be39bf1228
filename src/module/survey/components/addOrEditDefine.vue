<template>
	<div>
		<a-modal
			:title="requestId ? `修改等级` : `添加等级`"
			:visible="showPop"
			@ok="getValue"
			@cancel="closePop"
		>
			<template slot="footer">
				<a-button key="back" @click="closePop">
					取消
				</a-button>
				<a-button type="primary" key="submit" @click="getValue">
					{{ requestId ? "修改" : "添加" }}
				</a-button>
			</template>
			<a-layout>
				<a-layout-content
					class="pop_content"
					style="min-height: calc(374px - 120px);"
				>
					<a-row>
						<a-col :span="6" class="label_style">
							<label for=""
								>内容<a class="mark_style">*</a></label
							>
						</a-col>
						<a-col :span="18">
							<a-input v-model="info.define" placeholder="内容" />
						</a-col>
					</a-row>
					<a-row>
						<a-col :span="6" class="label_style">
							<label for=""
								>分数上限<a class="mark_style">*</a></label
							>
						</a-col>
						<a-col :span="18">
							<a-input
								v-model="info.upLimit"
								placeholder="分数上限"
							/>
						</a-col>
					</a-row>
					<a-row>
						<a-col :span="6" class="label_style">
							<label for=""
								>分数下限<a class="mark_style">*</a></label
							>
						</a-col>
						<a-col :span="18">
							<a-input
								v-model="info.downLimit"
								placeholder="分数下限"
							/>
						</a-col>
					</a-row>
					<a-row>
						<a-col :span="6" class="label_style">
							<label for=""
								>状态<a class="mark_style">*</a></label
							>
						</a-col>
						<a-col :span="18">
							<a-radio-group
								name="radioGroup"
								v-model="info.state"
							>
								<a-radio
									:value="v.key"
									v-for="v in stateList"
									:key="v.key"
									>{{ v.value }}</a-radio
								>
							</a-radio-group>
						</a-col>
					</a-row>
					<a-row>
						<a-col :span="6" class="label_style">
							<label for="">值<a class="mark_style">*</a></label>
						</a-col>
						<a-col :span="18">
							<a-input
								v-model="info.defineValue"
								placeholder="值"
							/>
						</a-col>
					</a-row>
					<a-row>
						<a-col :span="6" class="label_style">
							<label for="">备注<a class="mark_style"></a></label>
						</a-col>
						<a-col :span="18">
							<a-textarea v-model="info.remark" :rows="2" />
						</a-col>
					</a-row>
				</a-layout-content>
			</a-layout>
		</a-modal>
	</div>
</template>
<script>
	// import { surveyDefineDetail,surveyDefineAdd,surveyDefineEdit } from "@/service/service.js";
	import api from "../api";
	export default {
		name: "addOrEditDefine",
		data() {
			return {
				stateList: this.$baseDict.base.stateList, //状态列表
				isTrueList: this.$baseDict.base.isTrueList, //答案列表
				info: {
					define: "",
					upLimit: "",
					downLimit: "",
					state: "1",
					defineValue: "",
					mark: ""
				},
				createBy: "9090",
				modifiedBy: "9090"
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			requestId: {
				type: String,
				default: ""
			},
			leftInfo: {
				type: Object,
				default: () => {
					return {};
				}
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					console.log(val);
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		components: {},
		methods: {
			//关闭
			closePop() {
				this.showPop = false;
				this.reset();
			},
			//提交
			getValue() {
				let _this = this;
				if (_this.info.define == "") {
					_this.$message.error("内容不能为空");
					return false;
				} else if (_this.info.upLimit === "" || this.info.upLimit == null) {
					_this.$message.error("分数上限不能为空");
					return false;
				} else if (
					_this.info.downLimit === "" ||
					this.info.upLimit == null
				) {
					_this.$message.error("分数下限不能为空");
					return false;
				} else if (_this.info.state == "") {
					_this.$message.error("请选择状态");
					return false;
				} else if (_this.info.defineValue == "") {
					_this.$message.error("值不能为空");
					return false;
				}
				_this.$confirm({
					title: "温馨提示",
					content: `是否提交?`,
					okType: "danger",
					onOk() {
						let param = _this.info;
						if (_this.requestId == "") {
							//新增
							let param1 = {
								subId: _this.leftInfo.subjectId,
								createBy: _this.createBy,
								modifiedBy: _this.createBy
							};
							param = Object.assign(param, param1);
							api.surveyDefineAdd(param, {
								encode: false,
								post: 2
							}).then(res => {
								if (res.code === 0) {
									_this.$message.success(res.msg);
									_this.$emit("updateList", true);
									_this.closePop();
								} else {
									_this.$message.error(res.msg);
								}
							});
						} else if (_this.requestId != "") {
							//编辑
							let param1 = {
								subId: _this.leftInfo.subjectId,
								defineId: _this.requestId,
								modifiedBy: _this.createBy
							};
							param = Object.assign(param, param1);
							api.surveyDefineEdit(param, {
								encode: false,
								post: 2
							}).then(res => {
								if (res.code === 0) {
									_this.$message.success(res.msg);
									_this.$emit("updateList", true);
									_this.closePop();
								} else {
									_this.$message.error(res.msg);
								}
							});
						}
					}
				});
			},
			//重置
			reset() {
				this.info = {
					define: "",
					upLimit: "",
					downLimit: "",
					state: "1",
					defineValue: "",
					mark: ""
				};
			},
			handleChange() {},
			//获取详情
			getDetail() {
				let _this = this;
				api.surveyDefineDetail(
					{
						defineId: _this.requestId
					},
					{
						encode: false
					}
				).then(res => {
					if (res.code === 0) {
						console.info("res", res);
						_this.info = res.data;
					}
				});
			}
		},
		watch: {
			isPopShow(n) {
				let _this = this;
				if (n) {
					if (_this.requestId != "") {
						_this.getDetail();
					}
				}
			}
		}
	};
</script>
<style scoped>
	.jc_pop_fixed {
		margin-top: -187px;
		margin-left: -300px;
		width: 600px;
		height: 374px;
	}
</style>