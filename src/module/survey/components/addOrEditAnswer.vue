<template>
  <div>
    <a-modal :title="requestId ? `编辑答案` : `添加答案`"
      :visible="showPop"
      @ok="getValue"
      @cancel="closePop">
      <template slot="footer">
        <a-button key="back"
          @click="closePop">
          取消
        </a-button>
        <a-button type="primary"
          key="submit"
          @click="getValue">
          {{ requestId ? "修改" : "添加" }}
        </a-button>
      </template>
      <a-layout>
        <a-layout-content class="pop_content"
          style="min-height: calc(270px - 120px);">
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">答案题目<a class="mark_style">*</a></label>
            </a-col>
            <a-col ::span="18">
              <a-input v-model="info.name"
                placeholder="答案题目" />
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">分值<a class="mark_style">*</a></label>
            </a-col>
            <a-col ::span="18">
              <a-input v-model="info.mark"
                placeholder="分值" />
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">是否正确答案<a class="mark_style">*</a></label>
            </a-col>
            <a-col ::span="18">
              <a-radio-group name="radioGroup"
                v-model="info.isTrue">
                <a-radio :value="v.key"
                  v-for="v in isTrueList"
                  :key="v.key">{{ v.value }}</a-radio>
              </a-radio-group>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">状态<a class="mark_style">*</a></label>
            </a-col>
            <a-col ::span="18">
              <a-select v-model="info.state"
                placeholder=""
                @change="handleChange"
                class="select_wid">
                <a-select-option :value="v.key"
                  v-for="v in stateList"
                  :key="v.key">
                  {{ v.value }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">外部扩展编号<a class="mark_style">*</a></label>
            </a-col>
            <a-col ::span="18">
              <a-input v-model="info.extCode"
                placeholder="" />
            </a-col>
          </a-row>
          <a-row v-show="requestId != ''">
            <a-col :span="6"
              class="label_style">
              <label for="">排序值<a class="mark_style">*</a></label>
            </a-col>
            <a-col ::span="18">
              <a-input v-model="info.orderLine"
                placeholder="" />
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">答案脚本</label>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-select v-model="parentId"
                placeholder="请选择"
                @change="selectScript(parentId)"
                class="select_wid">
                <a-select-option :value="v.uuid"
                  v-for="v in groupList"
                  :key="v.uuid">
                  {{ v.scriptName }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <a-select v-model="info.scriptId"
                placeholder="请选择"
                class="select_wid">
                <a-select-option :value="v.uuid"
                  v-for="v in scriptList"
                  :key="v.uuid">
                  {{ v.scriptName }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </a-layout-content>
      </a-layout>
    </a-modal>
  </div>
</template>
<script>
// import { surveyAnswerDetail,surveyAnswerAdd,surveyAnswerEdit } from "@/service/service.js";
import api from '../api';
import api2 from '../../scriptManage/api';
export default {
  name: 'addOrEditAnswer',
  data() {
    return {
      stateList: this.$baseDict.base.stateList, //状态列表
      isTrueList: this.$baseDict.base.isTrueList, //答案列表
      info: {
        name: '',
        mark: '',
        isTrue: '0',
        state: '',
        extCode: '',
        orderLine: '',
        scriptId: '',
      },
      parentId: '',
      groupList: [],
      scriptList: [],
      createBy: '9090',
      modifiedBy: '9090',
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    requestId: {
      type: String,
      default: '',
    },
    requstInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        console.log(val);
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
  },
  methods: {
    //关闭
    closePop() {
      this.showPop = false;
      this.reset();
    },
    //提交
    getValue() {
      let _this = this;
      if (_this.info.name == '') {
        _this.$message.error('答案题目不能为空');
        return false;
      } else if (_this.info.mark == '') {
        _this.$message.error('分值不能为空');
        return false;
      } else if (_this.info.isTrue == '') {
        _this.$message.error('请选择答案类型');
        return false;
      } else if (_this.info.state == '') {
        _this.$message.error('请选择状态');
        return false;
      } else if (_this.info.extCode == '') {
        _this.$message.error('外部扩展编号不能为空');
        return false;
      } else if (_this.info.orderLine === '' && _this.requestId != '') {
        //若为编辑
        _this.$message.error('排序值不能为空');
        return false;
      }
      _this.$confirm({
        title: '温馨提示',
        content: `是否提交?`,
        okType: 'danger',
        onOk() {
          let param = _this.info;
          if (_this.requestId == '') {
            //新增
            let param1 = {
              queId: _this.requstInfo.queId,
              createBy: _this.createBy,
              modifiedBy: _this.createBy,
            };
            param = Object.assign(param, param1);
            api
              .surveyAnswerAdd(param, {
                encode: false,
                post: 2,
              })
              .then((res) => {
                if (res.code === 0) {
                  _this.$message.success(res.msg);
                  _this.$emit('updateList', true);
                  _this.closePop();
                } else {
                  _this.$message.error(res.msg);
                }
              });
          } else if (_this.requestId != '') {
            //编辑
            let param1 = {
              queId: _this.requstInfo.queId,
              answerId: _this.requestId,
              modifiedBy: _this.createBy,
            };
            param = Object.assign(param, param1);
            api
              .surveyAnswerEdit(param, {
                encode: false,
                post: 2,
              })
              .then((res) => {
                if (res.code === 0) {
                  _this.$message.success(res.msg);
                  _this.$emit('updateList', true);
                  _this.closePop();
                } else {
                  _this.$message.error(res.msg);
                }
              });
          }
        },
      });
    },
    //重置
    reset() {
      this.info = {
        name: '',
        mark: '',
        isTrue: '0',
        state: '',
        extCode: '',
        orderLine: '',
        scriptId: '',
      };
      this.parentId = '';
      this.groupList = [];
      this.scriptList = [];
    },

    handleChange() {},

    selectScript(id) {
      let _this = this;
      if (id) {
        api2
          .getScriptList({
            parentId: id,
          })
          .then((res) => {
            if (res.code == 0) {
              if (id == '') {
                this.$nextTick(() => {
                  this.$set(_this.info, 'scriptId', '');
                });
              }
              _this.scriptList = res.data;
            }
          });
      }
    },
    searchScript(id) {
      return api2.getScriptQueryByUid({
        uid: id,
      });
    },
    //获取详情
    getDetail() {
      let _this = this;
      Promise.all([
        api.surveyAnswerDetail(
          {
            answerId: _this.requestId,
          },
          {
            encode: false,
          },
          {
            type: 1,
          }
        ),
        api2.getScriptGroupList(),
      ]).then((res) => {
        let data1 = res[0].data;
        let data2 = res[1].data;
        _this.info = data1;
        _this.groupList = data2;
        if (data1.scriptId) {
          _this.searchScript(data1.scriptId).then((result) => {
            _this.parentId = result.data.parentId;
            _this.selectScript(result.data.parentId);
          });
        }
      });
    },
  },
  watch: {
    isPopShow(n) {
      let _this = this;
      if (n) {
        if (_this.requestId != '') {
          _this.getDetail();
        } else {
          api2.getScriptGroupList().then((res) => {
            if (res.code != 0) return;
            _this.groupList = res.data;
          });
        }
      }
    },
  },
};
</script>
<style scoped>
.jc_pop_fixed {
  margin-top: -135px;
  margin-left: -300px;
  width: 600px;
  height: 270px;
}
</style>