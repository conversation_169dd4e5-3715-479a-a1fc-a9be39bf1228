<template>
	<div>
		<a-modal
			:title="requestId ? `修改测评问卷主题` : `添加测评问卷主题`"
			:visible="showPop"
			@ok="getValue"
			@cancel="closePop"
		>
			<template slot="footer">
				<a-button key="back" @click="closePop">
					取消
				</a-button>
				<a-button type="primary" key="submit" @click="getValue">
					{{ requestId ? "修改" : "添加" }}
				</a-button>
			</template>
			<a-layout>
				<a-layout-content
					class="pop_content"
					style="min-height: calc(200px - 120px);"
				>
					<a-row>
						<a-col :span="6" class="label_style">
							<label for=""
								>主题名称<a class="mark_style">*</a></label
							>
						</a-col>
						<a-col :span="18">
							<a-input v-model="info.name" placeholder="" />
						</a-col>
					</a-row>
					<a-row>
						<a-col :span="6" class="label_style">
							<label for=""
								>英文标识<a class="mark_style">*</a></label
							>
						</a-col>
						<a-col :span="18">
							<a-input
								v-model="info.subjectNo"
								placeholder=""
								:disabled="requestId ? true : false"
							/>
						</a-col>
					</a-row>
                    <a-row>
						<a-col :span="6" class="label_style">
							<label for=""
								>每日最大测评次数<a class="mark_style">*</a></label
							>
						</a-col>
						<a-col :span="18">
							<a-input
								v-model="info.answerNumLimit"
								placeholder=""
							/>
						</a-col>
					</a-row>
					<!-- <a-row>
                        <a-col :span="6" class="label_style">
                            <label for="">状态<a class="mark_style">*</a></label>
                        </a-col>
                        <a-col :span="18">
                            <a-select v-model="info.state"
                                placeholder=""
                                @change="handleChange"
                                class="select_wid">
                                <a-select-option :value="v.value" v-for="v in stateList" :key="v.value">
                                    {{v.name}}
                                </a-select-option>
                            </a-select>
                        </a-col>
                    </a-row> -->
					<!-- <a-row v-show="requestId != ''">
                        <a-col :span="6" class="label_style">
                            <label for="">排序值<a class="mark_style"></a></label>
                        </a-col>
                        <a-col :span="18">
                            <a-input  v-model="info.orderLine" placeholder=""/>
                        </a-col>
                    </a-row> -->
					<a-row>
						<a-col :span="6" class="label_style">
							<label for="">描述<a class="mark_style"></a></label>
						</a-col>
						<a-col :span="18">
							<a-textarea v-model="info.description" :rows="2" />
						</a-col>
					</a-row>
				</a-layout-content>
			</a-layout>
		</a-modal>
	</div>
</template>
<script>
	// import { surveySubjectDetail,surveySubjectAdd,surveySubjectEdit } from "@/service/service.js";
	import api from "../api";
	export default {
		name: "addOrEdit",
		data() {
			return {
				info: {
					name: "",
					subjectNo: "",
					// state: '',
                    description: "",
                    answerNumLimit: 2
					// orderLine: ''
				},
				stateList: this.$baseDict.base.validList,  //状态列表
				createBy: "9090",
				modifiedBy: "9090"
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			leftInfo: {
				type: Object,
				default: () => {
					return {};
				}
			},
			requestId: {
				type: String,
				default: ""
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					console.log(val);
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		components: {},
		methods: {
			//关闭
			closePop() {
				this.showPop = false;
				this.info = {
					name: "",
					subjectNo: "",
					// state: '',
                    description: "",
                    answerNumLimit: 2
					// orderLine: ''
				};
			},
			//提交
			getValue() {
				let _this = this;
				if (_this.info.name == "") {
					_this.$message.error("主题名称不能为空");
					return false;
				} else if (_this.info.subjectNo == "") {
					_this.$message.error("英文标识不能为空");
					return false;
				} else if (_this.info.answerNumLimit == "") {
                    _this.$message.error("问卷每日最大测评次数不能为空")
                    return false
                }
				// else if(_this.info.state == '') {
				//     _this.$message.error('状态不能为空');
				//     return false
				// }
				// else if(_this.info.orderLine == '' && _this.requestId != '') {  //若为编辑
				//     _this.$message.error('排序值不能为空');
				//     return false
				// }
				let param = _this.info;
				if (_this.requestId == "") {
					//新增
					let param1 = {
						createBy: _this.createBy,
						modifiedBy: _this.createBy,
						state: "0"
					};
					param = Object.assign(param, param1);
					api.surveySubjectAdd(param, {
						encode: false,
						post: 2
					}).then(res => {
						if (res.code === 0) {
							_this.$message.success(res.msg);
							_this.$emit("updateList", true);
							_this.closePop();
						} else {
							_this.$message.error(res.msg);
						}
					});
				} else if (_this.requestId != "") {
					//编辑
					let param1 = {
						subjectId: _this.requestId,
						createBy: _this.createBy,
						modifiedBy: _this.createBy
					};
					param = Object.assign(param, param1);
					api.surveySubjectEdit(param, {
						encode: false,
						post: 2
					}).then(res => {
						if (res.code === 0) {
							_this.$message.success(res.msg);
							_this.$emit("updateList", true);
							_this.closePop();
						} else {
							_this.$message.error(res.msg);
						}
					});
				}
			},
			// handleChange() {},
			//获取详情
			getDetail() {
				let _this = this;
				api.surveySubjectDetail(
					{
						subjectNo: _this.requestId,
						subjectId: ""
					},
					{
						encode: false
					}
				).then(res => {
					if (res.code === 0) {
						console.info("res", res);
						_this.info = res.data;
					}
				});
			}
		},
		watch: {
			isPopShow(n) {
				let _this = this;
				if (n) {
					if (_this.requestId != "") {
						_this.getDetail();
					}
				}
			}
		}
	};
</script>
<style scoped>
	.jc_pop_fixed {
		margin-top: -100px;
		margin-left: -175px;
		width: 350px;
		height: 200px;
	}
</style>