<template>
  <div>
    <a-modal :title="requestId ? `编辑问题` : `添加问题`"
      :visible="showPop"
      @ok="getValue"
      @cancel="closePop"
      :width="750">
      <template slot="footer">
        <a-button key="back"
          @click="closePop">
          取消
        </a-button>
        <a-button type="primary"
          key="submit"
          @click="getValue">
          {{ requestId ? "修改" : "添加" }}
        </a-button>
      </template>
      <a-layout>
        <a-layout-content class="pop_content"
          style="min-height: calc(270px - 120px);">
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">问题名称<a class="mark_style">*</a></label>
            </a-col>
            <a-col :span="18">
              <a-input v-model="info.name"
                placeholder="" />
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">问题类型<a class="mark_style">*</a></label>
            </a-col>
            <a-col :span="18">
              <a-select v-model="info.type"
                placeholder=""
                @change="handleChange"
                class="select_wid">
                <a-select-option :value="v.key"
                  v-for="v in typeList"
                  :key="v.key">
                  {{ v.value }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">状态<a class="mark_style">*</a></label>
            </a-col>
            <a-col :span="18">
              <a-select v-model="info.state"
                placeholder=""
                @change="handleChange"
                class="select_wid">
                <a-select-option :value="v.key"
                  v-for="v in stateList"
                  :key="v.key">
                  {{ v.value }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">外部扩展编号<a class="mark_style">*</a></label>
            </a-col>
            <a-col :span="18">
              <a-input v-model="info.extCode"
                placeholder=""
                type="text"
                :maxLength="6" />
            </a-col>
          </a-row>
          <a-row v-show="requestId != ''">
            <a-col :span="6"
              class="label_style">
              <label for="">排序值<a class="mark_style">*</a></label>
            </a-col>
            <a-col :span="18">
              <a-input v-model="info.orderLine"
                placeholder="" />
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">描述<a class="mark_style"></a></label>
            </a-col>
            <a-col :span="18">
              <a-textarea v-model="info.description"
                :rows="2" />
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6"
              class="label_style">
              <label for="">是否默认选中</label>
            </a-col>
            <a-col :span="18">
              <a-select v-model="info.isDefault"
                placeholder=""
                @change="handleChange"
                class="select_wid">
                <a-select-option :value="v.key"
                  v-for="v in selectList"
                  :key="v.key">
                  {{ v.value }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
          <div v-show="info.isDefault == '1'">
            <a-row>
              <a-col :span="6"
                class="label_style">
                <label for="">是否从上下文中获取</label>
              </a-col>
              <a-col :span="18">
                <a-select v-model="info.isByContext"
                  placeholder=""
                  @change="handleChange"
                  class="select_wid">
                  <a-select-option :value="v.key"
                    v-for="v in selectList"
                    :key="v.key">
                    {{ v.value }}
                  </a-select-option>
                </a-select>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="6"
                class="label_style">
                <label for="">问卷取值元素<a class="mark_style"></a></label>
              </a-col>
              <a-col :span="18">
                <a-select v-if="info.isByContext == '1'"
                  show-search
                  v-model="info.element"
                  :filter-option="filterOption"
                  :allowClear="true"
                  class="select_wid">
                  <a-select-option :value="v.contextKey"
                    v-for="v in elementList"
                    :key="v.contextKey">
                    {{ `${v.contextName}(${v.contextKey})` }}
                  </a-select-option>
                </a-select>
                <a-input v-else
                  v-model="info.element"
                  placeholder="" />
              </a-col>
            </a-row>
          </div>
        </a-layout-content>
      </a-layout>
    </a-modal>
  </div>
</template>
<script>
// import { surveyQuestionDetail,surveyQuestionAdd,surveyQuestionEdit } from "@/service/service.js";
import api from '../api';
export default {
  name: '',
  data() {
    return {
      info: {
        name: '',
        type: '',
        state: '',
        extCode: '',
        orderLine: '',
        description: '',
        isDefault: '',
        isByContext: '',
        element: '',
      },
      typeList: this.$baseDict.base.qusetionTypeList, //问题类型列表
      stateList: this.$baseDict.base.stateList, //状态列表
      selectList: this.$baseDict.base.selectList, //是否默认选中
      elementList: [],
      createBy: '9090',
      modifiedBy: '9090',
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    leftInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    requestId: {
      type: String,
      default: '',
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        console.log(val);
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
  },
  components: {},
  methods: {
    //关闭
    closePop() {
      this.showPop = false;
      this.info = {
        name: '',
        type: '',
        state: '',
        extCode: '',
        orderLine: '',
        description: '',
        isDefault: '',
        isByContext: '',
        element: '',
      };
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    //提交
    getValue() {
      let _this = this;
      if (_this.info.name == '') {
        _this.$message.error('问题名称不能为空');
        return false;
      } else if (_this.info.type == '') {
        _this.$message.error('问题类型不能为空');
        return false;
      } else if (_this.info.state == '') {
        _this.$message.error('状态不能为空');
        return false;
      } else if (_this.info.extCode == '') {
        _this.$message.error('外部扩展编号不能为空');
        return false;
      } else if (_this.info.orderLine == '' && _this.requestId != '') {
        //若为编辑
        _this.$message.error('排序值不能为空');
        return false;
      }
      _this.$confirm({
        title: '温馨提示',
        content: `是否提交`,
        okType: 'danger',
        onOk() {
          _this.confirmSub();
        },
      });
    },
    //确认提交
    confirmSub() {
      let _this = this,
        param = _this.info;
      if (_this.requestId == '') {
        //新增
        let param1 = {
          subjectId: _this.leftInfo.subjectId,
          createBy: _this.createBy,
          modifiedBy: _this.createBy,
        };
        param = Object.assign(param, param1);
        api
          .surveyQuestionAdd(param, {
            encode: false,
            post: 2,
          })
          .then((res) => {
            if (res.code === 0) {
              _this.$message.success(res.msg);
              _this.$emit('updateList', true);
              _this.closePop();
            } else {
              _this.$message.error(res.msg);
            }
          });
      } else if (_this.requestId != '') {
        //编辑
        let param1 = {
          queId: _this.requestId,
          createBy: _this.createBy,
          modifiedBy: _this.createBy,
        };
        param = Object.assign(param, param1);
        api
          .surveyQuestionEdit(param, {
            encode: false,
            post: 2,
          })
          .then((res) => {
            if (res.code === 0) {
              _this.$message.success(res.msg);
              _this.$emit('updateList', true);
              _this.closePop();
            } else {
              _this.$message.error(res.msg);
            }
          });
      }
    },
    handleChange() {},
    //获取详情
    getDetail() {
      let _this = this;
      api
        .surveyQuestionDetail(
          {
            queId: _this.requestId,
          },
          {
            encode: false,
          }
        )
        .then((res) => {
          if (res.code === 0) {
            console.info('res', res);
            _this.info = res.data;
          }
        });
    },

    //获取上下文列表
    getContext() {
      let _this = this;
      api.contextQuery().then((res) => {
        if (res.code == 0) {
          console.log('contextRes', res);
          _this.elementList = res.data;
        }
      });
    },
  },
  watch: {
    isPopShow(n) {
      let _this = this;
      if (n) {
        if (_this.requestId != '') {
          _this.getDetail();
          _this.getContext();
        }
      }
    },
  },
};
</script>
<style scoped>
.jc_pop_fixed {
  margin-top: -135px;
  margin-left: -300px;
  width: 600px;
  height: 270px;
}
</style>