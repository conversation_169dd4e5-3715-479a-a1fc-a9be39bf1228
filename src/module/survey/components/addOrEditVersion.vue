<template>
	<div>
		<a-modal
			:title="requestNo ? `修改问卷测评版本` : `添加测评问卷版本`"
			:visible="showPop"
			@ok="getValue"
			@cancel="closePop"
		>
			<template slot="footer">
				<a-button key="back" @click="closePop">
					取消
				</a-button>
				<a-button type="primary" key="submit" @click="getValue">
					{{ requestNo ? "修改" : "添加" }}
				</a-button>
			</template>
			<a-layout>
				<a-layout-content
					class="pop_content"
					style="min-height: calc(200px - 120px);"
				>
					<a-row>
						<a-col :span="8" class="label_style">
							<label for=""
								>回访类别<a class="mark_style"></a
							></label>
						</a-col>
						<a-col :span="16">
							<a-select
								class="select_wid"
								allowClear
								placeholder="请选择回访类别"
								v-model="info.visitType"
							>
								<a-select-option
									:value="v.dictValue"
									v-for="v in visitTypeList"
									:key="v.dictCode"
								>
									{{ v.dictLabel }}
								</a-select-option>
							</a-select>
						</a-col>
					</a-row>
					<a-row>
						<a-col :span="8" class="label_style">
							<label for=""
								>状态<a class="mark_style">*</a></label
							>
						</a-col>
						<a-col :span="16">
							<a-radio-group
								name="state"
								v-model="info.state"
								button-style="solid"
							>
								<a-radio-button value="0" key="0"
									>无效</a-radio-button
								>
								<a-radio-button value="1" key="1"
									>有效</a-radio-button
								>
							</a-radio-group>
						</a-col>
					</a-row>
				</a-layout-content>
			</a-layout>
		</a-modal>
	</div>
</template>
<script>
	// import { surveySubjectDetail,queryEnumNoList,surveyVersionAdd,surveyVersionEdit } from "@/service/service.js";
    import api from '../api'
    export default {
		name: "addOrEditVersion",
		data() {
			return {
				info: {
					state: "0",
					visitType: undefined
				},
				visitTypeList: []
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			requestId: {
				type: String,
				default: ""
			},
			requestNo: {
				type: String,
				default: ""
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					console.log(val);
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			}
		},
		components: {},
		created() {
			this.getVisitType();
		},
		methods: {
			//关闭
			closePop() {
				this.showPop = false;
				this.info = {
					state: "0",
					visitType: ""
				};
			},
			getVisitType() {
				let _this = this;
				api.queryEnumNoList('visitType')
					.then(res => {
						if (res.code == "0") {
							_this.visitTypeList = res.data;
						} else {
							_this.$message.error(res.msg);
						}
					})
					.catch(e => {
						_this.$message.error(e.message);
					});
			},
			//提交
			getValue() {
				let _this = this;
				if (_this.requestNo == "") {
					//新增
					let param = {
						subjectId: _this.requestId,
						state: _this.info.state,
						visitType: _this.info.visitType
					};
					api.surveyVersionAdd(param, {
						encode: false,
						post: 2
					}).then(res => {
						if (res.code === 0) {
							_this.$message.success(res.msg);
							_this.$emit("updateList", true);
							_this.closePop();
						} else {
							_this.$message.error(res.msg);
						}
					});
				} else {
					//编辑
					let param = {
						subjectId: _this.requestId,
						subjectNo: _this.requestNo,
						state: _this.info.state,
						visitType: _this.info.visitType
					};
					api.surveyVersionEdit(param, {
						encode: false,
						post: 2
					}).then(res => {
						if (res.code === 0) {
							_this.$message.success(res.msg);
							_this.$emit("updateList", true);
							_this.closePop();
						} else {
							_this.$message.error(res.msg);
						}
					});
				}
			},
			// handleChange() {},
			//获取详情
			getDetail() {
				let _this = this;
				api.surveySubjectDetail(
					{
						subjectNo: "",
						subjectId: _this.requestId
					},
					{
						encode: false
					}
				).then(res => {
					if (res.code === 0) {
						console.info("res", res);
						_this.info = res.data;
					}
				});
			}
		},
		watch: {
			isPopShow(n) {
				let _this = this;
				if (n) {
					if (_this.requestNo != "") {
						_this.getDetail();
					}
				}
			}
		}
	};
</script>
<style scoped>
	.jc_pop_fixed {
		margin-top: -100px;
		margin-left: -175px;
		width: 350px;
		height: 200px;
	}
</style>