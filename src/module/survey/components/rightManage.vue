<template>
	<div class="page_main">
		<a-row class="btn_row">
			<a-col :span="24">
				<a-button
					type="primary"
					icon="plus"
					@click="dealYb('add')"
					:disabled="!id"
					>新增</a-button
				>
				<a-button
					type="primary"
					icon="edit"
					@click="dealYb('edit')"
					:disabled="!(selectedRowKeys.length == 1)"
					>编辑</a-button
				>
				<a-button
					type="primary"
					icon="delete"
					@click="dealYb('remove')"
					:disabled="selectedRowKeys.length == 0"
					>删除</a-button
				>
				<a-button
					type="primary"
					icon="redo"
					@click="dealYb('redo')"
					:disabled="!id"
					>刷新</a-button
				>
				<!-- <a-button type="primary" icon="check" @click="dealYb('check')" :disabled="!id">查看问卷等级</a-button> -->
				<!-- <a-button
					icon="download"
					type="primary"
					@click="downloadExcel"
				>
					导入
				</a-button>
				<a-button
					icon="upload"
					type="primary"
					@click="exportExcel"
				>
					导出
				</a-button> -->
			</a-col>
		</a-row>
		<a-table
			:columns="columns"
			:data-source="data"
			:loading="loading"
			:pagination="pagination"
			:customRow="customRow"
			:row-selection="{
				fixed: true,
				selectedRowKeys: selectedRowKeys,
				onChange: onSelectChange
			}"
			@change="handleChange"
			row-key="key"
		>
			<template slot="name" slot-scope="name">
				<p class="single_omit" :title="name">{{ name }}</p>
			</template>
			<template slot="description" slot-scope="description">
				<p class="single_omit" :title="description">
					{{ description }}
				</p>
			</template>
			<template slot="state" slot-scope="state">
				{{ state | dealType(stateList) }}
			</template>
			<template slot="type" slot-scope="type">
				{{ type | dealType(typeList) }}
			</template>
			<a
				slot="action"
				slot-scope="text, record"
				@click.stop="() => toBusinessConfig(record)"
				>查看答案选项</a
			>
		</a-table>
		<a-pagination
			style="margin-top: 10px;text-align: right;"
			:total="total"
			:current="current"
			:pageSize="pageSize"
			:pageSizeOptions="pageSizeOptions"
			show-size-changer
			show-quick-jumper
			:show-total="total => `共 ${total} 条`"
			@change="change"
			@showSizeChange="showSizeChange"
		>
		</a-pagination>
		<addOrEditQues
			:isPopShow.sync="isPopShow"
			:leftInfo="leftInfo"
			@updateList="updateList"
			:requestId="postId"
		></addOrEditQues>
		<answerList
			:isPopShow.sync="isAnswerPopShow"
			:requestId="queId"
			:requstInfo="requstInfo"
		></answerList>
		<defineList
			:isPopShow.sync="isDefinePopShow"
			:requestId="id"
			:leftInfo="leftInfo"
		></defineList>
		<upload
			:visible.sync="uploadShow"
			:downloadeUrl="downloadeUrl"
			:uploadUrl="uploadUrl"
			downloadeTitle=""
            @success="query"
		/>
	</div>
</template>

<script>
	// import { surveyQuestionList,surveyQuestionDelete } from "@/service/service.js";
	import upload from "@c/upload/upload";
	import api from "../api";
	export default {
		name: "rightManage",
		data() {
			return {
				downloadeUrl: "",
				uploadUrl: "survey/import",
				// constShowDelelte : $hvue.config.constShowDelelte,
				key: "", //搜索key
				isKey: "", //确认搜索Key
				typeList: this.$baseDict.base.qusetionTypeList, //问题类型列表
				stateList: this.$baseDict.base.stateList, //状态列表
				//table start
				//表格数据
				data: [],
				//表头数据
				columns: [
					{
						title: "问题名称",
						dataIndex: "name",
						key: "name",
						sorter: true,
						ellipsis: true,
						width: 200,
						scopedSlots: { customRender: "name" }
					},
					{
						title: "描述",
						dataIndex: "description",
						key: "description",
						sorter: true,
						ellipsis: true,
						scopedSlots: { customRender: "description" }
					},
					{
						title: "状态",
						dataIndex: "state",
						key: "state",
						sorter: true,
						scopedSlots: { customRender: "state" }
					},
					{
						title: "类型",
						dataIndex: "type",
						key: "type",
						sorter: true,
						scopedSlots: { customRender: "type" }
					},
					{
						title: "外部扩展编号",
						dataIndex: "extCode",
						key: "extCode",
						ellipsis: true,
						width: 130,
						sorter: true
					},
					{
						title: "操作",
						key: "operation",
						fixed: "right",
						width: 200,
						scopedSlots: { customRender: "action" }
					}
				],

				selectedRowKeys: [], // Check here to configure the default column
				pagination: false, //是否显示table分页
				loading: false, //是否显示loading
				orderByColumn: "", //排序name
				isAsc: "", //排序方向
				scroll: { x: 900, y: 680 },
				//table end

				//pagination start
				total: 0, //table总页数
				current: 1, //当前页数
				pageSize: 10, //每页条数
				pageSizeOptions: ["10", "20", "50", "100"], //指定每页可以显示多少条
				//pagination end

				//新增弹窗
				isPopShow: false, //弹窗是否显示
				postId: "", //id

				//答案选项弹窗
				isAnswerPopShow: false, //弹窗是否显示
				queId: "", //题目id
				requstInfo: {}, //答案信息

				//问卷等级
				isDefinePopShow: false, //弹窗是否显示
                // queId: "",    //题目id
                
                uploadShow: false
			};
		},
		props: {
			id: {
				type: String,
				default: ""
			},
			leftInfo: {
				type: Object,
				default: () => {
					return {};
				}
			}
		},
		created() {},
		components: {
            upload,
			addOrEditQues: () =>
				import(
					/* webpackChunkName: "addOrEditQues" */ "./addOrEditQues.vue"
				),
			answerList: () =>
				import(/* webpackChunkName: "answerList" */ "./answerList.vue"),
			defineList: () =>
				import(/* webpackChunkName: "defineList" */ "./defineList.vue")
		},
		methods: {
            // 导入downloadExcel
			downloadExcel() {
				this.uploadShow = true;
            },
            
            // 导出exportExcel
			exportExcel() {
                window.location.href = '/bc-manage-server/survey/export'
            },

            query() {
                this.queryTable();
                this.$emit('uploadSuccess')
            },

			//新增刷新
			updateList(param) {
				if (param) {
					this.current = 1;
					this.queryTable();
				}
			},
			//请求table
			queryTable() {
				let _this = this;
				this.loading = true;
				api.surveyQuestionList(
					{
						subjectId: this.leftInfo.subjectId, //唯一编码ID
						pageNum: this.current,
						pageSize: this.pageSize,
						orderByColumn: this.orderByColumn,
						isAsc: this.isAsc
					},
					{
						encode: false
					}
				)
					.then(res => {
						this.loading = false;
						this.selectedRowKeys = [];
						if (res.code == 0) {
							if (res.data) {
								let data = res.data;
								this.total = data.total;
								this.current = data.pageNum;
								this.data = res.data.list;
							} else {
								this.data = [];
							}
						} else {
							_this.$message.error(res.msg);
						}
						console.info("res", res);
					})
					.catch(e => {
						_this.$message.error(e.message);
					});
			},
			//排序
			handleChange(pagination, filters, sorter) {
				this.orderByColumn = sorter.columnKey;
				this.isAsc = sorter.order ? sorter.order.replace("end", "") : "";
				this.queryTable();
			},
			//单选全选
			onSelectChange(selectedRowKeys) {
				console.log("selectedRowKeys changed: ", selectedRowKeys);
				this.selectedRowKeys = selectedRowKeys;
			},
			//点击一行选中效果
			customRow(record, index) {
				let _this = this;
				return {
					on: {
						click: () => {
							if (!_this.selectedRowKeys.includes(index)) {
								_this.selectedRowKeys = [index];
							}
						}
					}
				};
			},
			//页码改变的回调，参数是改变后的页码及每页条数
			change(page, pageSize) {
				console.log(page);
				console.log(pageSize);
				this.current = page;
				this.pageSize = pageSize;
				this.queryTable();
			},
			//总数变化时回调
			showSizeChange(current, size) {
				this.current = current;
				this.pageSize = size;
				this.queryTable();
			},
			//处理
			dealYb(val) {
				let _this = this;
				if (val == "add") {
					//添加
					if (!_this.leftInfo && _this.leftInfo.subjectId == "") {
						_this.$message.error("请选择问卷主题");
						return false;
					} else {
						_this.postId = "";
						_this.isPopShow = true;
					}
				} else if (val == "edit") {
					//编辑
					_this.postId = _this.data[_this.selectedRowKeys[0]].queId; //获取id
					_this.isPopShow = true;
				} else if (val == "remove") {
					//删除
					if (_this.selectedRowKeys.length > 0) {
						_this.remove();
					}
				} else if (val == "redo") {
					//刷新
					_this.queryTable();
				} else if (val == "check") {
					//搜索
					_this.isDefinePopShow = true;
				}
			},
			//删除
			remove() {
				let _this = this;
				_this.$confirm({
					title: "温馨提示",
					content: `是否确认删除?`,
					okType: "danger",
					onOk() {
						_this.deleteApi();
					}
				});
			},
			//请求删除接口
			deleteApi() {
				let _this = this,
					str = [];
				_this.selectedRowKeys.forEach(v => {
					str.push(_this.data[v].queId);
				});
				api.surveyQuestionDelete(
					{ ids: str },
					{
						encode: false,
						post: 2
					}
				)
					.then(res => {
						if (res.code === 0) {
							_this.$message.success(res.msg);
							_this.queryTable();
						} else {
							_this.$message.error(res.msg);
						}
					})
					.catch(e => {
						_this.$message.error(e.message);
					});
			},
			//查看答案选项
			toBusinessConfig(record) {
				this.queId = record.queId;
				this.requstInfo = record;
				this.isAnswerPopShow = true;
			}
		},
		watch: {
			id: {
				handler: function(n) {
					if (n) {
						this.isKey = "";
						this.$nextTick(() => {
							this.current = 1;
							this.queryTable();
						});
					}
				},
				deep: true
			}
		},
		filters: {
			dealType(val, list) {
				if (!val) return false;
				let arr = [];
				arr = list.filter(v => {
					return v.key == val;
				});
				return arr[0] ? arr[0].value : "";
			}
		}
	};
</script>

<style>
</style>
