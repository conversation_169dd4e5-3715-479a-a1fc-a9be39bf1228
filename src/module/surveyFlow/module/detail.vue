<template>
  <a-modal title="问卷详情" :width="800" v-model="showPop" :footer="null">
    <iframe
      v-if="url"
      :src="fileUrl + url"
      frameborder="0"
      width="100%"
      height="600px"
    ></iframe>
    <div v-else>暂无问卷详情数据！</div>
  </a-modal>
</template>
<script>
export default {
  name: "survey_detail",
  inject: ["api"],
  data() {
    return {
      url: "",
      fileUrl: window.$hvue.customConfig.fileUrl
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.formId) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    ok() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    query() {
      this.reset();
      return this.api
        .queryFileUrl({
          bizType: this.parameterData.bizType,
          clientId: this.parameterData.clientId,
          formId: this.parameterData.formId,
        })
        .then((res) => {
          if (res.code != 0) return;
          this.url = res.data;
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
