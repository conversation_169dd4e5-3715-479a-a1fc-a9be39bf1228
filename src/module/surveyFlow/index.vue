<template>
  <a-card title="问卷流水" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query(1)" @reset="reset">
          <a-form-model-item label="问卷名称">
            <a-input
              v-model="tableForm.subjectName"
              placeholder="请输入问卷名称"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="状态">
            <a-select v-model="tableForm.visitStatus" placeholder="请选择">
              <a-select-option
                v-for="(item, index) in [
                  '待完成',
                  '已完成',
                  '已作废',
                  '超时转人工',
                ]"
                :key="index"
                :value="index + ''"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="来源渠道">
            <a-input
              v-model="tableForm.channelType"
              placeholder="请输入来源渠道"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="发放时间">
            <a-range-picker
              v-model="tableForm.beginRangeDate"
              @change="(date, dateString) => onChange(date, dateString, 0)"
            />
          </a-form-model-item>
          <a-form-model-item label="完成时间">
            <a-range-picker
              v-model="tableForm.endRangeDate"
              @change="(date, dateString) => onChange(date, dateString, 1)"
            />
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcReturnVisitRecord/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="reqTableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button type="primary" icon="redo" @click="reset(1)">刷新</a-button>
          <a-button icon="upload" type="primary" @click="exportExcel">
            导出
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="detail(data)"
            >查看问卷详情</a-button
          >
        </template>
      </tk-table>
      <LookComponent
        :isPopShow.sync="isLookPopShow"
        :parameterData="selectData"
      />
    </div>
  </a-card>
</template>

<script>
// 引入查看弹窗
import LookComponent from "./module/detail";
import api from "./api";
// 默认表单属性
const defaultForm = {
  subjectName: "",
  visitStatus: undefined,
  channelType: "",
  createDateBeginTime: "",
  createDateEndTime: "",
  completeDateBeginTime: "",
  completeDateEndTime: "",
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "id",
          label: "流水号",
          isSorter: true,
        },
        {
          field: "clientId",
          label: "客户号",
          isSorter: false,
        },
        {
          field: "clientName",
          label: "姓名",
          isSorter: false,
        },
        {
          field: "mobileTel",
          label: "手机号",
          isSorter: false,
        },
        {
          field: "subjectName",
          label: "问卷名称",
          isSorter: false,
        },
        {
          field: "subjectVersion",
          label: "问卷版本",
          isSorter: false,
        },
        {
          field: "visitStatus",
          label: "状态",
          isSorter: false,
          filter: (item) => this.filterStatus(item),
        },
        {
          field: "egliStatus",
          label: "是否合规",
          isSorter: false,
          filter: (item) => (item === "0" ? "否" : "是"),
        },
        {
          field: "toVisit",
          label: "是否转人工",
          isSorter: false,
          filter: (item) => (item === "0" ? "否" : "是"),
        },
        {
          field: "channelType",
          label: "来源渠道",
          isSorter: false,
        },
        {
          field: "createDate",
          label: "发放时间",
          isSorter: true,
        },
        {
          field: "completeDate",
          label: "完成时间",
          isSorter: true,
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      tableForm: {
        subjectName: "",
        visitStatus: undefined,
        channelType: "",
        createDateBeginTime: "",
        createDateEndTime: "",
        completeDateBeginTime: "",
        completeDateEndTime: "",
      },
      reqTableForm: {},
      selectedRowKeys: [], // Check here to configure the default column
      isLookPopShow: false,
      selectData: {},
    };
  },
  provide: { api: api },
  components: { LookComponent },
  methods: {
    filterStatus(item) {
      let status;
      switch (item) {
        case "0":
          status = "待完成";
          break;
        case "1":
          status = "已完成";
          break;
        case "2":
          status = "已作废";
          break;
        case "3":
          status = "超时转人工";
          break;
        default:
          break;
      }
      return status;
    },
    detail(data) {
      this.isLookPopShow = true;
      this.selectData = data;
    },
    exportExcel() {
      let url = "/bc-manage-server/bcReturnVisitRecord/exportVisitRecord";
      if (this.selectedRowKeys.length !== 0) {
        url = url + "?ids=" + this.selectedRowKeys.join(",");
      } else {
        Object.keys(this.reqTableForm).forEach((item) => {
          if (!this.reqTableForm[item]) {
            delete this.reqTableForm[item];
          }
        });
        if (Object.keys(this.reqTableForm).length !== 0) {
          Object.keys(this.reqTableForm).forEach((item, index) => {
            if (index === 0) {
              url =
                url +
                "?" +
                item +
                "=" +
                Object.values(this.reqTableForm)[index];
            } else {
              url =
                url +
                "&" +
                item +
                "=" +
                Object.values(this.reqTableForm)[index];
            }
          });
        }
      }
      window.location.href = url;
    },
    onChange(date, dateString, type) {
      if (!type) {
        this.tableForm.createDateBeginTime = dateString[0];
        this.tableForm.createDateEndTime = dateString[1];
      } else {
        this.tableForm.completeDateBeginTime = dateString[0];
        this.tableForm.completeDateBeginTime = dateString[1];
      }
    },
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableForm.createDateBeginTime)) >
        Date.parse(this.DateFormat(this.tableForm.createDateEndTime))
      ) {
        param["createDateBeginTime"] = "";
        param["createDateEndTime"] = "";
      }
      if (
        Date.parse(this.DateFormat(this.tableForm.completeDateBeginTime)) >
        Date.parse(this.DateFormat(this.tableForm.completeDateBeginTime))
      ) {
        param["completeDateBeginTime"] = "";
        param["completeDateBeginTime"] = "";
      }
      delete param.beginRangeDate;
      delete param.endRangeDate;
      return param;
    },
    query(type) {
      if (type) {
        this.reqTableForm = JSON.parse(JSON.stringify(this.tableForm));
      }
      this.$nextTick(() => {
        this.$refs.table.getTableData();
        this.selectedRowKeys = [];
      });
    },
    // 重置
    reset(type) {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
      if (type) {
        this.$nextTick(() => {
          this.$refs.table.getTableData();
          this.selectedRowKeys = [];
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
