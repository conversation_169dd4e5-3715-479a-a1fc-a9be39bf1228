// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

  /**
   * 新增系统参数
   * @param {Object} param
   * - configKey {String} 参数键名
   * - configName {String} 参数名称
   * - configType {String} 系统内置
   * - configValue {String} 参数键值
   * - createBy {String} 创建者
   * - status {String} 当前状态
   * - updateBy {String} 更新者
   */
  @Parameters(["configKey", "configName", "configType", "configValue", "createBy", "status", "updateBy"])
  addConfig() {
    return this.services.initPost({ reqUrl: "config/add", param: this.param });
  }

  /**
   * 修改系统参数
   * @param {Object} param
   * - configId {String} 参数主键ID
   * - configKey {String} 参数键名
   * - configName {String} 参数名称
   * - configType {String} 系统内置
   * - configValue {String} 参数键值
   * - createBy {String} 创建者
   * - status {String} 当前状态
   * - updateBy {String} 更新者
   */
  @Parameters(["configId","configKey", "configName", "configType", "configValue", "createBy", "status", "updateBy"])
  editConfig() {
    return this.services.initPost({ reqUrl: "config/edit", param: this.param });
  }

  /**
   * 删除单个系统参数
   * @param {Object} param
   * - configId {String} 参数主键ID
   */
  @Parameters(["configIds"])
  deleteConfig() {
    return this.services.initPost({ reqUrl: "config/delete", param: this.param });
  }

  /**
   * 删除多个系统参数
   * @param {Object} param
   * - configIds {String} 参数主键ID列表，多个以,分隔
   */
  @Parameters(["configIds"])
  deletesConfig() {
    return this.services.initPost({ reqUrl: "config/deletes", param: this.param });
  }

  /**
   * 查询系统参数列表
   * @param {Object} param
   * - beginTime {String} 开始时间
   * - endTime {String} 结束时间
   * - limit {integer} 查询数量
   * - offset {integer} 起始位置
   * - orderBy {String} 排序字段
   * - searchValue {String} 搜索关键字
   */
  @Parameters(["beginTime", "endTime", "limit", "offset", "orderBy", "searchValue"])
  queryConfigList() {
    return this.services.initGet({ reqUrl: "config/List", param: this.param });
  }

  /**
   * 查询系统参数Map
   * @param {Object} param
   * - configKey {String} 参数键名
   */
  @Parameters(["configKey"])
  queryConfigMap() {
    return this.services.initGet({ reqUrl: "config/map", param: this.param });
  }

  /**
   * 查询系统参数翻页列表
   * @param {Object} param
   * - beginTime {String} 开始时间
   * - endTime {String} 结束时间
   * - pageSize {integer} 每页数量
   * - pageNumber {integer} 当前页码
   * - orderBy {String} 排序字段
   * - searchValue {String} 搜索关键字
   */
  @Parameters(["beginTime", "endTime", "pageSize", "pageNum", "orderBy", "searchValue"])
  queryConfigPage() {
    return this.services.initGet({ reqUrl: "config/page", param: this.param });
  }

  /**
   * 查询指定系统参数详情
   * @param {Object} param
   * - configId {String} 参数主键ID
   */
  @Parameters(["configId"])
  queryConfigDetail() {
    return this.services.initGet({ reqUrl: "config/query", param: this.param });
  }

}

export default new api();
