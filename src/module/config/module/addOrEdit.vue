<template>
  <a-modal
    :title="`${typeTitle}系统参数`"
    :width="500"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="参数键名" prop="configKey">
        <a-input v-model="form.configKey" placeholder="请输入参数键名" />
      </a-form-model-item>
      <a-form-model-item label="参数名称" prop="configName">
        <a-input v-model="form.configName" placeholder="请输入参数名称" />
      </a-form-model-item>
      <a-form-model-item label="参数键值" prop="configValue">
        <a-input v-model="form.configValue" placeholder="请输入参数键值" />
      </a-form-model-item>
      <a-form-model-item label="系统内置" prop="configType">
        <a-switch v-model="form.configType" />
      </a-form-model-item>
      <a-form-model-item label="状态" prop="status">
        <a-switch v-model="form.status" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultFrom = {
  configKey: "",
  configName: "",
  configValue: "",
  configType: true,
  status: true,
};

export default {
  name: "config_AddOrEdit",
  inject: ["api"],
  data() {
    return {
      form: Object.assign({}, defaultFrom), //表单数据,
      rules: {
        configKey: [
          { required: true, message: "参数键名不能为空", trigger: "blur" },
        ],
        configName: [
          { required: true, message: "参数名称不能为空", trigger: "blur" },
        ],
        configValue: [
          { required: true, message: "参数键值不能为空", trigger: "blur" },
        ],
      },
      // 异步加载
      confirmLoading: false,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 操作类型
    operationType: {
      type: String,
      default: "add",
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
    typeTitle() {
      return this.operationType == "add" ? "添加" : "修改";
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.configId) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultFrom);
      this.confirmLoading = false;
    },
    query() {
      this.reset();
      return this.api
        .queryConfigDetail({ configId: this.parameterData.configId })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          data.configType = data.configType == "Y" ? true : false;
          this.form = data;
        });
    },
    // 提交数据权限分组创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        param.configType = param.configType ? "Y" : "N";
        let callback = ({ code, msg }) => {
          if (code != 0) {
            this.confirmLoading = false;
            return this.$message.error(`系统参数${this.typeTitle}失败：${msg}`);
          }
          this.$message.success(`系统参数${this.typeTitle}成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
        };
        this.operationType == "add"
          ? this.api.addConfig(param).then(callback)
          : this.api.editConfig(param).then(callback);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>