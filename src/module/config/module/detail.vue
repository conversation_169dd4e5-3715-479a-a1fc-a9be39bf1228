<!--
 * @Author: liu quan
 * @Date: 2021-07-13 21:12:28
 * @LastEditors: liu quan
 * @LastEditTime: 2021-07-20 13:30:08
 * @FilePath: \bus-child-view\src\module\config\module\detail.vue
-->
<template>
  <a-modal
    :title="`查看系统参数`"
    :width="500"
    v-model="showPop"
    :maskClosable="false"
  >
    <a-descriptions bordered :column="1">
      <a-descriptions-item label="参数键名">
        {{form.configKey}}
      </a-descriptions-item>
      <a-descriptions-item label="参数名称">
        {{form.configName}}
      </a-descriptions-item>
      <a-descriptions-item label="参数键值">
       {{form.configValue}}
      </a-descriptions-item>
      <a-descriptions-item label="系统内置">
        <a-switch v-model="form.configType" disabled />
      </a-descriptions-item>
      <a-descriptions-item label="状态">
        <a-switch v-model="form.status" disabled />
      </a-descriptions-item>
    </a-descriptions>
    <br/>
    <template slot="footer">
      <a-button type="primary" @click="ok">确定</a-button>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: "config_detail",
  inject: ["api"],
  data() {
    return {
      form: {}, //表单数据,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.configId) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    ok() {
      this.showPop = false;
      this.reset()
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    query() {
      this.reset();
      return this.api
        .queryConfigDetail({ configId: this.parameterData.configId })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          data.configType = data.configType == "Y" ? true : false;
          this.form = data;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.ant-descriptions-bordered.ant-descriptions-item-content, 
.ant-descriptions-bordered .ant-descriptions-item-label{
  min-width: 110px;
}
.ant-descriptions-item-content{
  word-break: break-all;
}
</style>