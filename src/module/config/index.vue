<template>
  <a-card title="系统配置列表" :bordered="false">
    <div class="searchFrom">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="搜索关键字" prop="searchValue">
            <a-input v-model="tableForm.searchValue" placeholder="请输入搜索关键字" />
          </a-form-model-item>
          <a-form-model-item label="开始时间">
            <a-date-picker v-model="tableForm.beginTime" valueFormat="YYYY-MM-DD" placeholder="开始时间" @openChange="openChange" />
          </a-form-model-item>
         <a-form-model-item label="结束时间">
            <a-date-picker v-model="tableForm.endTime" valueFormat="YYYY-MM-DD" placeholder="结束时间" @openChange="openChange" />
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table ref="table" :tableData.sync="columns" getMethods="work-manage-server/config/page" :intercept-response="intercept_response" :isSelected="true" :isPaging="true" :tableFromFilter="tableFormFilter" :tableFrom="tableForm" :selectedRowKeys.sync="selectedRowKeys" tableId="configId">
        <div class="table-buttun-area" slot="tableHeader">
          <a-button type="primary" icon="plus" @click="add"> 新增 </a-button>
          <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove"> 删除 </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click="look(data)"> 查看 </a-button>
          <a-button type="link" @click="modify(data)"> 修改 </a-button>
        </template>
      </tk-table>
      <addOrEdit :isPopShow.sync="isPopShow" @success="query" :operationType="operationType" :parameterData="selectData" />
      <LookComponent :isPopShow.sync="isLookPopShow" :parameterData="selectData" />
    </div>
  </a-card>
</template>

<script>
// 引入添加和编辑弹窗
import addOrEdit from './module/addOrEdit';
// 引入查看弹窗
import LookComponent from './module/detail';

import api from './api';
// 默认表单属性
const defaultForm = {
  searchValue: "", // 搜索关键字
  beginTime: "", // 开始时间
  endTime: "", // 结束时间
};
export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      //表头数据
      columns: [
        { field: "configKey", label: "参数键名", isSorter: true },
        { field: "configName", label: "参数名称", isSorter: true },
        { field: "configType", label: "系统内置", filter: item => item == 'Y' ? '是' : '否', isSorter: true },
        { field: "configValue", label: "参数键值", isSorter: true },
        { field: "createBy", label: "创建者", isSorter: true },
        { field: "createTime", label: "创建时间", filter: item => !item ? '--' : this.DateFormat(item).format("yyyy-MM-dd"), isSorter: true },
        { field: "status", label: "是否生效", filter: item => item == '0' ? '生效' : '失效', isSorter: true },
        { field: "operation", label: "操作", align: "center" },
      ],
      // 查询接口所需字段
      tableForm: {
        searchValue: "", // 搜索关键字
        beginTime: "", // 开始时间
        endTime: "", // 结束时间
      },
      selectedRowKeys: [], // Check here to configure the default column

      //新增弹窗
      isPopShow: false,   //弹窗是否显示
      //查看弹窗
      isLookPopShow: false,
      selectData: {},
      operationType: "add",
    }
  },
  provide: { "api": api, },
  components: { addOrEdit, LookComponent },

  methods: {
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 表单重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    add(){ //点击新增按钮
      this.isPopShow = true;
      this.operationType='add';
      this.selectData={}
    },
    // 用户修改对应的岗位
    modify(data) {
      this.isPopShow = true;
      this.operationType = "set";
      this.selectData = data;
    },
    // 查看
    look(data) {
      this.isLookPopShow = true;
      this.selectData = data;
    },
    openChange(status) {
      if (status == false) {
        // 切出事件，用于组件缓冲数据
        setTimeout(() => {
          if (this.tableForm.beginTime && this.tableForm.endTime) {
            if (Date.parse(this.DateFormat(this.tableForm.beginTime)) > Date.parse(this.DateFormat(this.tableForm.endTime))) return this.$message.error('结束日期不应早于开始日期');
          }
        })
      }
    },
    tableFormFilter(param) {
      if (Date.parse(this.DateFormat(this.tableForm.beginTime)) > Date.parse(this.DateFormat(this.tableForm.endTime))) {
        param["beginTime"] = "";
        param["endTime"] = "";
      }
      return param;
    },
    // 删除对应的权限
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: '删除系统参数',
          content: () => <p>确定删除当前勾选的系统参数数据？</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            api.deletesConfig({ configIds: this.selectedRowKeys.join(",") }).then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`删除系统参数失败：${msg}`);
              this.$message.success('删除系统参数成功！');
              this.$refs.table.getTableData();
              this.selectedRowKeys = [];
            })
          },
        });
      }
    },
  },
}
</script>

<style lang="scss" scoped>
</style>