<template>
  <a-card title="行权融资白名单" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="行权代码">
            <a-input
              v-model="tableForm.efCode"
              placeholder="请输入行权代码"
            ></a-input>
          </a-form-model-item>

          <a-form-model-item label="状态">
            <a-select
              v-model="tableForm.status"
              placeholder="全部"
              :options="dictMap['bc.white.status']"
              :allowClear="true"
            >
            </a-select>
          </a-form-model-item>

          <a-form-model-item label="协议编号">
            <a-input
              v-model="tableForm.agreeId"
              placeholder="请输入协议编号"
            ></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        getMethods="bc-manage-server/bcEfWhitelist/page"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
        :tableFromFilter="tableFormFilter"
      >
        <div slot="validTime" slot-scope="data">
          {{ data.beaginTime }} ~ {{ data.endTime }}
        </div>
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add">
            手动添加
          </a-button>
          <a-button icon="download" type="primary" @click="importAdd">
            名单导入
          </a-button>
        </div>

        <div slot="operation" slot-scope="data">
          <a-button
            style="margin: 0 !important"
            type="link"
            @click.stop="look(data)"
          >
            查看
          </a-button>
          <a-button
            v-if="data.status == '1'"
            style="margin: 0 !important"
            type="link"
            @click.stop="setInvalidity(data)"
          >
            设为失效
          </a-button>
          <a-button
            v-if="data.status == '0'"
            style="margin: 0 !important"
            type="link"
            @click.stop="modify(data)"
          >
            编辑
          </a-button>
        </div>
      </tk-table>
    </div>
    <add
      :isPopShow.sync="isAddPopShow"
      :dictMap="dictMap"
      @success="query"
    ></add>
    <importAdd
      :isPopShow.sync="isImportPopShow"
      :dictMap="dictMap"
      @success="query"
    ></importAdd>
    <LookComponent
      :isPopShow.sync="isLookPopShow"
      :parameterData="selectData"
      :dictMap="dictMap"
    />
    <modifyComponent
      :isPopShow.sync="isEditPopShow"
      :parameterData="selectData"
      :dictMap="dictMap"
      @success="query"
    />
    <importModify
      :isPopShow.sync="isImportEditPopShow"
      :parameterData="selectData"
      :dictMap="dictMap"
      @success="query"
    />
  </a-card>
</template>

<script>
import api from "./api";
import add from "./module/add";
import importAdd from "./module/importAdd";
import LookComponent from "./module/detail";
import modifyComponent from "./module/edit";
import importModify from "./module/importEdit";
// 默认表单属性
const defaultForm = {
  efCode: "", // 行权代码
  status: undefined, // 状态（0无效；1有效）
  agreeId: "", // 协议编号
  beginTime: "", // 开始时间
  endTime: "", // 结束时间
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        {
          field: "id",
          label: "序号",
          isSorter: false,
          width: 80,
        },
        {
          field: "efCode",
          label: "行权代码",
          isSorter: false,
          width: 120,
        },
        {
          field: "validTime",
          label: "有效期",
          isSorter: false,
          // align: "center",
          width: 200,
        },
        {
          field: "agreeId",
          label: "协议编号",
          isSorter: false,
          width: 200,
        },
        {
          field: "updatedBy",
          label: "最后操作人",
          isSorter: false,
        },
        {
          field: "updatedTime",
          label: "最后操作时间",
          isSorter: false,
        },
        {
          field: "status",
          label: "状态",
          isSorter: false,
          filter: (item) =>
            item === "0"
              ? "未生效"
              : item === "1"
              ? "生效中"
              : item === "2"
              ? "已失效"
              : "",
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          width: 200,
          fixed: "right",
        },
      ],
      tableForm: {
        efCode: "", // 行权代码
        agreeId: "", // 协议编号
        status: undefined, // 状态（0：无效、1：有效）
      },
      selectedRowKeys: [], // Check here to configure the default column
      businessList: [],
      smsSendTemplateList: [],
      dictMap: {
        "bc.white.status": [],
      },
      selectData: {},
      isAddPopShow: false, //添加弹窗是否显示
      isImportPopShow: false,
      isLookPopShow: false,
      isEditPopShow: false,
      isImportEditPopShow: false,
    };
  },
  provide: { api: api },
  components: { add, importAdd, LookComponent, modifyComponent, importModify },
  created() {
    Promise.all([this.queryDictMap()]).finally();
  },
  methods: {
    onChange() {},
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableForm.beginTime)) >
        Date.parse(this.DateFormat(this.tableForm.endTime))
      ) {
        param["beginTime"] = "";
        param["endTime"] = "";
      }
      delete param.rangeDate;
      return param;
    },
    queryDictMap() {
      return new Promise((resolve) => {
        Object.keys(this.dictMap).forEach((item) => {
          this.$dict.dictContent(item).then((data) => {
            this.dictMap[item] = (data || []).map((data) => {
              return {
                label: data.dictLabel,
                value: data.dictValue,
              };
            });
            resolve();
          });
        });
      });
    },
    getDictText(key, value) {
      let results = this.dictMap[key] || [];
      results = results.filter((item) => {
        return item.value == value;
      });
      return (results && results.length && results[0].label) || value || "";
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    add() {
      this.isAddPopShow = true;
      this.selectData = {};
    },
    importAdd() {
      this.isImportPopShow = true;
    },
    // 查看
    look(data) {
      this.isLookPopShow = true;
      this.selectData = data;
    },
    // 点击修改按钮
    modify(data) {
      if (data.fileName) {
        this.isImportEditPopShow = true;
      } else {
        this.isEditPopShow = true;
      }
      this.selectData = data;
    },
    setInvalidity(data) {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确定将此条记录置为失效?`,
        okType: "danger",
        onOk() {
          api
            .edit({
              efCode: data.efCode,
              status: "2",
              id: data.id,
            })
            .then((res) => {
              if (res.code === 0) {
                _this.$message.success(res.msg);
                _this.query();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch((e) => {
              _this.$message.error(e.message);
            });
        },
      });
    },

    downloadExcel() {},
  },
};
</script>

<style lang="scss" scoped></style>
