// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 白名单手动添加
   * @param {Object} param
   */
  @Parameters(["_data"])
  handAdd() {
    return this.services.initPost({
      reqUrl: "bcEfWhitelist/handAdd",
      param: this.param,
    });
  }

  /**
   * 白名单上传
   * @param {Object} param
   */
  @Parameters(["_data"])
  import() {
    return this.services.initPost({
      reqUrl: "bcEfWhitelist/import",
      param: this.param,
    });
  }
  /**
   * 白名单下载
   * @param {Object} param
   */
  @Parameters(["_data"])
  dowloadBcEfWhitelist() {
    return this.services.initGet({
      reqUrl: "bcEfWhitelist/dowloadBcEfWhitelist",
      param: this.param,
    });
  }
  /**
   * 白名单删除
   * @param {Object} param
   */
  @Parameters(["_data"])
  deletebcEfWhitelist() {
    return this.services.initPost({
      reqUrl: "bcEfWhitelist/deletebcEfWhitelist",
      param: this.param,
    });
  }
  /**
   * 白名单编辑
   * @param {Object} param
   */
  @Parameters(["_data"])
  edit() {
    return this.services.initPost({
      reqUrl: "bcEfWhitelist/edit",
      param: this.param,
    });
  }
}

export default new api();
