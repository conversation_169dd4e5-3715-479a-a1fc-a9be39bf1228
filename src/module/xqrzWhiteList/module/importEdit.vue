<template>
  <a-modal
    title="编辑"
    :width="800"
    v-model="showPop"
    ok-text="保存"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="行权代码" prop="efCode">
        <a-input
          v-model="form.efCode"
          placeholder="请输入行权代码"
        ></a-input>
      </a-form-model-item>
      <a-form-model-item label="名单" prop="fileName">
        <div>
          <a-button type="link" @click="downloademon">
            <a-icon type="download" />下载导入模板
          </a-button>
        </div>
        <div v-if="defaultFileList.length">
          <div>
            {{ parameterData.fileName }}
            <a-button type="link" @click="exportExcel">
              下载文件
            </a-button>
            <a-button style="color: red" type="link" @click="deleteFile">
              删除文件
            </a-button>
          </div>
        </div>
        <a-upload
          :fileList.sync="fileList"
          name="file"
          :disabled="defaultFileList.length > 0"
          :beforeUpload="() => false"
          @change="handleChange"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, .json"
        >
          <a-button type="primary" :disabled="defaultFileList.length > 0">
            <a-icon type="upload" /> 上传文件
          </a-button>
        </a-upload>
      </a-form-model-item>
      <a-form-model-item label="生效时间" prop="rangeDate">
        <a-range-picker
          v-model="form.rangeDate"
          :show-time="timePickerOptions"
          :disabled="checked"
          style="margin-right: 8px;"
          @change="onChange"
        />
        <a-checkbox v-model="checked" @change="setLongTime">
          永久有效
        </a-checkbox>
      </a-form-model-item>
      <a-form-model-item label="协议编号" prop="agreeIdList">
        <div
          v-for="(item, index) in form.agreeIdList"
          :key="index"
          class="makeInline"
          style="margin-bottom:8px"
        >
          <div class="makeInline">
            <div>
              <a-input
                v-model="item.agreeId"
                placeholder="请输入协议编号"
                allowClear
                style="margin-right:8px; width: 400px;"
              ></a-input>
              <a-space>
                <a-icon
                  v-if="form.agreeIdList.length > 1"
                  type="minus"
                  style="fontSize:20px;color:red"
                  @click="deleteAgree(index)"
                />
                <a-icon
                  v-if="index == form.agreeIdList.length - 1"
                  type="plus"
                  style="fontSize:20px;"
                  @click="addAgree"
                />
              </a-space>
            </div>
          </div>
        </div>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { request } from "bus-common-component/lib/extension";
import moment from "moment";
// 默认表单属性
const defaultForm = {
  id: "",
  efCode: "", // 行权代码
  fileName: "", // 文件名称
  beaginTime: "",
  endTime: "",
  rangeDate: [],
  agreeId: "", // 协议编号
  agreeIdList: [
    {
      agreeId: "",
    },
  ],
};

export default {
  name: "xqrz_importEdit",
  inject: ["api"],
  data() {
    return {
      form: Object.assign({}, defaultForm), //表单数据
      file: {},
      fileList: [],
      defaultFileList: [],
      // 异步加载
      confirmLoading: false,
      timePickerOptions: {
        format: "HH:mm:ss", // 设置时间格式为小时:分钟:秒
        defaultValue: [
          moment("00:00:00", "HH:mm:ss"),
          moment("23:59:59", "HH:mm:ss"),
        ], // 设置默认结束时间为23:59:59
      },
      rules: {
        efCode: [
          { required: true, message: "行权代码不能为空", trigger: "blur" },
        ],
        rangeDate: [
          { required: true, message: "生效时间不能为空", trigger: "blur" },
        ],
        agreeIdList: [
          { required: true, message: "协议编号不能为空", trigger: "blur" },
        ],
      },
      checked: false,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false,
    },
    dictMap: {
      type: Object,
      default: () => {},
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.file = undefined;
        this.fileList = [];
      } else {
        this.reset();
      }
      this.$nextTick(() => {});
    },
    parameterData() {
      this.initData();
    },
  },
  methods: {
    initData() {
      let parameterData = this.parameterData;
      if (Object.keys(parameterData).length) {
        Object.keys(this.form).forEach((key) => {
          if (key !== "rangeDate") {
            this.form[key] = parameterData[key];
          }
        });
        if (!this.form.rangeDate.length) {
          this.form.rangeDate.push(parameterData.beaginTime);
          this.form.rangeDate.push(parameterData.endTime);
        }
        if (parameterData.agreeId) {
          let list = parameterData.agreeId.split("|");
          this.form.agreeIdList = list.map((item) => {
            return {
              agreeId: item,
            };
          });
        }
        if (parameterData.endTime.includes("9999-12-31")) {
          this.checked = true;
        }
      }
      this.defaultFileList.push({
        fileName: parameterData.fileName,
        filePath: parameterData.filePath,
      });
    },
    downloademon() {
      window.location.href = "/bc-manage-view/行权名单模板表.xlsx";
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm, {
        agreeIdList: [{ agreeId: "" }],
      });
      this.form.rangeDate = [];
      this.checked = false;
      this.confirmLoading = false;
    },
    getStringLength(string) {
      // 使用正则表达式匹配中文和全角字符
      var pattern = /[\u4e00-\u9fa5\uff00-\uffff]/g;
      // 获取中文和全角字符的个数
      var chineseCount = (string.match(pattern) || []).length;
      // 返回字符串长度
      return string.length + chineseCount;
    },
    checkArrayForEmptyValues(arr) {
      return arr.every(function(obj) {
        return Object.values(obj).every(function(value) {
          return value !== null && value !== undefined && value !== "";
        });
      });
    },
    addList() {
      this.form.bcEfWhitelistClientsAddDTO.push({
        idType: undefined,
        idNo: "",
        clientName: "",
      });
    },
    deleteItem(index) {
      this.form.bcEfWhitelistClientsAddDTO.splice(index, 1);
    },
    addAgree() {
      this.form.agreeIdList.push({
        agreeId: "",
      });
    },
    deleteAgree(index) {
      this.form.agreeIdList.splice(index, 1);
    },
    onChange(date, dateString) {
      console.log(this.form.rangeDate[0]);
      this.form.beaginTime = dateString[0];
      this.form.endTime = dateString[1];
    },
    setLongTime(e) {
      if (e.target.checked) {
        let nowDate = new Date();
        let today = `${nowDate.getFullYear()}-${(
          "0" +
          (nowDate.getMonth() + 1)
        ).slice(-2)}-${("0" + nowDate.getDate()).slice(-2)}`;
        if (this.form.rangeDate.length) {
          this.form.rangeDate = [];
        }
        this.form.beaginTime = `${today} 00:00:00`;
        this.form.endTime = "9999-12-31 23:59:59";
        this.form.rangeDate.push(today);
        this.form.rangeDate.push("9999-12-31 23:59:59");
      }
    },
    compareDates(date) {
      const d1 = new Date(date);
      const d2 = new Date();

      if (d1 < d2) {
        return -1; // date1 小于 date2
      } else if (d1 > d2) {
        return 1; // date1 大于 date2
      } else {
        return 0; // date1 等于 date2
      }
    },
    query() {},
    deleteFile() {
      let { fileName, filePath } = this.parameterData;
      let _this = this;
      if (fileName && filePath) {
        // let baseUrl = "/bc-manage-server/bcEfWhitelist/dowloadBcEfWhitelist";
        // window.location.href = `${baseUrl}?fileName=${fileName}&filePath=${filePath}`;
        this.$confirm({
          title: "温馨提示",
          content: "是否确认删除?",
          okType: "danger",
          onOk() {
            _this.defaultFileList = [];
          },
        });
      } else {
        this.$message.error("未找到可删除的名单");
      }
    },
    exportExcel() {
      let { fileName, filePath } = this.parameterData;
      console.log(fileName, filePath);
      if (fileName && filePath) {
        let baseUrl = "/bc-manage-server/bcEfWhitelist/dowloadBcEfWhitelist";
        window.location.href = `${baseUrl}?fileName=${fileName}&filePath=${filePath}`;
        // this.api
        //   .dowloadBcEfWhitelist({
        //     fileName,
        //     filePath,
        //   })
        //   .then(({ code, msg }) => {
        //     if (code != 0) {
        //       return this.$message.error(`下载失败：${msg}`);
        //     }
        //   })
        //   .catch((err) => {
        //     this.$message.error(`下载失败：${err}`);
        //   });
      } else {
        this.$message.error("未找到可下载的名单");
      }
    },
    handleChange({ file, fileList }) {
      let list = fileList.filter((item) => item.name == file.name);
      if (list.length > 1) {
        list.pop();
      }
      this.fileList = list;
      this.form.fileName = file.name;
      this.file = file;
    },
    // 提交数据权限分组创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if (this.getStringLength(this.form.efCode) > 20) {
          return this.$message.error("行权代码必须在20位字符以内!");
        }
        if (
          (!this.file || this.fileList.length <= 0) &&
          this.defaultFileList.length <= 0
        )
          return this.$message.error(`上传文件不能为空`);
        if (this.compareDates(this.form.endTime) < 0) {
          return this.$message.error("结束时间不能小于今天!");
        }
        if (!this.checkArrayForEmptyValues(this.form.agreeIdList)) {
          return this.$message.error("协议编号未补充完整!");
        }
        this.form.agreeId = this.form.agreeIdList
          .map((item) => item.agreeId)
          .join("|");
        let formData = new FormData();
        let otherParams = JSON.parse(JSON.stringify(this.form));
        if (!this.defaultFileList.length) {
          formData.append("file", this.file);
          for (var key in otherParams) {
            if (Object.prototype.hasOwnProperty.call(otherParams, key)) {
              formData.append(key, otherParams[key]);
            }
          }
          new request({ address: "/bc-manage-server" })
            .upload({
              reqUrl: "bcEfWhitelist/import",
              param: formData,
            })
            .then(({ code, msg }) => {
              this.confirmLoading = false;
              if (code != 0)
                return this.$message.error(`白名单修改失败: ${msg}`);
              this.$message.success("白名单修改成功！");
              this.showPop = false;
              this.$emit("success");
            });
        } else {
          let callback = ({ code, msg }) => {
            this.confirmLoading = false;
            if (code != 0) return this.$message.error(`白名单修改失败: ${msg}`);
            this.$message.success(`白名单修改成功！`);
            // 关闭弹窗
            this.showPop = false;
            // 通知操作成功
            this.$emit("success");
            // 重置表单
            this.reset();
          };
          this.api.edit(otherParams).then(callback);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.makeInline {
  display: flex;
  align-items: center;
}
</style>
