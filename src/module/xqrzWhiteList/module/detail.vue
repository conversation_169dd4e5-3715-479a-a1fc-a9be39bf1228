<template>
  <a-modal title="查看" :width="800" v-model="showPop" :maskClosable="false">
    <template slot="footer">
      <a-button key="back" @click="submit">
        返回
      </a-button>
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="行权代码" prop="efCode">
        <a-input
          v-model="form.efCode"
          :disabled="true"
          placeholder="请输入行权代码"
        ></a-input>
      </a-form-model-item>
      <a-form-model-item
        v-if="!parameterData.fileName"
        label="名单"
        prop="bcEfWhitelistClientsList"
      >
        <div
          v-for="(item, index) in form.bcEfWhitelistClientsList"
          :key="index"
          class="makeInline"
          style="margin-bottom:8px"
        >
          <div class="makeInline">
            <div>
              <!-- 选择内容 -->
              <a-select
                v-model="item.idType"
                style="margin-right:8px;width: 150px"
                placeholder="请选择证件类型"
                :disabled="true"
                :allowClear="true"
              >
                <a-select-option
                  v-for="(item, index) in [
                    { idTypeName: '身份证', idType: '0' },
                    { idTypeName: '港澳居民来往内地通行证', idType: 'G' },
                    { idTypeName: '台湾居民来往大陆通行证', idType: 'H' },
                    { idTypeName: '港澳台居民居住证', idType: 'l' },
                  ]"
                  :key="index"
                  :value="item.idType"
                >
                  {{ item.idTypeName }}
                </a-select-option>
              </a-select>

              <a-input
                v-model="item.idNo"
                :disabled="true"
                placeholder="请输入证件号码"
                style="margin-right:8px;width: 242px"
              ></a-input>
              <a-input
                v-model="item.clientName"
                :disabled="true"
                placeholder="请输入姓名"
                style="margin-right:8px;width: 100px"
              ></a-input>
              <a-space> </a-space>
            </div>
          </div>
        </div>
      </a-form-model-item>
      <a-form-model-item v-if="parameterData.fileName" label="名单">
        <div>
          {{ parameterData.fileName }}
          <a-button type="link" @click="exportExcel">
            <a-icon type="download" /> 下载文件
          </a-button>
        </div>
      </a-form-model-item>
      <a-form-model-item label="生效时间" prop="rangeDate">
        <a-range-picker
          v-model="form.rangeDate"
          show-time
          :disabled="true"
          style="margin-right: 8px;"
          @change="onChange"
        />
        <a-checkbox v-model="checked" :disabled="true" @change="setLongTime">
          永久有效
        </a-checkbox>
      </a-form-model-item>
      <a-form-model-item label="协议编号" prop="agreeIdList">
        <div
          v-for="(item, index) in form.agreeIdList"
          :key="index"
          class="makeInline"
          style="margin-bottom:8px"
        >
          <div class="makeInline">
            <div>
              <a-input
                v-model="item.agreeId"
                placeholder="请输入协议编号"
                :disabled="true"
                style="margin-right:8px; width: 400px;"
              ></a-input>
            </div>
          </div>
        </div>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
// 默认表单属性
const defaultForm = {
  efCode: "", // 行权代码
  bcEfWhitelistClientsList: [
    {
      idType: undefined,
      idNo: "",
      clientName: "",
    },
  ],
  beaginTime: "",
  endTime: "",
  rangeDate: [],
  status: undefined, // 状态（0无效；1有效）
  agreeId: "", // 协议编号
  agreeIdList: [
    {
      agreeId: "",
    },
  ],
};

export default {
  name: "xqrz_detail",
  inject: ["api"],
  data() {
    return {
      form: Object.assign({}, defaultForm), //表单数据
      // 异步加载
      confirmLoading: false,
      rules: {
        efCode: [
          { required: true, message: "行权代码不能为空", trigger: "blur" },
        ],
        bcEfWhitelistClientsList: [
          { required: true, message: "名单不能为空", trigger: "blur" },
        ],
        rangeDate: [
          { required: true, message: "生效时间不能为空", trigger: "blur" },
        ],
        agreeIdList: [
          { required: true, message: "协议编号不能为空", trigger: "blur" },
        ],
      },
      checked: false,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false,
    },
    dictMap: {
      type: Object,
      default: () => {},
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.query();
      }
      this.$nextTick(() => {});
    },
    parameterData() {
      this.initData();
    },
  },
  methods: {
    initData() {
      this.checked = false;
      let parameterData = this.parameterData;
      if (parameterData.fileName) {
        console.log(parameterData.fileName);
      }
      if (Object.keys(parameterData).length) {
        Object.keys(this.form).forEach((key) => {
          if (key !== "rangeDate") {
            this.form[key] = parameterData[key];
          } else {
            this.form[key] = [];
          }
        });
        if (!this.form.rangeDate.length) {
          this.form.rangeDate.push(parameterData.beaginTime);
          this.form.rangeDate.push(parameterData.endTime);
        }
        if (parameterData.agreeId) {
          let list = parameterData.agreeId.split("|");
          this.form.agreeIdList = list.map((item) => {
            return {
              agreeId: item,
            };
          });
        }
        if (parameterData.endTime.includes("9999-12-31")) {
          this.checked = true;
        }
      }
    },
    checkArrayForEmptyValues(arr) {
      return arr.every(function(obj) {
        return Object.values(obj).every(function(value) {
          return value !== null && value !== undefined && value !== "";
        });
      });
    },
    addList() {
      this.form.bcEfWhitelistClientsList.push({
        idType: undefined,
        idNo: "",
        clientName: "",
      });
    },
    deleteItem(index) {
      this.form.bcEfWhitelistClientsList.splice(index, 1);
    },
    onChange(date, dateString) {
      console.log(this.form.rangeDate[0]);
      this.form.beaginTime = dateString[0];
      this.form.endTime = dateString[1];
    },
    setLongTime(e) {
      if (e.target.checked) {
        let nowDate = new Date();
        let today = `${nowDate.getFullYear()}-${(
          "0" +
          (nowDate.getMonth() + 1)
        ).slice(-2)}-${("0" + nowDate.getDate()).slice(-2)}`;
        if (this.form.rangeDate.length) {
          this.form.rangeDate = [];
        }
        this.form.beaginTime = `${today} 00:00:00`;
        this.form.endTime = "9999-12-31 23:59:59";
        this.form.rangeDate.push(today);
        this.form.rangeDate.push("9999-12-31 23:59:59");
      }
    },
    query() {},
    exportExcel() {
      let { fileName, filePath } = this.parameterData;
      console.log(fileName, filePath);
      if (fileName && filePath) {
        let baseUrl = "/bc-manage-server/bcEfWhitelist/dowloadBcEfWhitelist";
        window.location.href = `${baseUrl}?fileName=${fileName}&filePath=${filePath}`;
        // this.api
        //   .dowloadBcEfWhitelist({
        //     fileName,
        //     filePath,
        //   })
        //   .then(({ code, msg }) => {
        //     if (code != 0) {
        //       return this.$message.error(`下载失败：${msg}`);
        //     }
        //   })
        //   .catch((err) => {
        //     this.$message.error(`下载失败：${err}`);
        //   });
      } else {
        this.$message.error("未找到可下载的名单");
      }
    },
    // 提交数据权限分组创建
    submit() {
      this.showPop = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.makeInline {
  display: flex;
  align-items: center;
}
</style>
