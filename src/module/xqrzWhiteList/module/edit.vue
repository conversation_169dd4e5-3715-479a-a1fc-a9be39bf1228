<template>
  <a-modal
    title="编辑"
    :width="800"
    v-model="showPop"
    ok-text="保存"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="行权代码" prop="efCode">
        <a-input
          v-model="form.efCode"
          placeholder="请输入行权代码"
        ></a-input>
      </a-form-model-item>
      <a-form-model-item label="名单" prop="bcEfWhitelistClientsAddDTO">
        <div
          v-for="(item, index) in form.bcEfWhitelistClientsAddDTO"
          :key="index"
          class="makeInline"
          style="margin-bottom:8px"
        >
          <div class="makeInline">
            <div>
              <!-- 选择内容 -->
              <a-select
                v-model="item.idType"
                style="margin-right:8px;width: 150px"
                placeholder="请选择证件类型"
                :allowClear="true"
              >
                <a-select-option
                  v-for="(item, index) in [
                    { idTypeName: '身份证', idType: '0' },
                    { idTypeName: '港澳居民来往内地通行证', idType: 'G' },
                    { idTypeName: '台湾居民来往大陆通行证', idType: 'H' },
                    { idTypeName: '港澳台居民居住证', idType: 'l' },
                  ]"
                  :key="index"
                  :value="item.idType"
                >
                  {{ item.idTypeName }}
                </a-select-option>
              </a-select>

              <a-input
                v-model="item.idNo"
                placeholder="请输入证件号码"
                style="margin-right:8px;width: 242px"
              ></a-input>
              <a-input
                v-model="item.clientName"
                placeholder="请输入姓名"
                style="margin-right:8px;width: 100px"
              ></a-input>
              <a-space>
                <a-icon
                  v-if="form.bcEfWhitelistClientsAddDTO.length > 1"
                  type="delete"
                  style="fontSize:20px;color:red"
                  @click="deleteItem(index)"
                />
              </a-space>
            </div>
          </div>
        </div>
        <a-button
          icon="plus"
          type="primary"
          style="margin: 8px 0 0 0;"
          @click="addList"
        >
          新增
        </a-button>
      </a-form-model-item>
      <a-form-model-item label="生效时间" prop="rangeDate">
        <a-range-picker
          v-model="form.rangeDate"
          :show-time="timePickerOptions"
          :disabled="checked"
          style="margin-right: 8px;"
          @change="onChange"
        />
        <a-checkbox v-model="checked" @change="setLongTime">
          永久有效
        </a-checkbox>
      </a-form-model-item>
      <a-form-model-item label="协议编号" prop="agreeIdList">
        <div
          v-for="(item, index) in form.agreeIdList"
          :key="index"
          class="makeInline"
          style="margin-bottom:8px"
        >
          <div class="makeInline">
            <div>
              <a-input
                v-model="item.agreeId"
                placeholder="请输入协议编号"
                allowClear
                style="margin-right:8px; width: 400px;"
              ></a-input>
              <a-space>
                <a-icon
                  v-if="form.agreeIdList.length > 1"
                  type="minus"
                  style="fontSize:20px;color:red"
                  @click="deleteAgree(index)"
                />
                <a-icon
                  v-if="index == form.agreeIdList.length - 1"
                  type="plus"
                  style="fontSize:20px;"
                  @click="addAgree"
                />
              </a-space>
            </div>
          </div>
        </div>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import moment from "moment";

// 默认表单属性
const defaultForm = {
  id: "",
  efCode: "", // 行权代码
  bcEfWhitelistClientsAddDTO: [
    {
      idType: undefined,
      idNo: "",
      clientName: "",
    },
  ],
  beaginTime: "",
  endTime: "",
  rangeDate: [],
  status: undefined, // 状态（0无效；1有效）
  agreeId: "", // 协议编号
  agreeIdList: [
    {
      agreeId: "",
    },
  ],
};

export default {
  name: "xqrz_edit",
  inject: ["api"],
  data() {
    return {
      form: Object.assign({}, defaultForm), //表单数据
      // 异步加载
      confirmLoading: false,
      timePickerOptions: {
        format: "HH:mm:ss", // 设置时间格式为小时:分钟:秒
        defaultValue: [
          moment("00:00:00", "HH:mm:ss"),
          moment("23:59:59", "HH:mm:ss"),
        ], // 设置默认结束时间为23:59:59
      },
      rules: {
        efCode: [
          { required: true, message: "行权代码不能为空", trigger: "blur" }
        ],
        bcEfWhitelistClientsAddDTO: [
          { required: true, message: "名单不能为空", trigger: "blur" },
        ],
        rangeDate: [
          { required: true, message: "生效时间不能为空", trigger: "blur" },
        ],
        agreeIdList: [
          { required: true, message: "协议编号不能为空", trigger: "blur" },
        ],
      },
      checked: false,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false,
    },
    dictMap: {
      type: Object,
      default: () => {},
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.query();
      } else {
        this.reset();
      }
      this.$nextTick(() => {});
    },
    parameterData() {
      this.initData();
    },
  },
  methods: {
    initData() {
      let parameterData = this.parameterData;
      if (Object.keys(parameterData).length) {
        Object.keys(this.form).forEach((key) => {
          if (key !== "rangeDate") {
            if (key == "bcEfWhitelistClientsAddDTO") {
              this.form.bcEfWhitelistClientsAddDTO =
                parameterData.bcEfWhitelistClientsList;
            } else {
              this.form[key] = parameterData[key];
            }
          }
        });
        if (!this.form.rangeDate.length) {
          this.form.rangeDate.push(parameterData.beaginTime);
          this.form.rangeDate.push(parameterData.endTime);
        }
        if (parameterData.agreeId) {
          let list = parameterData.agreeId.split("|");
          this.form.agreeIdList = list.map((item) => {
            return {
              agreeId: item,
            };
          });
        }
        if (parameterData.endTime.includes("9999-12-31")) {
          this.checked = true;
        }
      }
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm, {
        bcEfWhitelistClientsAddDTO: [
          {
            idType: undefined,
            idNo: "",
            clientName: "",
          },
        ],
        agreeIdList: [{ agreeId: "" }],
      });
      this.form.rangeDate = [];
      this.checked = false;
      this.confirmLoading = false;
    },
    checkArrayForEmptyValues(arr) {
      return arr.every(function(obj) {
        return Object.values(obj).every(function(value) {
          return value !== null && value !== undefined && value !== "";
        });
      });
    },
    getStringLength(string) {
      // 使用正则表达式匹配中文和全角字符
      var pattern = /[\u4e00-\u9fa5\uff00-\uffff]/g;
      // 获取中文和全角字符的个数
      var chineseCount = (string.match(pattern) || []).length;
      // 返回字符串长度
      return string.length + chineseCount;
    },
    checkNameLength(arr) {
      let arrTips = [];
      console.log(arr);
      console.log(arrTips)
      for (let i = 0; i < arr.length; i++) {
        if (this.getStringLength(arr[i].clientName) > 120) {
          arrTips.push("姓名必须在120个字符以内!");
        }
        if (this.getStringLength(arr[i].idNo) > 32) {
          arrTips.push("证件号码必须在32个字符以内!");
        }
      }
      if (arrTips.length) {
        this.$message.error(arrTips[0]);
        return false;
      } else {
        return true;
      }
    },
    addList() {
      this.form.bcEfWhitelistClientsAddDTO.push({
        idType: undefined,
        idNo: "",
        clientName: "",
      });
    },
    deleteItem(index) {
      this.form.bcEfWhitelistClientsAddDTO.splice(index, 1);
    },
    addAgree() {
      this.form.agreeIdList.push({
        agreeId: "",
      });
    },
    deleteAgree(index) {
      this.form.agreeIdList.splice(index, 1);
    },
    onChange(date, dateString) {
      console.log(this.form.rangeDate[0]);
      this.form.beaginTime = dateString[0];
      this.form.endTime = dateString[1];
    },
    setLongTime(e) {
      if (e.target.checked) {
        let nowDate = new Date();
        let today = `${nowDate.getFullYear()}-${(
          "0" +
          (nowDate.getMonth() + 1)
        ).slice(-2)}-${("0" + nowDate.getDate()).slice(-2)}`;
        if (this.form.rangeDate.length) {
          this.form.rangeDate = [];
        }
        this.form.beaginTime = `${today} 00:00:00`;
        this.form.endTime = "9999-12-31 23:59:59";
        this.form.rangeDate.push(today);
        this.form.rangeDate.push("9999-12-31 23:59:59");
      }
    },
    compareDates(date) {
      const d1 = new Date(date);
      const d2 = new Date();

      if (d1 < d2) {
        return -1; // date1 小于 date2
      } else if (d1 > d2) {
        return 1; // date1 大于 date2
      } else {
        return 0; // date1 等于 date2
      }
    },
    query() {},
    // 提交数据权限分组创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if (
          !this.checkArrayForEmptyValues(this.form.bcEfWhitelistClientsAddDTO)
        ) {
          return this.$message.error("名单未补充完整!");
        }
        if (this.getStringLength(this.form.efCode) > 20) {
          return this.$message.error("行权代码必须在20位字符以内!");
        }
        if (!this.checkNameLength(this.form.bcEfWhitelistClientsAddDTO)) {
          return;
        }
        if (this.compareDates(this.form.endTime) < 0) {
          return this.$message.error("结束时间不能小于今天!");
        }
        if (!this.checkArrayForEmptyValues(this.form.agreeIdList)) {
          return this.$message.error("协议编号未补充完整!");
        }
        this.form.agreeId = this.form.agreeIdList
          .map((item) => item.agreeId)
          .join("|");
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`白名单修改失败：${msg}`);
          this.$message.success(`白名单修改成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
        };
        this.api.handAdd(param).then(callback);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.makeInline {
  display: flex;
  align-items: center;
}
</style>
