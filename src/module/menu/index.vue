<template>
  <a-layout class="menu-content">
    <a-layout-sider v-show="siderShow">
      <a-card title="菜单列表" :bordered="false">
        <div class="menu-left-tree">
          <tkTightTree @select="select" @rightClick="rightClick">
            <tk-tree :treeData="treeData" :replaceFields="replaceFields" :isIcon="false" :selectedKeys.sync="checkedKeys"></tk-tree>
            <a-menu-item key="1" @click="riskMenu('add')"> 添加菜单 </a-menu-item>
            <a-menu-item key="2" @click="riskMenu('addChild')" v-show="Object.keys(rightMenu).length > 0"> 添加子菜单 </a-menu-item>
            <a-menu-item key="3" @click="riskMenu('remove')" v-show="Object.keys(rightMenu).length > 0"> 删除当前菜单 </a-menu-item>
            <a-menu-item key="4" @click="getMenuTree"> 刷新当前菜单栏目 </a-menu-item>
          </tkTightTree>
        </div>
      </a-card>
    </a-layout-sider>
    <a-layout-content class="menu-right-content">
      <div class="icon-buttun">
        <a href="javascript:void(0)" @click="siderShow = !siderShow">
          <a-icon :type="siderShow?'left':'right'" />
        </a>
      </div>
      <a-row>
        <a-col :span="10" class="menu-right-table-content">
          <menuForm :menuId="menuId" :menuType="menuType" :menuName="menuName" @success="success" />
        </a-col>
        <a-col :span="14" class="menu-right-table">
          <a-card title="添加权限" :bordered="false">
            <menuLimits :menuId="menuId" :menuName="menuName" />
          </a-card>
        </a-col>
      </a-row>
    </a-layout-content>
  </a-layout>
</template>

<script>
import menuForm from './module/form'; // 引入中间表单区域
import menuLimits from './module/limits'; // 引入右侧
import api from './api'; // 引入请求类

export default {
  data() {
    return {
      siderShow: true,
      menuId: undefined, // 用户选中项菜单
      menuType: undefined, // 用户菜单指定操作
      menuName: undefined, // 用户点击右击菜单所操作的菜单名称
      checkedKeys: [],
      treeData: [], // 树状列表填充数据
      rightMenu: {},
      // 树状列表调整对应的key
      replaceFields: {
        children: 'children', title: 'menuName', key: 'menuId'
      },
    };
  },
  provide: { "api": api, },
  created() {
    this.getMenuTree();
  },
  components: { menuForm, menuLimits },
  methods: {
    select({ menuId, menuName }) {
      // 先进行空置处理
      this.menuName = undefined;
      this.menuId = undefined;
      this.menuType = undefined;
      // 赋值
      this.menuId = menuId;
      this.menuName = menuName;
      this.menuType = menuId ? "set" : "add";
    },
    rightClick(data) {
      this.rightMenu = data;
    },
    // 查询当前菜单栏树状列表
    getMenuTree() {
      api.getMenuTree({ isContainBtnMenu: "N" }).then(({ code, data }) => {
        if (code != 0) return;
        data = data.map(item => {
          item["icon"] = "";
          return item;
        })
        this.treeData = data;
      })
    },
    riskMenu(type) {
      if (type == "add" || type == "addChild") {
        let { menuName, menuId } = this.rightMenu;
        this.menuType = type;
        type == "addChild" ? this.menuName = menuName : this.menuName = undefined;
        this.menuId = menuId;
      } else if (type == "remove") {
        let { menuName, menuId } = this.rightMenu;
        this.$confirm({
          title: '删除菜单',
          content: () => <p>确定删除指定的“{menuName}”菜单？</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            api.removeMenu({ menuId }).then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`删除菜单失败：${msg}`);
              this.$message.success('删除菜单成功！');
              this.getMenuTree();
            })
          },
        });
      }
    },
    // 表单操作成功
    success() {
      // 重新查询左侧菜单栏
      this.getMenuTree();
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-right-content {
  position: relative;
  .icon-buttun {
    position: absolute;
    width: 10px;
    left: 5px;
    top: 50%;
    z-index: 1;
    transform: translateY(-50%);
    & > a > i {
      font-size: 8px;
    }
  }
}
.menu-left-tree {
  background: #ffffff;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;

  .thinkive-right-tree {
    height: 100%;
    .thinkive-tree {
      height: 100%;
    }
  }
}
</style>