// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

  /**
   * 查询当前维护中的菜单栏列表
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["isContainBtnMenu"])
  getMenuTree() {
    // 查询事件统一使用GET请求
    return this.services.initGet({ reqUrl: "menu/list/tree", param: this.param });
  }

  /**
   * 查询菜单下的权限
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["menuId"])
  getBtnMenuList() {
    // 查询事件统一使用GET请求
    return this.services.initGet({ reqUrl: "menu/btnMenuList", param: this.param });
  }

  /**
   * 查询当前指定菜单栏编号的详情信息
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["menuId"])
  getMenuDetail() {
    return this.services.initGet({ reqUrl: "menu/query", param: this.param });
  }

  /**
   * 新增菜单
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["icon", "loadMode", "menuName", "menuType", "orderNum", "parentId", "path", "perms", "status", "visible"])
  addMenu() {
    return this.services.initPost({ reqUrl: "menu/add", param: this.param });
  }

  /**
   * 修改菜单
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["icon", "loadMode", "menuName", "menuType", "orderNum", "parentId", "path", "perms", "status", "visible", "menuId"])
  setMenu() {
    return this.services.initPost({ reqUrl: "menu/edit", param: this.param });
  }

  /**
   * 删除菜单
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["menuId"])
  removeMenu() {
    return this.services.initPost({ reqUrl: "menu/delete", param: this.param });
  }

  /**
   * 删除权限
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["menuIds"])
  removeMenuBtn() {
    return this.services.initPost({ reqUrl: "menu/deletesBtnMenu", param: this.param });
  }
}

export default new api();