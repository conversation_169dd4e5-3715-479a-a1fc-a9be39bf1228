<template>
  <div class="menu-right-limits">
    <tk-table ref="table" :intercept-response="intercept_response" :tableData.sync="comlun" getMethods="work-manage-server/menu/btnMenuList" :isSelected="true" :isPaging="true" :tableFrom="tableFrom" :selectedRowKeys.sync="selectedRowKeys" tableId="menuId">
      <div class="table-buttun-area" slot="tableHeader">
        <a-button :disabled="!menuId" type="primary" icon="plus" @click="limitsOperShow = true,dataType='add'"> 新增 </a-button>
        <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove"> 删除 </a-button>
      </div>
      <template slot="operation" slot-scope="data">
        <a-button type="link" @click="modifyData(data)"> 修改 </a-button>
      </template>
    </tk-table>
    <limitsOper :visible.sync="limitsOperShow" :menuName="menuName" :menuId="menuId" :parameterData="data" :dataType="dataType" @success="success" />
  </div>
</template>

<script>
import limitsOper from './limitsOper';
export default {
  data() {
    return {
      // 表格展示字段
      comlun: [
        { field: "menuName", label: "权限名称", fixed: true },
        { field: "createTime", label: "创建时间", filter: item => !item ? '--' : this.DateFormat(item).format("yyyy-MM-dd") },
        { field: "perms", label: "权限标识", isEllipsis: true },
        { field: "status", label: "权限状态", filter: item => item == '0' ? '开启' : '关闭', align: "center" },
        { field: "operation", label: "操作", align: "center" },
      ],
      // 查询接口所需字段
      tableFrom: {
        menuId: ""
      },
      selectedRowKeys: [], // 当前用户选中参数
      data: {}, // 所要修改的权限
      limitsOperShow: false,
      dataType: "add",

    };
  },
  inject: ["api"],
  components: { limitsOper },
  props: {
    // 菜单编号
    menuId: {
      type: String,
      default: undefined
    },
    // 菜单名称
    menuName: {
      type: String,
      default: undefined
    },
  },
  watch: {
    // 用户传入菜单编号
    menuId(val) {
      // 具有菜单编号时查询对应的权限列表
      this.tableFrom["menuId"] = val;
      this.$refs.table.getTableData()
    },
  },
  methods: {
    // 用户点击修改按钮
    modifyData(data) {
      this.data = data;
      this.dataType = "set";
      this.limitsOperShow = true;
    },
    // 操作成功
    success() {
      this.$refs.table.getTableData()
    },
    // 删除对应的权限
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: '删除权限',
          content: () => <p>确定删除当前勾选的权限？</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            this.api.removeMenuBtn({ menuIds: this.selectedRowKeys.join(",") }).then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`删除权限失败：${msg}`);
              this.$message.success('删除权限成功！');
              this.$refs.table.getTableData();
              this.selectedRowKeys = [];
            })
          },
        });
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.menu-right-limits {
  width: 100%;
  height: 100%;
  /deep/.ant-table-body {
  }
}
</style>