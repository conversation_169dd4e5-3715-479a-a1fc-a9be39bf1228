<template>
  <a-card :title="formTitle" :bordered="false">
    <a-form-model ref="form" :model="form" :label-col="{span:5}" :wrapper-col="{span:18}" :rules="rules">
      <a-form-model-item label="父级菜单" v-show="menuName && menuType!='set'">
        <a-input v-model="menuName" disabled />
      </a-form-model-item>
      <a-form-model-item label="菜单名称" prop="menuName">
        <a-input v-model="form.menuName" placeholder="请输入菜单名称" />
      </a-form-model-item>
      <a-form-model-item label="访问地址" prop="path">
        <a-input v-model="form.path" placeholder="请输入访问地址" />
      </a-form-model-item>
      <a-form-model-item label="栏目图标" prop="icon">
        <a-select v-model="form.icon" class="iconlist">
          <a-select-option :value="item" v-for="item in iconList" :key="item">
            <a-icon :type="item" />
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="加载模式" prop="loadMode">
        <a-radio-group v-model="form.loadMode" button-style="solid">
          <a-radio-button value="0"> 单页面加载 </a-radio-button>
          <a-radio-button value="1"> 微前端加载 </a-radio-button>
          <a-radio-button value="2"> 内嵌页加载 </a-radio-button>
          <a-radio-button value="3"> 新开页面 </a-radio-button>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="显示排序" prop="orderNum">
        <a-input v-model="form.orderNum" placeholder="请输入显示排序" />
      </a-form-model-item>
      <a-form-model-item label="菜单状态" prop="status">
        <a-switch v-model="form.status" checked-children="生效" un-checked-children="关闭" />
      </a-form-model-item>
      <a-form-model-item label="菜单显示" prop="visible">
        <a-switch v-model="form.visible" checked-children="显示" un-checked-children="隐藏" />
      </a-form-model-item>
      <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
        <a-button :loading="loading" type="primary" @click="menuOperation"> {{formTitle.slice(0,2)}} </a-button>
        <a-button style="margin-left: 10px;" @click="menuTypeResult"> 重置 </a-button>
      </a-form-model-item>
    </a-form-model>
  </a-card>
</template>

<script>
import iconList from './icon'; // 栏目图标库
export default {
  data() {
    return {
      form: {
        menuName: undefined,
        path: undefined,
        icon: iconList[0],
        loadMode: "0",
        orderNum: "1",
        status: true,
        visible: true,
        menuType: ""
      }, // 表单填充项
      iconList, // 表单图标
      // 表单校验规则
      rules: {
        menuName: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],
      },
      formTitle: "", // 表单列表
      loading: false,
    };
  },
  inject: ["api"],
  props: {
    // 菜单编号
    menuId: {
      type: String,
      default: undefined
    },
    // 操作类型
    menuType: {
      type: String,
      default: "add"
    },
    // 操作菜单名称
    menuName: {
      type: String,
      default: undefined
    }
  },
  watch: {
    // 用户传入菜单编号
    menuId(val) {
      // 用户切换菜单编号后重置表单
      this.formLoad();
      // 当具有菜单编号且类型为修改时查询详情
      if (val && this.menuType == "set") this.menuDetail();
    },
    // 根据用户传入类型进行相关操作处理
    menuType(val) {
      if (!val) this.menuType = "add";
      this.menuTypeResult();
    }
  },
  created() {
    this.formLoad();
  },
  mounted() {
    this.menuTypeResult();
  },
  methods: {
    // 初始化表单参数
    formLoad() {
      this.form = {
        menuName: undefined,
        path: undefined,
        icon: iconList[0],
        loadMode: "0",
        orderNum: "1",
        status: true,
        visible: true,
        menuType: ""
      };
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    // 查询菜单详情
    menuDetail() {
      this.api.getMenuDetail({ menuId: this.menuId }).then(({ code, data }) => {
        if (code != 0) return;
        // 添加之前先进行表单初始化
        this.formLoad();
        this.form = data;
      })
    },
    // 获取菜单指定操作的结果
    menuTypeResult() {
      // 切换操作时初始化表单
      this.formLoad();
      // 调用添加逻辑
      if (this.menuType == "add") {
        this.formTitle = "添加菜单";
      }
      // 调用修改逻辑
      if (this.menuType == "set") {
        this.formTitle = "修改菜单";
        this.menuDetail();
      }
      // 调用添加子菜单逻辑
      if (this.menuType == "addChild") {
        this.formTitle = "添加子菜单";
      }
    },
    // 菜单栏操作-点击高亮按钮
    menuOperation() {
      this.$refs.form.validate(valid => {
        if (!valid) return;
        this.loading = true;
        // 对数据进行转换,防止数据地址被关联
        let param = JSON.parse(JSON.stringify(this.form));
        // 如果是添加则其父级菜单默认为0
        if (this.menuType == "add") param["parentId"] = 0;
        // 如果是子菜单添加则其父级菜单为传入的菜单编号
        if (this.menuType == "addChild") param["parentId"] = this.menuId;
        // 锁定菜单类型为C-栏目
        param["menuType"] = "C";
        if (this.menuType == "add" || this.menuType == "addChild") {
          this.api.addMenu(param).then(({ code, msg }) => {
            this.loading = false;
            if (code != 0) return this.$message.error(`菜单${this.formTitle.slice(0, 2)}失败：${msg}`);
            this.$message.success(`菜单${this.formTitle.slice(0, 2)}成功！`);
            // 对外通知操作成功
            this.$emit("success");
          })
        } else if (this.menuType == "set") {
          this.api.setMenu(param).then(({ code, msg }) => {
            this.loading = false;
            if (code != 0) return this.$message.error(`菜单${this.formTitle.slice(0, 2)}失败：${msg}`);
            this.$message.success(`菜单${this.formTitle.slice(0, 2)}成功！`);
            // 对外通知操作成功
            this.$emit("success");
          })
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.iconlist {
  width: 160px;
}
</style>