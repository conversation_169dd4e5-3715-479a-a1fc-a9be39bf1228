<template>
  <a-modal title="添加权限" v-model="isvisible" ok-text="确认" cancel-text="取消" @ok="submit" @cancel="getModuleReset" :confirm-loading="confirmLoading" :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
      <a-form-model-item label="归属菜单">
        <a-input v-model="menuName" disabled />
      </a-form-model-item>
      <a-form-model-item label="权限名称" prop="menuName">
        <a-input v-model="form.menuName" placeholder="请输入权限名称" />
      </a-form-model-item>
      <a-form-model-item label="权限标识" prop="perms">
        <a-input v-model="form.perms" placeholder="请输入权限标识" />
      </a-form-model-item>
      <a-form-model-item label="状态" prop="status">
        <a-switch v-model="form.status" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import modalMixed from '@u/modalMixed';
import { reqDataField } from '@u/parameter';
export default {
  data() {
    return {
      form: {}, // 权限添加表单
      // 表单权限验证
      rules: {
        menuName: [{ required: true, message: '权限名称不能为空', trigger: 'blur' }],
        perms: [{ required: true, message: '权限标识不能为空', trigger: 'blur' }],
      },
      // 异步加载
      confirmLoading: false
    };
  },
  props: {
    menuName: String, // 父级菜单名称 
    menuId: String, // 父级菜单编号 
  },
  inject: ["api"],
  mixins: [modalMixed],
  created() {
    this.getModuleReset();
  },
  methods: {
    // 重置对应的表单
    getModuleReset() {
      this.form = {
        menuName: undefined,
        perms: undefined,
        status: true,
      }
      this.$refs.form && this.$refs.form.resetFields();
    },
    // 用户点击修改进入
    getModuleSet(data) {
      // 获取修改属性前先进行表单重置
      this.getModuleReset();
      data = reqDataField(data, true);
      this.form = data;
    },
    // 提交权限创建
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        // 新增权限和新增菜单公用同一接口但是入参不同
        param["parentId"] = this.menuId;
        // 锁定菜单类型为F-按钮(权限)
        param["menuType"] = "F";
        let func = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`权限${this.typeTitle}失败：${msg}`);
          this.$message.success(`权限${this.typeTitle}成功！`);
          // 关闭弹窗
          this.isvisible = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.getModuleReset();
        }
        this.dataType == "add" ? this.api.addMenu(param).then(func) : this.api.setMenu(param).then(func);
      })
    },
  },
};
</script>
