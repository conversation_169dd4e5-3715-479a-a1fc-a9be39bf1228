<template>
    <a-card title="见证审核记录" class="management" :bordered="false">
        <div class="searchFrom">
            <a-form-model layout="inline" :model="tableFrom">
                <tkSelectForm @query="success" @reset="reset">
                    <a-form-model-item label="见证时间">
                        <a-range-picker v-model="tableFrom.applyTime" valueFormat="YYYY-MM-DD" />
                    </a-form-model-item>
                    <a-form-model-item label="视频业务类型">
                        <a-select v-model="tableFrom.businessType" placeholder="请视频业务类型" show-search
                            option-filter-prop="children" allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.businessTypes']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="见证结果">
                        <a-select v-model="tableFrom.result" placeholder="请选择见证结果" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.results']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="证件号码">
                        <a-input v-model="tableFrom.clientCode" placeholder="请输入证件号码" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="客户编号">
                        <a-input v-model="tableFrom.preengageId" placeholder="请输入客户编号" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="客户姓名">
                        <a-input v-model="tableFrom.clientName" placeholder="请输入客户姓名" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="手机号">
                        <a-input v-model="tableFrom.mobile" placeholder="请输入手机号" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="推荐人编码">
                        <a-input v-model="tableFrom.cooperationCode" placeholder="请输入推荐人编码" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="产品类型">
                        <a-select v-model="tableFrom.productId" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.productIds']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="见证座席">
                        <a-input v-model="tableFrom.operatorName" placeholder="请输入见证座席" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="资金账号">
                        <a-input v-model="tableFrom.clientId" placeholder="请输入资金账号" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="开户证件">
                        <a-select v-model="tableFrom.idKind" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.cardSource']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>

                </tkSelectForm>
            </a-form-model>
        </div>
        <div class="access-table">
            <tk-table ref="table" :tableData.sync="comlun" getMethods="bc-manage-server/gjKhHis/double/queryAuditList"
                :intercept-response="intercept_response" :isPaging="true" :tableFromFilter="tableFormFilter"
                :tableFrom="tableFrom" tableId="id">
                <div class="table-button-area" slot="tableHeader">
                    <a-button icon="upload" type="primary" @click="exportExcel">
                        导出
                    </a-button>
                </div>
                <template slot="operation" slot-scope="data">
                    <a class="action-btn" @click="showDetail(data)"> 查看详情 </a>
                </template>
            </tk-table>

        </div>

    </a-card>
</template>

<script>
// import api from './api';
export default {
    name: 'auditRecords',
    data() {
        return {
            comlun: [
                { field: 'preengageId', label: '客户编号', width: 180 },
                { field: 'clientName', label: '客户姓名', width: 100 },
                {
                    field: 'productId', label: '产品类型', width: 100,
                    filter: item => this.getDictText('bc.common.productIds', item)
                },
                {
                    field: 'businessType', label: '视频业务类型', width: 100,
                    filter: item => this.getDictText('bc.common.businessTypes', item)
                },
                {
                    field: 'idKind', label: '开户证件', width: 100,
                    filter: item => this.getDictText('bc.common.cardSource', item)
                },
                { field: 'mobile', label: '手机号', width: 120 },
                { field: 'clientCode', label: '证件号码', width: 180 },

                { field: 'operatorName', label: '见证座席', width: 150 },
                {
                    field: 'result', label: '见证结果', width: 150,
                    filter: item => this.getDictText('bc.common.results', item)
                },
                { field: 'remark', label: '见证备注', width: 180, isEllipsis: true },
                { field: 'startTime', label: '开始见证时间', width: 200 },
                { field: 'witnessTime', label: '总时长', width: 100 },
                { field: 'cooperationCode', label: '推荐人编码', width: 100 },
                { field: 'clientId', label: '资金账号', width: 150 },
                { field: 'operation', label: '操作', align: 'center', fixed: 'right', width: 100 }
            ],
            dictMap: {
                'bc.common.results': [
                    { value: 1, label: '通过' },
                    { value: 2, label: '中断' },
                    { value: 3, label: '驳回' },
                    { value: 4, label: '复审通过' },
                    { value: 5, label: '复审驳回' },
                ],
                'bc.common.cardSource': [
                    { value: '0', label: '身份证' },
                    { value: 'G', label: '港澳居民来往内地通行证' },
                    { value: 'H', label: '台湾居民来往内地通行证' },
                    { value: 'I', label: '外国人永久居留身份证' }
                ],
                'bc.common.productIds': [
                    { value: '1', label: '佣金宝' },
                    { value: '2', label: '投资宝' },
                ],
                'bc.common.businessTypes': [
                    { value: '1001', label: '开股东户' },
                    { value: '1002', label: '开股东户' },
                    { value: '1003', label: '开股东户（微信）' },
                    { value: '1004', label: '1.0迁移3.0' },
                ]
            },
            tableFrom: {
                applyTime:['2024-04-26','']
            },
            exportForm: {},
        }
    },
    methods: {
        getDictText(key, value) {
            let results = this.dictMap[key] || [];
            results = results.filter((item) => {
                return item.value == value;
            });
            return (results && results.length && results[0].label) || value || '';
        },
        success() {

            this.$refs.table.getTableData();
        },
        tableFormFilter(param) {
            console.log('this.tableFrom?.applyTime', this.tableFrom?.applyTime)
            // 见证时间
            if (this.tableFrom?.applyTime && this.tableFrom.applyTime?.length > 0) {
                param["startTimeFrom"] = this.tableFrom.applyTime[0];
                param["startTimeTo"] = this.tableFrom.applyTime[1];
            }
            // 是否脱敏
            const params = new URLSearchParams(window.location.search);
            param['dataTm'] = params.get('dataTm');

            delete param.applyTime;
            this.exportForm = param;
            return param;
        },
        // 重置
        reset() {
            this.tableFrom = {};

        },
        objectToQueryString(obj) {
            return Object.keys(obj)
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
                .join('&');
        },
        // 导出exportExcel
        exportExcel() {
            const searchs = this.objectToQueryString(this.exportForm);
            console.log('searchs===========', searchs)
            window.location.href = "/bc-manage-server/gjKhHis/double/exportAuditList?" + searchs;
        },
        // 查看详情
        showDetail(data) {
            console.log('data', data)
            const params = new URLSearchParams(window.location.search);
            let href = `/bc-manage-view/auditVideoRecords/DetailInfo?processId=${data.id}&dataTm=${params.get('dataTm')}`
            window.open(href, '_blank')
        },
    },
    created() {

    }
}
</script>