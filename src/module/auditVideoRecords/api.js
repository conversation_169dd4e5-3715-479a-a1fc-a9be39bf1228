// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询详情
   * @param {Object} param
   * - id {String} id
   * - dataTm {String} 是否脱敏
   */
  @Parameters(["id","dataTm"])
  queryItem() {
    return this.services.initGet({
      reqUrl: "gjKhHis/double/queryDetail",
      param: this.param,
    });
  }

  /**
   * 查询文件信息
   * @param {Object} param
   * - file_id {String} 文件id
   */
  @Parameters(["file_id"])
  queryImgfileUrl() {
    return this.services.initGet({
      reqUrl: "gjKhHis/archive/picture/download",
      param: this.param,
    });
  }

}

export default new api();
