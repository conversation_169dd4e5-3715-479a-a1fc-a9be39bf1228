<template>
    <a-card title="信用信息查询" class="management" :bordered="false">
        <div class="searchFrom">
            <a-form-model layout="inline" :model="tableFrom">
                <tkSelectForm @query="success" @reset="reset">
                    <a-form-model-item label="客户编号">
                        <a-input v-model="tableFrom.clientId" placeholder="请输入客户编号" allowClear />
                    </a-form-model-item>
                </tkSelectForm>
            </a-form-model>
        </div>
        <div class="access-table">
            <tk-table ref="table" :tableData.sync="comlun"
                getMethods="bc-manage-server/gjKhHis//margin/clientCreditInfoQuery" :intercept-response="intercept_response"
                :isPaging="true" :tableFromFilter="tableFormFilter" :tableFrom="tableFrom" tableId="id">
                <template slot="operation" slot-scope="data">
                    <a class="action-btn" @click="showDetail(data)"> 查看详情 </a>
                </template>
            </tk-table>
        </div>
    </a-card>
</template>

<script>

export default {
    name: 'auditRecords',
    data() {
        return {
            comlun: [
                { field: 'client_id', label: '客户编号', width: 180 },
                {
                    field: 'business_type', label: ' 办理业务', width: 100,
                    filter: item => this.getDictText('bc.common.businessType', item),
                },
                {
                    field: 'restrict_secu_flag', label: '限售股份持有情况', width: 120,
                    filter: item => this.getDictText('bc.common.restrictSecuFlag', item),
                },
                {
                    field: 'senior_person_flag', label: '高管标识', width: 100,
                    filter: item => this.getDictText('bc.common.seniorPersonFlag', item),
                },
                {
                    field: 'shareholder_flag', label: '是否持股5%以上', width: 100,
                    filter: item => this.getDictText('bc.common.shareholderFlag', item),
                },
                {
                    field: 'relatedholder_flag', label: '有无关联账户', width: 150,
                    filter: item => this.getDictText('bc.common.relatedholderFlag', item),
                },
                {
                    field: 'update_time', label: '更新时间', width: 150,
                },
                {
                    field: 'op_station', label: '站点信息', width: 150, isEllipsis: true
                },
                { field: 'operation', label: '操作', align: 'center', fixed: 'right', width: 100 }

            ],
            dictMap: {
                'bc.common.businessType': [
                    { value: '30072', label: '两融开户' },
                    { value: '30110', label: '信用北交所' },
                ],
                'bc.common.restrictSecuFlag': [
                    { value: '0', label: '未持有' },
                    { value: '1', label: '持有' },
                ],
                'bc.common.seniorPersonFlag': [
                    { value: '0', label: '不是' },
                    { value: '1', label: '是' },
                ],
                'bc.common.shareholderFlag': [
                    { value: '0', label: '不是' },
                    { value: '1', label: '是' },
                ],
                'bc.common.relatedholderFlag': [
                    { value: '0', label: '没有' },
                    { value: '1', label: '有' },
                ],
            },
            tableFrom: {},
        }
    },
    methods: {
        getDictText(key, value) {
            let results = this.dictMap[key] || [];
            results = results.filter((item) => {
                return item.value == value;
            });
            return (results && results.length && results[0].label) || '';
        },
        tableFormFilter(param) {
            return param;
        },
        success() {
            this.$refs.table.getTableData();
        },
        // 重置
        reset() {
            this.tableFrom = {};
        },
        // 查看详情
        showDetail(data) {
            console.log('data', data)
            const datas = JSON.stringify(data);
            let href = `/bc-manage-view/auditCreditInfoQuery/auditCreditInfoQueryDetail?details=${datas}`
            window.open(href, '_blank')
        },
    }
}
</script>

