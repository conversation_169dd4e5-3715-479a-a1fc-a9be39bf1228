<template>
    <div>
        <a-layout>
            <a-layout-header style="background: #fff;">
                <a-row class="pg-header pg-header-top">
                    客户融资融券信用详情
                </a-row>
            </a-layout-header>
            <a-layout-content style="background: #f9f9f9; padding: 10px 25px 90px 25px">

                <div>
                    <div class="list_header">
                        <a-row>
                            <a-col :span="6" class="pop-title">详情信息</a-col>
                        </a-row>
                    </div>
                    <a-descriptions bordered>
                        <a-descriptions-item label="客户编号">
                            {{ userInfo.client_id }}
                        </a-descriptions-item>
                        <a-descriptions-item label="办理业务">
                            {{ getDictText("businessType", userInfo.business_type) }}
                        </a-descriptions-item>
                        <a-descriptions-item label="限售股份持有情况">
                            {{ getDictText("restrictSecuFlag", userInfo.restrict_secu_flag) }}
                        </a-descriptions-item>
                        <a-descriptions-item label="高管标识">
                            {{ getDictText("seniorPersonFlag", userInfo.senior_person_flag) }}
                        </a-descriptions-item>
                        <a-descriptions-item label="是否持股5%以上">
                            {{ getDictText("shareholderFlag", userInfo.shareholder_flag) }}
                        </a-descriptions-item>
                        <a-descriptions-item label="有无关联账户">
                            {{ getDictText("relatedholderFlag", userInfo.relatedholder_flag) }}
                        </a-descriptions-item>
                        <a-descriptions-item label="更新时间">
                            {{ userInfo.update_time }}
                        </a-descriptions-item>
                        <a-descriptions-item label="站点信息">
                            {{ userInfo.op_station }}
                        </a-descriptions-item>

                    </a-descriptions>
                </div>

                <div>
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="list_header top8">
                            <a-row>
                                <a-col :span="6" class="pop-title">持有上市公司限售股份详情</a-col>
                            </a-row>
                        </div>
                        <div class="content-border">
                            <a-table :dataSource="restrictSecuInfoList" :columns="restrictSecuInfoColumns"
                                :pagination="false" bordered></a-table>
                        </div>
                    </a-row>
                </div>
                <div>
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="list_header top8">
                            <a-row>
                                <a-col :span="6" class="pop-title">任职上市公司高管详情</a-col>
                            </a-row>
                        </div>
                        <div class="content-border">
                            <a-table :dataSource="seniorPersonInfoList" :columns="seniorPersonInfoColumns"
                                :pagination="false" bordered></a-table>
                        </div>
                    </a-row>
                </div>
                <div>
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="list_header top8">
                            <a-row>
                                <a-col :span="6" class="pop-title">上市公司持股5%以上股东详情</a-col>
                            </a-row>
                        </div>
                        <div class="content-border">
                            <a-table :dataSource="shareholderInfoList" :columns="shareholderInfoColumns" :pagination="false"
                                bordered></a-table>
                        </div>
                    </a-row>
                </div>
                <div>
                    <a-row :style="{ marginTop: 15 + 'px' }">
                        <div class="list_header top8">
                            <a-row>
                                <a-col :span="6" class="pop-title">关联账户详情</a-col>
                            </a-row>
                        </div>
                        <div class="content-border">
                            <a-table :dataSource="relatedholderInfoList" :columns="relatedholderInfoColumns"
                                :pagination="false" bordered></a-table>
                        </div>
                    </a-row>
                </div>

            </a-layout-content>
        </a-layout>
    </div>
</template>

<script>
export default {
    name: "AuditContactDetail",
    data() {
        return {
            userInfo: {},
            restrictSecuInfoList: [],
            seniorPersonInfoList: [],
            shareholderInfoList: [],
            relatedholderInfoList: [],

            restrictSecuInfoColumns: [
                {
                    title: "证券代码",
                    dataIndex: "secu_code",
                    key: "secu_code",
                },
                {
                    title: "证券名称",
                    dataIndex: "secu_name",
                    key: "secu_name",
                },
                {
                    title: "持有数量",
                    dataIndex: "secu_shares",
                    key: "secu_shares",
                },
                {
                    title: "解禁日期",
                    dataIndex: "restrict_endDate",
                    key: "restrict_endDate",
                },
            ],
            seniorPersonInfoColumns: [
                {
                    title: "上市公司证券代码",
                    dataIndex: "company_code",
                    key: "company_code",
                },
                {
                    title: "上市公司名称",
                    dataIndex: "company_name",
                    key: "company_name",
                },
                {
                    title: "职位",
                    dataIndex: "position",
                    key: "position",
                },
                {
                    title: "任职截止日期",
                    dataIndex: "position_endDate",
                    key: "position_endDate",
                },
            ],

            shareholderInfoColumns: [
                {
                    title: "证券代码",
                    dataIndex: "secu_code",
                    key: "secu_code",
                },
                {
                    title: "股东账号",
                    dataIndex: "holder_account",
                    key: "holder_account",
                },
                {
                    title: "持有数量",
                    dataIndex: "secu_shares",
                    key: "secu_shares",
                },
            ],

            relatedholderInfoColumns: [
                {
                    title: "关联人姓名",
                    dataIndex: "holder_name",
                    key: "holder_name",
                },
                {
                    title: "关联人证件号码",
                    dataIndex: "holder_idno",
                    key: "holder_idno",
                },
                {
                    title: "关联人关系",
                    dataIndex: "holder_relation",
                    key: "holder_relation",
                },
                {
                    title: "股东账户",
                    dataIndex: "holder_account",
                    key: "holder_account",
                },
            ],

            dictMap: {
                businessType: [
                    { value: '30072', label: '两融开户' },
                    { value: '30110', label: '信用北交所' },
                ],
                restrictSecuFlag: [
                    { value: '0', label: '未持有' },
                    { value: '1', label: '持有' },
                ],
                seniorPersonFlag: [
                    { value: '0', label: '不是' },
                    { value: '1', label: '是' },
                ],
                shareholderFlag: [
                    { value: '0', label: '不是' },
                    { value: '1', label: '是' },
                ],
                relatedholderFlag: [
                    { value: '0', label: '没有' },
                    { value: '1', label: '有' },
                ],
            },

        }
    },


    created() {
        if (this.$route.query.details) {
            this.queryClientDetail()
        }
    },
    methods: {
        getDictText(key, value) {
            let results = this.dictMap[key] || [];
            results = results.filter((item) => {
                return item.value == value;
            });
            return (results && results.length && results[0].label) || '';
        },
        queryClientDetail() {
            const detail = JSON.parse(this.$route.query.details);

            this.userInfo = detail
            this.restrictSecuInfoList = detail.restrictSecuInfo;
            this.seniorPersonInfoList = detail.seniorPersonInfo;
            this.shareholderInfoList = detail.shareholderInfo;
            this.relatedholderInfoList = detail.relatedholderInfo;
        }
    },
}
</script>

<style lang="scss" scoped>
::v-deep .ant-layout {
    background-color: #fff;
}

::v-deep .ant-layout-content {
    background-color: #fff !important;
}

.pop_header {
    background-color: #ffffff;
    color: "#282828";
}

.ant_list> :first-child {
    padding: 0;
}

.ant_list_item>div {
    word-break: break-all;
    padding-right: 25px;
    margin-top: 5px;
    min-height: 27px;
}

.ant_list_item>div:not(:last-child):after {
    content: "";
    width: 1px;
    height: 100%;
    background-color: #d4d4d4;
    position: absolute;
    right: 20px;
    top: 0px;
}

.allow_input {
    box-sizing: border-box;
    margin-top: -5px;
    font-variant: tabular-nums;
    list-style: none;
    -webkit-font-feature-settings: "tnum";
    font-feature-settings: "tnum";
    position: relative;
    display: inline-block;
    width: 250px;
    height: 32px;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    line-height: 1.5;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
}

.list_header {
    padding: 10px 20px;
    background-color: #fafafa;
    font-size: 16px;
    font-weight: bold;
}

/* add 20210302 */
.pg-header {
    height: 46px;
    line-height: 46px;
    font-size: 16px;
}

.pg-header-title {
    padding-left: 25px;
    font-weight: bold;
    // background-color: #2E3649;
    color: #1890ff;
}

.pop_content .ant-row {
    margin-bottom: 0;
}

.ant-card-body {
    padding: 15px;
}

.ant-card-bordered {
    margin-bottom: 15px;
}

.ant-card-head {
    border-bottom: none;
}

.ant-list-grid .ant-col>.ant-list-item {
    text-align: center;
}

.btn-block {
    background: #f9f9f9;
    text-align: center;
    position: fixed;
    bottom: 0px;
}

.idcardImg img {
    width: 335px;
    height: 200px;
    margin: 12px;
}

.idcardImg_context {
    width: 350px;
    display: inline-block;
}

.top8 {
    position: relative;
    top: 8px;
}
</style>


