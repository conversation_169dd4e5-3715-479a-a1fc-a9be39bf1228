<template>
  <a-modal
    title="导入流程信息"
    :width="500"
    v-model="isvisible"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="getModuleReset"
    :confirmLoading="confirmLoading"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
    >
    <a-form-model-item label="指定项目" prop="projectId">
        <a-select v-model="form.projectId" placeholder="请选择指定项目" allowClear>
          <a-select-option :value="item.id" v-for="item in projectList" :key="item.id">
            {{item.projectName}}
          </a-select-option>
        </a-select>
     </a-form-model-item>
      <a-form-model-item label="导入文件">
        <a-upload
          :fileList.sync="fileList"
          name="file"
          :multiple="true"
          :beforeUpload="() => false"
          @change="handleChange"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        >
          <a-button> <a-icon type="upload" /> 上传文件 </a-button>
        </a-upload>
      </a-form-model-item>
         <a-form-model-item label="模板文件">
        <a @click="downloademon"  >
             <a-icon type="download" />下载流程模板文件
        </a>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import modalMixed from "@u/modalMixed";
import { request } from "bus-common-component/lib/extension";
// import api from '../api';
export default {
  data() {
    return {
      form: {}, // 权限添加表单
      dictFiledShow: false,
      // 异步加载
      confirmLoading: false,
      file: {},
      fileList: [],
      projectList:[]
    };
  },
  props: {
    parameterData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  // inject: ["api"],
  mixins: [modalMixed],
  inject: ["api"],
  watch: {
    isvisible(newVal) {
      if (newVal) {
        this.file = undefined;
        this.fileList = [];
      }
    },
  },
  created(){
    this.api.queryFlowProjectList().then(({ code, data, msg }) => {
        if (code != 0) return this.$message.error(`${msg}`)
        this.projectList = data
      })
  },
  methods: {
    downloademon() {
      window.location.href="/bc-manage-view/流程定义表.xlsx" ; 
    },
    getModuleReset() {},
    // 提交数据权限分组创建
    submit() {
      if(!this.form.projectId){
        return this.$message.error('请选择指定的项目')
      }
      if (!this.file || this.fileList.length <= 0)
        return this.$message.error(`上传文件不能为空`);
      let data = new FormData();
      data.append("file", this.file);
      data.append("projectId", this.form.projectId);
      this.confirmLoading = true;
      new request({ address: "/bf-manage-server" })
        .upload({ reqUrl: "bf/flow/import", param: data })
        .then(({ code, msg }) => {
          if (code != 0) return this.$message.error(`上传失败：${msg}`);
          this.$message.success("上传成功！");
          this.isvisible = false;
          this.$emit("success");
        })
        .finally(() => (this.confirmLoading = false));
    },
    handleChange({ file, fileList }) {
      this.file = file;
      if (fileList.length >= 0 && fileList.length <= 1) {
        this.fileList = fileList;
      }
    },
  },
};
</script>
