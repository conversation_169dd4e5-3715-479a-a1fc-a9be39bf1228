<template>
  <a-card title="导入数据" :bordered="false">
    <a-button type="primary" icon="upload" @click="contextUploadShow = true"
      >导入上下文数据</a-button
    >
    <a-button type="primary" icon="upload" @click="flowUploadShow = true"
      >导入流程数据</a-button
    >
    <contextUpload :visible.sync="contextUploadShow" @success="refresh" />
    <flowUpload :visible.sync="flowUploadShow" @success="refresh" />
  </a-card>
</template>

<script>
import contextUpload from "./module/contextUpload"; //上传组件
import flowUpload from "./module/flowUpload"; //上传组件
import api from "./api";

export default {
  name: "flowDataManage",
  provide: { api: api },
  components: { contextUpload, flowUpload },
  data() {
    return {
      contextUploadShow: false, //判断是否导入上下文
      flowUploadShow: false, //判断是否导入流程
    };
  },
  methods: {
    refresh() {},
  },
};
</script>

<style lang="scss" scoped></style>
