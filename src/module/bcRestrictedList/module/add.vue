<template>
  <a-modal
    title="手动添加名单客户"
    :width="600"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item label="业务类型" prop="bizType">
        <a-select
          v-model="form.bizType"
          placeholder="请选择业务类型"
          show-search
          option-filter-prop="children"
          allowClear
        >
          <a-select-option
            v-for="item in dictMap['bf.engine.biztype']"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}({{ item.value }})
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="用途" prop="useage">
        <a-select
          v-model="form.useage"
          placeholder="请选择用途"
          :allowClear="true"
          :options="dictMap['bc.common.restrictedUseage']"
        ></a-select>
      </a-form-model-item>
      <a-form-model-item label="客户号" prop="clientId">
        <a-input v-model="form.clientId" placeholder="请输入客户号"></a-input>
      </a-form-model-item>
      <a-form-model-item label="资金账号" prop="fundAccount">
        <a-input
          v-model="form.fundAccount"
          placeholder="请输入资金账号"
        ></a-input>
      </a-form-model-item>
      <a-form-model-item label="证件类型">
        <a-select
          v-model="form.idType"
          placeholder="请选择证件类型"
          :allowClear="true"
          :options="dictMap['bc.common.idKind']"
        ></a-select>
      </a-form-model-item>
      <a-form-model-item label="证件号码">
        <a-input v-model="form.idNo" placeholder="请输入证件号码"></a-input>
      </a-form-model-item>
      <a-form-model-item label="手机号">
        <a-input v-model="form.mobileTel" placeholder="请输入手机号"></a-input>
      </a-form-model-item>
      <a-form-model-item label="策略编号" prop="strategyNo">
        <a-select
          v-model="form.strategyNo"
          placeholder="请选择策略编号"
          show-search
          option-filter-prop="children"
          :allowClear="true"
        >
          <a-select-option
            v-for="item in strategyList"
            :value="item.strategyNo"
            :key="item.id"
          >
            {{ item.strategyName }}({{ item.strategyNo }})
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="有效日期" prop="rangeDate">
        <a-range-picker
          v-model="form.rangeDate"
          :show-time="timePickerOptions"
          @change="onChange"
        />
      </a-form-model-item>
      <a-form-model-item label="备注">
        <a-input v-model="form.remark" placeholder="请输入备注"></a-input>
      </a-form-model-item>
      <a-form-model-item label="状态" prop="status">
        <a-radio-group v-model="form.status">
          <a-radio
            v-for="(item, index) in ['无效', '有效']"
            :key="index"
            :value="index"
          >
            {{ item }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import moment from "moment";
// 默认表单属性
const defaultForm = {
  bizType: undefined, // 业务类型
  useage: undefined, // 用途
  clientId: "", // 客户号
  fundAccount: "", // 资金账号
  idType: undefined, // 证件类型
  idNo: "", // 证件号码
  mobileTel: "", // 手机号
  strategyNo: undefined, // 策略编号
  rangeDate: [],
  beaginTime: "", // 有效开始时间
  endTime: "", // 有效结束时间
  remark: "", // 备注
  status: "", // 状态（0：无效、1：有效）
};

export default {
  name: "bcRestrictedList_Add",
  inject: ["api"],
  data() {
    return {
      form: Object.assign({}, defaultForm), //表单数据,
      rules: {
        bizType: [
          { required: true, message: "业务类型不能为空", trigger: "blur" },
        ],
        useage: [{ required: true, message: "用途不能为空", trigger: "blur" }],
        clientId: [
          { required: true, message: "客户号不能为空", trigger: "blur" },
        ],
        fundAccount: [
          { required: true, message: "资金账号不能为空", trigger: "blur" },
        ],
        // idType: [
        //   { required: true, message: "证件类型不能为空", trigger: "blur" },
        // ],
        // idNo: [
        //   { required: true, message: "证件号码不能为空", trigger: "blur" },
        // ],
        // mobileTel: [
        //   { required: true, message: "手机号不能为空", trigger: "blur" },
        // ],
        strategyNo: [
          { required: true, message: "策略编号不能为空", trigger: "blur" },
        ],
        rangeDate: [
          { required: true, message: "有效日期不能为空", trigger: "blur" },
        ],
        status: [
          {
            required: true,
            message: "状态不能为空",
            trigger: "blur",
          },
        ],
      },
      // 异步加载
      confirmLoading: false,
      timePickerOptions: {
        format: "HH:mm:ss", // 设置时间格式为小时:分钟:秒
        defaultValue: [
          moment("00:00:00", "HH:mm:ss"),
          moment("23:59:59", "HH:mm:ss"),
        ], // 设置默认结束时间为23:59:59
      },
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
    dictMap: {
      type: Object,
      default: () => {},
    },
    strategyList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.bcRestrictedListId) {
        this.query();
      } else {
        this.reset();
      }
      this.$nextTick(() => {});
    },
  },
  methods: {
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    query() {
      this.reset();
      return this.api
        .queryBcRestrictedListDetail({
          bcRestrictedListId: this.parameterData.bcRestrictedListId,
        })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          this.form = data;
        });
    },
    getStringLength(string) {
      // 使用正则表达式匹配中文和全角字符
      var pattern = /[\u4e00-\u9fa5\uff00-\uffff]/g;
      // 获取中文和全角字符的个数
      var chineseCount = (string.match(pattern) || []).length;
      // 返回字符串长度
      return string.length + chineseCount;
    },
    onChange(date, dateString) {
      this.form.beaginTime = dateString[0];
      this.form.endTime = dateString[1];
    },
    // 提交业务准入限制名单创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if (this.getStringLength(this.form.clientId) > 32) {
          return this.$message.error("客户号必须在32个字符以内！");
        }
        if (this.getStringLength(this.form.fundAccount) > 32) {
          return this.$message.error("资金账号必须在32个字符以内！");
        }
        if (this.form.idNo && this.getStringLength(this.form.idNo) > 32) {
          return this.$message.error("证件号码必须在32个字符以内！");
        }
        if (this.form.mobileTel && this.getStringLength(this.form.mobileTel) > 32) {
          return this.$message.error("手机号必须在32个字符以内！");
        }
        if (this.form.remark && this.getStringLength(this.form.remark) > 200) {
          return this.$message.error("备注必须在200个字符以内！");
        }
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(`准入白名单添加失败：${msg}`);
          this.$message.success("准入白名单添加成功！");
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
        };
        this.api.addBcRestrictedList(param).then(callback);
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
