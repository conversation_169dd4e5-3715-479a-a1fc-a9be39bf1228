<template>
  <a-modal
    :title="`查看业务准入限制名单`"
    :width="600"
    v-model="showPop"
    :maskClosable="false"
  >
    <a-descriptions bordered :column="1">
      <a-descriptions-item label="业务类型" prop="bizType">
        {{ form.bizName }}
      </a-descriptions-item>
      <a-descriptions-item label="客户号" prop="clientId">
        {{ form.clientId }}
      </a-descriptions-item>
      <a-descriptions-item label="资金账号" prop="fundAccount">
        {{ form.fundAccount }}
      </a-descriptions-item>
      <a-descriptions-item label="用途" prop="useage">
        {{ form.useageValue }}
      </a-descriptions-item>
      <a-descriptions-item label="证件类型" prop="idType">
        {{ form.idTypeValue }}
      </a-descriptions-item>
      <a-descriptions-item label="证件号码" prop="idNo">
        {{ form.idNo }}
      </a-descriptions-item>
      <a-descriptions-item label="手机号" prop="mobileTel">
        {{ form.mobileTel }}
      </a-descriptions-item>
      <a-descriptions-item label="策略编号" prop="strategyNo">
        {{ form.strategyNo }}
      </a-descriptions-item>
      <a-descriptions-item label="有效开始时间" prop="beaginTime">
        {{ form.beaginTime }}
      </a-descriptions-item>
      <a-descriptions-item label="有效结束时间" prop="endTime">
        {{ form.endTime }}
      </a-descriptions-item>
      <a-descriptions-item label="备注" prop="remark">
        {{ form.remark }}
      </a-descriptions-item>
      <a-descriptions-item label="状态" prop="status">
        {{ form.status == 1 ? "有效" : "无效" }}
      </a-descriptions-item>
      <a-descriptions-item label="更新人" prop="updatedBy">
        {{ form.updatedBy }}
      </a-descriptions-item>
      <a-descriptions-item label="更新时间" prop="updatedTime">
        {{ form.updatedTime }}
      </a-descriptions-item>
      <a-descriptions-item label="创建人" prop="createBy">
        {{ form.createBy }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间" prop="createTime">
        {{ form.createTime }}
      </a-descriptions-item>
    </a-descriptions>
    <template slot="footer">
      <a-button type="primary" @click="ok">确定</a-button>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: "BcRestrictedList_detail",
  inject: ["api"],
  data() {
    return {
      form: {}, //表单数据,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    dictMap: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id) {
        this.query();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    ok() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    query() {
      this.reset();
      return this.api
        .queryBcRestrictedListDetail({
          bcRestrictedListId: this.parameterData.id,
        })
        .then((res) => {
          if (res.code != 0) return;
          let data = res.data;
          data.bizName = this.getType(
            data.bizType,
            this.dictMap["bf.engine.biztype"]
          );
          data.idTypeValue = this.getType(
            data.idType,
            this.dictMap["bc.common.idKind"]
          );
          data.useageValue = this.getType(
            data.useage,
            this.dictMap["bc.common.restrictedUseage"]
          );
          this.form = data;
        });
    },
    getType(val, list) {
      if (!val) return "";
      let arr = [];
      arr = list.filter((v) => {
        return v.value == val;
      });
      return arr[0] ? arr[0].label : "";
    },
  },
};
</script>

<style lang="scss" scoped>
.ant-descriptions-bordered.ant-descriptions-item-content,
.ant-descriptions-bordered .ant-descriptions-item-label {
  min-width: 110px;
}
.ant-descriptions-item-content {
  word-break: break-all;
}
</style>
