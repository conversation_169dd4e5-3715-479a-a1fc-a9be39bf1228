<template>
  <a-modal
    title="导入名单数据"
    :width="600"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
    >
      <div>
        <p>
          1、业务类型请参照中台-系统管理-系统设置-数据字典管理-流程引擎-流程引擎业务类型（例如：创业板交易权限申请-010061，业务类型为010061）。
        </p>
        <p>
          2、证件类型请参照中台-系统管理-系统设置-数据字典管理-统一业务受理平台-证件类型（例如：身份证-0，证件类型为0）。
        </p>
        <p>
          3、策略编号请参照中台-平台配置-规则管理-策略库管理（例如：客户风险要素合格检查-wt_client_risk_info_check，策略编号为wt_client_risk_info_check）。
        </p>
        <p>
          4、用途请参照中台-系统管理-系统设置-数据字典管理-统一业务受理平台-白名单限制使用（例如：新三板股转一类资产检测白名单-客户
          ID(客户编号)-1，用途为1）。
        </p>
        <p>
          5、请不要删除表头。
        </p>
        <p>
          6、支持xls和xlsx文件，一次最多导入5000条。
        </p>
      </div>
      <a-button type="link" @click="downloademon">
        <a-icon type="download" />下载导入模板
      </a-button>
      <a-upload-dragger
        :fileList.sync="fileList"
        name="file"
        :multiple="false"
        :beforeUpload="() => false"
        action="/bc-manage-server"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, .json"
        @change="handleChange"
        @drop="handleChange"
      >
        <p style="margin-bottom: 30px">
          文件拖入此区域
        </p>
        <a-button> <a-icon type="upload" /> 上传文件 </a-button>
      </a-upload-dragger>
    </a-form-model>
  </a-modal>
</template>

<script>
import { request } from "bus-common-component/lib/extension";
// 默认表单属性
const defaultForm = {};
export default {
  name: "bcRestrictedList_ImportAdd",
  inject: ["api"],
  data() {
    return {
      form: Object.assign({}, defaultForm), //表单数据,
      // 异步加载
      confirmLoading: false,
      file: {},
      fileList: [],
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.bcRestrictedListId) {
        this.query();
      } else {
        this.reset();
      }
      this.$nextTick(() => {});
    },
  },
  methods: {
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.file = {};
      this.fileList = [];
      this.confirmLoading = false;
    },
    query() {
      this.reset();
    },
    downloademon() {
      window.location.href = "/bc-manage-view/准入白名单模板表.xlsx";
    },
    handleChange({ file, fileList }) {
      this.file = file;
      if (fileList.length >= 0 && fileList.length <= 1) {
        this.fileList = fileList;
      }
    },
    // 提交业务准入限制名单创建
    submit() {
      if (!this.file || this.fileList.length <= 0) {
        return this.$message.error(`上传文件不能为空`);
      }
      this.confirmLoading = true;
      let data = new FormData();
      data.set("file", this.file);
      new request({ address: "/bc-manage-server" })
        .upload({
          reqUrl: "bcRestrictedList/import",
          param: data,
        })
        .then(({ code, msg }) => {
          if (code != 0) return this.$message.error(`上传失败：${msg}`);
          this.$message.success("上传成功！");
          this.showPop = false;
          this.$emit("success");
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
