// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 新增业务准入限制名单
   * @param {Object} param
   * - id {Long} 自增ID
   * - bizType {String} 业务类型
   * - useage {String} 用途（参见字典：bc_common_restrictedUseage）
   * - clientId {String} 客户号
   * - fundAccount {String} 资金账号
   * - idType {String} 证件类型
   * - idNo {String} 证件号码
   * - mobileTel {String} 手机号
   * - strategyNo {String} 策略编号
   * - beaginTime {Date} 有效开始时间
   * - endTime {Date} 有效结束时间
   * - remark {String} 备注
   * - status {String} 状态（0：无效、1：有效）
   * - updatedBy {String} 更新人
   * - updatedTime {Date} 更新时间
   * - createBy {String} 创建人
   * - createTime {Date} 创建时间
   */
  @Parameters(["_data"])
  addBcRestrictedList() {
    return this.services.initPost({
      reqUrl: "bcRestrictedList/add",
      param: this.param,
    });
  }

  /**
   * 修改业务准入限制名单
   * @param {Object} param
   * - id {Long} 自增ID
   * - bizType {String} 业务类型
   * - useage {String} 用途（参见字典：bc_common_restrictedUseage）
   * - clientId {String} 客户号
   * - fundAccount {String} 资金账号
   * - idType {String} 证件类型
   * - idNo {String} 证件号码
   * - mobileTel {String} 手机号
   * - strategyNo {String} 策略编号
   * - beaginTime {Date} 有效开始时间
   * - endTime {Date} 有效结束时间
   * - remark {String} 备注
   * - status {String} 状态（0：无效、1：有效）
   * - updatedBy {String} 更新人
   * - updatedTime {Date} 更新时间
   * - createBy {String} 创建人
   * - createTime {Date} 创建时间
   * @returns {Promise}
   */
  @Parameters(["_data"])
  editBcRestrictedList() {
    return this.services.initPost({
      reqUrl: "bcRestrictedList/edit",
      param: this.param,
    });
  }

  /**
   * 删除单个业务准入限制名单
   * @param {Object} param
   * - bcRestrictedListId {String} 参数主键ID
   */
  @Parameters(["_data"])
  deleteBcRestrictedList() {
    return this.services.initPost({
      reqUrl: "bcRestrictedList/delete",
      param: this.param,
    });
  }

  /**
   * 删除多个业务准入限制名单
   * @param {Object} param
   * - bcRestrictedListIds {String} 参数主键ID列表，多个以“，”隔开
   *
   */
  @Parameters(["_data"])
  deletesBcRestrictedList() {
    return this.services.initPost({
      reqUrl: "bcRestrictedList/deletes",
      param: this.param,
    });
  }

  /**
   * 查询业务准入限制名单翻页列表
   * @param {Object} param
   * - beginTime {String} 开始时间
   * - endTime {String} 结束时间
   * - pageSize {integer} 每页数量
   * - pageNumber {integer} 当前页码
   * - orderBy {String} 排序字段
   * - id {Long} 自增ID
   * - fundAccount {String} 资金账号
   */
  @Parameters(["_data"])
  queryBcRestrictedListPage() {
    return this.services.initGet({
      reqUrl: "bcRestrictedList/page",
      param: this.param,
    });
  }

  /**
   * 查询指定业务准入限制名单详情
   * @param {Object} param
   * - bcRestrictedListId {String} 参数主键ID
   */
  @Parameters(["_data"])
  queryBcRestrictedListDetail() {
    return this.services.initGet({
      reqUrl: "bcRestrictedList/query",
      param: this.param,
    });
  }

  /**
   * 查询新网厅首页列表
   * @param {Object} param
   * - status {String} 是否有效
   */
  @Parameters(["_data"])
  queryBcClientPortalList() {
    return this.services.initGet({
      reqUrl: "bcClientPortal/list",
      param: this.param,
    });
  }
  
  /**
   * 查询规则策略表列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getStrategyList() {
    return this.qcBizServices.initGet({
      reqUrl: "inner/qcrule/v2/strategy/list",
      param: this.param,
    });
  }
}

export default new api();
