<!-- 业务准入限制名单首页 -->
<template>
  <a-card title="准入白名单维护" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="业务类型">
            <a-select
              v-model="tableForm.bizType"
              placeholder="请选择业务类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="item in dictMap['bf.engine.biztype']"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}({{ item.value }})
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="用途">
            <a-select
              v-model="tableForm.useage"
              placeholder="请选择用途"
              :allowClear="true"
              :options="dictMap['bc.common.restrictedUseage']"
            ></a-select>
          </a-form-model-item>
          <a-form-model-item label="客户号">
            <a-input
              v-model="tableForm.clientId"
              placeholder="请输入客户号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="资金账号">
            <a-input
              v-model="tableForm.fundAccount"
              placeholder="请输入资金账号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="证件类型">
            <a-select
              v-model="tableForm.idType"
              placeholder="请选择证件类型"
              :allowClear="true"
              :options="dictMap['bc.common.idKind']"
            ></a-select>
          </a-form-model-item>
          <a-form-model-item label="证件号码">
            <a-input
              v-model="tableForm.idNo"
              placeholder="请输入证件号码"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="手机号">
            <a-input
              v-model="tableForm.mobileTel"
              placeholder="请输入手机号"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="策略编号">
            <a-select
              v-model="tableForm.strategyNo"
              placeholder="请选择规则"
              show-search
              option-filter-prop="children"
              :allowClear="true"
            >
              <a-select-option
                v-for="item in strategyList"
                :value="item.strategyNo"
                :key="item.id"
              >
                {{ item.strategyName }}({{ item.strategyNo }})
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item label="有效期">
            <a-range-picker v-model="tableForm.rangeDate" @change="onChange" />
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/bcRestrictedList/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        tableId="id"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add"> 新增 </a-button>
          <a-button icon="download" type="primary" @click="importAdd">
            名单导入
          </a-button>
          <a-button
            icon="delete"
            type="danger"
            :disabled="selectedRowKeys.length <= 0"
            @click="remove"
          >
            删除
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click.stop="look(data)"> 查看 </a-button>
          <a-button type="link" @click.stop="modify(data)"> 修改 </a-button>
        </template>
      </tk-table>
      <add
        :isPopShow.sync="isAddPopShow"
        @success="query"
        :dictMap="dictMap"
        :parameterData="selectData"
        :strategyList="strategyList"
      />
      <importAdd
        :isPopShow.sync="isImportAddPopShow"
        @success="query"
        :parameterData="selectData"
      />
      <edit
        :isPopShow.sync="isEditPopShow"
        @success="query"
        :dictMap="dictMap"
        :parameterData="selectData"
        :strategyList="strategyList"
      />
      <LookComponent
        :isPopShow.sync="isLookPopShow"
        :dictMap="dictMap"
        :parameterData="selectData"
      />
    </div>
  </a-card>
</template>

<script>
// 引入添加和编辑弹窗
import add from "./module/add";
import importAdd from "./module/importAdd";
import edit from "./module/edit";
// 引入查看弹窗
import LookComponent from "./module/detail";
import api from "./api";

// 默认表单属性
const defaultForm = {
  id: "", // 名单ID
  bizType: "", // 业务类型
  useage: '', // 用途
  clientId: "", // 客户号
  fundAccount: "", // 资金账号
  idType: "", // 证件类型
  idNo: "", // 证件号码
  mobileTel: "", // 手机号
  strategyNo: "", // 策略编号
  beaginTime: "", // 有效开始时间
  endTime: "", // 有效结束时间
};

export default {
  data() {
    return {
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        // 循环
        {
          field: "id",
          label: "序号",
          isSorter: false,
          align: "center",
          width: 60,
        },
        {
          field: "bizType",
          label: "业务类型",
          isSorter: false,
          width: 140,
          filter: (item) =>
            this.getType(item, this.dictMap["bf.engine.biztype"]),
        },
        {
          field: "clientId",
          label: "客户号",
          isSorter: false,
          width: 150,
        },
        {
          field: "fundAccount",
          label: "资金账号",
          isSorter: false,
          width: 150,
        },
        {
          field: "useage",
          label: "用途",
          isSorter: false,
          width: 100,
          filter: (item) =>
            this.getType(item, this.dictMap["bc.common.restrictedUseage"]),
        },
        {
          field: "idType",
          label: "证件类型",
          isSorter: false,
          width: 100,
          filter: (item) =>
            this.getType(item, this.dictMap["bc.common.idKind"]),
        },
        {
          field: "idNo",
          label: "证件号码",
          isSorter: false,
          width: 150,
        },
        {
          field: "mobileTel",
          label: "手机号",
          isSorter: false,
          width: 150,
        },
        {
          field: "strategyNo",
          label: "策略编号",
          isSorter: false,
          width: 100,
        },
        {
          field: "beaginTime",
          label: "有效开始时间",
          isSorter: false,
          width: 120,
        },
        {
          field: "endTime",
          label: "有效结束时间",
          isSorter: false,
          width: 120,
        },
        {
          field: "remark",
          label: "备注",
          isSorter: false,
          width: 100,
        },
        {
          field: "status",
          label: "状态",
          isSorter: false,
          width: 100,
          filter: (item) => (item === "1" ? "有效" : "无效"),
        },
        {
          field: "updatedBy",
          label: "更新人",
          isSorter: false,
          width: 100,
        },
        {
          field: "updatedTime",
          label: "更新时间",
          isSorter: false,
          width: 100,
        },
        {
          field: "createBy",
          label: "创建人",
          isSorter: false,
          width: 100,
        },
        {
          field: "createTime",
          label: "创建时间",
          isSorter: false,
          width: 100,
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          width: 200,
          fixed: "right",
        },
      ],
      tableForm: {
        id: "", // 自增ID
        fundAccount: "", // 资金账号
      },
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isAddPopShow: false, //添加弹窗是否显示
      isImportAddPopShow: false,
      isEditPopShow: false, //修改弹窗是否显示
      //查看弹窗
      isLookPopShow: false,
      selectData: {},
      operationType: "add",
      businessList: [],
      dictMap: {
        "bf.engine.biztype": [],
        "bc.common.idKind": [],
        "bc.common.restrictedUseage": [],
      },
      strategyList: [],
    };
  },
  provide: { api: api },
  created() {
    Promise.all([this.queryDictMap(), this.getStrategyList()]).finally();
  },
  components: { add, importAdd, edit, LookComponent },
  methods: {
    getStrategyList() {
      api.getStrategyList().then((res) => {
        this.strategyList = res.data;
      });
    },
    queryDictMap() {
      return new Promise((resolve) => {
        Object.keys(this.dictMap).forEach((item) => {
          this.$dict.dictContent(item).then((data) => {
            this.dictMap[item] = (data || []).map((data) => {
              return {
                label: data.dictLabel,
                value: data.dictValue,
              };
            });
            resolve();
          });
        });
      });
    },
    onChange(date, dateString) {
      this.tableForm.beaginTime = dateString[0];
      this.tableForm.endTime = dateString[1];
    },
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableForm.beaginTime)) >
        Date.parse(this.DateFormat(this.tableForm.endTime))
      ) {
        param["beaginTime"] = "";
        param["endTime"] = "";
      }
      delete param.rangeDate;
      return param;
    },
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    // 点击新增按钮
    add() {
      this.isAddPopShow = true;
      this.selectData = {};
    },
    importAdd() {
      this.isImportAddPopShow = true;
      this.selectData = {};
    },
    // 点击修改按钮
    modify(data) {
      this.isEditPopShow = true;
      this.selectData = data;
    },
    // 查看
    look(data) {
      this.isLookPopShow = true;
      this.selectData = data;
    },
    openChange(status) {
      if (status == false) {
        // 切出事件，用于组件缓冲数据
        setTimeout(() => {
          if (this.tableForm.beaginTime && this.tableForm.endTime) {
            if (
              Date.parse(this.DateFormat(this.tableForm.beaginTime)) >
              Date.parse(this.DateFormat(this.tableForm.endTime))
            )
              return this.$message.error("结束日期不应早于开始日期");
          }
        });
      }
    },
    // 删除
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: "业务准入限制名单",
          content: () => <p>确定删除当前业务准入限制名单数据?</p>,
          okText: "确定",
          cancelText: "取消",
          onOk: () => {
            api
              .deletesBcRestrictedList({
                bcRestrictedListIds: this.selectedRowKeys.join(","),
              })
              .then(({ code, msg }) => {
                if (code != 0)
                  return this.$message.error(
                    `删除业务准入限制名单失败：${msg}`
                  );
                this.$message.success("删除业务准入限制名单成功！");
                this.$refs.table.getTableData();
                this.selectedRowKeys = [];
              });
          },
        });
      }
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    getType(val, list) {
      if (!val) return "";
      let arr = [];
      arr = list.filter((v) => {
        return v.value == val;
      });
      return arr[0] ? arr[0].label : "";
    },
  },
};
</script>

<style lang="scss" scoped></style>
