// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询短信模板列表
   * @param {Object} param
   */
  @Parameters([
    "pageNum",
    "pageSize",
    "orderByColumn",
    "isAsc",
    "name",
    "isDelete",
    "state",
  ])
  querySmsList() {
    return this.services.initGet({ reqUrl: "sms/list", param: this.param });
  }

  /**
   * 新增短信模板信息
   * @param {Object} param
   */
  @Parameters([
    "bizType",
    "type",
    "name",
    "smsNo",
    "state",
    "isDelete",
    "content",
    "description",
    // "dayMaxSendnum",
    // "validTime",
    // "lockIntervalTime",
    // "faileLockTime",
    // "dayMaxSendnumByIp",
    "orderline",
    "createBy",
    "modifiedBy",
    "bizType"
  ])
  addSms() {
    return this.services.initPost({ reqUrl: "sms", param: this.param });
  }

  /**
   * 修改短信模板信息
   * @param {Object} param
   */
  @Parameters([
    "bizType",
    "id",
    "type",
    "name",
    "smsNo",
    "state",
    "isDelete",
    "content",
    "description",
    // "dayMaxSendnum",
    // "validTime",
    // "lockIntervalTime",
    // "faileLockTime",
    // "dayMaxSendnumByIp",
    "orderline",
    "createBy",
    "modifiedBy",
    "bizType"
  ])
  editSms() {
    return this.services.initPost({ reqUrl: "sms/edit", param: this.param });
  }

  /**
   * 删除短信模板信息
   * @param {Object} param
   */
  @Parameters(["ids"])
  deleteSms() {
    return this.services.initPost({ reqUrl: "sms/delete", param: this.param });
  }

  /**
   * 查询指定短信模板
   * @param {Object} param
   */
  @Parameters(["id"])
  querySms() {
    return this.services.initGet({ reqUrl: "sms/get", param: this.param });
  }

  /**
    * 查询已上架业务类型
    * @param {Object} param
    * - isShelfed {String} 是否上架
    */
   @Parameters(["isShelfed"])
   queryBusinessList() {
     return this.services.initGet({ reqUrl: "bcBusinessShelf/list", param: this.param});
   }
  
  /**
    * 查询新网厅首页列表
    * @param {Object} param
    * - status {String} 是否有效
    */
    @Parameters(["status"])
    queryBcClientPortalList() {
      return this.services.initGet({ reqUrl: "bcClientPortal/list", param: this.param});
    }
}

export default new api();
