<template>
  <div>
    <a-modal
      :title="(requestId ? '编辑' : '添加') + '短信模板'"
      :visible="showPop"
      @ok="getValue"
      @cancel="closePop"
      class="ant_modal_bigtable"
      :width="1000"
    >
      <template slot="footer">
        <a-button key="back" @click="closePop"> 取消 </a-button>
        <a-button type="primary" key="submit" @click="getValue">
          {{ requestId ? "修改" : "添加" }}
        </a-button>
      </template>
      <a-layout>
        <a-layout-content
          class="pop_content"
          style="min-height: calc(500px - 120px)"
        >
          <a-row>
            <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for="">模板名称<a class="mark_style">*</a></label>
                </a-col>
                <a-col :span="14">
                  <a-input v-model="info.name" placeholder="" />
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for="">模板英文名<a class="mark_style">*</a></label>
                </a-col>
                <a-col :span="14">
                  <a-input v-model="info.smsNo" placeholder="" />
                </a-col>
              </a-row>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for="">是否有效<a class="mark_style">*</a></label>
                </a-col>
                <a-col :span="14">
                  <a-radio-group name="radioGroup" v-model="info.state">
                    <a-radio
                      :value="v.value"
                      v-for="(v, i) in stateList"
                      :key="i"
                      >{{ v.name }}</a-radio
                    >
                  </a-radio-group>
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for="">业务类型<a class="mark_style"></a></label>
                </a-col>
                <a-col :span="14">
                  <!-- <a-radio-group name="radioGroup" v-model="info.isDelete">
                    <a-radio
                      :value="v.value"
                      v-for="(v, i) in isDeleteList"
                      :key="i"
                      >{{ v.name }}</a-radio
                    >
                  </a-radio-group> -->
                  <a-select
                  v-model="info.bizType"
                  placeholder="请选择业务类型"
                  show-search
                  option-filter-prop="children"
                  allowClear
                  class="select_wid"
                >
                  <a-select-option
                    v-for="item in businessList"
                    :key="item.id"
                    :value="item.bizType"
                  >
                    {{ item.bizNickName }}({{ item.bizType }})
                  </a-select-option>
                </a-select>
                </a-col>
              </a-row>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for="">模板类型<a class="mark_style">*</a></label>
                </a-col>
                <a-col :span="14">
                  <a-select
                    v-model="info.type"
                    placeholder=""
                    @change="handleChange"
                    class="select_wid"
                  >
                    <a-select-option
                      :value="v.dictValue"
                      v-for="v in typeList"
                      :key="v.dictValue"
                    >
                      {{ v.dictLabel }}
                    </a-select-option>
                  </a-select>
                </a-col>
              </a-row>
            </a-col>
          </a-row>
          <!-- <a-row>
            <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for=""
                    >单IP每日发送最大数量<a class="mark_style">*</a></label
                  >
                </a-col>
                <a-col :span="14">
                  <a-input
                    v-model="info.dayMaxSendnumByIp"
                    placeholder="范围（0-9999）"
                  />
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for=""
                    >单用户每日发送最大数量<a class="mark_style">*</a></label
                  >
                </a-col>
                <a-col :span="14">
                  <a-input
                    v-model="info.dayMaxSendnum"
                    placeholder="范围（0-9999）"
                  />
                </a-col>
              </a-row>
            </a-col>
          </a-row> -->
          <!-- <a-row>
            <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for="">短信生效时间（秒）<a class="mark_style">*</a></label>
                </a-col>
                <a-col :span="14">
                  <a-input
                    v-model="info.validTime"
                    placeholder="范围（0-9999）"
                  />
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for=""
                    >短信解锁时间间隔（秒）<a class="mark_style">*</a></label
                  >
                </a-col>
                <a-col :span="14">
                  <a-input
                    v-model="info.lockIntervalTime"
                    placeholder="范围（0-9999）"
                  />
                </a-col>
              </a-row>
            </a-col>
          </a-row> -->
          <!-- <a-row>
              <a-col :span="12">
              <a-row>
                <a-col :span="10" class="label_style">
                  <label for="">短信锁定时间（秒）<a class="mark_style">*</a></label>
                </a-col>
                <a-col :span="14">
                  <a-input
                    v-model="info.faileLockTime"
                    placeholder="范围（0-9999）"
                  />
                </a-col>
              </a-row>
            </a-col>
          </a-row> -->
          <a-row>
            <a-col :span="24">
              <a-row>
                <a-col :span="5" class="label_style">
                  <label for="">模板内容<a class="mark_style">*</a></label>
                </a-col>
                <a-col :span="19">
                  <a-textarea v-model="info.content" />
                </a-col>
              </a-row>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-row>
                <a-col :span="5" class="label_style">
                  <label for=""
                    >备注<a class="mark_style">{{
                      requestId != "" ? "*" : ""
                    }}</a></label
                  >
                </a-col>
                <a-col :span="19">
                  <a-textarea v-model="info.description" />
                </a-col>
              </a-row>
            </a-col>
          </a-row>
        </a-layout-content>
      </a-layout>
    </a-modal>
  </div>
</template>
<script>
export default {
  name: "",
  inject:["api"],
  data() {
    return {
      stateList: [{value: "1", name: "有效"},{value: "0", name: '无效'}], //有效值列表
      isDeleteList: [
        { name: "正常", value: "1" },
        { name: "删除", value: "0" },
      ], //删除状态列表
      typeList: window.$dict.dictTypeList('bc.common.smsTemplate'),
      info: {
        type: "",
        name: "", //Y 模版名称
        smsNo: "", //Y 短信英文标识
        state: "1", //Y 是否有效(1:有效;0:无效;)
        isDelete: "1", //Y 是否删除 0删除，1正常
        content: "", //Y 模版内容
        description: "", //N 描述
        dayMaxSendnum: "", //Y 单个用户每日发送最大数量
        validTime: "", //Y 短信生效时间
        lockIntervalTime: "", //Y 短信解锁间隔时间(单位: 秒)
        faileLockTime: "", //Y 短信锁定时间
        dayMaxSendnumByIp: "", //Y 单IP每日发送最大数
        orderline: "", //N 排序值
        createBy: "", //N 创建人
        modifiedBy: "", //N 修改人
      },
      createBy: "9090",
      modifiedBy: "9090",
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    requestId: {
      type: String,
      default:''
    },
    businessList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        console.log(val);
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  components: {},
  created() {
    // this.queryMessageDictory();
  },
  methods: {
    //关闭
    closePop() {
      this.showPop = false;
      this.resetForm();
    },
    //提交
    getValue() {
      let _this = this;
      if (_this.info.name == "") {
        _this.$message.error("模版名称不能为空");
        return false;
      }
      if (_this.info.smsNo == "") {
        _this.$message.error("模版英文名不能为空");
        return false;
      } else if (_this.info.type == "") {
        _this.$message.error("模板类型不能为空");
        return false;
      } 
      // else if (_this.info.dayMaxSendnumByIp === "") {
      //   _this.$message.error("单IP每日发送最大数不能为空");
      //   return false;
      // } else if (this.info.dayMaxSendnum === "") {
      //   _this.$message.error("单用户每日发送最大数量不能为空");
      //   return false;
      // } else if (this.info.validTime === "") {
      //   _this.$message.error("短信生效时间不能为空");
      //   return false;
      // } else if (this.info.lockIntervalTime === "") {
      //   _this.$message.error("短信解锁时间间隔不能为空");
      //   return false;
      // } else if (this.info.faileLockTime === "") {
      //   _this.$message.error("短信锁定时间不能为空");
      //   return false;
      // } 
      else if (this.info.content == "") {
        _this.$message.error("模板内容不能为空");
        return false;
      } else if (!this.info.description && this.requestId != "") {
        _this.$message.error("备注不能为空");
        return false;
      }
      let param = this.info;
      if (_this.requestId == "") {
        //新增
        this.api.addSms(param, {
          encode: false,
          post: 2,
        }).then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.$emit("updateList", true);
            _this.closePop();
          } else {
            _this.$message.error(res.msg);
          }
        });
      } else if (_this.requestId != "") {
        //编辑
        let param1 = {
          id: Number(_this.requestId),
          modifiedBy: _this.createBy,
        };
        param = Object.assign(param, param1);
        this.api.editSms(param, {
          encode: false,
          post: 2,
        }).then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.$emit("updateList", true);
            _this.closePop();
          } else {
            _this.$message.error(res.msg);
          }
        });
      }
    },
    handleChange() {},
    //获取邮编详情
    getDetail() {
      let _this = this;
      this.api.querySms(
        {
          id: Number(_this.requestId),
        },
        {
          encode: false,
        }
      ).then((res) => {
        if (res.code === 0) {
          console.info("res", res);
          _this.info = res.data;
        }
      });
    },
    //重置
    resetForm() {
      this.info = {
        type: "",
        name: "", //Y 模版名称
        smsNo: "", //Y 短信英文标识
        state: "1", //Y 是否有效(1:有效;0:无效;)
        isDelete: "1", //Y 是否删除 0删除，1正常
        content: "", //Y 模版内容
        description: "", //N 描述
        dayMaxSendnum: "", //Y 单个用户每日发送最大数量
        validTime: "", //Y 短信生效时间
        lockIntervalTime: "", //Y 短信解锁间隔时间(单位: 秒)
        faileLockTime: "", //Y 短信锁定时间
        dayMaxSendnumByIp: "", //Y 单IP每日发送最大数
        orderline: "", //N 排序值
        createBy: "", //N 创建人
        modifiedBy: "", //N 修改人
      };
    },
    // queryMessageDictory() {
    //   queryDictory(
    //     { dictType: "messageType" },
    //     {
    //       encode: false,
    //     }
    //   ).then((res) => {
    //     if (res.code === 0) {
    //       if (res.data && res.data.list && res.data.list.length > 0) {
    //         res.data.list.forEach(v => {
    //           this.typeList.push({ value: v.dictLabel, key: v.dictValue });
    //         });
    //       }
    //     }
    //   });
    // },
  },
  watch: {
    isPopShow(n) {
      let _this = this;
      if (n) {
        if (_this.requestId != "") {
          _this.getDetail();
        }
      }
    },
  },
};
</script>
<style scoped>
.jc_pop_fixed {
  margin-top: -250px;
  margin-left: -375px;
  width: 750px;
  height: 500px;
}
</style>
