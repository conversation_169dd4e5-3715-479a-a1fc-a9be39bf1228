<template>
  <a-card title="短信模板列表" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="dealYb('search')" @reset="handleReset">
          <a-form-model-item label="模板名称">
            <a-input
              v-model="tableForm.name"
              placeholder="请输入模板名称"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="业务类型">
            <a-select
              v-model="tableForm.bizType"
              placeholder="请选择业务类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="item in businessList"
                :key="item.id"
                :value="item.bizType"
              >
                {{ item.bizNickName }}({{ item.bizType }})
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="是否有效">
            <a-select
              v-model="tableForm.state"
              placeholder="请选择状态"
              allowClear
            >
              <a-select-option
                :value="v.value"
                v-for="v in stateList"
                :key="v.value"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <!-- <a-form-model-item label="是否删除">
            <a-select v-model="tableForm.isDelete" placeholder="请选择是否删除">
              <a-select-option :value="1" :key="1"> 正常 </a-select-option>
              <a-select-option :value="0" :key="0"> 删除 </a-select-option>
            </a-select>
          </a-form-model-item> -->
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="page_main">
      <a-divider />
      <a-row class="btn_row">
        <a-col>
          <a-button type="primary" icon="plus" @click="dealYb('add')"
            >新增</a-button
          >
          <a-button
            type="primary"
            icon="edit"
            @click="dealYb('edit')"
            :disabled="!(selectedRowKeys.length == 1)"
            >编辑</a-button
          >
          <a-button
            v-if="constShowDelelte"
            type="primary"
            icon="delete"
            @click="dealYb('remove')"
            :disabled="selectedRowKeys.length == 0"
            >删除</a-button
          >
          <a-button type="primary" icon="redo" @click="dealYb('redo')"
            >刷新</a-button
          >
        </a-col>
      </a-row>
      <div class="access-table">
        <tk-table
          ref="table"
          :tableData.sync="columns"
          getMethods="bc-manage-server/sms/list"
          :isSelected="true"
          :isPaging="true"
          :tableFrom="tableForm"
          :selectedRowKeys.sync="selectedRowKeys"
          tableId="id"
        >
        </tk-table>
      </div>
      <addOrEdit
        :isPopShow.sync="isPopShow"
        @updateList="updateList"
        :requestId="requestId"
        :businessList="businessList"
      ></addOrEdit>
    </div>
  </a-card>
</template>

<script>
import api from "./api";

const defaultForm = {
  bizType: undefined,
  name: "",
  state: undefined,
  // isDelete: undefined,
};

export default {
  name: "messageManage",
  provide: { api },
  data() {
    return {
      tableForm: {
        bizType: undefined,
        name: "",
        state: undefined,
        // isDelete: undefined,
      },
      // name: "", //搜索用户名key
      // name1: "", //确认key
      // state: undefined,
      stateList: [
        { value: "1", name: "有效" },
        { value: "0", name: "无效" },
      ], //状态列表
      constShowDelelte: true,
      // isDelete: undefined,
      //table start
      //表格数据
      data: [],
      //表头数据
      columns: [
        {
          label: "模板名称",
          // dataIndex: "name",
          field: "name",
          isSorter: false,
        },
        {
          label: "模板类型",
          // dataIndex: "type",
          field: "type",
          isSorter: false,
          // scopedSlots: { customRender: "type" },
          filter: (item) => this.getType(item, this.typeList),
        },
        {
          label: "业务类型",
          field: "bizType",
          isSorter: false,
          filter: (item) => this.getBusinessName(item, this.businessList),
        },
        // {
        //   label: "是否删除",
        //   // dataIndex: "isDelete",
        //   field: "isDelete",
        //   isSorter: false,
        //   // scopedSlots: { customRender: "isDelete" },
        //   filter: (item) => (item === "1" ? "正常" : "删除"),
        // },
        {
          label: "状态",
          // dataIndex: "state",
          field: "state",
          isSorter: false,
          // sorter: true,
          // customRender: 'tradeState' -> 自定义 slot 属性名，对应模版中的 slot 属性，即这里自定义为啥，对应模版中的 slot 就要等于啥
          // 模板中对应的 slot-scope 属性可以用来传递三个参数（val,row,index），分别是当前值、当前行数据和当前索引
          // scopedSlots: { customRender: "state" },
          filter: (item) => (item === "1" ? "有效" : "无效"),
        },
        // {
        //   label: "短信生效时间",
        //   // dataIndex: "validTime",
        //   field: "validTime",
        //   isSorter: true,
        // },
        // {
        //   label: "短信锁定时间",
        //   // dataIndex: "faileLockTime",
        //   field: "faileLockTime",
        //   isSorter: true,
        // },
        {
          label: "创建时间",
          // dataIndex: "createDate",
          field: "createDate",
          isSorter: true,
        },
      ],
      selectedRowKeys: [], // Check here to configure the default column

      //新增弹窗
      isPopShow: false, //弹窗是否显示
      requestId: '', //id
      typeList: window.$dict.dictTypeList("bc.common.smsTemplate"),
      businessList: [],
    };
  },
  components: {
    addOrEdit: () =>
      import(/* webpackChunkName: "addOrEdit" */ "./module/addOrEdit.vue"),
  },
  created(){
    this.queryBusinessList();
  },
  methods: {
    queryBusinessList() {
      api.queryBcClientPortalList({ status: "1" }).then((res) => {
        this.businessList = res.data;
      });
    },
    //新增刷新
    updateList(param) {
      if (param) {
        this.$refs.table.getTableData();
      }
    },
    //处理按钮
    dealYb(val) {
      if (val == "add") {
        //添加
        this.requestId = '';
        this.isPopShow = true;
      } else if (val == "edit") {
        //编辑
        this.requestId = this.selectedRowKeys[0]+''; //获取id
        this.isPopShow = true;
      } else if (val == "remove") {
        //删除
        if (this.selectedRowKeys.length > 0) {
          this.remove();
        }
      } else if (val == "search") {
        //搜索
        this.$refs.table.getTableData();
      } else if (val == "redo") {
        this.$refs.table.getTableData();
      }
    },
    //删除
    remove() {
      let _this = this;
      this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deleteApi();
        },
      });
    },
    //请求删除接口
    deleteApi() {
      api
        .deleteSms({ ids: this.selectedRowKeys })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.$refs.table.getTableData();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    handleReset() {
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    getType(val, list) {
      if (!val) return "";
      let arr = [];
      arr = list.filter((v) => {
        return v.dictValue == val;
      });
      return arr[0] ? arr[0].dictLabel : "";
    },
    getBusinessName(val,list){
      if (!val) return "";
      let arr = [];
      arr = list.filter((v) => {
        return v.bizType == val;
      });
      return arr[0] ? arr[0].bizNickName : "";
    }
  },
};
</script>

<style>
.alert-test {
  text-align: center;
  padding: 5px;
}
</style>
