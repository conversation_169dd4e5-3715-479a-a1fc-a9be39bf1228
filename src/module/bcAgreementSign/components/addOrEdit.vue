<template>
	<div>
		<a-modal
			:title="
				(requestId ? '编辑' : '添加') + '银行信息'
			"
			:visible="showPop"
			:destroyOnClose="true"
			@ok="getValue"
			@cancel="closePop"
			class="ant_modal_bigtable"
		>
			<template slot="footer">
				<a-button key="back" @click="resetForm">
					重置
				</a-button>
				<a-button
					type="primary"
					key="submit"
					@click="getValue"
				>
					{{ requestId ? '修改' : '添加' }}
				</a-button>
			</template>
			<a-form-model
				ref="form"
				:model="form"
				:rules="rules"
				:label-col="{ span: 6 }"
				:wrapper-col="{ span: 16 }"
			>
				<a-form-model-item
					label="协议名称"
					prop="agreementName"
				>
					<a-select
						v-model="form.agreementName"
						placeholder="请选择协议名称"
						show-search
						option-filter-prop="children"
					>
						<a-select-option
							:value="v.dictValue"
							v-for="v in agreementList"
							:key="v.dictValue"
						>
							{{ v.dictLabel }}
						</a-select-option>
					</a-select>
				</a-form-model-item>
				<a-form-model-item
					label="资金账户"
					prop="fundAccount"
				>
					<a-input
						placeholder="请输入资金账户"
						v-model="form.fundAccount"
					></a-input>
				</a-form-model-item>
			</a-form-model>
		</a-modal>
	</div>
</template>
<script>
export default {
	name: '',
	inject: ['api'],
	data() {
		return {
			requestId: '',
			agreementList: [],
			form: {
				agreementName: '', //Y
				fundAccount: '', //Y
			},
			rules: {
				agreementName: [
					{
						required: true,
						message: '协议名称不能为空',
						trigger: 'change',
					},
				],
				fundAccount: [
					{
						required: true,
						message: '资金账户不能为空',
						trigger: 'blur',
					},
				],
			},
		};
	},
	props: {
		isPopShow: {
			type: Boolean,
			default: false,
		},
		parameterData: {
			type: Object,
		},
	},
	mounted() {},
	computed: {
		showPop: {
			get() {
				return this.isPopShow;
			},
			set(val) {
				this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
			},
		},
	},
	created() {
		this.api.agreeDict().then((res) => {
			var obj = {};
			this.agreementList = res.data.reduce(function (
				item,
				next
			) {
				obj[next.dictValue]
					? ''
					: (obj[next.dictValue] =
							true && item.push(next));
				return item;
			},
			[]);
		});
	},
	methods: {
		updateList() {
			this.closePop();
			this.$emit('success');
		},
		// 重置表单
		resetForm() {
			this.form = {
				bankNo: '', //Y
				bankName: '', //Y
			};
		},
		//关闭
		closePop() {
			this.resetForm();
			(this.PCFileList = []),
				(this.APPFileList = []),
				(this.showPop = false);
		},
		//提交
		getValue() {
			this.$refs.form.validate((valid) => {
				if (!valid) return;
				let param = {
					agreementName:
						this.agreementList.filter(
							(item) =>
								item.dictValue ===
								this.form.agreementName
						)[0].dictLabel,
					fundAccount: this.form.fundAccount,
				};
				console.log(param);
				let callback = ({ code, msg }) => {
					if (code != 0)
						return this.$message.error(
							`协议补签信息${
								this.requestId
									? '修改'
									: '添加'
							}失败：${msg}`
						);
					this.$message.success(
						`协议补签信息${
							this.requestId ? '修改' : '添加'
						}成功！`
					);
					this.updateList();
				};
				this.requestId
					? this.api
							.agreementEdit(param)
							.then(callback)
					: this.api
							.agreementAdd(param)
							.then(callback);
			});
		},
	},
	watch: {
		isPopShow(n) {
			let _this = this;
			if (n) {
				if (_this.requestId != '') {
					_this.$nextTick(() => {
						_this.getDetail();
					});
				}
			}
		},
	},
};
</script>
<style lang="scss" scoped>
::v-deep .idKind .ant-checkbox-wrapper {
	display: block;
}

::v-deep
	.idKind
	.ant-checkbox-wrapper
	+ .ant-checkbox-wrapper {
	margin-left: 0;
}
</style>
