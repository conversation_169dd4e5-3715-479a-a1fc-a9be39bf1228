// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	// /**
	//  * 根据ID查询银行基本信息
	//  * @param {Object} param
	//  * - bcBankId {Number} 银行ID
	//  */
	// @Parameters(["_data"])
	// queryBankById() {
	// 	return this.services.initGet({
	// 		reqUrl: '/bcBank/query',
	// 		param: this.param,
	// 	});
	// }

	/**
	 * 查询所有协议
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	agreeDict() {
		return this.services.initGet({
			reqUrl: '/dict/agreeDict',
			param: this.param,
		});
	}

	/**
	 * 新增银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	agreementAdd() {
		return this.services.initPost({
			reqUrl: '/bcAgreementSignNameList/add',
			param: this.param,
		});
	}

	/**
	 * 导出银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	agreementExport() {
		return this.services.initGet({
			reqUrl: '/bcAgreementSignNameList/export',
			param: this.param,
		});
	}
}

export default new api();
