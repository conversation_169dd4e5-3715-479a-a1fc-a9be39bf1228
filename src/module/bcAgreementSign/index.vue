<template>
	<a-card title="协议补签管理" :bordered="false">
		<div class="searchForm">
			<a-form-model
				layout="inline"
				:model="tableForm"
			>
				<tkSelectForm
					@query="query"
					@reset="reset(0)"
				>
					<a-form-model-item label="协议名称">
						<a-input
							placeholder="请输入"
							v-model="
								tableForm.agreementName
							"
						></a-input>
					</a-form-model-item>
					<a-form-model-item label="资金账号">
						<a-input
							placeholder="请输入"
							v-model="tableForm.fundAccount"
						></a-input>
					</a-form-model-item>
				</tkSelectForm>
			</a-form-model>
		</div>
		<div class="access-table">
			<tk-table
				ref="table"
				:intercept-response="intercept_response"
				:tableData.sync="columns"
				:tableFromFilter="tableFormFilter"
				getMethods="bc-manage-server/bcAgreementSignNameList/page"
				:isSelected="true"
				:isPaging="true"
				:tableFrom="tableForm"
				:selectedRowKeys.sync="selectedRowKeys"
				tableId="bankId"
			>
				<div
					class="table-button-area"
					slot="tableHeader"
				>
					<a-button
						icon="plus"
						type="primary"
						@click="add"
					>
						添加
					</a-button>
					<a-button
						icon="download"
						type="primary"
						@click="downloadExcel"
					>
						导入
					</a-button>
					<a-button
						icon="upload"
						type="primary"
						@click="exportExcel"
					>
						导出
					</a-button>
					<a-button
						type="primary"
						icon="redo"
						@click="reset(1)"
						>刷新</a-button
					>
				</div>
			</tk-table>
		</div>

		<upload
			:visible.sync="uploadShow"
			@success="query"
		/>
		<addOrEdit
			:isPopShow.sync="isPopShow"
			@updateList="updateList"
			@success="query"
			:parameterData="selectData"
			:requestId="requestId"
		/>
	</a-card>
</template>

<script>
// 引入添加和编辑弹窗
import addOrEdit from './components/addOrEdit';
import upload from './components/upload';
import api from './api';

export default {
	data() {
		return {
			columns: [
				// 循环
				{
					field: 'agreementName',
					label: '协议名称',
					isSorter: false,
				},
				{
					field: 'fundAccount',
					label: '资金账号',
					isSorter: false,
				},
				{
					field: 'branchName',
					label: '营业部名称',
					isSorter: false,
				},
				{
					field: 'clientName',
					label: '用户名称',
					isSorter: false,
				},
				{
					field: 'createDate',
					label: '创建时间',
					isSorter: false,
				},
				// {
				// 	field: 'state',
				// 	label: '是否启用',
				// 	isSorter: false,
				// 	filter: (item) =>
				// 		item === '1' ? '是' : '否',
				// },
			],
			isBuffetList: this.$baseDict.base.isBuffetList, //方式列表
			tableForm: {
				agreementName: '',
				fundAccount: '',
			},
			selectData: {},
			selectedRowKeys: [], // Check here to configure the default column

			//新增弹窗
			isPopShow: false, //弹窗是否显示
			requestId: '', //id

			uploadShow: false,
		};
	},
	provide: { api: api },
	components: { addOrEdit, upload },
	methods: {
		// 导入downloadExcel
		downloadExcel() {
			this.uploadShow = true;
		},

		// 导出exportExcel
		exportExcel() {
			window.location.href =
				'/bc-manage-server/bcAgreementSignNameList/export';
		},

		// 搜索框参数
		tableFormFilter(param) {
			return param;
		},

		query() {
			this.$refs.table.getTableData();
			this.selectedRowKeys = [];
			this.requestId = '';
		},

		updateList() {},

		reset(type) {
			this.tableForm = {
				bankNo: '',
				keyWord: '',
			};
			if (type) {
				this.$nextTick(() => {
					this.$refs.table.getTableData();
				});
			}
		},

		// 点击新增按钮
		add() {
			this.isPopShow = true;
			this.selectData = {};
			this.requestId = '';
		},

		// queryBankById(data) {
		// 	api.queryBankById({}).then((res) => {
		// 		this.isPopShow = true;
		// 		this.selectData = res.data;
		// 		this.requestId = data.id;
		// 	});
		// },
	},
};
</script>

<style lang="scss" scoped>
::v-deep .img-table {
	max-height: 23px;
}
</style>
