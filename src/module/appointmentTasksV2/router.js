/*
 * @Author: wusm
 * @Date: 2024-11-26 17:13:14
 * @LastEditors: wusm
 * @LastEditTime: 2024-11-26 23:00:29
 * @Description: 
 * @FilePath: \bc-manage-view\src\module\appointmentTasksV2\router.js
 */
// 注册对应的访问路由
export default [{
  path: '/appointmentTasksV2',
  component: (resolve) => require(['./index'], resolve),
  name: 'appointmentTasksV2',
  meta: { title: '预约挽留列表' }
},
  {
    path: '/taskDetailV2',
    component: (resolve) => require(['./module/taskDetail'], resolve),
    name: 'taskDetailV2',
    meta: { title: '查看详情' }
  }
]
