// 加载对应的装饰器
import { Parameters } from '@u/decorator'

class api {
  /**
   * 新增预约任务表
   * @param {Object} param
   */
  @Parameters([])
  addTask () {
    return this.services.initPost({
      reqUrl: 'taskInfo/add',
      param: this.param,
    })
  }

  /**
   * 修改预约任务表
   * @param {Object} param
   */
  @Parameters([])
  editTask () {
    return this.services.initPost({
      reqUrl: 'taskInfo/edit',
      param: this.param,
    })
  }

  /**
   * 查询预约任务表列表
   * @param {Object} param
   */
  @Parameters([])
  listTask () {
    return this.services.initPost({
      reqUrl: 'taskInfo/list',
      param: this.param,
    })
  }

  /**
   * 查间指定预约任务表
   * @param {Object} param
   */
  @Parameters(['taskInfoId'])
  queryTask () {
    return this.services.initGet({
      reqUrl: 'taskInfo/automation/query',
      param: this.param,
    })
  }

  /**
   * 查询预约任务表翻页列表
   * @param {Object} param
   */
  @Parameters([])
  pageTask () {
    return this.services.initPost({
      reqUrl: 'taskInfo/page',
      param: this.param,
    })
  }

  /**
   * 删除指定预约任务表
   * @param {Object} param
   */
  @Parameters([])
  deleteTask () {
    return this.services.initPost({
      reqUrl: 'taskInfo/delete',
      param: this.param,
    })
  }

  /**
   * 批量删除预约任务表
   * @param {Object} param
   */
  @Parameters([])
  deletesTask () {
    return this.services.initPost({
      reqUrl: 'taskInfo/deletes',
      param: this.param,
    })
  }

  /**
   * 查询执行组织信息
   * @param {Object} param
   */
  @Parameters([])
  queryAllTeams () {
    return this.services.initGet({
      reqUrl: 'taskInfo/customer/allTeams',
      param: this.param,
    })
  }
}

export default new api()
