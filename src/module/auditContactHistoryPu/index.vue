<template>
    <a-card title="视频见证历史" class="management" :bordered="false">
        <div class="searchFrom">
            <a-form-model layout="inline" :model="tableFrom">
                <tkSelectForm @query="success" @reset="reset">
                    <a-form-model-item label="客户姓名">
                        <a-input v-model="tableFrom.name" placeholder="请输入客户姓名" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="性别">
                        <a-select v-model="tableFrom.gender" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.gender']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="手机号码">
                        <a-input v-model="tableFrom.phone" placeholder="请输入手机号码" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="证件号码">
                        <a-input v-model="tableFrom.cardNo" placeholder="请输入证件号码" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="流水号">
                        <a-input v-model="tableFrom.clientId" placeholder="请输入流水号" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="见证状态">
                        <a-select v-model="tableFrom.witnessStatus" placeholder="全部" show-search
                            option-filter-prop="children" allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.witnessStatus']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="见证完成时间">
                        <a-range-picker v-model="tableFrom.endTimes" allowClear valueFormat="YYYY-MM-DD" />
                    </a-form-model-item>
                    <a-form-model-item label="见证方式">
                        <a-select v-model="tableFrom.openType" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.openType']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="视频业务类型">
                        <a-select v-model="tableFrom.videoType" placeholder="全部" show-search option-filter-prop="children"
                            allowClear>
                            <a-select-option :value="v.value" v-for="v, i in dictMap['bc.common.videotype']" :key="i">
                                {{ v.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="见证坐席">
                        <a-input v-model="tableFrom.agentName" placeholder="请输入见证坐席" allowClear />
                    </a-form-model-item>
                </tkSelectForm>
            </a-form-model>
        </div>
        <div class="access-table">
            <tk-table ref="table" :tableData.sync="comlun"
                getMethods="bc-manage-server/gjKhHis/margin/migration/contact/history"
                :intercept-response="intercept_response" :isPaging="true" :tableFromFilter="tableFormFilter"
                :tableFrom="tableFrom" tableId="id">
                <template slot="operation" slot-scope="data">
                    <a class="action-btn" @click="showDetail(data)"> 查看详情 </a>
                </template>
            </tk-table>

        </div>
    </a-card>
</template>

<script>

export default {
    name: 'auditRecords',
    data() {
        return {
            comlun: [
                {
                    field: 'openType', label: '见证方式', width: 100,
                    filter: item => this.getDictText('bc.common.openType', item),
                },
                { field: 'clientId', label: '流水号', width: 120 },
                {
                    field: 'name', label: ' 客户姓名', width: 100,
                },
                {
                    field: 'gender', label: '客户性别', width: 100,
                    filter: item => this.getDictText('bc.common.gender', item),
                },
                {
                    field: 'cardNum', label: '证件号码', width: 150,
                },
                {
                    field: 'videotype', label: '视频业务类型', width: 100,
                    filter: item => this.getDictText('bc.common.videotype', item),
                },
                {
                    field: 'videoTime', label: '见证完成时间', width: 150,
                },
                {
                    field: 'agentName', label: '见证坐席', width: 100,
                },
                {
                    field: 'status', label: '见证结果', width: 100,
                    filter: item => this.getDictText('bc.common.witnessStatus', item),
                },
                {
                    field: 'memo', label: '备注', width: 180, isEllipsis: true
                },
                { field: 'operation', label: '操作', align: 'center', fixed: 'right', width: 100 }

            ],
            dictMap: {
                'bc.common.gender': [
                    { value: '0', label: '男' },
                    { value: '1', label: '女' },
                ],
                'bc.common.videotype': [
                    { value: '0', label: '新开证券账户' },
                    { value: '1', label: '补开证券账户' },
                    { value: '2', label: '融资融券开户' },
                    { value: '3', label: '创业板新开' },
                ],
                'bc.common.witnessStatus': [
                    { value: '0', label: '未通过' },
                    { value: '1', label: '通过' },
                    { value: '2', label: '中断' },
                ],
                'bc.common.openType': [
                    { value: '3', label: '在线' },
                    { value: '4', label: '离线' },

                ],


            },
            tableFrom: {
                videoType: '0'
            },
        }
    },
    methods: {
        getDictText(key, value) {
            let results = this.dictMap[key] || [];
            results = results.filter((item) => {
                return item.value == value;
            });
            return (results && results.length && results[0].label) || '';
        },
        tableFormFilter(param) {
            // 申请时间
            if (this.tableFrom?.endTimes && this.tableFrom.endTimes?.length > 0) {
                param["startTime"] = this.tableFrom.endTimes[0];
                param["endTime"] = this.tableFrom.endTimes[1];
            }
            delete param.endTimes
            return param;
        },
        success() {
            this.$refs.table.getTableData();
        },
        // 重置
        reset() {
            this.tableFrom = {};
        },
        // 查看详情
        showDetail(data) {
            console.log('data', data)
            // const params = new URLSearchParams(window.location.search);
            data.witnessFlag = data.status;
            const datas = JSON.stringify(data);
            let href = `/bc-manage-view/auditContactHistory/DetailInfo?processId=${data.clientId}&details=${datas}`
            window.open(href, '_blank')
        },
    }
}
</script>

