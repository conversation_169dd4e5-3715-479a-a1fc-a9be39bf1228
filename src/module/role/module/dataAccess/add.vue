<template>
  <a-modal title="添加角色数据权限" v-model="isvisible" ok-text="确认" cancel-text="取消" @ok="submit" @cancel="reset" :confirm-loading="confirmLoading" :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :rules="rules">
      <a-form-model-item label="数据权限" prop="roleDataGroupKey">
        <a-select v-model="form.roleDataTypeId" placeholder="请选择对应的数据权限" @change="dataChange">
          <a-select-option :value="item.roleDataTypeId" v-for="item in dataTree" :key="item.roleDataTypeId">
            {{item.roleDataName}}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="是否本人数据" prop="roleDataName">
        <a-radio-group button-style="solid" v-model="form.selfOnly">
          <a-radio-button value="0"> 是 </a-radio-button>
          <a-radio-button value="1"> 否 </a-radio-button>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="数据" prop="roleDataValues" v-if="form.roleDataTypeId">
        <!-- 此处需要根据用户选择的数据权限进行判断后选择 -->
        <!-- 数据类型为字符串统一进行该操作 -->
        <a-textarea allow-clear v-model="form.roleDataValues" :rows="8" v-if="selectObj.roleDataMode == 2" />
        <!-- 数据类型为树状或者列表时进行操作 -->
        <tk-tree :isSelected="true" :checkedKeys.sync="form.roleDataValues" :tree-data="sourceData || []" v-if="selectObj.roleDataMode == 0 || selectObj.roleDataMode == 1" :replaceFields="{ title:selectObj.datasourceFieldLabel||'label', key:selectObj.datasourceFieldValue||'key' }" />
        <!-- 动态 -->
        <!-- 静态 -->
        <!-- 数据类型为树状 -->
        <!-- 数据类型为列表 -->
        <!-- 数据类型为字符串 -->
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import modalMixed from '@u/modalMixed';
// 注册当前请求对应的上下文请求组
import { request } from 'bus-common-component/lib/extension';
export default {
  data() {
    return {
      // 权限添加表单
      form: {
        selfOnly: "0",
        roleDataValues: undefined
      },
      // 选中对象
      selectObj: {},
      // 表单权限验证
      rules: {
        roleDataTypeId: [{ required: true, message: '数据权限不能为空', trigger: 'blur' }],
        selfOnly: [{ required: true, message: '数据权限分组名称不能为空', trigger: 'blur' }],
      },
      // 异步加载
      confirmLoading: false,
      // 可以添加的数据权限列表
      dataTree: [],
      // 添加数据节点
      sourceData: [],
      // 当前用户选中节点
      checkedKeys: [],
    };
  },
  inject: ["api"],
  props: {
    roleId: String, // 父级菜单编号 -- 不可为空
  },
  mixins: [modalMixed],
  created() {
    this.getTypeList();
  },
  methods: {
    // 重置对应的表单
    reset() {
      this.form = {
        roleDataTypeId: undefined,
        selfOnly: "0",
      }
      this.$refs.form.resetFields();
    },
    // 查询数据权限
    getTypeList() {
      this.api.getRoleTypeList().then(({ code, data }) => {
        if (code != 0) return;
        this.dataTree = data;
      })
    },
    // 提交数据权限分组创建
    submit() {
      this.confirmLoading = true;
      this.$refs.form.validate(valid => {
        if (!valid) return this.confirmLoading = false;
        let param = JSON.parse(JSON.stringify(this.form));
        if ((this.selectObj.roleDataMode == 0 || this.selectObj.roleDataMode == 1) && param.roleDataValues && param.roleDataValues.length > 0) {
          param.roleDataValues = param.roleDataValues.join(',')
        }
        param["roleId"] = this.roleId;
        this.api.addRoleData(param).then(({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`数据权限添加失败：${msg}`);
          this.$message.success('数据权限添加成功！');
          // 关闭弹窗
          this.isvisible = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
        })
      })
    },
    // 值改变事件
    dataChange(val) {
      let data = JSON.parse(JSON.stringify(this.dataTree));
      let obj = data.find(item => item.roleDataTypeId == val);
      this.selectObj = obj;
      // 当前为对应的动态数据源时
      if (obj.datasourceMode == 1 && obj.roleDataMode != 2) {
        this.queryTree();
      } else {
        // 静态数据
        if (obj.roleDataMode == 0 || obj.roleDataMode == 1) {
          this.sourceData = obj.datasource;
        } else {
          this.form.roleDataValues = obj.datasource;// JSON.stringify(data);
        }
      }
    },
    queryTree() {
      new request().initGet({ reqUrl: this.selectObj.datasource.substr(1) }).then(({ code, msg, data }) => {
        if (code != 0) return this.$message.error(`获取失败：${msg}`);
        if (this.selectObj.roleDataMode == 0 || this.selectObj.roleDataMode == 1) {
          this.sourceData = data;
        } else {
          this.form.roleDataValues = data;// JSON.stringify(data);
        }
        this.$forceUpdate();
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.ant-select-dropdown-menu {
}
</style>