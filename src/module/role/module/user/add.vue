<template>
  <a-modal title="添加用户" v-model="isvisible" :width="850" :maskClosable="false" ok-text="确认" cancel-text="取消" @ok="addUser" @cancel="reset" >
    <div class="searchFrom">
      <a-form-model layout="inline" :model="tableFrom" :labelCol="{span:7}" :wrapperCol="{span:15}">
        <a-form-model-item label="用户名称">
          <a-input v-model="tableFrom.realName" placeholder="请输入用户名称" allowClear />
        </a-form-model-item>
        <a-form-model-item label="手机号">
          <a-input v-model="tableFrom.phone" placeholder="请输入手机号" />
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" @click="query" style="margin-right:10px"> 查询 </a-button>
          <a-button type="primary" @click="reset"> 重置 </a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table ref="table" :intercept-response="intercept_response" :tableData.sync="comlun" :tableFrom="tableFrom" getMethods="work-manage-server/user/list" :isSelected="true" :isPaging="true" :selectedRowKeys.sync="selectedRowKeys" tableId="userId">
      </tk-table>
    </div>
  </a-modal>
</template>
<script>
import modalMixed from '@u/modalMixed';
export default {
  data() {
    return {
      // 表格查询条件
      tableFrom: {
        realName: '',
        phone: '',
      },
      // 权限添加表单
      form: {
        selfOnly: "0",
        roleDataValues: undefined
      },
      selectedRowKeys: [],
      // 选中对象
      selectObj: {},
      // 表单权限验证
      rules: {
        roleDataTypeId: [{ required: true, message: '数据权限不能为空', trigger: 'blur' }],
        selfOnly: [{ required: true, message: '数据权限分组名称不能为空', trigger: 'blur' }],
      },
      comlun: [
        // { field: "nickName", label: "用户昵称", fixed: true, isEllipsis: true },
        { field: "realName", label: "用户姓名", fixed: true, isEllipsis: true},
        { field: "userName", label: "用户账号", },
        { field: "phone", label: "手机号", width: 130, },
        // { field: "userType", label: "用户类型", width: 120, filter: item => item ? '--' : item },
        { field: "status", label: "是否生效", filter: item => item == '0' ? '生效' : '失效', width: 120 },
      ],
      // 异步加载
      confirmLoading: false,
      // 可以添加的数据权限列表
      dataTree: [],
      // 添加数据节点
      sourceData: [],
      // 当前用户选中节点
      checkedKeys: [],
    };
  },
  inject: ["api"],
  props: {
    roleId: String, // 父级菜单编号 -- 不可为空
  },
  mixins: [modalMixed],
  methods: {

    // 表单重置
    reset() {
      this.tableFrom.realName = null;
      this.tableFrom.phone = null;
      this.selectedRowKeys = [];
      this.$refs.table.getTableData();
    },
    query() {
      this.$refs.table.getTableData();
    },
    // 添加用户
    addUser() {
      this.api.addRoleUser({ roleId: this.roleId, userIds: this.selectedRowKeys.join(",") }).then(({ code, msg }) => {
        if (code != 0) return this.$message.error(`用户添加失败：${msg}`);
        this.$message.success('用户添加成功！');
        // 关闭弹窗
        this.isvisible = false;
        this.selectedRowKeys = [];
        // 通知操作成功
        this.$emit("success");
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.ant-select-dropdown-menu {
}
</style>