<template>
  <div class="menu-right-limits">
    <div class="searchFrom">
      <a-form-model layout="inline" :model="tableFrom" :labelCol="{span:6}" :wrapperCol="{span:18}">
        <a-form-model-item label="关键字">
          <a-input v-model="tableFrom.searchValue" placeholder="请输入需要搜索的关键字" allowClear />
        </a-form-model-item>
        <a-form-model-item label="生效状态">
          <a-select v-model="tableFrom.status">
            <a-select-option value=""> 全选 </a-select-option>
            <a-select-option value="0"> 生效 </a-select-option>
            <a-select-option value="1"> 失效 </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" @click="success"> 查询 </a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
    <!-- 添加 -->
    <dataForm :visible.sync="addShow" :roleGroupId="roleGroupId" @success="success" dataType="add" />
    <!-- 修改 -->
    <dataForm :visible.sync="setShow" :roleGroupId="roleGroupId" @success="success" dataType="set" :roleId="roleId" />
    <!-- 数据权限弹窗 -->
    <dataAccess :visible.sync="accessShow" :roleId="roleId" />
    <!-- 用户管理 -->
    <user :visible.sync="userShow" :roleId="roleId" />
    <div>
      <tk-table ref="table" :intercept-response="intercept_response" :tableWidth="1300" tableId="roleId" :tableData.sync="comlun" getMethods="work-manage-server/role/page" :isSelected="true" :isPaging="true" :tableFrom="tableFrom" :selectedRowKeys.sync="selectedRowKeys">
        <div class="table-buttun-area" slot="tableHeader">
          <a-button :disabled="!roleGroupId" type="primary" icon="plus" @click="addShow = true"> 新增 </a-button>
          <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove"> 删除 </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a-button type="link" @click="modifyData(data)"> 修改 </a-button>
          <a-button type="link" @click="dataAccessModule(data)"> 数据权限 </a-button>
          <a-button type="link" @click="userModule(data)"> 用户管理 </a-button>
        </template>
      </tk-table>
    </div>
  </div>
</template>

<script>
import dataForm from './form'; // 角色信息操作
import dataAccess from './dataAccess'; // 数据权限操作
import user from './user'; // 数据权限操作
// 此处为对应的装饰器辅助类
import { reqDataField } from '@u/parameter';
export default {
  data() {
    return {
      // 表格展示字段
      comlun: [
        { field: "roleName", label: "角色名称", fixed: true },
        { field: "roleKey", label: "角色标识" },
        { field: "status", label: "是否生效", filter: item => item == '0' ? '生效' : '失效' },
        { field: "createBy", label: "创建者" },
        { field: "createTime", label: "创建时间", filter: item => !item ? '--' : this.DateFormat(item).format("yyyy-MM-dd") },
        { field: "operation", label: "操作", align: "center", fixed: "right", width: 280 },
      ],
      // 查询接口所需字段
      tableFrom: {
        roleGroupId: "",
        status: ""
      },
      // 是否显示添加弹窗
      addShow: false,
      // 是否显示修改弹窗
      setShow: false,
      // 是否显示数据权限弹窗
      accessShow: false,
      // 用户管理是否显示
      userShow: false,
      roleId: "", // 所要修改的权限
      selectedRowKeys: [], // 当前用户选中参数
      form: {
      }, // 保存当前用户选择的检索项
    };
  },
  inject: ["api"],
  components: { dataForm, dataAccess, user },
  props: {
    // 数据权限分组编号
    roleGroupId: {
      type: String,
      default: undefined
    },
  },
  created() {
    // 初始给与值
    this.tableFrom["roleGroupId"] = this.roleGroupId;
  },
  watch: {
    // 用户传入菜单编号
    roleGroupId: {
      handler(val) {
        // 具有菜单编号时查询对应的权限列表
        this.tableFrom["roleGroupId"] = val;
        this.$refs.table.getTableData()
        // 初始话选中
        this.selectedRowKeys = [];
      },
      deep: true
    },
  },
  methods: {
    // 用户点击修改按钮
    modifyData(data) {
      data = reqDataField(data, true);
      this.roleId = data.roleId;
      this.setShow = true;
    },
    // 用户点击数据权限
    dataAccessModule(data) {
      this.roleId = data.roleId;
      this.accessShow = true;
    },
    // 用户点击用户管理
    userModule(data) {
      this.roleId = data.roleId;
      this.userShow = true;
    },
    // 操作成功
    success() {
      this.selectedRowKeys = [];
      this.$refs.table.getTableData()
    },
    // 删除对应的权限
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: '删除数据权限分类',
          content: () => <p>确定删除当前勾选的数据权限分类？</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            this.api.removeRole({ roleIds: this.selectedRowKeys.join(",") }).then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`删除角色失败：${msg}`);
              this.$message.success('删除角色成功！');
              this.$refs.table.getTableData();
              this.selectedRowKeys = [];
            })
          },
        });
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.menu-right-limits {
  width: 100%;
  height: 100%;
  .searchFrom {
    margin: 10px 0px;
  }
  /deep/.ant-table-title {
    padding: 0px;
    padding-bottom: 10px;
    .ant-btn {
      margin-right: 15px;
    }
  }
  /deep/.ant-table-body {
  }
}
</style>