<template>
  <a-modal title="添加角色分组" v-model="isvisible" ok-text="确认" cancel-text="取消" @ok="submit" @cancel="getModuleReset" :confirm-loading="confirmLoading" :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :rules="rules">
      <a-form-model-item label="角色分组标记" prop="roleGroupKey">
        <a-input v-model="form.roleGroupKey" placeholder="请输入角色分组标记" />
      </a-form-model-item>
      <a-form-model-item label="角色分组名称" prop="roleGroupName">
        <a-input v-model="form.roleGroupName" placeholder="请输入角色分组名称" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import modalMixed from '@u/modalMixed';
// 此处为对应的装饰器辅助类
import { reqDataField } from '@u/parameter';
export default {
  data() {
    return {
      form: {}, // 权限添加表单
      // 表单权限验证
      rules: {
        roleGroupKey: [{ required: true, message: '角色分组标记不能为空', trigger: 'blur' }],
        roleGroupName: [{ required: true, message: '角色分组名称不能为空', trigger: 'blur' }],
      },
      // 异步加载
      confirmLoading: false
    };
  },
  inject: ["api"],
  props: {
    menuName: String, // 父级菜单名称 -- 不可为空
    menuId: String, // 父级菜单编号 -- 不可为空
  },
  mixins: [modalMixed],
  methods: {
    // 重置对应的表单
    getModuleReset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {
        roleGroupKey: undefined,
        roleGroupName: undefined,
      }
    },
    // 用户点击修改进入
    getModuleSet(data) {
      // 获取修改属性前先进行表单重置
      this.getModuleReset();
      data = reqDataField(data, true);
      this.form = data;
    },
    // 提交数据权限分组创建
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        let func = ({ code, msg }) => {
          if (code != 0) return this.$message.error(`角色分组${this.typeTitle}失败：${msg}`);
          this.$message.success(`角色分组${this.typeTitle}成功！`);
          // 关闭弹窗
          this.isvisible = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.getModuleReset();
          this.confirmLoading = false;
        }
        this.dataType == "add" ? this.api.addRoleGroup(param).then(func) : this.api.setRoleGroup(param).then(func);
      })
    },
  },
};
</script>
