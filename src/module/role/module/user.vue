<template>
  <a-modal title="角色用户管理" v-model="isvisible" :footer="null" :width="950" :maskClosable="false">
    <div class="access-table">
      <tk-table ref="table" :intercept-response="intercept_response" :tableData.sync="comlun" getMethods="work-manage-server/role/userList" :tableFrom="tableFrom" :isSelected="true" :isPaging="true" :selectedRowKeys.sync="selectedRowKeys" tableId="userId">
        <div class="table-buttun-area" slot="tableHeader">
          <a-button :disabled="!roleId" type="primary" icon="plus" @click="addShow = true"> 新增 </a-button>
          <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove"> 删除 </a-button>
        </div>
      </tk-table>
      <userAdd :visible.sync="addShow" @success="success" :roleId="roleId" />
    </div>
  </a-modal>
</template>
<script>
import modalMixed from '@u/modalMixed';
import userAdd from './user/add';
// 此处为对应的装饰器辅助类
import { reqDataField } from '@u/parameter';
export default {
  data() {
    return {
      confirmLoading: false,
      selectedRowKeys: [], // 当前用户选中参数
      // 表格展示字段
      comlun: [
        // { field: "nickName", label: "用户昵称", fixed: true, isEllipsis: true },
        { field: "realName", label: "用户姓名", },
        { field: "userName", label: "用户账号", },
        { field: "phone", label: "用户手机号", },
        { field: "email", label: "用户邮箱", },
        // { field: "userType", label: "用户类型", filter: item => item ? '--' : item },
        { field: "status", label: "是否生效", filter: item => item == '0' ? '生效' : '失效' },
        // { field: "roles", label: "角色列表", filter: item => item == '0' ? '树形' : item == "1" ? '列表' : '字符串' },
      ],
      // 表格查询条件
      tableFrom: {
        roleId: "",
      },
      // 所要修改的对象
      roleData: {},
      // 添加弹窗是否显示
      addShow: false,
      // 修改弹窗是否显示
      setShow: false,
    }
  },
  props: {
    // 对应的操作表单
    roleId: {
      type: String,
      default: ""
    },
  },
  watch: {
    roleId: {
      handler(val) {
        this.tableFrom.roleId = val;
        this.success();
      },
      deep: true,
      immediate: true
    }
  },
  inject: ["api"],
  components: { userAdd },
  computed: {
  },
  mixins: [modalMixed],
  mounted() {
  },
  methods: {
    // 用户点击修改按钮
    modifyData(data) {
      data = reqDataField(data, true);
      this.roleData = data;
      this.setShow = true;
    },
    reset() { },
    success() {
      this.$refs.table && this.$refs.table.getTableData()
    },
    submit() { },
    // 删除对应的权限
    remove() {
      if (this.selectedRowKeys.length > 0) {
        this.$confirm({
          title: '删除用户',
          content: () => <p>确定删除当前勾选的用户？</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            this.api.removeRoleUser({ userIds: this.selectedRowKeys.join(","), roleId: this.roleId }).then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`删除用户失败：${msg}`);
              this.$message.success('删除用户成功！');
              this.$refs.table.getTableData();
              this.selectedRowKeys = [];
            })
          },
        });
      }
    }
  },
};
</script>
<style lang="scss" scoped>
/deep/.ant-modal-body {
  overflow: hidden;
  overflow-y: auto;
}
.access-table {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>