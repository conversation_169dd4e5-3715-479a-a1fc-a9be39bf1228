<template>
  <a-modal title="数据权限分类" v-model="isvisible" ok-text="确认" cancel-text="取消" @ok="submit" @cancel="reset" :confirm-loading="confirmLoading" :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <a-form-model-item label="角色标识" prop="roleKey">
        <a-input v-model="form.roleKey" placeholder="请输入角色标识" />
      </a-form-model-item>
      <a-form-model-item label="角色名称" prop="roleName">
        <a-input v-model="form.roleName" placeholder="请输入角色名称" />
      </a-form-model-item>
      <a-form-model-item label="角色排序" prop="roleSort">
        <a-input v-model="form.roleSort" placeholder="请输入角色排序" />
      </a-form-model-item>
      <a-form-model-item label="角色状态" prop="status">
        <a-switch v-model="form.status" checked-children="显示" un-checked-children="隐藏" />
      </a-form-model-item>
      <a-form-model-item label="菜单权限" prop="menuIds">
        <div class="menuTree">
          <tk-tree :treeData="treeData" :checkStrictly="true" :replaceFields="replaceFields" :isSelected="true" :isIcon="false" :checkedKeys.sync="selectedKeysUmn" />
        </div>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import modalMixed from '@u/modalMixed';
import menuApi from '@m/menu/api';
export default {
  data() {
    return {
      // 权限添加表单 - 默认
      form: {
        roleKey: undefined,
        roleName: undefined,
        roleSort: 1,
        menuIds: undefined,
        status: true,
      },
      // 表单权限验证
      rules: {
        roleKey: [{ required: true, message: '角色标识不能为空', trigger: 'blur' }],
        roleName: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],
      },
      // 异步加载
      confirmLoading: false,
      // 菜单栏转化操作
      replaceFields: {
        children: 'children', title: 'menuName', key: 'menuId'
      },
      // 树状数据
      treeData: [],
      // 选中项
      selectedKeysUmn: [],
    };
  },
  props: {
    roleGroupId: [String, Number], // 数据权限类型组编号
    // 对应的操作表单
    roleId: {
      type: String,
      default: ""
    },
  },
  watch: {
    roleId: {
      handler(val) {
        if (val) {
          this.getRoleDetail();
        }
      },
      deep: true,
      immediate: true,
    },
    isvisible() {
      this.getRoleDetail();
    }
  },
  inject: ["api"],
  computed: {
    suspension: {
      get() {
        return this.form.datasource && this.form.datasource.length > 0 ? true : false;
      },
      set(val) {
        this.form.datasource = val;
      }
    },
  },
  mixins: [modalMixed],
  mounted() {
    this.reset();
    this.getRoleDetail();
  },
  methods: {
    // 查询角色详情信息
    getRoleDetail() {
      if (!this.roleId) return;
      this.api.getRoleDetail({ roleId: this.roleId }).then(({ code, data }) => {
        if (code != 0) return;
        this.form = data;
        if (data.menus && data.menus.length > 0) {
          this.selectedKeysUmn = data.menus.map(item => item.menuId)
        }
        this.$forceUpdate();
      })
    },
    // 查询当前菜单栏树状列表
    getMenuTree() {
      menuApi.getMenuTree().then(({ code, data }) => {
        if (code != 0) return;
        this.treeData = data;
      })
    },
    // 重置对应的表单
    reset() {
      this.getMenuTree();
      this.form = {
        roleKey: undefined,
        roleName: undefined,
        roleSort: 1,
        status: true,
        menuIds: undefined
      }
      this.selectedKeysUmn = [];
      this.$refs.form && this.$refs.form.resetFields();
    },
    // 提交权限创建
    submit() {
      this.confirmLoading = true;
      this.$refs.form.validate(valid => {
        if (!valid) return this.confirmLoading = false;
        let param = JSON.parse(JSON.stringify(this.form));
        if (this.selectedKeysUmn.checked.length > 0) param["menuIds"] = this.selectedKeysUmn.checked.join(",")
        // 新增权限和新增菜单公用同一接口但是入参不同
        this.dataType == "add" ? param["roleGroupId"] = this.roleGroupId : '';
        let func = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`权限${this.dataType == "add" ? "添加" : "修改"}失败：${msg}`);
          this.$message.success(`权限${this.dataType == "add" ? "添加" : "修改"}成功！`);
          // 关闭弹窗
          this.isvisible = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
          this.confirmLoading = false;
        }
        this.dataType == "add" ? this.api.addRole(param).then(func) : this.api.setRole(param).then(func);
      })
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/.ant-modal-body {
  overflow: hidden;
  overflow-y: auto;
}
.menuTree {
  overflow: hidden;
  height: 300px;
  overflow-y: auto;
}
</style>