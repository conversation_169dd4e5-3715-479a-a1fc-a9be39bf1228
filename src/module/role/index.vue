<template>
  <a-layout class="menu-content">
    <a-layout-sider>
      <a-card title="角色分组管理" :bordered="false">
        <div class="menu-left-tree">
          <tkTightTree @select="select" @rightClick="rightClick">
            <tk-tree :treeData="treeData" :replaceFields="replaceFields" :isIcon="false" :selectedKeys.sync="selectedKeys"></tk-tree>
            <a-menu-item key="1" @click="treeOperShow = true,dataType='add'"> 添加角色分组分组 </a-menu-item>
            <a-menu-item key="2" @click="treeOperShow = true,dataType='set'" v-show="Object.keys(rightMenu).length > 0"> 修改角色分组分组 </a-menu-item>
            <a-menu-item key="3" @click="riskMenu('remove')" v-show="Object.keys(rightMenu).length > 0"> 删除当前角色分组 </a-menu-item>
            <a-menu-item key="4" @click="getTreeData"> 刷新数据权限分组 </a-menu-item>
          </tkTightTree>
          <treeOper :visible.sync="treeOperShow" @success="getTreeData" :parameterData="rightMenu" :dataType="dataType" />
        </div>
      </a-card>

    </a-layout-sider>
    <a-layout-content class="menu-right-content">
      <a-card title="角色列表" :bordered="false" class="mt-left10">
        <dataTable :roleGroupId="roleGroupId" />
      </a-card>
    </a-layout-content>
  </a-layout>
</template>

<script>
import treeOper from './module/treeOper';
import api from './api';
import dataTable from './module/table'; // 引入右侧表格

export default {
  data() {
    return {
      roleGroupId: undefined, // 用户选中项菜单
      treeData: [], // 树状列表填充数据
      rightMenu: {}, // 右击对象
      // 树状列表调整对应的key
      replaceFields: {
        children: 'children', title: 'roleGroupName', key: 'roleGroupKey'
      },
      selectedKeys: [],
      treeOperShow: false, // 数据权限分组添加弹窗是否显示
      dataType: "add"
    };
  },
  created() {
    this.getTreeData();
  },
  components: { dataTable, treeOper },
  provide: { "api": api, },
  methods: {
    // 查询当前菜单栏树状列表
    getTreeData() {
      api.getRoleGroupList().then(({ code, data }) => {
        if (code != 0) return;
        this.treeData = data;
      })
    },
    // 点击右击菜单
    riskMenu(type) {
      if (type == "remove") {
        let { roleGroupName, roleGroupId } = this.rightMenu;
        this.$confirm({
          title: '删除菜单',
          content: () => <p>确定删除指定的“{roleGroupName}”角色分组？</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            api.removeRoleGroup({ roleGroupId }).then(({ code, msg }) => {
              if (code != 0) return this.$message.error(`删除角色分组失败：${msg}`);
              this.$message.success('删除角色分组成功！');
              this.getTreeData();
            })
          },
        });
      }
    },
    rightClick(data) {
      this.rightMenu = data;
    },
    select({ roleGroupId }) {
      // 赋值
      this.roleGroupId = roleGroupId;
    },
    // 表单操作成功
    success() {
      // 重新查询左侧菜单栏
      this.getTreeData();
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-left-tree {
  background: #ffffff;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  .thinkive-tree {
    height: 100%;
  }
  .thinkive-right-tree {
    height: 100%;
  }
}
</style>