// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {

  /**
   * 查询角色分组列表
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["searchValue"])
  getRoleGroupList() {
    return this.services.initGet({ reqUrl: "roleGroup/list", param: this.param });
  }

  /**
   * 添加角色分组
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleGroupKey", "roleGroupName"])
  addRoleGroup() {
    return this.services.initPost({ reqUrl: "roleGroup/add", param: this.param });
  }

  /**
   * 修改角色分组
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleGroupKey", "roleGroupName", "roleGroupId"])
  setRoleGroup() {
    return this.services.initPost({ reqUrl: "roleGroup/edit", param: this.param });
  }

  /**
   * 删除角色分组
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleGroupId"])
  removeRoleGroup() {
    return this.services.initPost({ reqUrl: "roleGroup/delete", param: this.param });
  }

  /**
   * 添加角色
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["menuCheckStrictly", "menuIds", "roleGroupId", "roleKey", "roleName", "roleSort", "status"])
  addRole() {
    return this.services.initPost({ reqUrl: "role/add", param: this.param });
  }

  /**
   * 查询对应的角色信息
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleId"])
  getRoleDetail() {
    return this.services.initGet({ reqUrl: "role/query", param: this.param });
  }

  /**
   * 修改角色
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["menuCheckStrictly", "menuIds", "roleGroupId", "roleKey", "roleName", "roleSort", "status", "roleId"])
  setRole() {
    return this.services.initPost({ reqUrl: "role/edit", param: this.param });
  }

  /**
   * 删除角色
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleIds"])
  removeRole() {
    return this.services.initPost({ reqUrl: "role/deletes", param: this.param });
  }

  /**
   * 查询角色可添加的数据权限
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters()
  getRoleTypeList() {
    return this.services.initGet({ reqUrl: "roleDataType/list", param: this.param });
  }

  /**
   * 向指定角色添加数据权限
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleDataTypeId", "roleDataValues", "roleId", "selfOnly"])
  addRoleData() {
    return this.services.initPost({ reqUrl: "role/addRoleData", param: this.param });
  }

  /**
   * 向指定角色刪除数据权限
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["roleDataTypeIds", "roleId"])
  removeRoleData() {
    return this.services.initPost({ reqUrl: "role/deleteRoleDatas", param: this.param });
  }

  /**
   * 向指定角色添加对应的用户列表
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["userIds", "roleId"])
  addRoleUser() {
    return this.services.initPost({ reqUrl: "role/addUser", param: this.param });
  }

  /**
   * 向指定角色删除对应的用户列表
   * @description: 
   * @param {*}
   * @return {*}
   */
  @Parameters(["userIds", "roleId"])
  removeRoleUser() {
    return this.services.initPost({ reqUrl: "role/delUser", param: this.param });
  }
}

export default new api();