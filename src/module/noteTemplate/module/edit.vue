<template>
	<a-modal
		:title="`修改业务初始化配置`"
		:width="600"
		v-model="showPop"
		ok-text="确认"
		cancel-text="取消"
		@ok="submit"
		@cancel="reset"
		:maskClosable="false"
	>
		<a-form-model
			ref="form"
			:model="form"
			:label-col="{ span: 8 }"
			:wrapper-col="{ span: 14 }"
		>
			<a-form-model-item label="模板名称" prop="modelCnName">
				<a-input
					placeholder="请输入模板名称"
					v-model="form.modelCnName"
				></a-input>
			</a-form-model-item>
			<a-form-model-item label="模板编号" prop="modelNo">
				<a-input
					placeholder="请输入模板编号"
					v-model="form.modelNo"
				></a-input>
			</a-form-model-item>
			<a-form-model-item label="模板内容" prop="modelContent">
				<a-textarea
					v-model="form.modelContent"
					placeholder="请输入模板内容"
					:auto-size="{ minRows: 3, maxRows: 5 }"
				/>
			</a-form-model-item>
			<a-form-model-item label="备注" prop="modelRemark">
				<a-input
					placeholder="请输入备注"
					v-model="form.modelRemark"
				></a-input>
			</a-form-model-item>
			<a-form-model-item label="状态" prop="validFlag">
				<a-radio-group
					v-model="form.validFlag"
					:options="validFlagList"
				/>
			</a-form-model-item>
		</a-form-model>
	</a-modal>
</template>

<script>
	const validFlagList = [
		{ label: "有效", value: "1" },
		{ label: "无效", value: "0" }
	];
	// 默认表单属性
	const defaultForm = {
		modelCnName: "",
		modelNo: "",
		modelContent: "",
		modelRemark: "",
		validFlag: "1"
	};

	export default {
		inject: ["api"],
		data() {
			return {
				validFlagList,
				form: Object.assign({}, defaultForm), //表单数据,
				confirmLoading: false,
				dictMapArray: {}
			};
		},
		props: {
			isPopShow: {
				type: Boolean,
				default: false
			},
			// 是否展示添加弹窗
			visible: {
				type: Boolean,
				default: false
			},
			bizType: {
				type: String,
				default: ""
			},
			// 修改时传入参数
			parameterData: {
				type: Object,
				default: () => {}
			}
		},
		computed: {
			showPop: {
				get() {
					return this.isPopShow;
				},
				set(val) {
					this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
				}
			},
			typeTitle() {
				return "添加";
			}
		},
		watch: {
			isPopShow(n) {
				if (n) {
					this.query();
				} else {
					this.reset();
				}
				this.$nextTick(() => {});
			}
		},
		methods: {
			// 重置对应的表单
			reset() {
				// 重置表单验证属性
				this.$refs.form && this.$refs.form.resetFields();
				this.form = Object.assign({}, defaultForm);
				this.confirmLoading = false;
			},
			query() {
				this.form = {
					modelCnName: this.parameterData.modelCnName,
					modelNo: this.parameterData.modelNo,
					modelContent: this.parameterData.modelContent,
					modelRemark: this.parameterData.modelRemark,
					validFlag: this.parameterData.validFlag
				};
			},

			submit() {
				this.$refs.form.validate(valid => {
					if (!valid) return;
					this.confirmLoading = true;
					let callback = ({ code, msg }) => {
						this.confirmLoading = false;
						if (code != 0)
							return this.$message.error(
								`修改短信模板失败：${msg}`
							);
						this.$message.success(`修改短信模板成功！`);
						// 关闭弹窗
						this.showPop = false;
						// 通知操作成功
						this.$emit("success");
						// 重置表单
						this.reset();
					};
					this.api
						.getBcSmsModelEdit({ ...this.form, bizType: this.bizType, id:this.parameterData.id })
						.then(callback);
				});
			}
		}
	};
</script>

<style lang="scss" scoped></style>
