<template>
	<div class="menu-right-limits">
		<div class="searchForm">
			<a-form-model layout="inline" :model="tableForm">
				<tkSelectForm @query="query" @reset="reset">
					<a-form-model-item label="模板名称">
						<a-input placeholder="请输入模板名称" v-model="tableForm.modelCnName"></a-input>
					</a-form-model-item>
					<a-form-model-item label="模板编号">
						<a-input placeholder="请输入模板编号" v-model="tableForm.modelCnNo"></a-input>
					</a-form-model-item>
					<a-form-model-item label="状态">
						<a-select
							show-search
							optionFilterProp="children"
							:allowClear="true"
                            v-model="tableForm.validFlag"
							placeholder="请选择模板状态"
						>
							<a-select-option value="1">
								有效
							</a-select-option>
							<a-select-option value="0">
								无效
							</a-select-option>
						</a-select>
					</a-form-model-item>
				</tkSelectForm>
			</a-form-model>
		</div>
		<div class="access-table">
			<tk-table
				ref="table"
				:intercept-response="intercept_response"
				:tableData.sync="columns"
				:tableFromFilter="tableFormFilter"
				getMethods="bc-manage-server/bcSmsModel/page"
				:isSelected="true"
				:isPaging="true"
				:tableFrom="tableForm"
				:selectedRowKeys.sync="selectedRowKeys"
				tableId="id"
			>
				<div class="table-button-area" slot="tableHeader">
					<a-button :disabled="!regId" icon="plus" type="primary" @click="add">
						添加
					</a-button>
					<a-button
						icon="delete"
						type="danger"
						:disabled="selectedRowKeys.length <= 0"
						@click="remove"
					>
						删除
					</a-button>
				</div>
				<template slot="operation" slot-scope="data">
					<a-button type="link" @click.stop="modify(data)">
						修改
					</a-button>
					<!-- <a-button type="link" @click.stop="detail(data)">
						查看
					</a-button> -->
				</template>
			</tk-table>
		</div>

		<add :isPopShow.sync="isAddPopShow" :bizType="regId" @success="query" />
		<edit
			:isPopShow.sync="isEditPopShow"
			:bizType="regId"
			@success="query"
			:parameterData="selectData"
		/>
	</div>
</template>

<script>
	// 引入添加和编辑弹窗
	import add from "./add";
	import edit from "./edit";

	export default {
		data() {
			return {
				columns: [
					// 循环
					{
						field: "id",
						label: "序号",
						isSorter: true,
						width: 80
					},
					{
						field: "modelCnName",
						label: "模板名称",
						width: 140
					},
					{
						field: "modelNo",
						label: "模板编号",
						width: 220
					},
					{
						field: "modelLabel",
						label: "标签",
						width: 120
					},
					{
						field: "validFlag",
						label: "状态",
						filter: item => (item === "1" ? "有效" : "无效"),
						width: 220
					},
					{
						field: "operation",
						label: "操作",
						align: "center",
						width: 180,
						fixed: "right"
					}
				],
				tableForm: {
                    modelCnName:'',
                    modelCnNo:'',
					validFlag: undefined
				},
				selectData: {},
				selectedRowKeys: [], // Check here to configure the default column
				isAddPopShow: false, //添加弹窗是否显示
				isEditPopShow: false //修改弹窗是否显示
			};
		},
		inject: ["api"],
		components: { add, edit },
		props: {
			regId: {
				type: String
			}
		},
		watch: {
			regId: {
				handler(val) {
					this.tableForm["bizType"] = val;
					this.$refs.table.getTableData();
					this.selectedRowKeys = [];
				},
				deep: true
			}
		},
		methods: {
			// 搜索框参数
			tableFormFilter(param) {
				return param;
			},

			query() {
				this.$refs.table.getTableData();
				this.selectedRowKeys = [];
			},

			reset() {
				// 没有选中对应的菜单时进行初始化重置
				// this.tableForm = JSON.parse(JSON.stringify(defaultForm));
			},

			// 点击新增按钮
			add() {
				this.isAddPopShow = true;
				this.selectData = {};
			},

			modify(data) {
				this.isEditPopShow = true;
				this.selectData = data;
			},

			detail(data) {
				console.log(data);
			},

			remove() {
				if (this.selectedRowKeys.length > 0) {
					this.$confirm({
						title: "短信模板",
						content: () => <p>确定删除当前短信模板数据?</p>,
						okText: "确定",
						cancelText: "取消",
						onOk: () => {
							this.api
								.getBcSmsModelDeletes({
									bcSmsModelIds: this.selectedRowKeys.join(",")
								})
								.then(({ code, msg }) => {
									if (code != 0)
										return this.$message.error(
											`删除短信模板失败：${msg}`
										);
									this.$message.success("删除短信模板成功！");
									this.$refs.table.getTableData();
									this.selectedRowKeys = [];
								});
						}
					});
				}
			}
		}
	};
</script>

<style lang="sass" scoped>

</style>