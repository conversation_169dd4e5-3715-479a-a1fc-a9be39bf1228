// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
    /**
	 * 获取业务类型名称list
	 * @param {Object} param
	 */
    @Parameters()
    getBcBizTypeList() {
        return this.services.initGet({
            reqUrl:'bcBusiness/list',
            param: this.param
        });
    }

	/**
	 * 新增短信末班
	 * @param {Object} param
	 */
	@Parameters([
        'bizType',
		'modelCnName',
		'modelContent',
		'modelRemark',
		'validFlag',
		'modelNo',
	])
	getBcSmsModelAdd() {
		return this.services.initPost({
			reqUrl: 'bcSmsModel/add',
			param: this.param,
		});
    }
    
    /**
	 * 删除短信末班
	 * @param {Object} param
	 */
	@Parameters([
		'bcSmsModelIds',
	])
	getBcSmsModelDeletes() {
		return this.services.initPost({
			reqUrl: 'bcSmsModel/deletes',
			param: this.param,
		});
    }
    
    /**
	 * 修改短信末班
	 * @param {Object} param
	 */
	@Parameters([
        'id',
        'bizType',
		'modelCnName',
		'modelContent',
		'modelRemark',
		'validFlag',
		'modelNo',
	])
	getBcSmsModelEdit() {
		return this.services.initPost({
			reqUrl: 'bcSmsModel/edit',
			param: this.param,
		});
	}
}

export default new api();
