<template>
  <a-card title="受理单列表" class="management" :bordered="false">
    <div class="searchFrom">
      <a-form-model layout="inline" :model="tableFrom">
        <tkSelectForm @query="success" @reset="reset">
          <a-form-model-item label="客户号">
            <a-input
              v-model="tableFrom.customerId"
              placeholder="请输入客户号"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item label="客户姓名">
            <a-input
              v-model="tableFrom.customerName"
              placeholder="请输入客户姓名"
              allowClear
            />
          </a-form-model-item>
          <!-- <a-form-model-item label="流程编号">
            <a-input v-model="tableFrom.flowNo" placeholder="请输入流程编号" allowClear />
          </a-form-model-item> -->
          <a-form-model-item label="渠道名称">
            <a-input
              v-model="tableFrom.channelName"
              placeholder="请输入渠道名称"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item label="操作来源">
            <a-select
              v-model="tableFrom.opSource"
              placeholder="请选择操作来源"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                :value="v.value"
                v-for="(v, i) in dictMap['bc.common.opSource']"
                :key="i"
              >
                {{ v.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="业务状态">
            <a-select
              v-model="tableFrom.status"
              placeholder="请选择业务状态"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                :value="v.value"
                v-for="(v, i) in dictMap['bc.common.processStatus']"
                :key="i"
              >
                {{ v.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="创建时间">
            <a-range-picker v-model="tableFrom.rangeDate" @change="onChange" />
          </a-form-model-item>
          <a-form-model-item label="业务类型" v-if="isShowQueryBizType">
            <a-select
              v-model="tableFrom.bizType"
              placeholder="请选择业务类型"
              show-search
              option-filter-prop="children"
              allowClear
            >
              <a-select-option
                v-for="item in businessList"
                :key="item.id"
                :value="item.bizType"
              >
                {{ item.bizNickName }}({{ item.bizType }})
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :tableData.sync="comlun"
        getMethods="bc-manage-server/flowIns/page"
        :intercept-response="intercept_response"
        :isPaging="true"
        :tableFromFilter="tableFromFilter"
        :tableFrom="tableFrom"
        tableId="id"
      >
        <template slot="bizType" slot-scope="data">
          {{ changeBizType(data) }}
        </template>
        <template slot="operation" slot-scope="data">
          <a class="action-btn" @click="showDetail(data)"> 查看详情 </a>
          <a
            class="action-btn"
            :disabled="data.status === '2'"
            @click="remove(data)"
          >
            作废
          </a>
          <a
            class="action-btn"
            v-if="data.bizType === '010174' && data.taskType"
            @click="download(data)"
          >
            下载协议
          </a>
        </template>
      </tk-table>
      <Agree :isPopShow.sync="isPopShow" :parameterData="selectData" />
      <collect-remark
        :isPopShow.sync="isCollectRemarkShow"
        @CollectRemark="callbackConfirm"
      />
    </div>
  </a-card>
</template>

<script>
import Agree from "./module/showAgree";
import CollectRemark from "./module/CollectRemark";
import api from "./api";
import { getUrlCodeParam } from "@/utils/parameter.js";
export default {
  name: "acceptanceOrgForm",
  data() {
    return {
      comlun: [
        { field: "id", label: "受理单号", width: 120 },
        { field: "customerId", label: "客户号", width: 150 },
        // { field: 'fundAccount', label: '资金号', width: 150 },
        { field: "customerName", label: "客户姓名", width: 120 },
        { field: "mobileNo", label: "手机号码", width: 150 },
        { field: "bizType", label: "业务类型", width: 150 },
        {
          field: "branchName",
          label: "所属营业部",
          width: 150,
        },
        { field: "curNode", label: "前端步骤", width: 150 },
        {
          field: "status",
          label: "流程状态",
          width: 120,
          filter: (item) => this.getDictText("bc.common.processStatus", item),
        },
        {
          field: "opSource",
          label: "操作来源",
          width: 120,
          filter: () => "Android",
        },
        {
          field: "channelName",
          label: "渠道",
          width: 120,
          filter: () => "Pad",
        },
        { field: "createTime", label: "创建时间", width: 180, isSorter: true },
        { field: "updateTime", label: "修改时间", width: 180, isSorter: true },
        // { field: 'acceptCompTime', label: '最近提交时间', width: 180 },
        // {
        //   field: 'businessCode',
        //   label: '业务名称',
        //   filter: item => this.getDictText('businessList', item),
        //   width: '150px'
        // },
        // {
        //   field: 'formStatus',
        //   label: '流程状态',
        //   width: '150px',
        //   filter: item => this.getDictText('formStatusList', item)
        // },
        // {
        //   field: 'opSource',
        //   label: '操作来源',
        //   width: '120px',
        //   filter: item => this.getDictText('opSourceList', item)
        // },
        // {
        //   field: 'createDate',
        //   label: '提交时间',
        //   isSorter: true,
        //   width: '180px'
        // },
        // {
        //   field: 'handleStatus',
        //   label: '办理结果',
        //   filter: item => this.getDictText('handleStatusList', item),
        //   width: '120px'
        // },

        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
          width: 220,
        },
      ],
      tableFrom: {},
      merchantId: "",
      param: {},
      dictMap: {
        "bc.common.processStatus": [],
        "bc.common.opSource": [],
      },
      businessList: [],
      isShowQueryBizType: true,
      isPopShow: false, //弹窗是否显示
      selectData: {},
      isCollectRemarkShow: false,
    };
  },
  provide: { api: api },
  components: { Agree, CollectRemark },
  created() {
    if (getUrlCodeParam("bizType")) {
      this.isShowQueryBizType = false;
      this.tableFrom.bizType = getUrlCodeParam("bizType");
    } else {
      this.queryBusinessList();
    }
    Promise.all([this.queryDictMap()]).finally();
  },
  methods: {
    download(data) {
      // this.selectData = data
      // this.isPopShow = true
      window.open(
        `/bc-manage-server/flowIns/downLoadAgreement?bizType=${data.bizType}&flowInsId=${data.id}&formId=${data.formId}`,
        "_blank"
      );
    },
    queryBusinessList() {
      api.queryBcClientPortalList({ status: "1" }).then((res) => {
        this.businessList = res.data;
      });
    },
    onChange(date, dateString) {
      this.tableFrom.beginTime = dateString[0];
      this.tableFrom.endTime = dateString[1];
    },
    tableFormFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableFrom.beginTime)) >
        Date.parse(this.DateFormat(this.tableFrom.endTime))
      ) {
        param["beginTime"] = "";
        param["endTime"] = "";
      }
      delete param.rangeDate;
      return param;
    },
    queryDictMap() {
      return new Promise((resolve) => {
        Object.keys(this.dictMap).forEach((item) => {
          this.$dict.dictContent(item).then((data) => {
            this.dictMap[item] = (data || []).map((data) => {
              return { label: data.dictLabel, value: data.dictValue };
            });
            resolve();
          });
        });
      });
    },
    getDictText(key, value) {
      let results = this.dictMap[key] || [];
      results = results.filter((item) => {
        return item.value == value;
      });
      return (results && results.length && results[0].label) || value || "";
    },
    showDetail(data) {
      const opSourceObj = this.dictMap["bc.common.opSource"].find(
        (item) => item.value == data.opSource
      );
      const bizObj = this.businessList.find(
        (item) => item.bizType === data.bizType
      );
      data.bizName = bizObj ? bizObj.bizNickName : data.bizName;
      if (opSourceObj) {
        data.opSourceName = opSourceObj.label;
      }
      let href = `/bc-manage-view/detailOrgInfo?flowInsId=${data.id}`;
      window.open(href, "_blank");
    },
    success() {
      if (
        Date.parse(this.DateFormat(this.tableFrom.startTime)) >
        Date.parse(this.DateFormat(this.tableFrom.endTime))
      ) {
        this.tableFrom.endTime = "";
        return this.$message.error("结束日期不应早于开始日期");
      }
      this.$refs.table.getTableData();
    },
    // 重置
    reset() {
      this.tableFrom = {};
      if (!this.isShowQueryBizType) {
        this.tableFrom.bizType = getUrlCodeParam("bizType");
      }
    },
    openChange(status) {
      if (status == false) {
        // 切出事件，用于组件缓冲数据
        setTimeout(() => {
          if (this.tableFrom.startTime && this.tableFrom.endTime) {
            if (
              Date.parse(this.DateFormat(this.tableFrom.startTime)) >
              Date.parse(this.DateFormat(this.tableFrom.endTime))
            ) {
              this.tableFrom.endTime = "";
              return this.$message.error("结束日期不应早于开始日期");
            }
          }
        });
      }
    },
    tableFromFilter(param) {
      if (
        Date.parse(this.DateFormat(this.tableFrom.beginTime)) >
        Date.parse(this.DateFormat(this.tableFrom.endTime))
      ) {
        param["beginTime"] = "";
        param["endTime"] = "";
      }
      delete param.rangeDate;
      return param;
    },
    // 作废对应的受理单
    remove(data) {
      // this.$confirm({
      //   title: '受理单作废',
      //   content: () => <p>确定作废当前受理单？</p>,
      //   okText: '确定',
      //   cancelText: '取消',
      //   onOk: () => {
      //     this.isCollectRemarkShow = true
      //     this.selectData = data
      //   }
      // });
      this.isCollectRemarkShow = true;
      this.selectData = data;
    },
    callbackConfirm(param) {
      this.abandonAccept({ ...this.selectData, reason: param });
    },
    abandonAccept(param) {
      api.invalidForm({ ids: param.id }).then(({ code, msg }) => {
        api
          .taskCancel({
            formId: param.formId,
            bizType: param.bizType,
            reason: param.reason,
          })
          .then(() => {
            if (code != 0) return this.$message.error(`受理单作废失败：${msg}`);
            this.$message.success("受理单作废成功！");
            this.$refs.table.getTableData(true);
            this.selectData = {};
          });
      });
    },
    changeBizType(data) {
      const obj = this.businessList.find(
        (item) => item.bizType === data.bizType
      );
      return obj ? obj.bizNickName : data.bizName;
    },
  },
};
</script>

<style lang="scss" scoped>
.action-btn {
  margin-right: 10px;
}
</style>
