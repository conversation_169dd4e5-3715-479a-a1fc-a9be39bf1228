<template>
  <a-modal title="受理单流水" :width="1000" v-model="showPop" :footer="null">
    <a-layout class="menu-content" style="background: #fff">
      <a-layout-content class="menu-right-content" style="margin-left: 24px">
        <a-card
          title="受理过程"
          :bordered="false"
          v-if="flowStepLogVOS&&flowStepLogVOS.length !== 0"
        >
          <a-table
            :columns="flowStepLogVOSColumns"
            :dataSource.sync="flowStepLogVOS"
            :pagination="pagination"
            :loading="loading"
          >
          </a-table>
        </a-card>
        <a-card
          title="复核过程"
          :bordered="false"
          v-if="formCheckVOS&&formCheckVOS.length !== 0"
        >
          <a-table
            :columns="formCheckVOSColumns"
            :dataSource.sync="formCheckVOS"
            :pagination="pagination"
            :loading="loading"
          >
            <template slot="taskData"> 复核 </template>
            <template slot="taskStatus" slot-scope="text, data">
              {{ getDictText('checkStatusList',data.taskStatus) }}
            </template>
          </a-table>
        </a-card>
        <a-card
          title="办理过程"
          :bordered="false"
          v-if="formTaskVOS&&formTaskVOS.length !== 0"
        >
          <a-table
            :columns="formTaskVOSColumns"
            :dataSource.sync="formTaskVOS"
            :pagination="pagination"
            :loading="loading"
          >
            <template slot="checkName"> 跑批系统 </template>
            <template slot="taskStatus" slot-scope="text, data">
              {{ getDictText('taskStatusList',data.taskStatus) }}
            </template>
          </a-table>
        </a-card>
      </a-layout-content>
    </a-layout>
  </a-modal>
</template>
<script>
import api from "../api";
export default {
  name: "AcceptFlow",
  data() {
    return {
      flowStepLogVOS: [],
      formCheckVOS: [],
      formTaskVOS: [],
      dictMap: {
        checkStatusList: [],
        taskStatusList: []
      },
      flowStepLogVOSColumns: [
        {
          title: "时间",
          dataIndex: "createTime",
        },
        {
          title: "节点",
          dataIndex: "stepName",
        },
        {
          title: "操作人",
          dataIndex: "customerName",
        },
        {
          title: "客户号",
          dataIndex: "customerId",
        },
      ],
      formCheckVOSColumns: [
        {
          title: "时间",
          dataIndex: "checkDate",
        },
        {
          title: "节点",
          dataIndex: "taskData",
          scopedSlots: { customRender: "taskData" },
        },
        {
          title: "操作人",
          dataIndex: "checkName",
        },
        {
          title: "操作结果",
          dataIndex: "taskStatus",
          scopedSlots: { customRender: "taskStatus" },
        },
      ],
      formTaskVOSColumns: [
        {
          title: "时间",
          dataIndex: "createTime",
        },
        {
          title: "节点",
          dataIndex: "taskName",
        },
        {
          title: "操作人",
          dataIndex: "checkName",
          scopedSlots: { customRender: "checkName" },
        },
        {
          title: "操作结果",
          dataIndex: "taskStatus",
          scopedSlots: { customRender: "taskStatus" },
        },
      ],
      pagination: false,
      loading: true,
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  },
  created() {
    this.dictMap.checkStatusList = this.$dict.dictTypeList("bc.common.checkStatus");
    this.dictMap.taskStatusList = this.$dict.dictTypeList("bc.common.taskStatus");
    this.queryFlowStepLop();
  },
  methods: {
    getDictText(key,value) {
      let results = this.dictMap[key]||[];
      results = results.filter((item) => {
        return item.dictValue == value;
      });
      return (results && results.length && results[0].dictLabel) || value || "";
    },
    queryFlowStepLop() {
      api
        .queryFlowStepLop({
          bizType: this.data.bizType,
          // bizType: "017001",
          flowInsId: this.data.id,
          // flowInsId: "1340",
          formId: this.data.formId,
          // formId: "141",
        })
        .then((res) => {
          this.flowStepLogVOS = res.data.flowStepLogVOS;
          this.formCheckVOS = res.data.formCheckVOS;
          this.formTaskVOS = res.data.formTaskVOS;
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
