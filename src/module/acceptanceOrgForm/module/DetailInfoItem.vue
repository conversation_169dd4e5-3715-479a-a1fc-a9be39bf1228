<template>
  <div>
    <a-row
      :style="{ marginTop: 15 + 'px' }"
      v-if="sourceArray.length > 0 && !noneDataFlag"
    >
      <div class="content-border">
        <div class="list_header">
          <a-row>
            <a-col :span="6" class="pop-title">{{ areaTitle }}</a-col>
          </a-row>
        </div>
        <!-- 引流信息 -->
        <a-row
          v-if="type == 'channel' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in filterSourceArray"
              :key="index"
            >
              {{ item.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>
        <!-- 居间人信息 -->
        <a-row
          a-row
          v-if="type == 'intermediay' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in filterSourceArray"
              :key="index"
            >
              {{ item.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>
        <!-- 图像信息 -->
        <a-row v-if="type == 'image' && sourceArray.length > 0">
          <a-descriptions bordered>
            <a-descriptions-item label="身份图像信息">
              <div class="content-body">
                <template v-for="(item, index) in filterImgSourceArray">
                  <div
                    v-if="item.value.length"
                    class="content-item"
                    :class="{ 'box-flex': item.type == 'arr' }"
                    :key="index"
                  >
                    <h3 style="margin-bottom: 10px;">{{ item.key }}</h3>
                    <viewer
                      :class="[
                        item.key.indexOf('身份证') > -1
                          ? 'idcardImg'
                          : 'actorImg',
                        item.type == 'arr' ? 'flex-content' : '',
                      ]"
                    >
                      <div v-if="item.type != 'arr'">
                        <img
                          width="100%"
                          style="cursor:pointer;"
                          :src="item.value"
                          :key="item.key"
                          alt="未上传"
                        />
                      </div>
                      <template v-else-if="item.type == 'arr'">
                        <div
                          v-for="(itm, i) in item.value"
                          :key="i"
                          class="img_arr"
                        >
                          <div>{{ itm.title }}</div>
                          <img
                            width="100%"
                            style="cursor:pointer;"
                            :src="itm.src"
                            alt="未上传"
                          />
                        </div>
                      </template>
                    </viewer>
                  </div>
                </template>
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 机构信息 -->
        <a-row v-if="type == 'ocr' && sourceArray.length > 0">
          <a-descriptions bordered>
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in sourceArray"
              :key="index"
            >
              {{ item.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 经办人信息 -->
        <a-row v-if="type == 'contact' && sourceArray.length > 0">
          <a-descriptions bordered>
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in sourceArray"
              :key="index"
            >
              {{ item.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 上传协议信息 -->
        <a-row v-if="type == 'agreeImage' && sourceArray.length > 0">
          <a-descriptions bordered>
            <a-descriptions-item label="上传协议信息">
              <div class="content-body fx-col">
                <div
                  class="content-item"
                  style="width: 100%"
                  v-for="(item, index) in sourceArray[0].value"
                  :key="index"
                >
                  <h3 style="margin-bottom: 10px;">{{ item.title }}</h3>
                  <div class="img_arr">
                    <viewer>
                      <div class="fx-row fx-wrap fx-m-center">
                        <img
                          v-for="(path, j) in item.src"
                          :key="j"
                          width="30%"
                          style="cursor:pointer; margin: 0 15px;"
                          :src="path"
                          alt="未上传"
                        />
                      </div>
                    </viewer>
                  </div>
                </div>
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 视频影像 -->
        <a-row v-if="type == 'video' && sourceArray.length > 0">
          <a-row v-if="sourceArray[0].value">
            <a-descriptions bordered :column="3">
              <a-descriptions-item
                :label="item.key"
                v-for="(item, index) in sourceArray.slice(1, 6)"
                :key="index"
              >
                {{ item.value }}
              </a-descriptions-item>
            </a-descriptions>
          </a-row>
          <a-list
            bordered
            :split="false"
            class="ant_list"
            :data-source="sourceArray.slice(0, 1)"
          >
            <a-list-item slot="renderItem" slot-scope="item">
              <a-row>
                <a-col>
                  <a-card :bordered="false" class="self">
                    <video width="600" height="400" controls="controls">
                      <source :src="item.value" type="video/mp4" />
                    </video>
                  </a-card>
                </a-col>
              </a-row>
              <!-- <a-row v-if="sourceArray[0].value">
                <a-col>
                  <div
                    style="background:#fafafa;width:600px;height: 400px;box-sizing: border-box;"
                  >
                    <div class="wordsTitle">聊天记录</div>
                    <div class="wordsContent">
                      <div
                        v-for="(v, i) in JSON.parse(sourceArray[6].value)"
                        :key="i"
                      >
                        <p>坐席：{{ v.content }}</p>
                        <p>答案提示：{{ v.prompt }}</p>
                      </div>
                    </div>
                  </div>
                </a-col>
              </a-row> -->
            </a-list-item>
          </a-list>
        </a-row>

        <!-- 双录 -->
        <a-row
          v-if="
            (type == 'audioVideo' ||
              type == 'doubleVideo' ||
              type == 'audioVideoSz' ||
              type == 'audioVideoSh') &&
              sourceArray.length > 0
          "
        >
          <a-row v-if="sourceArray[0].value">
            <a-descriptions bordered :column="3">
              <a-descriptions-item
                :label="item.key"
                v-for="(item, index) in sourceArray.slice(1)"
                :key="index"
              >
                {{ item.value }}
              </a-descriptions-item>
            </a-descriptions>
          </a-row>
          <a-list
            bordered
            :split="false"
            class="ant_list"
            :data-source="sourceArray.slice(0, 1)"
          >
            <a-list-item slot="renderItem" slot-scope="item">
              <a-row>
                <a-col>
                  <a-card :bordered="false" class="self">
                    <video width="600" height="400" controls="controls">
                      <source :src="item.value" type="video/mp4" />
                    </video>
                  </a-card>
                </a-col>
              </a-row>
            </a-list-item>
          </a-list>
        </a-row>

        <!-- 账户信息 -->
        <a-row
          v-if="type == 'account' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <template v-for="(item, index) in sourceArray">
              <a-descriptions-item
                v-if="item.value.length"
                :label="item.key"
                :key="index"
              >
                <div v-if="index == 0">
                  <div v-for="(data, j) in sourceArray[0].value" :key="j">
                    {{ data.marketName }}：{{ data.account }}
                  </div>
                </div>

                <div v-else>{{ item.value }}</div>
              </a-descriptions-item>
            </template>
          </a-descriptions>
        </a-row>

        <!-- 业务准入 -->
        <a-row
          v-if="type == 'access' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in sourceArray"
              :key="index"
            >
              <a-button
                type="link"
                @click="
                  (accessPop = true), (accessArray = sourceArray[0].value)
                "
                >查看报告</a-button
              >
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 适当性信息 -->
        <a-row
          v-if="type == 'suit' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in filterSourceArray"
              :key="index"
            >
              <span>{{ item.value }}</span>
              <a-button
                v-if="item.value1"
                type="link"
                @click="
                  (questionPop = true),
                    (questionObj = {
                      paperAnswer: item.value,
                      paperNo: item.value1,
                    })
                "
                >查看问卷</a-button
              >
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 银行信息 -->
        <a-row v-if="type == 'bank' && sourceArray.length > 0" class="avg-two">
          <a-descriptions
            bordered
            :column="2"
            v-for="(item, index) in sourceArray"
            :key="index"
          >
            <a-descriptions-item
              :label="itm.key"
              v-for="(itm, ind) in item"
              :key="ind"
            >
              <viewer v-if="itm.key === '银行卡照片'">
                <img width="20%" style="cursor:pointer;" :src="itm.value" />
              </viewer>
              <span v-else>{{ itm.value }}</span>
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 客户扩展信息 -->
        <a-row v-if="type == 'ext' && sourceArray.length > 0" class="avg-three">
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in filterSourceArray"
              :key="index"
            >
              {{ item.value }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 协议 -->
        <a-row
          v-if="type == 'agree' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in sourceArray"
              :key="index"
            >
              <a-button
                type="link"
                v-for="(itm, ind) in item.value"
                :key="ind"
                @click="(agreePop = true), (agreeObj = itm)"
                >{{ "《" + itm.agreementName + "》" }}</a-button
              >
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 知识测评 -->
        <a-row
          v-if="type == 'know' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in sourceArray"
              :key="index"
            >
              <span v-if="index === 0">{{ item.value }}</span>
              <a-button
                v-else
                type="link"
                @click="
                  (questionPop = true),
                    (questionObj = {
                      paperAnswer: sourceArray[1].value,
                      paperNo: sourceArray[1].value1,
                    })
                "
                >查看问卷</a-button
              >
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 征信测评 -->
        <a-row
          v-if="type == 'credit' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in sourceArray"
              :key="index"
            >
              <span v-if="index === 0">{{ item.value }}</span>
              <a-button
                v-else
                type="link"
                @click="
                  (questionPop = true),
                    (questionObj = {
                      paperAnswer: sourceArray[1].value,
                      paperNo: sourceArray[1].value1,
                    })
                "
                >查看问卷</a-button
              >
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 回访问卷 -->
        <a-row
          v-if="type == 'visit' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in sourceArray"
              :key="index"
            >
              <span v-if="index === 0">{{ item.value }}</span>
              <a-button
                v-else
                type="link"
                @click="
                  (questionPop = true),
                    (questionObj = {
                      paperAnswer: sourceArray[1].value,
                      paperNo: sourceArray[1].value1,
                    })
                "
                >查看问卷</a-button
              >
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 特殊信息申报 -->
        <a-row
          v-if="type == 'specail' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in sourceArray"
              :key="index"
            >
              {{ item.value }}
              <a-button
                v-if="item.value1"
                type="link"
                @click="(specailPop = true), (specailArray = item.value1)"
                >查看信息</a-button
              >
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 纸质协议 -->
        <a-row
          v-if="type == 'paperAgree' && sourceArray.length > 0"
          class="avg-two"
        >
          <a-descriptions bordered :column="2">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in filterImgSourceArray"
              :key="index"
            >
              <viewer>
                <img width="30%" style="cursor:pointer;" :src="item.value" />
              </viewer>
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 打印协议 -->
        <a-row
          v-if="type == 'agreePrint' && sourceArray.length > 0"
          class="avg-two"
        >
          <a-descriptions bordered :column="2">
            <a-descriptions-item :label="sourceArray[0].key">
              <a-button
                type="link"
                v-for="(item, index) in sourceArray[0].value"
                :key="index"
                @click="showAgree(item)"
              >
                {{ "《" + item.agreementName + "》" }}
              </a-button>
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <!-- 拟接受风险等级信息 -->
        <a-row
          v-if="type == 'corpRisk' && sourceArray.length > 0"
          class="avg-three"
        >
          <a-descriptions bordered :column="3">
            <a-descriptions-item
              :label="item.key"
              v-for="(item, index) in filterSourceArray"
              :key="index"
            >
              <span v-if="!item.value1"> {{ item.value }}</span>
              <a-button
                type="link"
                v-else
                @click="(accountPop = true), (accountArray = item.value1)"
                >查看数据</a-button
              >
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <a-row v-if="type == 'other' && sourceArray.length > 0">
          <a-list
            bordered
            class="ant_list"
            :data-source="filterOriginSourceArray"
          >
            <a-list-item
              class="ant_list_item"
              slot="renderItem"
              slot-scope="item"
            >
              <a-col :span="6">{{ item.filedDesc }}</a-col>
              <a-col :span="18">{{
                item.filedValue ? item.filedValue : ""
              }}</a-col>
            </a-list-item>
          </a-list>
        </a-row>
      </div>
    </a-row>
    <access-pop
      v-if="accessPop"
      :isPopShow.sync="accessPop"
      :data="accessArray"
    />
    <agree-pop v-if="agreePop" :isPopShow.sync="agreePop" :data="agreeObj" />
    <question-pop
      v-if="questionPop"
      :isPopShow.sync="questionPop"
      :data="questionObj"
    />
    <specail-pop
      v-if="specailPop"
      :isPopShow.sync="specailPop"
      :data="specailArray"
    />
    <account-pop
      v-if="accountPop"
      :isPopShow.sync="accountPop"
      :data="accountArray"
    />
  </div>
</template>

<script>
import api from "../api";
export default {
  provide: [api],
  data() {
    return {
      detailInfo: sessionStorage.getItem("detailInfo")
        ? JSON.parse(sessionStorage.getItem("detailInfo"))
        : {},
      accessPop: false,
      accessArray: [],
      agreePop: false,
      agreeObj: {},
      questionPop: false,
      questionObj: {},
      specailPop: false,
      specailArray: [],
      accountPop: false,
      accountArray: [],
    };
  },
  props: {
    /* 父组件传递给子组件的，实际上只是一个引用地址，当子组件修改这个对象时，是真的修改了在堆空间中保存的数值，当然父组件中的值也会发生变化，但是引用地址没有进行修改，所以并没有报错。 */
    type: {
      type: String,
      default: "",
    },
    sourceArray: {
      type: Array,
      default: () => [],
    },
    areaTitle: {
      type: String,
      default: "",
    },
  },
  components: {
    AccessPop: () =>
      import(/* webpackChunkName: "AccessPop" */ "./AccessPop.vue"),
    AgreePop: () => import(/* webpackChunkName: "AgreePop" */ "./AgreePop.vue"),
    QuestionPop: () =>
      import(/* webpackChunkName: "QuestionPop" */ "./QuestionPop.vue"),
    SpecailPop: () =>
      import(/* webpackChunkName: "SpecailPop" */ "./SpecailPop.vue"),
    AccountPop: () =>
      import(/* webpackChunkName: "AccountPop" */ "./AccountPop.vue"),
  },
  mounted() {
    if (this.type == "ocr") {
      console.log(this.sourceArray);
    }
  },
  methods: {
    showAgree(data) {
      api
        .queryAgreement({
          bizType: this.detailInfo.bizType,
          formId: this.detailInfo.formId,
          flowNo: this.detailInfo.flowNo,
          clientId: this.detailInfo.customerId,
          ...data,
        })
        .then((res) => {
          if (res.code === 0) {
            this.agreePop = true;
            this.agreeObj = res.data[0];
          }
        });
    },
  },
  computed: {
    noneDataFlag() {
      return this.sourceArray.every((item) => !item.value && !item.filedValue);
    },
    filterImgSourceArray() {
      return this.sourceArray.filter((item) => item.value);
    },
    filterSourceArray() {
      return this.sourceArray.filter((item) => {
        if (item.value === "") {
          item.value = "--";
        }
        return (
          item.value !== undefined && item.value !== null && item.value !== ""
        );
      });
    },

    filterOriginSourceArray() {
      return this.sourceArray.filter((item) => {
        return item.filedValue;
      });
    },
  },
};
</script>

<style lang="scss" scoped="scoped">
::v-deep .avg-two .ant-descriptions-item-label {
  width: 10%;
}
::v-deep .avg-two .ant-descriptions-item-content {
  width: 40%;
}
::v-deep .avg-three .ant-descriptions-item-label {
  width: 10%;
}
::v-deep .avg-three .ant-descriptions-item-content {
  width: 23.3%;
}
::v-deep .ocr .ant-descriptions-view {
  border: none;
}
::v-deep .ocr .ant-descriptions-item-label,
::v-deep .xh-reson .ant-descriptions-item-label {
  width: 10%;
}
::v-deep .ocr .ant-descriptions-item-content {
  width: 40%;
}
::v-deep .handle-result .ant-descriptions-item-label {
  width: 5%;
}
// ::v-deep .handle-result .ant-descriptions-item-content {
//   width: 8%;
// }
// ::v-deep .handle-result .ant-descriptions-item-content:first-child {
//   width: 10%;
// }
::v-deep .handle-result .ant-descriptions-item-content:last-child {
  width: 50%;
}

.ant-spin-container {
  background: #fff !important;
  padding: 10px 15px !important;
}

.content-border {
  border: 1px solid #e9e9e9;
  border-radius: 2px;
  background: #fff;
}

.list_header {
  padding: 10px 20px;
  background: #f9f9f9;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #e9e9e9;
}
.pop-title {
  border-left: 5px solid #1890ff;
  padding-left: 10px;
}

.content-body {
  background: #fff;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.content-item {
  text-align: center;
  padding: 10px 0px;
  background: #fafafa;
  flex-grow: 1;
  margin-right: 30px;
  width: 30%;
  margin-bottom: 15px;
}

.content-item:last-child {
  margin-right: 0px;
}

.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #fff;
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  float: right;
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  /* display: inline-block; */
  width: 125px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.wordsTitle {
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 2px solid #dfe1e6;
  background-color: #eeeff2;
  border-radius: 3px;
}
.wordsContent {
  padding: 15px 20px;
  height: 350px;
  overflow: auto;
}
.wordsContent p {
  font-weight: bold;
  font-size: 16px;
  line-height: 30px;
}
.idcardImg {
  overflow: hidden;
  text-align: center;
}
.idcardImg img {
  width: 335px;
  height: 200px;
}
.actorImg {
  overflow: hidden;
}
.actorImg img {
  width: 135px;
  height: 200px;
}
.ant-alert {
  margin: 0 10px;
}
.flex-content {
  display: flex;
  flex-wrap: wrap;
  .img_arr {
    width: 30%;
    img {
      width: 200px;
    }
  }
}
.box-flex {
  width: 100% !important;
}
.fx-row {
  display: flex;
  flex-direction: row;
}
.fx-col {
  display: flex;
  flex-direction: column;
}
.fx-wrap {
  flex-wrap: wrap;
}
.fx-m-center {
  justify-content: center;
}
</style>
