<template>
  <a-modal
    title="准入报告"
    :width="500"
    v-model="showPop"
    :footer="null"
  >
    <a-list item-layout="horizontal" :data-source="JSON.parse(data)">
    <a-list-item slot="renderItem" slot-scope="item">
      <a-list-item-meta
        :description="JSON.parse(item.ruleELResults[0].message).tips"
      >
        <a slot="title">{{ JSON.parse(item.ruleELResults[0].message).title }}</a>
        <a-avatar
          slot="avatar"
          :icon="item.ruleELResults[0].passed ? 'check-circle' : 'close-circle'"
          :style="{backgroundColor:item.ruleELResults[0].passed?'#2f54eb':'#eb615d'}"
        />
      </a-list-item-meta>
    </a-list-item>
  </a-list>
  </a-modal>
</template>
<script>

export default {
  name: "AccessPop",
  data() {
    return {
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    data: {
        type: String,
        default:''
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    }
  }
};
</script>

<style lang="scss" scoped>
</style>