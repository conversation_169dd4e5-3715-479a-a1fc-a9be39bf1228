<template>
  <div>
    <a-layout>
      <a-layout-header style="background: #fff;">
        <a-row class="pg-header pg-header-top">
          <a-col :span="8"
            ><a style="font-size: 18px;">
              <a-icon type="solution" /> &nbsp;{{ detailInfo.bizName }}({{
                detailInfo.bizType
              }})
            </a></a-col
          >
        </a-row>
      </a-layout-header>
      <a-layout-content
        style="background: #f9f9f9;padding: 10px 25px 90px 25px; "
      >
        <!-- 基本信息-->
        <basic-info
          v-if="JSON.stringify(detailInfo) !== '{}'"
          :detailInfo="detailInfo"
          :fundAccount="fundAccount"
        />
        <a-row v-if="showSpin" style="margin: 50px 45%;">
          <a-spin size="large" />
        </a-row>
        <!-- 引流信息 -->
        <detail-info-item
          v-if="channelInfo.length !== 0"
          type="channel"
          areaTitle="引流信息"
          :sourceArray="channelInfo"
        />
        <!-- 居间人信息 -->
        <detail-info-item
          v-if="intermediaryInfo.length !== 0"
          type="intermediay"
          areaTitle="居间人信息"
          :sourceArray="intermediaryInfo"
        />

        <!-- ocr信息 -->
        <detail-info-item
          v-if="ocrInfo.length !== 0"
          type="ocr"
          areaTitle="机构信息"
          :sourceArray="ocrInfo"
        />

        <!-- 经办人信息 -->
        <detail-info-item
          v-if="contactInfo.length !== 0"
          type="contact"
          areaTitle="经办人信息"
          :sourceArray="contactInfo"
        />

        <!-- 图片信息 -->
        <detail-info-item
          v-if="imageInfo.length !== 0"
          type="image"
          areaTitle="身份图片信息"
          :sourceArray="imageInfo"
        />

        <!-- 上传协议信息 -->
        <detail-info-item
          v-if="agreeImage.length !== 0"
          type="agreeImage"
          areaTitle="上传协议信息"
          :sourceArray="agreeImage"
        />

        <!-- 双录 -->
        <detail-info-item
          v-if="audioVideo.length !== 0"
          type="audioVideo"
          areaTitle="双录信息"
          :sourceArray="audioVideo"
        />

        <!-- 沪市双录 -->
        <detail-info-item
          v-if="audioVideoSh.length !== 0"
          type="audioVideoSh"
          areaTitle="沪市双录信息"
          :sourceArray="audioVideoSh"
        />

        <!-- 深市双录 -->
        <detail-info-item
          v-if="audioVideoSz.length !== 0"
          type="audioVideoSz"
          areaTitle="深市双录信息"
          :sourceArray="audioVideoSz"
        />

        <!-- 视频信息 -->
        <detail-info-item
          v-if="videoInfo.length !== 0"
          type="video"
          areaTitle="视频信息"
          :sourceArray="videoInfo"
        />
        <!-- 现场见证 -->
        <detail-info-item
          v-if="doubleVideo.length !== 0"
          type="doubleVideo"
          areaTitle="见证视频信息"
          :sourceArray="doubleVideo"
        />
        <!-- 账户信息 -->
        <detail-info-item
          v-if="selectAccount.length !== 0"
          type="account"
          areaTitle="账户信息"
          :sourceArray="selectAccount"
        />
        <!-- 业务准入 -->
        <detail-info-item
          v-if="accessInfo.length !== 0"
          type="access"
          areaTitle="业务准入"
          :sourceArray="accessInfo"
        />
        <!-- 适当性信息 -->
        <detail-info-item
          v-if="suitInfo.length !== 0"
          type="suit"
          areaTitle="适当性信息"
          :sourceArray="suitInfo"
        />
        <!-- 银行信息 -->
        <detail-info-item
          v-if="bankInfo.length !== 0"
          type="bank"
          areaTitle="银行信息"
          :sourceArray="bankInfo"
        />
        <!-- 客户扩展信息 -->
        <detail-info-item
          v-if="extInfo.length !== 0"
          type="ext"
          areaTitle="客户扩展信息"
          :sourceArray="extInfo"
        />
        <!-- 协议 -->
        <detail-info-item
          v-if="agreeInfo.length !== 0"
          type="agree"
          areaTitle="协议"
          :sourceArray="agreeInfo"
        />
        <!-- 知识测评 -->
        <detail-info-item
          v-if="knowInfo.length !== 0"
          type="know"
          areaTitle="知识测评"
          :sourceArray="knowInfo"
        />
        <!-- 征信测评 -->
        <detail-info-item
          v-if="creditInfo.length !== 0"
          type="credit"
          areaTitle="征信测评"
          :sourceArray="creditInfo"
        />
        <!-- 回访问卷 -->
        <detail-info-item
          v-if="visitInfo.length !== 0"
          type="visit"
          areaTitle="回访问卷"
          :sourceArray="visitInfo"
        />
        <!-- 特殊信息申报 -->
        <detail-info-item
          v-if="specialInfo.length !== 0"
          type="specail"
          areaTitle="特殊信息申报"
          :sourceArray="specialInfo"
        />
        <!-- 纸质协议 -->
        <detail-info-item
          v-if="paperAgreeInfo.length !== 0"
          type="paperAgree"
          areaTitle="纸质协议"
          :sourceArray="paperAgreeInfo"
        />
        <!-- 打印协议 -->
        <detail-info-item
          v-if="agreePrintInfo.length !== 0"
          type="agreePrint"
          areaTitle="打印协议"
          :sourceArray="agreePrintInfo"
        />
        <!-- 拟接受服务风险等级 -->
        <detail-info-item
          v-if="corpRiskInfo.length !== 0"
          type="corpRisk"
          areaTitle="拟接受服务风险等级"
          :sourceArray="corpRiskInfo"
        />
        <!-- 更多信息 -->
        <detail-info-item
          v-if="otherData.length !== 0"
          type="other"
          areaTitle="更多表单项"
          :sourceArray="otherData"
        />
      </a-layout-content>

      <a-layout-footer class="btn-block" style="width: 100%">
        <a-button
          type="primary"
          @click.stop="closePop"
          style="width: 400px;height: 40px;font-size: 16px;"
          >关闭</a-button
        >
      </a-layout-footer>
    </a-layout>
  </div>
</template>
<script>
import BasicInfo from "./BasicInfo";
import api from "../api";
import DetailInfoItem from "./DetailInfoItem";
export default {
  name: "DetailInfo",
  data() {
    return {
      detailInfo: {},
      fundAccount: "",
      showSpin: true,
      channelInfo: [],
      intermediaryInfo: [],
      ocrInfo: [],
      contactInfo: [],
      imageInfo: [],
      videoInfo: [],
      agreeImage: [],
      doubleVideo: [],
      audioVideo: [],
      audioVideoSz: [],
      audioVideoSh: [],
      selectAccount: [],
      accessInfo: [],
      suitInfo: [],
      bankInfo: [],
      extInfo: [],
      agreeInfo: [],
      knowInfo: [],
      creditInfo: [],
      visitInfo: [],
      specialInfo: [],
      otherData: [],
      paperAgreeInfo: [],
      corpRiskInfo: [],
      agreePrintInfo: [],
    };
  },
  provide: [api],
  components: {
    BasicInfo,
    DetailInfoItem,
  },
  created() {
    // this.queryUserInfo();
    this.queryClientDetail();
  },
  methods: {
    // 查询客户资料详情
    queryClientDetail() {
      api
        .queryFormDetail({
          flowInsId: this.$route.query.flowInsId,
        })
        .then((res) => {
          if (res.code == 0) {
            this.detailInfo = res.data.detailOrgInfo;
            setTimeout(() => {
              this.showSpin = false;
              this.sortData(res.data);
            }, 1500);
          } else {
            this.$message.error(res.msg);
          }
        });
    },
    getValue(prop, data, type) {
      const obj = data.find((item, index) => {
        if (item.filedKey.toLowerCase() === prop) {
          this.otherData.splice(index, 1);
          return item;
        }
      });
      if (!obj || !obj.filedValue) return "";
      if (type === "file") {
        try {
          return (
            window.$hvue.customConfig.fileUrl + JSON.parse(obj.filedValue).src
          );
        } catch (e) {
          return window.$hvue.customConfig.fileUrl + obj.filedValue;
        }
      } else {
        return obj.filedValue;
      }
    },
    async sortData(data) {
      let formData = await Promise.all(
        data.formDetailList.map(async (item) => {
          if (item.isEnum === "1" && item.filedValue) {
            const dictList = await this.$dict.dictTypeList(item.dictKey);
            let dictLabelList = [];
            item.filedValue.split(",").forEach((item) => {
              let dictObj = dictList.find((itm) => itm.dictValue == item);
              dictLabelList.push(dictObj ? dictObj.dictLabel : item);
            });
            item.filedValue = dictLabelList.join("，");
          }
          return item;
        })
      );
      this.handleData(data, formData);
    },
    handleData(data, formData) {
      this.otherData = formData;
      let videoData = data.witnessInfoVO;
      let accessData = data.taskResultJson;
      let agreementData = data.agreementRecordList;
      let {
        agreeimageData = "",
        businessLicenceData = "",
        certificateData = "",
        contactBareheadedPic = "",
        contactData = "",
        instreprData = "",
        instreprPowersData = "",
        scanimageData = "",
        selectAccountsData = "",
      } = data.handleData || {};
      this.fundAccount = this.getValue("fund_account", formData);
      this.getValue("update_time", formData);
      this.getValue("branch_no", formData);
      this.getValue("branch_name", formData);
      this.getValue("client_id", formData);
      this.getValue("full_name", formData);
      this.getValue("op_source", formData);
      this.getValue("nationality", formData);
      this.getValue("client_status", formData);
      this.getValue("epaper_sign_json", formData);
      this.getValue("video_json", formData);
      this.getValue("op_station", formData);
      this.getValue("client_age", formData);
      this.getValue("op_entrust_way", formData);

      const idNo = this.getValue("id_no", formData); // 身份证号 用作年龄的判断
      // let endDate = this.getValue("id_enddate", formData); // 证件结束日期
      // endDate = endDate !== "********" ? endDate : "长期";
      const riskScore = this.getValue("corp_risk_core", formData); // 风测的得分，用作是否查看风测问卷的条件
      const knowScore = this.getValue("knowledge_score", formData); // 知识测评的得分，用作是否查看知识测评的条件
      const paperAns = this.getValue("paper_answer", formData); // 问卷的答案
      const creditScore = this.getValue("credit_score", formData); // 征信测评的得分
      const creditPaperAns = this.getValue("credit_paper_answer", formData); // 征信测评的答案
      const revisitPaperAns = this.getValue("revisit_paper_answer", formData); // 问卷回访的答案
      // const bankJson = this.getValue("choose_bank_json", formData); // 银行JSON数据
      // const accountJson = this.getValue("qry_account_param", formData); // 证券账户JSON数据
      const paperAgreeJson = this.getValue("paper_pic_json", formData); // 纸质协议JSON数据
      const corpRiskJson = this.getValue("en_corp_risk_level_json", formData); // 拟接受服务风险等级JSON数据
      const agreePrintJson = this.getValue("agree_print_json", formData); // 打印协议JSON数据
      const faceToStaffName = this.getValue("face_to_staff_name", formData); // 面见人姓名
      const faceToStaffNo = this.getValue("face_to_staff_no", formData); // 面见人编号
      const faceToStaffWorkNum = this.getValue(
        "face_to_staff_work_num",
        formData
      ); // 面见人执业资格证号
      const contactType = this.getValue("contact_type", formData); // 办理人角色
      this.imageInfo = [
        {
          key: "经办人身份证人像面",
          value: this.handlePic(contactData.contactIdCardPortrait),
        },
        {
          key: "经办人身份证国徽面",
          value: this.handlePic(contactData.contactIdCardNational),
        },
        {
          key: "经办人身份证复印件",
          value: this.handlePic(contactData.contactCopy),
        },
        {
          key: "经办人大头照",
          value: this.handlePic(contactBareheadedPic[0]?.src),
        },
        {
          key: "法人身份证人像面",
          value:
            contactType != "1"
              ? this.handlePic(instreprData.instreprIdCardPortrait)
              : "",
        },
        {
          key: "法人身份证国徽面",
          value:
            contactType != "1"
              ? this.handlePic(instreprData.instreprIdCardNational)
              : "",
        },
        {
          key: "法人身份证复印件",
          value: this.handleArrPic(instreprData.instreprCopy),
          type: "arr",
        },

        {
          key:
            contactType == "1"
              ? "执行事务合伙人身份证明文件"
              : contactType == "0"
              ? "执行事务合伙人/委派代表身份证明文件"
              : "",
          value: this.handlePic(instreprData.partnershipCopy),
        },
        {
          key: "委托代表复印件",
          value: this.handlePic(instreprData.representCopy),
        },
        {
          key: "机构身份证明文件",
          value: this.handlePic(businessLicenceData.businessIdPic),
        },
        {
          key: videoData ? "面见人与经办人合照" : "面见人见证人和经办人合照",
          value: this.handlePic(scanimageData[0]?.src),
        },
        {
          key: "机构身份证明文件",
          value: this.handleArrPic(certificateData),
          type: "arr",
        },
        {
          key: "授权委托书",
          value: this.handleArrPic(instreprPowersData),
          type: "arr",
        },
      ];
      this.agreeImage = agreeimageData.length
        ? [
            {
              key: "协议上传",
              value: this.handleAgreePic(agreeimageData),
            },
          ]
        : [];
      this.ocrInfo = [
        {
          key: "机构全称",
          value: this.getValue("client_name", formData),
        },
        {
          key: "客户类型",
          value: this.getValue("client_type", formData),
        },

        { key: "证件类型", value: this.getValue("id_kind", formData) },
        { key: "证件号码", value: idNo },
        {
          key: "法人类别",
          value: this.getValue("shdc_instrepr_type", formData),
        },
        {
          key: "专业投资者标识",
          value: this.getValue("prof_flag", formData),
        },
      ];
      this.contactInfo = [
        { key: "经办人姓名", value: contactData.contactName },
        { key: "经办人证件号码", value: contactData.contactIdNo },
        {
          key: "经办人类型",
          value:
            contactType == "1" ? "经办人" : contactType == "0" ? "法人" : "",
        },
        {
          key: "法人代表姓名",
          value: this.getValue("instrepr_name", formData),
        },
        {
          key: "法人证件类型",
          value: this.getValue("instrepr_id_kind", formData),
        },
        {
          key: "法人证件号码",
          value: this.getValue("instrepr_id_no", formData),
        },
      ];
      this.videoInfo = videoData
        ? [
            {
              key: "视频源",
              value: this.getValue("witness_video", formData, "file"),
            },
            {
              id: "videoType",
              key: "视频类型",
              value:
                this.getValue("witness_way", formData).indexOf("单向") > -1
                  ? "双人见证"
                  : "单人见证",
            },
            {
              id: "videoLength",
              key: "视频大小(Byte)",
              value: videoData.videoLength ? videoData.videoLength : "",
            },
            {
              id: "receiveTime",
              key: "接入时间",
              value: videoData.receiveTime ? videoData.receiveTime : "",
            },
            {
              id: "totalTime",
              key: "视频时长(秒)",
              value: videoData.totalTime ? videoData.totalTime : "",
            },
            {
              id: "staffName",
              key: "见证人员",
              value: videoData.staffName ? videoData.staffName : "",
            },
            {
              id: "witnessFlowNo",
              key: "注册流水号",
              value: this.getValue("witness_flow_no", formData),
            },
            // {
            //   id: "tips",
            //   key: "聊天记录",
            //   value: videoData.tips ? videoData.tips : "",
            // },
          ]
        : [];
      this.audioVideo = this.determineValue("audio_video_file", formData)
        ? [
            {
              key: "双录",
              value: this.getValue("audio_video_file", formData, "file"),
            },
            {
              key: "双录录制时间",
              value: this.getValue("audio_video_recode_time", formData),
            },
            {
              key: "录制时长（秒）",
              value: this.getValue("audio_video_time", formData),
            },
            {
              key: "是否已完成事前双录审核",
              value: this.getValue("is_audio_video_audited", formData),
            },
            {
              key: "面见人姓名",
              value: faceToStaffName,
            },
            {
              key: "面见人编号",
              value: faceToStaffNo,
            },
            {
              key: "面见人执业资格证号",
              value: faceToStaffWorkNum,
            },
          ]
        : [];

      this.audioVideoSz = this.determineValue("audio_video_file_sz", formData)
        ? [
            {
              key: "深市双录",
              value: this.getValue("audio_video_file_sz", formData, "file"),
            },
            {
              key: "双录录制时间",
              value: this.getValue("audio_video_recode_time_sz", formData),
            },
            {
              key: "录制时长（秒）",
              value: this.getValue("audio_video_time_sz", formData),
            },
            {
              key: "面见人姓名",
              value: faceToStaffName,
            },
            {
              key: "面见人编号",
              value: faceToStaffNo,
            },
            {
              key: "面见人执业资格证号",
              value: faceToStaffWorkNum,
            },
          ]
        : [];
      this.audioVideoSh = this.determineValue("audio_video_file_sh", formData)
        ? [
            {
              key: "沪市双录",
              value: this.getValue("audio_video_file_sh", formData, "file"),
            },
            {
              key: "双录录制时间",
              value: this.getValue("audio_video_recode_time_sh", formData),
            },
            {
              key: "录制时长（秒）",
              value: this.getValue("audio_video_time_sh", formData),
            },
            {
              key: "面见人姓名",
              value: faceToStaffName,
            },
            {
              key: "面见人编号",
              value: faceToStaffNo,
            },
            {
              key: "面见人执业资格证号",
              value: faceToStaffWorkNum,
            },
          ]
        : [];
      this.doubleVideo = !videoData
        ? [
            {
              key: "现场见证",
              value: this.getValue("witness_video", formData, "file"),
            },
            {
              key: "视频开始时间",
              value: this.getValue("video_start_time", formData),
            },
            {
              key: "见证时长（秒）",
              value: this.getValue("witness_time", formData),
            },
            // {
            //   key: "视频文件大小",
            //   value: this.getValue("video_size", formData),
            // },
            {
              key: "见证人姓名",
              value: this.getValue("srv_name", formData),
            },
            {
              key: "见证人编号",
              value: this.getValue("witness_id", formData),
            },
            {
              key: "见证人执业编号",
              value: this.getValue("srv_account", formData),
            },
            {
              key: "见证类型",
              value:
                this.getValue("witness_way", formData).indexOf("单向") > -1
                  ? "双人见证"
                  : "单人见证",
            },
          ]
        : [];
      this.selectAccount = selectAccountsData.length
        ? [
            { key: "选择账户", value: selectAccountsData },
            {
              key: "联系人姓名",
              value: this.getValue("relation_name", formData),
            },
            {
              key: "联系人手机号",
              value: this.getValue("relation_mobile", formData),
            },
            {
              key: "联系人邮箱",
              value: this.getValue("relation_email", formData),
            },
          ]
        : [];

      // this.accountInfo = [
      //   { key: "资金账号", value: this.fundAccount },
      //   { key: "市场", value: this.getValue("exchange_type", formData) },
      //   {
      //     key: "证券账户",
      //     value: accountJson,
      //     value1: accountJson ? this.handleAccount(accountJson) : "",
      //   },
      // ];
      this.accessInfo = accessData
        ? [{ id: "taskResultJson", key: "准入检查报告", value: accessData }]
        : [];
      this.suitInfo = this.determineValue("busi_risk_level", formData)
        ? [
            {
              key: "业务风险等级",
              value: this.getValue("busi_risk_level", formData),
            }, // dict
            {
              key: "适当性匹配标识",
              value:
                this.getValue("elig_risk_flag", formData) == "1"
                  ? "匹配"
                  : "不匹配",
            },
            {
              key: "风险等级",
              value: this.getValue("corp_risk_level", formData),
            },
            riskScore
              ? {
                  key: "风测评分",
                  value: riskScore,
                  value1: riskScore && this.getValue("paper_no", formData),
                }
              : {},
            riskScore
              ? {
                  key: "风险测评到期日期",
                  value: this.getValue("corp_end_date", formData),
                }
              : {},
            {
              key: "拟投资品种",
              value: this.getValue("en_invest_kind", formData),
            },
            {
              key: "拟投资期限",
              value: this.getValue("en_invest_term", formData),
            },
            { key: "收益类型", value: this.getValue("income_type", formData) },
            {
              key: "业务服务种类",
              value: this.getValue("busi_svr_kind", formData),
            },
          ]
        : [];
      // this.bankInfo = bankJson
      //   ? JSON.parse(bankJson).map((item) => {
      //       return [
      //         { key: "银行名称", value: item["bank_name"] },
      //         { key: "银行卡号", value: item["bank_no"] },
      //         { key: "银行网点", value: item["bank_branch"] },
      //         {
      //           key: "银行卡照片",
      //           value:
      //             window.$hvue.customConfig.fileUrl +
      //             JSON.parse(item["bank_card_pic"]).src,
      //         },
      //       ];
      //     })
      //   : [];
      // this.extInfo = [
      //   // { key: "国籍", value: this.getValue("nationality",formData) },
      //   { key: "学历", value: this.getValue("degree_code", formData) },
      //   { key: "职业", value: this.getValue("profession_code", formData) },
      //   { key: "工作单位", value: this.getValue("work_unit", formData) },
      //   { key: "E-mail", value: this.getValue("email", formData) },
      //   { key: "邮编", value: this.getValue("zipcode", formData) },
      //   {
      //     key: "是否本人为控制人",
      //     value: this.getValue("control_person", formData),
      //   },
      //   {
      //     key: "是否本人为受益人",
      //     value: this.getValue("benefit_person", formData),
      //   },
      //   {
      //     key: "是否有不良诚信记录",
      //     value: this.getValue("credit_record", formData),
      //   },
      //   {
      //     key: "税收身份类型",
      //     value: this.getValue("tax_resident_person", formData),
      //   },
      //   { key: "政要关系", value: this.getValue("gov_rel", formData) },
      //   {
      //     key: "反洗钱风险等级",
      //     value: this.getValue("aml_risk_level", formData),
      //   },
      //   { key: "年收入", value: this.getValue("income", formData) },
      // ];
      this.agreeInfo = agreementData
        ? [{ id: "epaper_sign_json", key: "签署协议", value: agreementData }]
        : [];
      this.knowInfo =
        knowScore && paperAns
          ? [
              { key: "测评得分", value: knowScore },
              {
                key: "查看问卷",
                value: paperAns,
                value1: this.getValue("paper_no", formData),
              },
            ]
          : [];
      this.creditInfo =
        creditScore && creditPaperAns
          ? [
              { key: "测评得分", value: creditScore },
              {
                key: "查看问卷",
                value: creditPaperAns,
                value1: this.getValue("credit_paper_no", formData),
              },
            ]
          : [];
      this.visitInfo = revisitPaperAns
        ? [
            { id: "paper_type", key: "回访状态", value: "" },
            {
              key: "查看问卷",
              value: revisitPaperAns,
              value1: this.getValue("revisit_paper_no", formData),
            },
          ]
        : [];
      this.specialInfo = [];
      this.paperAgreeInfo = paperAgreeJson
        ? JSON.parse(paperAgreeJson).map((item) => {
            return {
              key: item.agreeName,
              value: item.picUrl
                ? window.$hvue.customConfig.fileUrl + item.picUrl
                : "",
            };
          })
        : [];
      this.corpRiskInfo = corpRiskJson
        ? [
            {
              key: "拟接受服务风险等级",
              value: corpRiskJson,
              value1: corpRiskJson ? this.handleCorpRisk(corpRiskJson) : "",
            },
          ]
        : [];
      this.agreePrintInfo = agreePrintJson
        ? [
            {
              key: "打印协议预览",
              value: agreePrintJson ? JSON.parse(agreePrintJson) : "",
            },
          ]
        : [];
    },
    //关闭
    closePop() {
      window.opener = null;
      window.open("", "_self");
      window.close();
    },
    handlePic(path) {
      if (!path) {
        // 如果path为null或undefined或空字符串
        return "";
      } else {
        return window.$hvue.customConfig.fileUrl + path;
      }
    },
    handleArrPic(arr) {
      arr =
        (arr || []).map((item) => {
          if (!item || !item.src) {
            // 如果item或item.src为空或undefined或null
            return item;
          } else {
            item.src = window.$hvue.customConfig.fileUrl + item.src; // 在src前添加$http前缀
            return item;
          }
        }) || [];
      return arr;
    },
    handleAgreePic(arr) {
      let newObj = {};
      for (let i = 0; i < arr.length; i++) {
        let srcArr = arr[i].src.split(",");
        for (let j = 0; j < srcArr.length; j++) {
          if (!newObj[arr[i].title]) {
            newObj[arr[i].title] = [
              window.$hvue.customConfig.fileUrl + srcArr[j],
            ];
          } else {
            newObj[arr[i].title].push(
              window.$hvue.customConfig.fileUrl + srcArr[j]
            );
          }
        }
      }

      let newArr = [];
      for (let key in newObj) {
        let obj = {
          src: newObj[key],
          title: key,
        };
        newArr.push(obj);
      }
      return newArr;
    },
    handleCorpRisk(array) {
      return JSON.parse(array).map((item) => {
        return [
          { key: "开通权限名称", value: item["holderRight"] },
          { key: "匹配等级", value: item["level"] },
          { key: "等级匹配结果(匹配/不匹配)", value: item["isMatch"] },
          { key: "投资期限", value: item["term"] },
          { key: "期限匹配结果(匹配/不匹配)", value: item["trMatch"] },
          { key: "投资品种", value: item["variety"] },
          { key: "品种匹配结果(匹配/不匹配)", value: item["vrMatch"] },
        ];
      });
    },
    handleAccount(array) {
      return JSON.parse(array).map((item) => {
        return [
          { key: "客户号", value: item["clientId"] },
          { key: "资金账号", value: item["fundAccount"] },
          { key: "股东账号", value: item["stockAccount"] },
          {
            key: "交易类别",
            value: this.handleAccountDict(
              item["exchangeType"],
              "bc.common.exchangeType"
            ),
          },
          {
            key: "账户类别",
            value: this.handleAccountDict(
              item["holderKind"],
              "bc.common.holderKind"
            ),
          },
          {
            key: "账户状态",
            value: this.handleAccountDict(
              item["holderStatus"],
              "bc.common.accountStatus"
            ),
          },
          {
            key: "指定交易状态",
            value: this.handleAccountDict(
              item["regflag"],
              "bc.common.transStatus"
            ),
          },
          { key: "一码通账号", value: item["acodeAccount"] },
          {
            key: "股东权限",
            value: this.handleAccountDict(
              item["holderRights"],
              "bc.common.holderRights"
            ),
          },
          {
            key: "资产属性",
            value: this.handleAccountDict(
              item["assetProp"],
              "bc.common.assetProp"
            ),
          },
          {
            key: "主帐标志",
            value: this.handleAccountDict(
              item["mainFlag"],
              "bc.common.mainFlag"
            ),
          },
        ];
      });
    },
    handleAccountDict(origin, key) {
      const dictList = this.$dict.dictTypeList(key);
      let dictLabelList = [];
      origin.split(",").forEach((item) => {
        let dictObj = dictList.find((itm) => itm.dictValue === item);
        dictLabelList.push(dictObj ? dictObj.dictLabel : item);
      });
      return dictLabelList.join("，");
    },
    // 判断上下文是否有值
    determineValue(prop, data) {
      const obj = data.find((item) => item.filedKey.toLowerCase() === prop);
      return obj ? !!obj.filedValue : false;
    },
    getAge(a, curDate = "") {
      var birthDay = this.idCardToBirthday(a);
      var birthDate = new Date(birthDay);
      curDate = curDate.replace(
        /^(\d{4})[.\-/](\d{1,2})[.\-/](\d{1,2})$/,
        "$1-$2-$3"
      );
      var nowDateTime = curDate ? new Date(curDate) : new Date();
      var age = nowDateTime.getFullYear() - birthDate.getFullYear();
      // 再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
      if (
        nowDateTime.getMonth() < birthDate.getMonth() ||
        (nowDateTime.getMonth() === birthDate.getMonth() &&
          nowDateTime.getDate() < birthDate.getDate())
      ) {
        age--;
      }
      return age;
    },
    idCardToBirthday(c) {
      if (c) {
        if (c.length === 18) {
          return c.replace(/\d{6}(\d{4})(\d{2})(\d{2})\d{3}[\dXx]/, "$1-$2-$3");
        } else if (c.length === 15) {
          return c.replace(/\d{6}(\d{2})(\d{2})(\d{2})\d{3}/, "19$1-$2-$3");
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-layout {
  background-color: #fff;
}
::v-deep .ant-layout-content {
  background-color: #fff !important;
}
.pop_header {
  background-color: #ffffff;
  color: "#282828";
}

.flow_col {
  word-break: break-all;
  min-height: 45px;
  margin: 20px 0px;
  padding-right: 25px;
}

.flow_col span {
  float: left;
  margin-right: 20px;
}

.flow_row .flow_col:not(:last-child):after {
  content: "";
  min-width: 1px;
  min-height: 45px;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.ant_list > :first-child {
  padding: 0;
}

.ant_list_item > div {
  word-break: break-all;
  padding-right: 25px;
  margin-top: 5px;
  min-height: 27px;
}

.ant_list_item > div:not(:last-child):after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  position: absolute;
  right: 20px;
  top: 0px;
}

.allow_input {
  box-sizing: border-box;
  margin-top: -5px;
  font-variant: tabular-nums;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  display: inline-block;
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.list_header {
  padding: 10px 20px;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: bold;
}

/* add 20210302 */
.pg-header {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
}
.pg-header-title {
  padding-left: 25px;
  font-weight: bold;
  // background-color: #2E3649;
  color: #1890ff;
}

.pop_content .ant-row {
  margin-bottom: 0;
}

.ant-card-body {
  padding: 15px;
}

.ant-card-bordered {
  margin-bottom: 15px;
}

.ant-card-head {
  border-bottom: none;
}

.ant-list-grid .ant-col > .ant-list-item {
  text-align: center;
}

.btn-block {
  background: #f9f9f9;
  text-align: center;
  position: fixed;
  bottom: 0px;
}
</style>
