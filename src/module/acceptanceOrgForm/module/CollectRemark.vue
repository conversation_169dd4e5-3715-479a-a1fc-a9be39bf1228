<template>
    <div>
        <a-modal
              title="输入作废原因"
              :visible="showPop"
              @ok="getValue"
              @cancel="closePop"
            >
          <template slot="footer">
            <a-button key="back" @click="closePop">
              取消
            </a-button>
            <a-button type="primary" key="submit" @click="getValue">
              提交
            </a-button>
          </template>
             <a-layout>
                <a-layout-content class="pop_content" style="min-height: calc(200px - 120px);">
                    <a-row>
                        <a-col :span="6" class="label_style">
                            <label>作废原因</label>
                        </a-col>
                        <a-col :span="18">
                            <a-input 
                                v-model="reason"
                                placeholder="请输入作废原因"
                                type="text"
                            />
                        </a-col>
                    </a-row>
                </a-layout-content>
            </a-layout>
        </a-modal>
    </div>
</template>
<script>
export default {
    name: 'CollectRemark',
    data() {
        return {
            reason:''
        }
    },
    props: {
        isPopShow: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        showPop: {
            get() {
                return this.isPopShow;
            },
            set(val) {
                console.log(val);
                this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
            },
        },
    },
    methods: {
        //关闭
        closePop() {
            this.showPop = false
        },
        //提交
        getValue() {
            if(!this.reason){
                return this.$message.error('请输入作废原因')
            }
            this.$emit('CollectRemark',this.reason)
            this.reason = ''
            this.showPop = false
        }
    }
}
</script>
<style scoped>
</style>