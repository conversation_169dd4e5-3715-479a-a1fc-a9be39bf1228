<template>
  <a-modal
    title="预览协议"
    :width="1000"
    v-model="showPop"
    :footer="null"
  >
    <iframe :src="src" frameborder="0" width="100%" height="800px"></iframe>
  </a-modal>
</template>
<script>
export default {
  name: "showAgree",
  inject: ["api"],
  data() {
    return {
      src:''
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 传入参数
    parameterData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.bizType) {
        this.src = `/bc-manage-server/flowIns/downLoadAgreement?bizType=${this.parameterData.bizType}&flowInsId=${this.parameterData.id}&formId=${this.parameterData.formId}`
      } else {
        this.reset();
      }
    },
  },
  methods: {
    ok() {
      this.showPop = false;
      this.reset()
    },
    // 重置对应的表单
    reset() {
      this.src = ''
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .ant-modal-body{
  height: 1000px;
}
</style>