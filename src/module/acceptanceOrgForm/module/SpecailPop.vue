<template>
    <div>
        <a-modal
              title="特殊申报信息"
              :visible="showPop"
              @cancel="closePop"
            >
          <template slot="footer">
            <a-button key="back" @click="closePop">
              关闭
            </a-button>
          </template>
             <a-layout>
                <a-layout-content class="pop_content" style="min-height: calc(200px - 120px);">
					<div v-for="(item,index) in JSON.parse(data)" :key="index">
                       <div v-for="(info,ind) in item" :key="ind">
                           <span>{{info.label}}</span>：<span>{{info.value}}</span> 
                       </div>
                       <br>
                    </div>
                </a-layout-content>
            </a-layout>
        </a-modal>
    </div>
</template>
<script>

export default {
    name: 'SpecailPop',
    data() {
        return {
        }
    },
    props: {
        isPopShow: {
            type: Boolean,
            default: false
        },
        data: {
            type: String,
            default : ''
        }
    },
    computed: {
        showPop: {
            get() {
                return this.isPopShow;
            },
            set(val) {
                this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
            },
        },
    },
    methods: {
        //关闭
        closePop() {
            this.showPop = false
        },
    }
}
</script>
<style scoped>
</style>
