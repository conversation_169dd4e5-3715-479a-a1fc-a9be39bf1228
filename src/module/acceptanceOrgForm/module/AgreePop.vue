<template>
  <a-modal
    :title="data.agreementName"
    :width="800"
    v-model="showPop"
    :footer="null"
  >
    <div v-html="data.signContent||data.agreementContent"></div>
  </a-modal>
</template>
<script>

export default {
  name: "AccessPop",
  data() {
    return {
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    data: {
        type: Object,
        default:()=>{}
    }
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // 改变的时候通知父组件
      },
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .ant-modal-body{
  font-size: 10px;
}
</style>