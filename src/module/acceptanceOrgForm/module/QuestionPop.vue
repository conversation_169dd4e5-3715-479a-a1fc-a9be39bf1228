<template>
  <div>
    <a-modal
      :title="title"
      v-model="showPop"
     :footer="null"
      :width="800"
    >
      <a-layout>
                <a-layout-content class="pop_content" style="min-height: calc(200px - 120px);">
					<div v-for="item,index in questionList" :key="item.questionId">
                        <h3>{{index+1}}.{{item.questionName}}</h3>
                        <p v-for="ans in item.answerList" :key="ans.answerId">
                            <a-checkbox disabled :checked="ans.userAnswer==='1'" />
                            {{ans.answerName}}
                            <br>
                        </p>
                     </div>
                </a-layout-content>
            </a-layout>
    </a-modal>
  </div>
</template>
<script>
import api from "../api";
export default {
  name: "Question",
  data() {
    return {
      questionList: [],
      title:''
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: ()=>{},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  watch: {
    isPopShow: {
      handler: function(n) {
        if (n) {
          this.$nextTick(() => {
            this.queryQuestionList();
          });
        }
      },
      immediate: true, //刷新加载 立马触发一次handler
      deep: true, // 可以深度检测到 rule 对象的属性值的变化
    },
  },
  methods: {
    queryQuestionList() {
      api
        .getSubjectInfoByUserAnswer({
          ...this.data,
        })
        .then((res) => {
          if(res.code === 0) {
            //   res.data[0].questionList.forEach(v=>{
            //       v.questionName =
            //       v.questionName.includes('client_name') || v.questionName.includes('client_id')
            //           ? v.questionName
            //               .replace('client_name', JSON.parse(sessionStorage.getItem('auditedDetail')).g_client_name)
            //               .replace('client_id', JSON.parse(sessionStorage.getItem('auditedDetail')).g_client_id)
            //           : v.questionName;
            //   })
            this.title = res.data.subjectName
            this.questionList = res.data.questionList
          }else{
              this.$message.error(res.msg);
          }
        });
    }
  }
};
</script>
<style scoped></style>
