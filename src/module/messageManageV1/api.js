// 加载对应的装饰器
import { Parameters } from "@u/decorator";

class api {
  /**
   * 查询短信模板标签表列表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getSmsTemplateLabelList() {
    return this.services.initGet({
      reqUrl: "bcSmsTemplateLabel/tree/list",
      param: this.param,
    });
  }

  /**
   * 查询指定短信模板标签表
   * @param {Object} param
   */
  @Parameters(["_data"])
  getSmsTemplateLabel() {
    return this.services.initPost({
      reqUrl: "bcSmsTemplateLabel/query",
      param: this.param,
    });
  }
  /**
   * 新增短信模板标签表
   * @param {Object} param
   */
  @Parameters(["_data"])
  addSmsTemplateLabel() {
    return this.services.initPost({
      reqUrl: "bcSmsTemplateLabel/add",
      param: this.param,
    });
  }
  /**
   * 修改短信模板标签表
   * @param {Object} param
   */
  @Parameters(["_data"])
  editSmsTemplateLabel() {
    return this.services.initPost({
      reqUrl: "bcSmsTemplateLabel/edit",
      param: this.param,
    });
  }
  /**
   * 删除短信模板标签表
   * @param {Object} param
   */
  @Parameters(["_data"])
  deleteSmsTemplateLabel() {
    return this.services.initPost({
      reqUrl: "bcSmsTemplateLabel/delete",
      param: this.param,
    });
  }
   
  /**
   * 查询指定短信模版表
   * @param {Object} param
   */
   @Parameters(["_data"])
   getSmsTemplate() {
     return this.services.initPost({
       reqUrl: "bcSmsSendTemplate/query",
       param: this.param,
     });
   }
   /**
    * 新增短信模板表
    * @param {Object} param
    */
   @Parameters(["_data"])
   addSmsSendTemplate() {
     return this.services.initPost({
       reqUrl: "bcSmsSendTemplate/add",
       param: this.param,
     });
   }
   /**
    * 修改短信模板表
    * @param {Object} param
    */
   @Parameters(["_data"])
   editSmsSendTemplate() {
     return this.services.initPost({
       reqUrl: "bcSmsSendTemplate/edit",
       param: this.param,
     });
   }
   /**
    * 批量删除短信模板表
    * @param {Object} param
    */
   @Parameters(["_data"])
   deletesSmsSendTemplate() {
     return this.services.initPost({
       reqUrl: "bcSmsSendTemplate/deletes",
       param: this.param,
     });
   }
   /**
    * 改变模板状态
    * @param {Object} param
    */
   @Parameters(["_data"])
   changeStatus() {
     return this.services.initPost({
       reqUrl: "bcSmsSendTemplate/changeStatus",
       param: this.param,
     });
   }
}

export default new api();
