<!--  -->
<template>
  <a-layout class="menu-content">
    <a-layout-sider class="menu-tree-sider">
      <a-card :bordered="false">
        <template slot="title">
          <div class="menu-left-title">短信模版</div>
          <a-input-search class="menu-left-search" @search="searchTag" />
        </template>
        <div class="menu-left-tree">
          <tkTightTree @select="select" @rightClick="rightClick">
            <tk-tree :treeData="treeData" :selectedKeys.sync="selectedKeys" :replaceFields="replaceFields"
              :isIcon="true" />
            <a-menu-item key="0" v-show="Object.keys(rightMenu).length == 0"
              @click="(isPopShow = true), (operationType = 'add'), (type = true)">
              添加模板分类
            </a-menu-item>
            <a-menu-item key="1" v-show="Object.keys(rightMenu).length > 0 && rightMenu.pid === 0"
              @click="(isPopShow = true), (operationType = 'add'), (type = false)">
              添加模板
            </a-menu-item>
            <a-menu-item key="2" v-show="Object.keys(rightMenu).length > 0 && rightMenu.pid === 0"
              @click="(isPopShow = true), (operationType = 'set'), (type = true)">
              修改模板分类
            </a-menu-item>
            <a-menu-item key="3" v-show="Object.keys(rightMenu).length > 0 && rightMenu.pid === 0" @click="remove">
              删除模板分类
            </a-menu-item>
            <a-menu-item key="4" v-show="Object.keys(rightMenu).length > 0 && rightMenu.pid !== 0"
              @click="(isPopShow = true), (operationType = 'set', (type = false))">
              修改模板
            </a-menu-item>
            <a-menu-item key="5" v-show="Object.keys(rightMenu).length > 0 && rightMenu.pid !== 0" @click="remove">
              删除模板
            </a-menu-item>
          </tkTightTree>
        </div>
      </a-card>
    </a-layout-sider>
    <a-layout-content class="menu-right-content">
      <a-card title="模板列表" :bordered="false">
        <dataTable ref="dataTable" :templateId="templateId" :selectInfo="selectInfo" />
        <treeOper :isPopShow.sync="isPopShow" @updateList="updateList" :parameterData="rightMenu"
          :operationType="operationType" :type="type"></treeOper>
      </a-card>
    </a-layout-content>
  </a-layout>
</template>

<script>
import api from "./api";
import dataTable from "./module/table";
import treeOper from "./module/treeOper";
export default {
  provide() {
    return { api: api };
  },
  data() {
    return {
      treeData: [], // 树状列表填充数据
      rightMenu: {}, // 右击对象
      operationType: "",
      type: true,
      replaceFields: {
        children: "children",
        title: "labelName",
        key: "id",
      },
      selectData: {}, // 选中数据
      selectInfo: {}, // 选中信息
      templateId: 0, // 用户选中项菜单
      selectedKeys: [],
      isPopShow: false, //弹窗是否显示
      uploadShow: false, // 导入弹框
    };
  },

  components: { dataTable, treeOper },

  computed: {},

  created() {
    this.getTreeData();
  },

  methods: {
    query() {
      this.$refs.dataTable.$refs.table.getTableData();
    },
    remove() {
      let _this = this;
      _this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deleteBus();
        },
      });
    },
    deleteBus() {
      let _this = this;
      api
        .deleteSmsTemplateLabel({ bcSmsTemplateLabelId: this.rightMenu.id })
        .then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.getTreeData();
            _this.reset();
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          _this.$message.error(e.message);
        });
    },
    updateList() {
      this.getTreeData();
      this.reset()
    },
    rightClick(data) {
      this.rightMenu = data;
      // 如果是子菜单获取父级菜单的名称
      let parentDate = null
      if (data.pid === 0) {
        parentDate = {
          labelCode: data.labelCode,
          labelName: data.labelName
        }
      } else if (data.pid > 0) {
        const treeData = this.treeData
        for (let i = 0; i < treeData.length; i++) {
          if (treeData[i].id == data.pid) {
            parentDate = treeData[i]
            break
          }
        }
      }
      if (parentDate !== null) {
        this.rightMenu.parentDate = parentDate
      }
    },
    select(data) {
      // 赋值
      if (data.pid === 0) {
        // 点击一级菜单
        this.selectInfo = {}
        this.templateId = data.id
      } else {
        // 查询父菜单
        const treeData = this.treeData
        let parentDate = null
        for (let i = 0; i < treeData.length; i++) {
          if (treeData[i].id == data.pid) {
            parentDate = treeData[i]
            break
          }
        }
        if (parentDate && data) {
          this.selectInfo = {
            parentLabelCode: parentDate.labelCode,
            parentLabelName: parentDate.labelName,
            labelCode: data.labelCode,
            labelName: data.labelName,
            smsNo: `${parentDate.labelCode}.${data.labelCode}`
          }
          this.templateId = data.id
          this.selectData = data;
        }
      }

    },
    // 表单重置
    reset() {
      // 没有选中对应的菜单时进行初始化重置
      this.operationType = "";
      this.selectData = {};
      this.selectedKeys = [];
      this.selectInfo = {}
      this.templateId = 0
    },
    // 查询当前菜单栏树状列表
    getTreeData(searchKey) {
      const params = {}
      if (searchKey) params.searchKey = searchKey
      api.getSmsTemplateLabelList(params).then(({ code, data }) => {
        if (code != 0) return;
        this.treeData = data.map((item) => this.formatConversion(item));
      });
    },
    formatConversion(item) {
      item["scopedSlots"] = { icon: "custom" };
      item["itemIcon"] = "file";
      if (item.children) {
        item.children.map(item => this.formatConversion(item))
      }
      return item;
    },
    // 搜索标签
    searchTag(value) {
      this.getTreeData(value)
    }
  },
};
</script>
<style lang="scss" scoped>
.menu-left-title {
  margin-bottom: 12px;
}
</style>
