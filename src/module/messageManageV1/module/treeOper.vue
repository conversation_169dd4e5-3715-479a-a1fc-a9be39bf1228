<!--  -->
<template>
  <div>
    <a-modal :title="`${typeTitle}${type ? '模板分类' : '模板'}`" :visible="showPop" :ok-text="typeTitle" cancel-text="重置"
      @ok="submit" @cancel="closePop" :maskClosable="false">
      <a-form-model ref="form" :model="form" :rules="rules" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
        <a-form-model-item :label="`${type ? '模板分类' : '模板'}名称`" prop="labelName">
          <a-input :placeholder="`请输入${type ? '模板分类' : '模板'}名称`" v-model="form.labelName"></a-input>
        </a-form-model-item>
        <a-form-model-item :label="`${type ? '英文名称' : '模板编码'}`" prop="labelCode" class="child-class">
          <a-input :placeholder="`请输入${type ? '英文名称' : '模板编码'}`" v-model="form.labelCode">
            <span class="span" v-if="!type" slot="addonBefore">{{ parameterData.parentDate &&
              parameterData.parentDate.labelCode }}</span>
          </a-input>
        </a-form-model-item>
      </a-form-model>
      <template slot="footer">
        <a-button key="back" @click="reset">重置</a-button>
        <a-button type="primary" key="submit" @click="submit">确认</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'treeOper',
  inject: ['api'],
  data() {
    return {
      form: {}
    };
  },
  mounted() {
    // 因为computed在data之后执行，所以rule需要重新赋值
    // this.rules = {
    //   labelName: [
    //     {
    //       required: true,
    //       message: `${this.type ? '模板分类' : '模板'}名称不能为空`,
    //       trigger: 'blur',
    //     },
    //   ],
    //   labelCode: [
    //     {
    //       required: true,
    //       message: `${this.type ? '英文名称' : '模板编码'}不能为空`,
    //       trigger: 'blur',
    //     },
    //     {
    //       pattern: /^[a-zA-Z0-9_]+$/,
    //       message: `${this.type ? '英文名称' : '模板编码'}只能为大小写字母、数字或者下划线`,
    //       trigger: 'blur'
    //     }
    //   ],
    // }
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => { },
    },
    // 操作类型
    operationType: {
      type: String,
      default: 'add',
    },
    // 是否是模版分类
    type: {
      type: Boolean,
      default: true
    }
  },

  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit('update:isPopShow', val); // visible 改变的时候通知父组件
      },
    },
    typeTitle() {
      return this.operationType == 'add' ? '添加' : '修改';
    },
    rules() {
      return {
        labelName: [
          {
            required: true,
            message: `${this.type ? '模板分类' : '模板'}名称不能为空`,
            trigger: 'blur',
          },
        ],
        labelCode: [
          {
            required: true,
            message: `${this.type ? '英文名称' : '模板编码'}不能为空`,
            trigger: 'blur',
          },
          {
            pattern: /^[a-zA-Z0-9_]+$/,
            message: `${this.type ? '英文名称' : '模板编码'}只能为大小写字母、数字或者下划线`,
            trigger: 'blur'
          }
        ],
      }
    }
  },
  watch: {
    isPopShow(n) {
      if (n && this.operationType != 'add' && this.parameterData.id) {
        this.modify();
      } else if (this.parameterData.id) {
        this.form.id = this.parameterData.id;
      } else if (this.operationType == 'add' && !this.parameterData.id) {
        this.form.id = null;
      } else {
        this.reset();
      }
    },
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {};
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = Object.assign({}, this.parameterData);
    },
    submit() {
      //新增;
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if (this.operationType == 'set') {
          let param = {
            id: this.parameterData.id,
            pid: this.parameterData.pid,
            labelName: this.form.labelName,
            labelCode: this.form.labelCode,
          };
          this.api.editSmsTemplateLabel(param).then((res) => {
            if (res.code === 0) {
              this.$message.success(res.msg);
              this.$emit('updateList', true);
              this.closePop();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          let param = {
            pid: this.parameterData.pid === 0 ? this.parameterData.id : 0,
            labelName: this.form.labelName,
            labelCode: this.form.labelCode,
          };
          this.api.addSmsTemplateLabel(param).then((res) => {
            if (res.code === 0) {
              this.$message.success(res.msg);
              this.$emit('updateList', true);
              this.closePop();
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      });
    },
  },
};
</script>
<style lang='scss' scoped>
::v-deep .child-class .ant-form-item-children {
  display: flex;
  align-items: center;

  .span {
    margin-right: 15px;
    color: #000000;
  }
}
</style>