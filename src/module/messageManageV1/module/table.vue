<template>
  <div class="menu-right-limits">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset(0)">
          <!-- <a-form-model-item label="模板名称">
            <a-input v-model="tableForm.name" placeholder="请输入模板名称"></a-input>
          </a-form-model-item> -->
          <a-form-model-item label="创建时间">
            <a-range-picker :value="[tableForm.beginTime, tableForm.endTime]" @change="onChangeRangeData" />
          </a-form-model-item>
          <a-form-model-item label="是否启用">
            <a-select placeholder="请选择是否启用" v-model="tableForm.state">
              <a-select-option value="1">
                是
              </a-select-option>
              <a-select-option value="0">
                否
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <!-- 添加修改 -->
    <addOrEdit :isPopShow.sync="isAddPopShow" :selectInfo="selectInfo" :templateId="templateId"
      :parameterData="selectData" :businessList="businessList" @success="query" />
    <tk-table ref="table" :tableData.sync="comlun" :intercept-response="intercept_response" :data-source="dataSource"
      getMethods="bc-manage-server/bcSmsSendTemplate/page" :isSelected="true" :isPaging="true" :tableFrom="tableForm"
      :selectedRowKeys.sync="selectedRowKeys" tableId="id" :isTableLoading="false">
      <div class="table-buttun-area" slot="tableHeader">
        <a-button :disabled="!Object.keys(selectInfo).length" type="primary" icon="plus" @click="add">
          新增
        </a-button>
        <a-button :disabled="selectedRowKeys.length <= 0" type="danger" icon="delete" @click="remove">
          删除
        </a-button>
        <a-button :disabled="!Object.keys(selectInfo).length" type="primary" icon="redo" @click="reset(1)">刷新</a-button>
      </div>
      <template slot="smsNo">{{ selectInfo.smsNo }}</template>
      <template slot="stateBtn" slot-scope="data">
        <a-switch :checked="data.state === '1'" checked-children="开" un-checked-children="关"
          @click.native.stop="changeState($event, data)"></a-switch>
      </template>
      <template slot="operation" slot-scope="data">
        <a-button type="link" @click.stop="edit(data)"> 修改 </a-button>
      </template>
    </tk-table>
  </div>
</template>

<script>
import addOrEdit from "./addOrEdit";
export default {
  data() {
    return {
      // 表格展示字段
      comlun: [
        // 循环
        {
          field: "name",
          label: "模板名称",
          isSorter: false,
        },
        {
          field: "smsNo",
          label: "模板编号",
          isSorter: false,
        },
        {
          field: "content",
          label: "模板内容",
          isSorter: false,
        },
        {
          field: "createDate",
          label: "创建时间",
          isSorter: true,
        },
        {
          field: "createBy",
          label: "创建人",
          isSorter: false,
        },
        {
          field: "stateBtn",
          label: "是否启用",
          isSorter: false,
          // filter: item=>item==='1'?'是':'否'
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: "right",
        },
      ],
      // 查询接口所需字段
      tableForm: {
        name: "",
        beginTime: '',
        endTime: '',
        state: undefined,
        templateId: 0
      },
      operationType: "add",
      // 是否显示弹窗
      isAddPopShow: false,
      isEditPopShow: false,
      //查看弹窗
      selectData: {}, // 所要修改的权限
      selectedRowKeys: [], // 当前用户选中参数
      agreeId: undefined,
      dataSource: []
    };
  },
  inject: ["api"],
  components: { addOrEdit },
  props: {
    // 数据权限分组编号
    templateId: {
      type: Number,
      default: 0,
    },
    // 选中的信息
    selectInfo: {
      type: Object
    },
    businessList: {
      type: Array,
    },
  },
  created() {
    // 初始给与值
    this.tableForm["templateId"] = this.templateId;
  },
  watch: {
    // 用户传入菜单编号
    templateId: {
      handler(val) {
        // 具有菜单编号时查询对应的权限列表
        this.tableForm["templateId"] = val;
        if (this.templateId == 0) this.dataSource = []
        this.$refs.table.getTableData();
        // 初始话选中
        this.selectedRowKeys = [];
      },
      deep: true,
    },
  },
  methods: {
    changeState(e, data) {
      console.log(e, data)
      this.api
        .changeStatus({
          state: data.state == "1" ? "0" : "1",
          id: data.id,
        })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("修改成功");
            this.$refs.table.getTableData();
            this.selectedRowKeys = [];
          }
        });
    },
    add() {
      //点击新增按钮
      this.isAddPopShow = true;
      this.selectData = {};
    },
    remove() {
      let _this = this;
      _this.$confirm({
        title: "温馨提示",
        content: `是否确认删除?`,
        okType: "danger",
        onOk() {
          _this.deleteBus();
        },
      });
    },
    deleteBus() {
      let _this = this;
      let bcSmsSendTemplateIds = this.selectedRowKeys.join(",");
      this.api
        .deletesSmsSendTemplate({ bcSmsSendTemplateIds })
        .then((res) => {
          if (res.code === 0) {
            _this.$message.success(res.msg);
            _this.query();
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          _this.$message.error(e.message);
        });
    },
    reset(type) {
      this.tableForm.beginTime = ''
      this.tableForm.endTime = ''
      this.tableForm.state = undefined
      if (type) {
        this.$nextTick(() => {
          this.$refs.table.getTableData();
        });
      }
    },
    // 修改
    edit(data) {
      this.selectData = data;
      this.isAddPopShow = true;
    },
    // 操作成功
    query() {
      if (this.templateId == 0) this.dataSource = []
      this.selectedRowKeys = [];
      this.$refs.table.getTableData();
    },
    // 选择日期
    onChangeRangeData(date, dateString) {
      console.log(date, dateString)
      this.tableForm.beginTime = dateString[0]
      this.tableForm.endTime = dateString[1]
    },
  },
};
</script>

<style lang="scss" scoped></style>
