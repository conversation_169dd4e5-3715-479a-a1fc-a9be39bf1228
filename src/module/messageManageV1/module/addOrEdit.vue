<template>
  <a-modal :title="`${typeTitle}短信模板`" class="data-ant-module" :width="700" v-model="showPop" ok-text="确认"
    cancel-text="重置" @ok="submit" @cancel="closePop" :confirm-loading="confirmLoading" :maskClosable="false">
    <a-form-model ref="form" :model="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :rules="rules">

      <a-form-model-item label="模板内容" prop="content">
        <a-textarea v-model="form.content" />
        <div>
          <a-tag v-for="dict, i in dictMap['bc.common.smsLabal']" :key="i" color="blue" @click="onClickTag(dict)">{{
            dict.label }}</a-tag>
        </div>
      </a-form-model-item>

      <a-form-model-item label="是否启用" prop="state">
        <a-radio-group name="state" v-model="form.state">
          <a-radio value="1" key="1">是</a-radio>
          <a-radio value="0" key="0">否</a-radio>
        </a-radio-group>
      </a-form-model-item>

    </a-form-model>
    <template slot="footer">
      <a-button key="back" @click="reset">重置</a-button>
      <a-button type="primary" key="submit" @click="submit">确认</a-button>
    </template>
  </a-modal>
</template>
<script>
// 默认表单属性
const defaultForm = {
  name: "",
  smsNo: "",
  templateId: 0,
  state: "1",
  content: ""
};

// 注册当前请求对应的上下文请求组
export default {
  name: "addOrEdit",
  inject: ["api"],
  data() {
    return {
      // 权限添加表单 - 默认
      form: Object.assign({}, defaultForm),
      // 表单权限验证
      rules: {
        content: [
          { required: true, message: "模板内容不能为空", trigger: "blur" },
        ],
      },
      confirmLoading: false,
      dictMap: {
        'bc.common.smsLabal': []
      },
    };
  },
  props: {
    isPopShow: Boolean, //是否展示弹窗
    templateId: Number,
    selectInfo: Object,
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => { },
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
    typeTitle() {
      return Object.keys(this.parameterData).length <= 0 ? "添加" : "修改";
    },
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.id > 0) {
        this.modify();
      } else {
        this.reset();
      }
    },
  },
  created() {
    Promise.all([this.queryDictMap()]).finally();
  },
  methods: {
    closePop() {
      this.showPop = false;
      this.reset();
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // 用户点击修改进入
    modify() {
      // 获取修改属性前先进行表单重置
      this.reset();
      this.form = this.parameterData;
    },

    // 提交创建
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        console.log(this.form);
        let param = JSON.parse(JSON.stringify(this.form));
        console.log(param);
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0)
            return this.$message.error(`短信模板${this.typeTitle}失败：${msg}`);
          this.$message.success(`短信模板${this.typeTitle}成功！`);
          // 通知操作成功
          this.$emit("success");
          // 关闭弹窗 重置表单
          this.closePop();
        };
        if (this.parameterData.id > 0) {
          this.api.editSmsSendTemplate(param).then(callback);
        } else {
          this.api
            .addSmsSendTemplate(
              Object.assign({}, param, {
                name: this.selectInfo.labelName,
                smsNo: this.selectInfo.smsNo,
                templateId: this.templateId,
                templateLabelCode: this.selectInfo.labelCode,
              })
            )
            .then(callback);
        }
      });
    },

    // 点击标签
    onClickTag(dict) {
      this.form.content += dict.value
    },

    queryDictMap() {
      return new Promise((resolve) => {
        Object.keys(this.dictMap).forEach((item) => {
          this.$dict.dictContent(item).then((data) => {
            this.dictMap[item] = (data || []).map((data) => {
              return { label: data.dictLabel, value: data.dictValue };
            });
            resolve();
          });
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
