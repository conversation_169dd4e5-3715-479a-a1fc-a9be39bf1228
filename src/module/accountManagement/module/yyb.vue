<template>
  <a-modal
    :title="`选择营业部`"
    :width="1200"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-card :bordered="false">
      <div class="searchForm">
        <a-form-model layout="inline" :model="tableForm">
          <tkSelectForm @query="query" @reset="reset">
            <a-form-model-item label="营业部名称">
              <a-input v-model="tableForm.branchName" placeholder="请输入营业部名称"></a-input>
            </a-form-model-item>
          </tkSelectForm>
        </a-form-model>
      </div>
      <div class="access-table">
        <tk-table
          ref="table"
          :tableData.sync="columns"
          getMethods="khyx-manage-server/branchInfo/page"
          :isPaging="true"
          :isSelected="true"
          :tableFrom="tableForm"
          :selectedRows.sync="selectedRows"
          :selectedRowKeys.sync="selectedRowKeys"
          tableId="roleId"
        >
        </tk-table>
      </div>
    </a-card>
  </a-modal>
</template>

<script>
// 默认表单属性
const defaultForm = {
  branchName: "", // 客户姓名
};
export default {
  name: "sysRole_Add",
  inject: ["api"],
  data() {
    return {
      form: Object.assign({}, defaultForm), //表单数据,
      // 异步加载
      confirmLoading: false,
      selectedRowKeys: [],
      selectedRows: [],
      // 表头数据
      columns: [
        // 循环
        {
          field: "branchNo",
          label: "营业部编号",
          isSorter: false,
        },
        {
          field: "branchName",
          label: "营业部名称",
          isSorter: false,
          width: 240
        },
        {
          field: "address",
          label: "营业部地址",
          isSorter: false,
        },
        {
          field: "tel",
          label: "营业部电话",
          isSorter: false,
        },
        {
          field: "regionId",
          label: "所属区域",
          isSorter: false,
        },
        {
          field: "provinceId",
          label: "所属省份",
          isSorter: false,
        },
        {
          field: "cityId",
          label: "所属城市",
          isSorter: false,
        },
      ],
      tableForm: {
          branchName: ""
      }
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 是否展示添加弹窗
    visible: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  methods: {
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    query() {
      this.$refs.table.getTableData();
    },
    // 提交角色信息表创建
    submit() {
      console.log(this.selectedRows)
      if(this.selectedRows.length != 1){
        this.$message.warning('只能选择一个营业部');
        return
      }
      // 关闭弹窗
      this.showPop = false;
      // 通知操作成功
      this.$emit("success",this.selectedRows[0]);
      // 重置表单
      this.reset();
      // this.$refs.form.validate((valid) => {
      //   if (!valid) return;
      //   this.confirmLoading = true;
      //   let param = JSON.parse(JSON.stringify(this.form));
      //   let callback = ({ code, msg }) => {
      //     this.confirmLoading = false;
      //     if (code != 0)
      //       return this.$message.error(
      //         `角色信息表${this.typeTitle}失败：${msg}`
      //       );
      //     this.$message.success(`角色信息表${this.typeTitle}成功！`);
      //     // 关闭弹窗
      //     this.showPop = false;
      //     // 通知操作成功
      //     this.$emit("success");
      //     // 重置表单
      //     this.reset();
      //   };
      //   this.api.addSysRole(param).then(callback);
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>