<template>
  <a-modal
    :title="`${typeTitle}经纪人信息`"
    :width="500"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="submit"
    @cancel="reset"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
    <a-form-model-item label="姓名" prop="staffName">
       <a-input v-model="form.staffName" placeholder="请输入姓名" ></a-input>
     </a-form-model-item>
    <a-form-model-item label="员工编号" prop="staffId">
       <a-input v-model="form.staffId" placeholder="请输入员工编号" ></a-input>
     </a-form-model-item>
     <a-form-model-item label="手机号码" prop="mobileNo">
       <a-input v-model="form.mobileNo" placeholder="请输入手机号码" ></a-input>
     </a-form-model-item>
     <a-form-model-item label="经纪人类型" prop="staffType">
        <a-select v-model="form.staffType" placeholder="请选择经纪人类型">
          <a-select-option :value="item.value" v-for="(item,index) in agentlList" :key="index">
            {{item.label}}
          </a-select-option>
        </a-select>
     </a-form-model-item>
     <a-form-model-item label="营业部" prop="branchNo">
      <a-select
        show-search
        :default-active-first-option="false"
        :show-arrow="false"
        :not-found-content="null"
        style="width: 100%"
        placeholder="请输入营业部"
        @change="handleChangeYyb"
        @search="searchYyb"
        @focus="searchYyb"
        :filter-option="filterOption"
      >
        <a-select-option :value="item.branchNo" v-for="(item,index) in yybList" :key="index">
          {{ item.branchName }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
     <a-form-model-item label="员工状态" prop="state">
      <a-radio-group v-decorator="['radio-group']" v-model="form.state">
        <a-radio :value="item.value" v-for="(item,index) in startList" :key="index">
          {{item.label}}
        </a-radio>
      </a-radio-group>
     </a-form-model-item>
     <a-form-model-item label="二维码中的logo">
      <a-upload
        name="file"
        list-type="picture-card"
        class="avatar-uploader"
        :show-upload-list="false"
        accept=".jpg, .jpeg, .png"
        @change="handleChange"
        action="/khyx-manage-server/base/uploadFile"
      >
        <img v-if="imageUrl" :src="imageUrl" alt="avatar" />
        <div v-else>
          <a-icon :type="loading ? 'loading' : 'plus'" />
          <div class="ant-upload-text">
            Upload
          </div>
        </div>
      </a-upload>
     </a-form-model-item>
    </a-form-model>
    <!-- <yyb :isPopShow.sync="isYybPopShow" @success="querys" /> -->
  </a-modal>
</template>

<script>
// import yyb from './yyb';
// 默认表单属性
const defaultForm = {
  staffId: "",
  staffName: "", // 姓名
  mobileNo: "", // 手机号码
  staffType: "", // 经纪人类型
  branchNo: "",  //营业部
  state: "", // 员工状态
  logo: "", // logo
};

export default {
  name: "sysRole_Add",
  inject: ["api"],
  // components: {yyb},
  data() {
    return {
      isYybPopShow: false,
      headers: {
        authorization: 'authorization-text',
      },
      form: Object.assign({}, defaultForm, {staffType: undefined}), //表单数据,
      rules: {
        staffId: [
            { required: true, message: "员工编号不能为空", trigger: "blur"}
        ],
        staffName: [
            { required: true, message: "姓名不能为空", trigger: "blur"}
        ],
        mobileNo: [
            { required: true, message: "手机号码不能为空", trigger: "blur"}
        ],
        staffType: [
            { required: true, message: "经纪人类型不能为空", trigger: "blur"}
        ],
        branchNo: [
            { required: true, message: "营业部不能为空", trigger: "blur"}
        ],
        state: [
            { required: true, message: "员工状态不能为空", trigger: "blur"}
        ]
      },
      // 异步加载
      confirmLoading: false,
      selectData: {},
      agentlList: [],
      startList: [],
      branchName: "",
      yybList: [],
      loading: false,
      imageUrl: '',
      fileUrl: ''
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: ()=>{}
    },
  },
  computed: {
    showPop: {
      get() {
          return this.isPopShow;
      },
      set(val) {
          this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      }
    },
    typeTitle (){
      return "添加";
    }
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.sysRoleId) {
        this.query();
      }else{
        this.reset();
      }
      this.$nextTick(()=> {
      });
    }
  },
  created() {
    this.queryDictMap()
  },
  methods: {
    getBase64(img, callback1) {
      const reader = new FileReader();
      reader.addEventListener('load', () => callback1(reader.result));
      reader.readAsDataURL(img);
    },
    handleChange(info) {
      console.log(info)
      if (info.file.status === 'uploading') {
        this.loading = true;
        return;
      }
      if (info.file.status === 'done') {
        this.form.logo = info.file.response.data.uploadPath
        this.getBase64(info.file.originFileObj, imageUrl => {
          this.imageUrl = imageUrl;
          this.loading = false;
        });
      }
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    searchYyb(value) {
      let parme = {
        branchNo:"",
        branchName:value,
        pageNum:1,
        pageSize:10,
        sortField:"",
        sortOrder:""
      }
      let that = this
      let callback = ({ code, msg, data }) => {
          that.confirmLoading = false;
          if (code != 0) return this.$message.error(`经纪人${this.typeTitle}失败：${msg}`);
          that.yybList = data.list
        };
      this.api.yybSysRole(parme).then(callback);
    },
    handleChangeYyb(value) {
      this.form.branchNo = value
    },
    queryDictMap() {
      return new Promise((resolve) => {
        [
          { dataIndex: "agentlList", key: "bc.futures.agent" },
          { dataIndex: "startList", key: "bc.futures.start" },
        ].forEach((item) => {
          this.$dict.dictContent(item.key).then((data) => {
            this[item.dataIndex] = (data || []).map((data) => {
              return { label: data.dictLabel, value: data.dictValue };
            });
            resolve();
          });
        });
      });
    },
    // 点击营业部弹窗
    yyb(){
      this.$refs.yyds.blur()
      this.isYybPopShow = true;
      this.selectData={}
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // query() {
    //   this.reset();
    //   return this.api.querySysRoleDetail({ sysRoleId: this.parameterData.sysRoleId}).then((res) => {
    //     if (res.code != 0) return;
    //     let data = res.data;
    //     this.form = data;
    //   });
    // },
    // querys(data) {
    //   this.branchName = data.branchName
    //   this.form.branchNo = data.branchNo
    // },
    // 提交角色信息表创建
    submit() {
      console.log(this.form)
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        let param = JSON.parse(JSON.stringify(this.form));
        console.log(param)
        let callback = ({ code, msg }) => {
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`经纪人${this.typeTitle}失败：${msg}`);
          this.$message.success(`经纪人${this.typeTitle}成功！`);
          // 关闭弹窗
          this.showPop = false;
          // 通知操作成功
          this.$emit("success");
          // 重置表单
          this.reset();
        };
        this.api.addSysRole(param).then(callback);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader > .ant-upload > span >img{
  width: 128px;
  height: 128px;
}
::v-deep .ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

::v-deep .ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>