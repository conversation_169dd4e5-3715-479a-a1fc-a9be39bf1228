<template>
  <a-modal
    :title="`${typeTitle}经纪人信息`"
    :width="1000"
    v-model="showPop"
    ok-text="确认"
    cancel-text="取消"
    @ok="ok"
    @cancel="reset"
    :maskClosable="false"
  >
  <a-row>
  <a-col :span="14">
    <a-form-model
      ref="form"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
     <a-form-model-item label="姓名" prop="staffName">
       <a-input v-model="form.staffName" :disabled="true" placeholder="请输入姓名" ></a-input>
     </a-form-model-item>
     <a-form-model-item label="手机号码" prop="mobileNo">
       <a-input v-model="form.mobileNo" :disabled="true" placeholder="请输入手机号码" ></a-input>
     </a-form-model-item>
     <a-form-model-item label="经纪人类型" prop="staffType">
        <a-select v-model="form.staffType" :disabled="true" placeholder="请选择经纪人类型">
          <a-select-option :value="item.value" v-for="(item,index) in agentlList" :key="index">
            {{item.label}}
          </a-select-option>
        </a-select>
     </a-form-model-item>
     <a-form-model-item label="营业部" prop="branchNo">
      <a-input allowClear ref="yyds" :disabled="true" @click="yyb" v-model="branchName" placeholder="请输入营业部"></a-input>
    </a-form-model-item>
     <a-form-model-item label="员工状态" prop="state">
      <a-radio-group v-decorator="['radio-group']" :disabled="true" v-model="form.state">
        <a-radio :value="item.value" v-for="(item,index) in startList" :key="index">
          {{item.label}}
        </a-radio>
      </a-radio-group>
     </a-form-model-item>
     <a-form-model-item label="二维码中的logo" v-if="form.logo">
      {{form.logo}}
     </a-form-model-item>
    </a-form-model>
    </a-col>
      <a-col :span="10">
        <img alt="暂无加载" @click="downImg" style="width:50%;display: block;margin:0 auto" :src="imgSrc" />
        <p style="color:red;height:50px;line-height:50px;font-size:16px;text-align:center">点击二维码图片即可下载</p>
      </a-col>
    </a-row>
    <yyb :isPopShow.sync="isYybPopShow" @success="querys" />
  </a-modal>
</template>

<script>
import yyb from './yyb';
// 默认表单属性
const defaultForm = {
  staffId: "",
  staffName: "", // 姓名
  mobileNo: "", // 手机号码
  staffType: "", // 经纪人类型
  branchNo: "",  //营业部
  state: "", // 员工状态
  logo: "", // logo
};

export default {
  name: "sysRole_Add",
  inject: ["api"],
  components: {yyb},
  data() {
    return {
      isYybPopShow: false,
      headers: {
        authorization: 'authorization-text',
      },
      form: Object.assign({}, defaultForm), //表单数据,
      rules: {
        staffName: [
            { required: true, message: "姓名不能为空", trigger: "blur"}
        ],
        mobileNo: [
            { required: true, message: "手机号码不能为空", trigger: "blur"}
        ],
        staffType: [
            { required: true, message: "经纪人类型不能为空", trigger: "blur"}
        ],
        branchNo: [
            { required: true, message: "营业部不能为空", trigger: "blur"}
        ],
        state: [
            { required: true, message: "员工状态不能为空", trigger: "blur"}
        ]
      },
      // 异步加载
      confirmLoading: false,
      selectData: {},
      agentlList: [],
      startList: [],
      branchName: "",
      imgSrc: require("../imges/ewm.png")
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    // 修改时传入参数
    parameterData: {
      type: Object,
      default: ()=>{}
    },
  },
  computed: {
    showPop: {
      get() {
          return this.isPopShow;
      },
      set(val) {
          this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      }
    },
    typeTitle (){
      return "查看";
    }
  },
  watch: {
    isPopShow(n) {
      if (n && this.parameterData.sysRoleId) {
        this.query();
      }else{
        this.reset();
      }
      this.$nextTick(()=> {
      });
    },
    parameterData(){
      this.initData()
    }
  },
  created() {
    this.queryDictMap()
  },
  methods: {
    initData() {
      let parameterData = this.parameterData
      this.form.staffId = parameterData.staffId
      this.form.staffName = parameterData.staffName
      this.form.mobileNo = parameterData.mobileNo
      this.form.branchNo = parameterData.branchNo
      this.branchName = parameterData.branchName
      this.form.staffType = parameterData.staffType
      this.form.state = parameterData.state
      this.form.logo = parameterData.logo
      this.qrCodeImg(parameterData.content)
    },
    qrCodeImg(value) {
      let callback = ({ code, msg, data }) => {
        console.log(data)
          this.confirmLoading = false;
          if (code != 0) return this.$message.error(`生成二维码失败：${msg}`);
          // this.$message.success(`生成二维码成功！`);
          this.imgSrc = "data:image/png;base64," + data.img
      };
      this.api.qrCodeSysRole({content:value}).then(callback);
    },
    queryDictMap() {
      return new Promise((resolve) => {
        [
          { dataIndex: "agentlList", key: "bc.futures.agent" },
          { dataIndex: "startList", key: "bc.futures.start" },
        ].forEach((item) => {
          this.$dict.dictContent(item.key).then((data) => {
            this[item.dataIndex] = (data || []).map((data) => {
              return { label: data.dictLabel, value: data.dictValue };
            });
            resolve();
          });
        });
      });
    },
    ok(){
      // 关闭弹窗
      this.showPop = false;
    },
    // 重置对应的表单
    reset() {
      // 重置表单验证属性
      this.$refs.form && this.$refs.form.resetFields();
      this.form = Object.assign({}, defaultForm);
      this.confirmLoading = false;
    },
    // 点击营业部弹窗
    yyb(){
      this.$refs.yyds.blur()
      this.isYybPopShow = true;
      this.selectData={}
    },
    querys(data) {
      this.branchName = data.branchName
      this.form.branchNo = data.branchNo
    },
    handleChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} file upload failed.`);
      }
    },
    downImg() {
      let a = document.createElement("a");
      a.href = this.imgSrc;
      a.setAttribute("download", "chart-download");
      a.click();
    }
  },
};
</script>

<style lang="scss" scoped>
</style>