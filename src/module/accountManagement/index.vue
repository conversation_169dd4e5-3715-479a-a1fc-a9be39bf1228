<!--
 * Generate by tech-manage-server-v1.0.0#work-code-generator-v1.0.2
 * @Author: admin
 * @Date: 2022-03-02 14:03:43
 * @LastEditTime: 2022-03-02 14:03:43
 * @LastEditors: admin
 * @Description: 客户经理管理
 * @FilePath: \sysRole\src\module\sysRole\index.vue
-->
<template>
  <a-card title="客户经理管理" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset">
          <a-form-model-item label="推荐人姓名">
            <a-input v-model="tableForm.staffName" placeholder="请输入推荐人姓名"></a-input>
          </a-form-model-item>
          <a-form-model-item label="手机号码">
            <a-input v-model="tableForm.mobileNo" placeholder="请输入手机号码"></a-input>
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table ref="table"
        :tableData.sync="columns"
        getMethods="khyx-manage-server/staffinfo/page"
        :isSelected="true"
        :isPaging="true"
        :tableFrom="tableForm"
        :selectedRowKeys.sync="selectedRowKeys"
        :selectedRows.sync="selectedRows"
        tableId="staffId"
        >
        <template slot="staffType" slot-scope="text">
          <span v-if="text.staffType=='1'">推荐人</span>
          <span v-if="text.staffType=='2'">居间人</span>
        </template>
        <template slot="state" slot-scope="text">
          <span v-if="text.state=='1'">有效</span>
          <span v-if="text.state=='0'">无效</span>
        </template>
        <div class="table-button-area" slot="tableHeader">
          <a-button icon="plus" type="primary" @click="add"> 新增 </a-button>
          <a-button icon="sync" type="primary" @click="resets"> 刷新 </a-button>
          <a-button icon="delete" type="danger" :disabled="selectedRowKeys.length <= 0" @click="remove"> 删除 </a-button>
          <a-button icon="vertical-align-bottom" type="primary" @click="download"> 下载Excel模板 </a-button>
          <a-button icon="vertical-align-bottom" type="primary" @click="derive"> 导出 </a-button>
          <a-upload
            name="file"
            :multiple="false"
            :headers="headers"
            @change="handleChange"
            :showUploadList = 'false'
          >
            <a-button type="primary"> <a-icon type="upload" /> 导入 </a-button>
          </a-upload>
        </div>
        <template slot="operation" slot-scope="data">
          <a type="link" @click.stop="look(data)"> 二维码 </a>
          <a type="link" style="margin-left:10px" @click.stop="modify(data)"> 修改 </a>
        </template>
      </tk-table>
      <add :isPopShow.sync="isAddPopShow" @success="query" />
      <edit :isPopShow.sync="isEditPopShow" ref="dszx" @success="query" :parameterData="selectData" />
      <LookComponent :isPopShow.sync="isLookPopShow" :parameterData="selectData" />
    </div>
  </a-card>
</template>

<script>
// 引入添加和编辑弹窗
import add from './module/add';
import edit from './module/edit';
// 引入查看弹窗
import LookComponent from './module/detail';
import api from './api';

// 默认表单属性
const defaultForm = {
  staffName: "",
  mobileNo: "",
};

export default {
  data() {
    return {
      headers: {
        authorization: 'authorization-text',
      },
      math: Math.random(), // 随机标识-标记当前右击菜单唯一性
      // 表头数据
      columns: [
        // 循环
        {
          field: "staffId",
          dataIndex: "staffId",
          label: "员工编号",
          isSorter: false,
          align: "center",
        },
        {
          field: "staffName",
          label: "姓名",
          isSorter: false,
          align: "center",
        },
        {
          field: "staffType",
          dataIndex: "staffType",
          scopedSlots: {customRender:"staffType"},
          label: "推广人类型",
          isSorter: false,
          align: "center",
        },
        {
          field: "mobileNo",
          label: "手机号码",
          isSorter: false,
          isEllipsis: true,
          align: "center",
        },
        {
          field: "branchName",
          label: "营业部",
          isSorter: false,
          isEllipsis: true,
          align: "center",
        },
        {
          field: "state",
          dataIndex: "state",
          scopedSlots: {customRender:"state"},
          label: "状态",
          isSorter: false,
          align: "center",
        },
        {
          field: "startTime",
          label: "修改人",
          isSorter: false,
          filter: item => !item ? '--' : this.DateFormat(item).format("yyyy-MM-dd"),
          align: "center",
        },
        {
          field: "currentStep",
          label: "修改时间",
          isSorter: false,
          align: "center",
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          width: 150,
          fixed: 'right'
        }
      ],
      tableForm: {
        staffName: "",
        mobileNo: "",
      },
      selectedRowKeys: [], // Check here to configure the default column
      //新增弹窗
      isAddPopShow: false,   //营业部弹窗
      isEditPopShow: false,   //修改弹窗是否显示
      //查看弹窗
      isLookPopShow: false,
      selectData: {},
      selectedRows: []
    }
  },
  provide: { "api": api, },
  created() {
  },
  components: {add, edit, LookComponent},
  methods:{
    query() {
      this.$refs.table.getTableData();
      this.selectedRowKeys = [];
    },
    // 重置
    reset() {
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
    },
    //刷新
    resets() {
      this.tableForm = JSON.parse(JSON.stringify(defaultForm));
      setTimeout(()=>{
        this.$refs.table.getTableData();
      },200)
    },
    // 点击新增按钮
    add(){
      this.isAddPopShow = true;
      this.selectData={}
    },
    // 点击修改按钮
    modify(data) {
      this.isEditPopShow = true;
      this.selectData = data;
      console.log(data)
      this.$refs.dszx.initData()
      // sessionStorage.setItem('staff',JSON.stringify(data))
    },
    // 查看
    look(data) {
      this.isLookPopShow = true;
      this.selectData = data;
    },
    //导出
    derive() {

    },
    //导入
    imports() {

    },
    //下载模版
    download() {
      const a = document.createElement('a')
      a.setAttribute('download','')
      a.setAttribute('href','kh-manage-server/staffinfo/fxckh_xls/tjrxxmb.xls')
      a.click()
    },
    openChange(status) {
      if (status == false) {
        // 切出事件，用于组件缓冲数据
        setTimeout(() => {
          if (this.tableForm.beginTime && this.tableForm.endTime) {
            if (Date.parse(this.DateFormat(this.tableForm.beginTime)) > Date.parse(this.DateFormat(this.tableForm.endTime))) return this.$message.error('结束日期不应早于开始日期');
          }
        })
      }
    },
    // 删除
    remove() {
      if (this.selectedRowKeys.length > 0) {
        // let staffIds = []
        // for(let i=0;i<this.selectedRows.length;i++){
        //   staffIds.push(this.selectedRows[i].staffId)
        // }
        this.$confirm({
          title: '角色信息表',
          content: () => <p>确定删除当前经纪人数据?</p>,
          okText: '确定',
          cancelText: '取消',
          onOk: () =>  {
            api.deleteSysRole({staffIds: this.selectedRowKeys}).then(({code, msg}) => {
              if (code != 0) return this.$message.error(`删除经纪人失败：${msg}`);
              this.$message.success('删除经纪人成功！');
              this.$refs.table.getTableData();
              this.selectedRowKeys = [];
            })
          },
        });
      }
    },
    handleChange() {
      
    }
  }
}
</script>

<style lang="scss" scoped>
</style>