<template>
  <a-card title="两融预约管理" :bordered="false">
    <div class="searchForm">
      <a-form-model layout="inline" :model="tableForm">
        <tkSelectForm @query="query" @reset="reset(0)">
          <a-form-model-item label="姓名">
            <a-input
              placeholder="请输入姓名"
              v-model="tableForm.customerName"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="客户号">
            <a-input
              placeholder="请输入客户号"
              v-model="tableForm.customerId"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="到期时间">
            <a-range-picker
              format="YYYY/MM/DD HH:mm:ss"
              :show-time="timePickerOptions"
              @change="onChange"
            />
          </a-form-model-item>
        </tkSelectForm>
      </a-form-model>
    </div>
    <div class="access-table">
      <tk-table
        ref="table"
        :intercept-response="intercept_response"
        :tableData.sync="columns"
        :tableFromFilter="tableFormFilter"
        getMethods="bc-manage-server/credit/queryPage"
        :isPaging="true"
        :tableFrom="tableForm"
        tableId="bankId"
        @success="success"
      >
        <div class="table-button-area" slot="tableHeader">
          <a-button type="primary" icon="redo" @click="reset(1)">刷新</a-button>
          <a-button icon="upload" type="primary" @click="exportExcel">
            导出
          </a-button>
        </div>
        <template slot="operation" slot-scope="data">
          <a-button
            type="link"
            @click.stop="modify(data)"
            :disabled="data.status == '1' || data.status == '3' ? true : false"
          >
            同步BOP
          </a-button>
        </template>
      </tk-table>
    </div>
  </a-card>
</template>

<script>
// 引入添加和编辑弹窗
import api from "./api";
import moment from "moment";
import { throttle } from "lodash";

export default {
  data() {
    return {
      columns: [
        // 循环
        {
          field: "id",
          label: "受理单号",
          isSorter: true,
        },
        {
          field: "customerName",
          label: "姓名",
          isSorter: true,
        },
        {
          field: "customerId",
          label: "客户号",
          isSorter: false,
        },
        {
          field: "acceptTime",
          label: "预约时间",
        },
        {
          field: "auditTime",
          label: "审核通过时间",
        },
        {
          field: "status",
          label: "同步状态",
          filter: (item) => this.getDictText("statusList", item),
        },
        {
          field: "syncTime",
          label: "同步时间",
        },
        {
          field: "endTime",
          label: "到期时间",
        },
        {
          field: "operation",
          label: "操作",
          align: "center",
          fixed: true,
        },
      ],
      isBuffetList: this.$baseDict.base.isBuffetList, //方式列表
      endDate: "",
      timePickerOptions: {
        format: "HH:mm:ss", // 设置时间格式为小时:分钟:秒
        defaultValue: [
          moment("00:00:00", "HH:mm:ss"),
          moment("23:59:59", "HH:mm:ss"),
        ], // 设置默认结束时间为23:59
      },
      tableForm: {
        customerName: "",
        customerId: "",
        endTimeBegin: "",
        endTimeEnd: "",
      },
      total: 0,
      statusList: [
        { value: "1", label: "已同步" },
        { value: "0", label: "未同步" },
        { value: "3", label: "已过期" },
      ],
    };
  },
  provide: { api: api },
  methods: {
    getDictText(key, value) {
      let res = this[key] || [];
      res = res.filter((item) => {
        return item.value == value;
      });
      return (res && res.length && res[0].label) || value || "";
    },
    // 搜索框参数
    tableFormFilter(param) {
      return param;
    },

    query() {
      this.$refs.table.getTableData();
      this.requestId = "";
    },

    updateList() {},

    onChange(a, dateString) {
      this.tableForm.endTimeBegin = dateString[0];
      this.tableForm.endTimeEnd = dateString[1];
    },

    exportExcel() {
      if (this.total === 0) {
        this.$message.error(`当前无可导出数据`);
      } else if (this.total > 10000) {
        this.$message.error(`当前检索结果数据量超出系统支持最大导出量(1万条)`);
      } else {
        const {
          customerName = "",
          customerId = "",
          endTimeBegin = "",
          endTimeEnd = "",
        } = this.tableForm;
        window.location.href = `/bc-manage-server/credit/acceptCreditFlowExportAll?customerName=${customerName}&customerId=${customerId}&endTimeBegin=${endTimeBegin}&endTimeEnd=${endTimeEnd}&current=1&total=10&pageSize=10&pageNum=1`;
      }
    },

    reset() {
      this.tableForm = {
        customerName: "",
        customerId: "",
        endTimeBegin: "",
        endTimeEnd: "",
      };
      this.$nextTick(() => {
        this.$refs.table.getTableData();
      });
    },

    success({ data, code }) {
      if (code === 0) {
        this.total = data.total;
      }
    },

    modify: throttle(function(data) {
      console.log(data);
      if (data.status == "1") return;
      api.acceptCreditFlow({ id: data.id }).then((res) => {
        if (res.code === 0) {
          this.$message.success("同步成功");
          this.$nextTick(() => {
            this.$refs.table.getTableData();
          });
        } else {
          this.$message.error(res.msg);
        }
      });
    }, 1000),

    queryPage(data) {
      api.queryPage({}).then((res) => {
        this.isPopShow = true;
        this.selectData = res.data;
        this.requestId = data.bankId;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .img-table {
  max-height: 23px;
}
</style>
