{"name": "bc-manage-view", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "rebuild": "vue-cli-service build --report", "lint": "vue-cli-service lint"}, "dependencies": {"@tinymce/tinymce-vue": "^3.2.8", "@wangeditor/editor": "^5.1.14", "@wangeditor/editor-for-vue": "^1.0.2", "core-js": "^3.6.5", "moment": "^2.29.1", "mxgraph": "^4.2.2", "tinymce": "^5.8.2", "v-viewer": "^1.6.4", "vue": "^2.6.11", "lodash": "^4.17.21"}, "devDependencies": {"@babel/polyfill": "^7.12.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "ace-builds": "^1.5.3", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "bus-common-component": "0.2.23", "dayjs": "1.11.10", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "exports-loader": "^0.7.0", "filemanager-webpack-plugin": "^2.0.5", "js-cookie": "^2.2.1", "less": "^4.1.3", "less-loader": "^7.3.0", "node-sass": "^6.0.1", "sass-loader": "^10.1.1", "script-ext-html-webpack-plugin": "^2.1.5", "vue-router": "^3.4.9", "vue-template-compiler": "^2.6.11", "vue2-ace-editor": "^0.0.15", "vuex": "^3.6.0", "vuex-persistedstate": "^4.0.0-beta.3", "webpack-bundle-analyzer": "^4.4.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["ie 11"]}