// 引入工程配置文件
const package = require("./package.json");
// 引入路径插件
const path = require("path");
// 声明文件路径加载函数
function resolve(dir) {
  return path.join(__dirname, dir);
}
const FileManagerPlugin = require("filemanager-webpack-plugin");

let env = "local"; // 默认本地环境
if (process.env.NODE_ENV === "production") {
  for (let key in process.env) {
    if (key.indexOf("npm_config_tag_sit") > -1 || process.env.npm_config_develop) {
      env = "test";
      break;
    } else if (key.indexOf("npm_config_tag_uat") > -1) {
      env = "uat";
      break;
    } else {
      env = "prod";
    }
  }
}
console.log("-----------  " + env + "  ------------");
const outPutPath = path.join(__dirname, "dist") + "/" + package.name;

// vue启动/打包等相关配置
module.exports = {
  outputDir: `dist/${package.name}/`,
  filenameHashing: true,
  publicPath: `/${package.name}/`,
  css: {
    loaderOptions: {
      sass: {
        // 配置公共样式表
        additionalData: `@import "~@/assets/global.scss";`,
      },
    },
  },
  devServer: {
    port: "8081",
    proxy: {
      "work-manage-server": {
        target: "https://fzsdbusiness.yjbtest.com/work-manage-server",
        changeOrigin: true,
        pathRewrite: {
          "^/work-manage-server": "",
        },
      },
      "wa-manage-server": {
        target: "https://fzsdbusiness.yjbtest.com/wa-manage-server",
        changeOrigin: true,
        pathRewrite: {
          "^/wa-manage-server": "",
        },
      },
      "bf-manage-server": {
        target: "https://fzsdbusiness.yjbtest.com/bf-manage-server",
        changeOrigin: true,
        pathRewrite: {
          "^/bf-manage-server": "",
        },
      },
      "bc-manage-server": {
        target: "https://fzsdbusiness.yjbtest.com/bc-manage-server",
        changeOrigin: true,
        pathRewrite: {
          "^/bc-manage-server": "",
        },
      },
      "qc-manage-server": {
        target: "https://fzsdbusiness.yjbtest.com/qc-manage-server",
        changeOrigin: true,
        pathRewrite: {
          "^/qc-manage-server": "",
        },
      },
      "khyx-manage-server": {
        target: "https://fzsdbusiness.yjbtest.com/khyx-manage-server",
        changeOrigin: true,
        pathRewrite: {
          "^/khyx-manage-server": "",
        },
      },
      "qc-bizengine-server": {
        target: "https://fzsdbusiness.yjbtest.com/qc-bizengine-server",
        changeOrigin: true,
        pathRewrite: {
          "^/qc-bizengine-server": "",
        },
      },
    },
  },
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: "static",
  //打包生成生产环境sourceMap文件
  productionSourceMap: env !== "prod" && env !== "uat",
  chainWebpack(config) {
    config.plugins.delete("preload"); // TODO: need test
    config.plugins.delete("prefetch"); // TODO: need test
    config.resolve.symlinks(true);
  },
  configureWebpack: {
    output: {
      library: `${package.name}-[name]`,
      libraryTarget: "umd",
      jsonpFunction: `webpackJsonp_${package.name}`,
    },
    plugins: [
      new FileManagerPlugin({
        onEnd: [
          {
            move: [
              //将当前打包环境的配置覆盖configuration.js
              {
                source: `${outPutPath}/configuration_${env}.js`,
                destination: `${outPutPath}/configuration.js`,
              },
            ],
          },
          {
            // 删除其他环境的配置文件
            delete: [`${outPutPath}/configuration_*.js`],
          },
        ],
      }),
    ],
    // 别名相关配置
    resolve: {
      alias: {
        // 工程访问文件
        "@": resolve("src"),
        // 工程全局组件
        "@c": resolve("src/components"),
        // 工程静态资源
        "@a": resolve("src/assets"),
        // 工程功能模块
        "@m": resolve("src/module"),
        // 辅助工具模块
        "@u": resolve("src/utils"),
        // 辅助工具模块
        "@config": resolve("src/config"),
        // 配置工程常量访问文件
        "@constant": resolve("src/constant.js"),
        // 配置全局工具类
        "@utils": resolve("src/utils/index.js"),
      },
    },
  },
  chainWebpack: (config) => {
    config.module
      .rule("")
      .test(/mxClient\.js$/)
      .use("exports-loader")
      .loader(
        "exports-loader?mxClient,mxToolbar,mxConnectionHandler,mxEllipse,mxConnectionConstraint,mxWindow," +
          "mxObjectCodec,mxGraphModel,mxActor,mxPopupMenu,mxShape,mxEventObject,mxGraph,mxPopupMenuHandler,mxPrintPreview," +
          "mxEventSource,mxRectangle,mxVertexHandler,mxMouseEvent,mxGraphView,mxCodecRegistry,mxImage,mxGeometry," +
          "mxRubberband,mxConstraintHandler,mxKeyHandler,mxDragSource,mxGraphModel,mxEvent,mxUtils,mxEvent,mxCodec,mxCell," +
          "mxConstants,mxPoint,mxGraphHandler,mxCylinder,mxCellRenderer,mxEvent,mxUndoManager,mxHierarchicalLayout,mxEffects,mxEdgeLabelLayout,mxPerimeter,mxEdgeStyle"
      )
      .end();
  },
};
