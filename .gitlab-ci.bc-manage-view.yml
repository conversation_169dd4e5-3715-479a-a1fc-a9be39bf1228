.build-bc-manage-view:
  stage: build
  only: 
    - sit
    - develop
    - /^release[-_].*$/
    - master
    - /^dev-*$/
  tags:
    - win205
  script:
    - $ENV:path='C:\nodedic\node-v16.13.1-win-x64;'+$ENV:path
    - npm install --registry=https://npm.thinkive.com/repository/npm-group
    - npm run build --$CI_COMMIT_BRANCH >tmp.log
    - push_to_git "dist\bc-manage-view" "account/bc-manage-view" "$CI_PROJECT_NAMESPACE $CI_PROJECT_NAME  $CI_COMMIT_SHA $CI_COMMIT_BRANCH $CI_COMMIT_AUTHOR"
  when: manual


